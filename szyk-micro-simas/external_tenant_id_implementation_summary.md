# External Tenant ID 功能实现总结

## 功能概述

在 AI 工具和智能问答功能中添加 `external_tenant_id` 参数，用于传递给 Dify 平台，以便根据煤炭知识平台的租户ID过滤对应租户的数据。

## 修改的文件

#### `szyk-micro-simas/src/main/java/com/snszyk/simas/ai/DifyChatFlowClient.java`
- **修改内容**:
  - 添加了 `ISysClient` 依赖注入
  - 在 `chatMessagesByStreaming` 方法中通过 `sysClient.getTenant()` 获取租户信息
  - 从租户信息中提取 `external_tenant_id` 并添加到 inputs 中
- **影响功能**: ai-assistant/chat-stream (智能问答流式接口)

#### `szyk-micro-simas/src/main/java/com/snszyk/simas/ai/service/AiToolsService.java`
- **修改内容**:
  - 添加了 `ISysClient` 依赖注入
  - 新增了 `getExternalTenantId()` 私有方法来获取租户的external_tenant_id
  - 在 `executeWorkflowAndGetResult` 方法中添加 `external_tenant_id` 参数
  - 在 `generateRepairSuggest` 方法中添加 `external_tenant_id` 参数
  - 在 `generateRepairSuggestStream` 方法中添加 `external_tenant_id` 参数
- **影响功能**:
  - ai-tools/generate-operate-standards (生成运维标准)
  - ai-tools/generate-repair-suggest (生成维修建议)

## 实现逻辑

1. **获取 external_tenant_id**: 通过 `sysClient.getTenant()` 方法获取当前租户信息，然后从 Tenant 对象中提取 `external_tenant_id` 字段
2. **默认值处理**: 如果 `external_tenant_id` 为空或获取失败，使用默认值 "000000"
3. **参数传递**: 将 `external_tenant_id`（实际值或默认值）添加到传递给 Dify 的参数中
4. **错误处理**: 所有异常情况都会使用默认值，确保参数始终传递

## 实现细节

### 获取 external_tenant_id 的方法

```java
private String getExternalTenantId() {
    try {
        R<Tenant> tenantResult = sysClient.getTenant(AuthUtil.getTenantId());
        if (tenantResult.isSuccess() && tenantResult.getData() != null) {
            String externalTenantId = tenantResult.getData().getExternalTenantId();
            if (Func.isNotEmpty(externalTenantId)) {
                return externalTenantId;
            } else {
                log.warn("租户的external_tenant_id为空，使用默认值: 000000");
                return "000000";
            }
        }
    } catch (Exception e) {
        log.error("获取external_tenant_id失败，使用默认值: 000000", e);
    }
    return "000000";
}
```

### 参数传递示例

```java
// 在 DifyChatFlowClient 中
String externalTenantId = "000000"; // 默认值
try {
    R<Tenant> tenantResult = sysClient.getTenant(AuthUtil.getTenantId());
    if (tenantResult.isSuccess() && tenantResult.getData() != null) {
        String tenantExternalId = tenantResult.getData().getExternalTenantId();
        if (Func.isNotEmpty(tenantExternalId)) {
            externalTenantId = tenantExternalId;
        }
    }
} catch (Exception e) {
    log.error("获取external_tenant_id失败，使用默认值: {}", externalTenantId, e);
}
inputs.put("external_tenant_id", externalTenantId); // 始终传递参数
```

## 测试验证

1. **智能问答测试**: 调用 ai-assistant/chat-stream 接口，验证 external_tenant_id 参数是否正确传递
2. **生成运维标准测试**: 调用 ai-tools/generate-operate-standards 接口，验证参数传递
3. **生成维修建议测试**: 调用 ai-tools/generate-repair-suggest 接口，验证参数传递

## 前置条件

确保 Tenant 实体类中已经包含 `externalTenantId` 字段，并且该字段在数据库中有对应的值。

## 日志监控

所有相关操作都添加了详细的日志记录：
- DEBUG 级别：成功获取和传递 external_tenant_id
- WARN 级别：无法获取 external_tenant_id 或字段为空
- ERROR 级别：获取过程中发生异常

## 注意事项

1. **默认值保证**: 无论任何情况，external_tenant_id 参数都会传递给 Dify，确保接口的一致性
2. **默认值 "000000"**: 当租户的 external_tenant_id 为空或获取失败时，使用 "000000" 作为默认值
3. **性能考虑**: external_tenant_id 的获取会在每次调用时执行，如果需要优化性能，可以考虑添加缓存
4. **错误处理**: 所有异常都被捕获并记录，使用默认值确保不影响主要业务流程
5. **数据完整性**: 建议在 tenant 表中正确填充 external_tenant_id 字段，避免频繁使用默认值

## 新增功能：煤炭设备知识库来源支持

### 修改内容

1. **DifyDbType.java** - 添加了新的枚举值：
   ```java
   COAL_KNOWLEDGE_DATABASE("煤炭设备知识库")
   ```

2. **DifyClient.java** - 在 `extractSourceList` 方法中添加了对煤炭设备知识库的处理：
   - dataset 名称：`煤炭知识知识库`
   - 来源类型：`煤炭设备知识库`
   - 文件链接：从元数据的 `attach_link` 字段获取

3. **DifyChatFlowClient.java** - 在 `extractSourceList` 方法中也添加了对煤炭设备知识库的处理：
   - 与 DifyClient 保持一致的处理逻辑
   - 支持智能问答流式接口的来源识别

### 处理逻辑

```java
// 如果是煤炭设备知识库
if ("煤炭知识知识库".equals(datasetName)) {
    DifySourceDTO sourceDTO = new DifySourceDTO();
    sourceDTO.setSourceType(DifyDbType.COAL_KNOWLEDGE_DATABASE.getDescription());
    String fileName = metadata.getString("document_name");
    sourceDTO.setFileName(fileName);

    // 从元数据中获取attach_link作为文件链接
    String attachLink = metadata.getString("attach_link");
    if (Func.isNotEmpty(attachLink)) {
        sourceDTO.setFileLink(attachLink);
    }

    // 防重复处理
    String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
    if (!repeatCheckList.contains(strRepeat)) {
        repeatCheckList.add(strRepeat);
        sourceList.add(sourceDTO);
    }
}
```

## 部署说明

1. 确保 ISysClient 的依赖注入正确配置
2. 验证 Tenant 实体类包含 externalTenantId 字段
3. 检查 tenant 表中 external_tenant_id 字段的数据
4. 验证 Dify 平台中煤炭知识库的 dataset 名称为 "煤炭知识知识库"
5. 确保煤炭知识库文档的元数据包含 attach_link 字段
6. 检查日志配置，确保能够看到相关的调试和警告信息
