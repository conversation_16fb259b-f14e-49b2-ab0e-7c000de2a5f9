/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas;

import com.snszyk.core.cloud.feign.EnableSzykFeign;
import com.snszyk.core.launch.SzykApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 设备全生命周期启动器
 * <AUTHOR>
 */
@EnableSzykFeign
@SpringCloudApplication
@EnableScheduling
public class SimasApplication {


	public static void main(String[] args) {
		SzykApplication.run("szyk-simas", SimasApplication.class, args);
	}

}
