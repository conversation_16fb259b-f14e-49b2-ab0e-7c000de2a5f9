package com.snszyk.simas.fault.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.fault.dto.FaultDefectCaseDTO;
import com.snszyk.simas.fault.entity.FaultDefectCase;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.fault.enums.FaultDefectTypeEnum;
import com.snszyk.simas.fault.vo.FaultDefectCaseVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class FaultDefectCaseWrapper extends BaseEntityWrapper<FaultDefectCase, FaultDefectCaseVO> {


	public static FaultDefectCaseWrapper build() {
		return new FaultDefectCaseWrapper();
	}

	@Override
	public FaultDefectCaseVO entityVO(FaultDefectCase entity) {
		FaultDefectCaseVO faultDefectCaseVO = Objects.requireNonNull(BeanUtil.copy(entity, FaultDefectCaseVO.class));
		return faultDefectCaseVO;
	}

	public FaultDefectCaseDTO entityDTO(FaultDefectCase entity){
		FaultDefectCaseDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, FaultDefectCaseDTO.class));
		if (Func.isNotEmpty(dto.getFaultLevel())){
			dto.setFaultLevelName(DefectLevelEnum.getByCode(dto.getFaultLevel()).getName());
		}
		if (Func.isNotEmpty(dto.getFaultType())){
			dto.setFaultTypeName(FaultDefectTypeEnum.getByCode(dto.getFaultType()).getName());
		}
		return dto;
	}

	public List<FaultDefectCaseDTO> listDTO(List<FaultDefectCase> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}
}
