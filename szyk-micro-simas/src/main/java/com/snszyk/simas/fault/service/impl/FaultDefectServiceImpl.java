 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.fault.service.impl;

 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.location.entity.Location;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.resource.entity.Attach;
 import com.snszyk.resource.feign.IAttachClient;
 import com.snszyk.simas.common.entity.BizLog;
 import com.snszyk.simas.common.enums.MessageContentTypeEnum;
 import com.snszyk.simas.common.enums.OrderActionEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.enums.TicketStatusEnum;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.processor.RepairLogProcessor;
 import com.snszyk.simas.common.service.IBizLogService;
 import com.snszyk.simas.common.service.logic.EquipmentFaultRepairMessageRuleLogicService;
 import com.snszyk.simas.common.wrapper.BizLogWrapper;
 import com.snszyk.simas.fault.dto.FaultDefectDTO;
 import com.snszyk.simas.fault.entity.FaultDefect;
 import com.snszyk.simas.fault.enums.DefectLevelEnum;
 import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
 import com.snszyk.simas.fault.enums.FaultSourceEnum;
 import com.snszyk.simas.fault.mapper.FaultDefectMapper;
 import com.snszyk.simas.fault.service.IFaultDefectService;
 import com.snszyk.simas.fault.vo.FaultDefectAbnormalVO;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import com.snszyk.simas.fault.wrapper.FaultDefectWrapper;
 import com.snszyk.simas.inspect.entity.InspectRecord;
 import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
 import com.snszyk.simas.inspect.mapper.InspectRecordMapper;
 import com.snszyk.simas.lubricate.entity.LubricateOrder;
 import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
 import com.snszyk.simas.maintain.entity.MaintainRecord;
 import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
 import com.snszyk.simas.maintain.mapper.MaintainRecordMapper;
 import com.snszyk.simas.overhaul.entity.Repair;
 import com.snszyk.simas.overhaul.enums.RepairActionEnum;
 import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
 import com.snszyk.simas.overhaul.service.IRepairService;
 import com.snszyk.simas.overhaul.vo.RepairVO;
 import com.snszyk.simas.overhaul.wrapper.RepairWrapper;
 import com.snszyk.system.cache.DictBizCache;
 import com.snszyk.system.cache.SysCache;
 import com.snszyk.system.entity.Dept;
 import com.snszyk.system.enums.DictBizEnum;
 import com.snszyk.user.cache.UserCache;
 import com.snszyk.user.entity.User;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Objects;
 import java.util.stream.Collectors;

 /**
  * 设备故障缺陷表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 @AllArgsConstructor
 @Service
 public class FaultDefectServiceImpl extends BaseServiceImpl<FaultDefectMapper, FaultDefect> implements IFaultDefectService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final InspectRecordMapper inspectRecordMapper;
	 private final InspectOrderMapper inspectOrderMapper;
	 private final MaintainOrderMapper maintainOrderMapper;
	 private final MaintainRecordMapper maintainRecordMapper;
	 private final LubricateOrderMapper lubricateOrderMapper;
	 private final EquipmentFaultRepairMessageRuleLogicService equipmentLevelLogicService;
	 private final IRepairService repairService;
	 private final IBizLogService bizLogService;
	 private final IAttachClient attachClient;


	 @Override
	 public IPage<FaultDefectDTO> page(IPage<FaultDefectDTO> page, FaultDefectVO vo) {
		 List<FaultDefectDTO> list = baseMapper.page(page, vo);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 if (Func.isNotEmpty(dto.getLocationId())) {
					 Location location = CommonCache.getLocation(dto.getLocationId());
					 if (Func.isNotEmpty(location)) {
						 dto.setLocationName(location.getName()).setLocationPath(location.getPath().replace(",", "/"));
					 }
				 }
				 dto.setSourceName(DictBizCache.getValue(DictBizEnum.FAULT_SOURCE, dto.getSource()))
					 .setLevelName(DictBizCache.getValue(DictBizEnum.DEFECT_LEVEL, dto.getLevel()))
					 .setStatusName(FaultBizStatusEnum.getByCode(dto.getStatus()).getName());
				 if (Func.isNotEmpty(dto.getReportUser())) {
					 User reportUser = UserCache.getUser(dto.getReportUser());
					 if(Func.isNotEmpty(reportUser)){
						 dto.setReportUserName(reportUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getReportDept())) {
					 Dept reportDept = SysCache.getDept(dto.getReportDept());
					 if(Func.isNotEmpty(reportDept)){
						 dto.setReportDeptName(reportDept.getDeptName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getOperateUser())) {
					 User operateUser = UserCache.getUser(dto.getOperateUser());
					 if(Func.isNotEmpty(operateUser)){
						 dto.setOperateUserName(operateUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getOperateDept())) {
					 Dept operateDept = SysCache.getDept(dto.getOperateDept());
					 if(Func.isNotEmpty(operateDept)){
						 dto.setReportDeptName(operateDept.getDeptName());
					 }
				 }
			 });
		 }
		 return page.setRecords(list);
	 }

	 @Override
	 public FaultDefectDTO detail(Long id) {
		 FaultDefect faultDefect = this.getById(id);
		 if (faultDefect == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 FaultDefectDTO detail = FaultDefectWrapper.build().entityDTO(faultDefect);
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(detail.getEquipmentId());
		 if (deviceAccountResult.isSuccess() && Func.isNotEmpty(deviceAccountResult.getData())) {
			 detail.setEquipmentAccount(deviceAccountResult.getData());
		 }
		 // 部位名称
		 //final String monitorName = Optional.ofNullable(detail.getMonitorId())
		 //	.map(equipmentMonitorService::getById)
		 //	.map(EquipmentMonitor::getName)
		 //	.orElse(null);
		 DeviceMonitor deviceMonitor = CommonCache.getMonitor(detail.getMonitorId());
		 if (Func.isNotEmpty(deviceMonitor)) {
			 detail.setMonitorName(deviceMonitor.getName());
		 }
		 if (Func.isNotEmpty(faultDefect.getAttachId())) {
			 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(faultDefect.getAttachId()));
			 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
				 detail.setAttachList(attachListR.getData());
			 }
		 }
		 return detail;
	 }

	 @Override
	 public FaultDefectDTO view(String no) {
		 FaultDefect faultDefect = this.getOne(Wrappers.<FaultDefect>query().lambda().eq(FaultDefect::getNo, no));
		 if (faultDefect == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 FaultDefectDTO detail = FaultDefectWrapper.build().entityDTO(faultDefect);
		 // 设备基本信息
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(detail.getEquipmentId());
		 if (deviceAccountResult.isSuccess() && Func.isNotEmpty(deviceAccountResult.getData())) {
			 detail.setEquipmentAccount(deviceAccountResult.getData());
		 }
		 // 异常信息
		 // 部位名称
		 DeviceMonitor deviceMonitor = CommonCache.getMonitor(detail.getMonitorId());
		 if (Func.isNotEmpty(deviceMonitor)) {
			 detail.setMonitorName(deviceMonitor.getName());
		 }
		 // 来源：点检
		 if (FaultSourceEnum.INSPECT == FaultSourceEnum.getByCode(detail.getSource())) {
			 InspectRecord inspectRecord = inspectRecordMapper.selectOne(Wrappers.<InspectRecord>query().lambda()
				 .eq(InspectRecord::getOrderId, detail.getBizId()).eq(InspectRecord::getEquipmentId, detail.getEquipmentId())
				 .eq(InspectRecord::getMonitorId, detail.getMonitorId()).eq(InspectRecord::getStandardId, detail.getStandardId())
				 .orderByDesc(InspectRecord::getId).last("limit 1"));
			 detail.setAbnormalLevel(inspectRecord.getAbnormalLevel())
				 .setAbnormalLevelName(DefectLevelEnum.getByCode(inspectRecord.getAbnormalLevel()).getName())
				 .setAbnormalComment(inspectRecord.getAbnormalComment());
			 if (Func.isNotEmpty(inspectRecord.getAbnormalImage())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(inspectRecord.getAbnormalImage()));
				 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
					 detail.setAbnormalAttachList(attachListR.getData());
				 }
			 }
		 }
		 // 来源：保养
		 if (FaultSourceEnum.MAINTAIN == FaultSourceEnum.getByCode(detail.getSource())) {
			 MaintainRecord maintainRecord = maintainRecordMapper.selectOne(Wrappers.<MaintainRecord>query().lambda()
				 .eq(MaintainRecord::getOrderId, detail.getBizId()).eq(MaintainRecord::getEquipmentId, detail.getEquipmentId())
				 .eq(MaintainRecord::getMonitorId, detail.getMonitorId()).eq(MaintainRecord::getStandardId, detail.getStandardId()));
			 detail.setAbnormalLevel(maintainRecord.getAbnormalLevel())
				 .setAbnormalLevelName(DefectLevelEnum.getByCode(maintainRecord.getAbnormalLevel()).getName())
				 .setAbnormalComment(maintainRecord.getAbnormalComment());
			 if (Func.isNotEmpty(maintainRecord.getAbnormalImage())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(maintainRecord.getAbnormalImage()));
				 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
					 detail.setAbnormalAttachList(attachListR.getData());
				 }
			 }
		 }
		 // 来源：润滑
		 if (FaultSourceEnum.LUBRICATE == FaultSourceEnum.getByCode(detail.getSource())) {
			 LubricateOrder lubricateOrder = lubricateOrderMapper.selectById(detail.getBizId());
			 detail.setAbnormalLevel(lubricateOrder.getAbnormalLevel())
				 .setAbnormalLevelName(DefectLevelEnum.getByCode(lubricateOrder.getAbnormalLevel()).getName())
				 .setAbnormalComment(lubricateOrder.getAbnormalComment());
			 if (Func.isNotEmpty(lubricateOrder.getAttachIds())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(lubricateOrder.getAttachIds()));
				 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
					 detail.setAbnormalAttachList(attachListR.getData());
				 }
			 }
		 }
		 if(FaultSourceEnum.MANUAL == FaultSourceEnum.getByCode(detail.getSource())){
			 FaultDefectAbnormalVO faultDefectAbnormal = JSONUtil.toBean(detail.getAbnormalInfo(), FaultDefectAbnormalVO.class);
			 detail.setAbnormalLevel(faultDefectAbnormal.getAbnormalLevel());
			 detail.setAbnormalLevelName(DefectLevelEnum.getByCode(faultDefectAbnormal.getAbnormalLevel()).getName());
			 detail.setAbnormalComment(faultDefectAbnormal.getAbnormalComment());
			 if (Func.isNotEmpty(faultDefectAbnormal.getAbnormalImage())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(faultDefectAbnormal.getAbnormalImage()));
				 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
					 detail.setAbnormalAttachList(attachListR.getData());
				 }
			 }
		 }
		 // 维修单信息
		 if (Func.isNotEmpty(faultDefect.getRepairNo())) {
			 Repair repair = repairService.getOne(Wrappers.<Repair>query().lambda()
				 .eq(Repair::getNo, faultDefect.getRepairNo()));
			 detail.setRepair(RepairWrapper.build().entityDTO(repair));
		 }
		 // 补充图片
		 if (Func.isNotEmpty(faultDefect.getAttachId())) {
			 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(faultDefect.getAttachId()));
			 if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
				 detail.setAttachList(attachListR.getData());
			 }
		 }
		 // 处理信息（状态已处理：处理结果：resultName，处理人：operateUser，处理时间：operateTime，备注：operateRemark）
		 // （状态已报修：处理结果：statusName，维修工单号：repairNo，故障缺陷名称：name，故障缺陷类型：typeName，
		 // 处理人：operateUser，处理时间：operateTime，异常描述：comment，补充图片：attachList）

		 // 业务日志列表
		 List<BizLog> bizLogList = bizLogService.list(Wrappers.<BizLog>query().lambda()
			 .eq(BizLog::getBizId, detail.getId()).orderByDesc(BizLog::getId));
		 detail.setBizLogList(BizLogWrapper.build().listVO(bizLogList));
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean add(FaultDefectVO vo) {
		 FaultDefect faultDefect = Objects.requireNonNull(BeanUtil.copy(vo, FaultDefect.class));
		 faultDefect.setNo(BizCodeUtil.generate("YC"))
			 .setSource(FaultSourceEnum.MANUAL.getCode())
			 .setStatus(FaultBizStatusEnum.WAIT_HANDLED.getCode());
		 User reportUser = UserCache.getUser(vo.getReportUser());
		 if (Func.isNotEmpty(reportUser)) {
			 faultDefect.setReportDept(Func.firstLong(reportUser.getDeptId()));
		 }
		 faultDefect.setReportTime(DateUtil.now());
		 FaultDefectAbnormalVO faultDefectAbnormal = vo.getFaultDefectAbnormal();
		 if(Func.isNotEmpty(faultDefectAbnormal)){
			 if(Func.isNotEmpty(faultDefectAbnormal.getMonitorId())){
				 faultDefect.setMonitorId(faultDefectAbnormal.getMonitorId());
			 }
			 faultDefect.setMonitorName(faultDefectAbnormal.getMonitorName());
			 faultDefect.setAbnormalInfo(JSONUtil.toJsonStr(vo.getFaultDefectAbnormal()));
		 }
		 boolean ret = this.save(faultDefect);
		 // 业务日志
		 OrderLogProcessor.saveBizLog(SystemModuleEnum.FAULT_DEFECT, JSON.parseObject(JSON.toJSONString(faultDefect)), OrderActionEnum.ABNORMAL_GEN);
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean modify(FaultDefectVO vo) {
		 FaultDefect entity = this.getById(vo.getId());
		 if (entity == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 BeanUtil.copy(vo, entity);
		 User reportUser = UserCache.getUser(vo.getReportUser());
		 if (Func.isNotEmpty(reportUser)) {
			 entity.setReportDept(Func.firstLong(reportUser.getDeptId()));
		 }
		 return this.updateById(entity);
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean submit(List<FaultDefectVO> list) {
		 List<FaultDefect> faultDefectList = list.stream().map(vo -> {
			 FaultDefect faultDefect = Objects.requireNonNull(BeanUtil.copy(vo, FaultDefect.class));
			 faultDefect.setNo(BizCodeUtil.generate("YC"));
			 User reportUser = UserCache.getUser(vo.getReportUser());
			 if (Func.isNotEmpty(reportUser)) {
				 faultDefect.setReportDept(Func.firstLong(reportUser.getDeptId()));
			 }
			 return faultDefect;
		 }).collect(Collectors.toList());
		 boolean ret = this.saveBatch(faultDefectList);
		 // 业务日志
		 faultDefectList.forEach(faultDefect -> {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.FAULT_DEFECT, JSON.parseObject(JSON.toJSONString(faultDefect)), OrderActionEnum.ABNORMAL_GEN);
		 });
		 if (Func.isNotEmpty(list)) {
			 list.forEach(vo -> {
				 equipmentLevelLogicService.pushFaultDefectOrRepairMessage(vo.getEquipmentId(), "", MessageContentTypeEnum.SIMAS_FAULT_DEFECT, TicketStatusEnum.GENERATED);
			 });
		 }
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean handle(FaultDefectVO vo) {
		 FaultDefect faultDefect = this.getById(vo.getId());
		 if (faultDefect == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 faultDefect.setOperateUser(AuthUtil.getUserId())
			 .setOperateTime(DateUtil.now())
			 .setOperateRemark(vo.getOperateRemark());
		 User operateUser = UserCache.getUser(faultDefect.getOperateUser());
		 if (Func.isNotEmpty(operateUser)) {
			 faultDefect.setOperateDept(Func.firstLong(operateUser.getDeptId()));
		 }
		 if (Func.isNotEmpty(vo.getResult())) {
			 Integer faultDefectStatus;
			 TicketStatusEnum ticketStatusEnum;
			 if (vo.getResult() == 1) {
				 faultDefectStatus = FaultBizStatusEnum.IS_HANDLED.getCode();
				 // 业务日志
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.FAULT_DEFECT, JSON.parseObject(JSON.toJSONString(faultDefect)),
					 OrderActionEnum.ABNORMAL_HANDLE);
				 ticketStatusEnum = TicketStatusEnum.COMPLETED;
			 } else {
				 faultDefectStatus = FaultBizStatusEnum.IS_CLOSED.getCode();
				 // 业务日志
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.FAULT_DEFECT, JSON.parseObject(JSON.toJSONString(faultDefect)),
					 OrderActionEnum.ABNORMAL_CLOSE);
				 ticketStatusEnum = TicketStatusEnum.CLOSED;
			 }
			 faultDefect.setResult(vo.getResult()).setRemark(vo.getRemark())
				 .setStatus(faultDefectStatus);
			 equipmentLevelLogicService.pushFaultDefectOrRepairMessage(faultDefect.getEquipmentId(), "",
				 MessageContentTypeEnum.SIMAS_FAULT_DEFECT, ticketStatusEnum);
		 } else {
			 // 维修上报，生成维修单
			 vo.setSource(faultDefect.getSource()).setNo(faultDefect.getNo());
			 vo.setEquipmentId(faultDefect.getEquipmentId()).setMonitorId(faultDefect.getMonitorId());
			 RepairVO repairVO = new RepairVO().toRepairVO(vo);
			 Repair repair = repairService.submit(repairVO);
			 faultDefect.setRepairNo(repair.getNo()).setName(repairVO.getFaultName()).setLevel(vo.getRepair().getFaultLevel())
				 .setType(repairVO.getRepairType()).setComment(vo.getRepair().getProblemComment())
				 .setStatus(FaultBizStatusEnum.IS_REPAIR.getCode());
			 // 异常图片
			 faultDefect.setAttachId(vo.getRepair().getAttachId());
			 // 业务日志
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.FAULT_DEFECT, JSON.parseObject(JSON.toJSONString(faultDefect)),
				 OrderActionEnum.ABNORMAL_REPAIR);
			 // 业务日志
			 final String logContent = RepairLogProcessor.getHandlingDefectsReportLogContent();
			 RepairLogProcessor.saveBizLog(RepairActionEnum.HANDLING_DEFECTS_REPORT, RepairBizTypeEnum.INTERNAL, repair.getId(), JSONUtil.toJsonStr(repair), logContent);
		 }
		 return this.updateById(faultDefect);
	 }

	 @Override
	 public boolean reportAbnormal(FaultDefectVO vo) {
		 vo.setReportUser(AuthUtil.getUserId());
		 return this.add(vo);
	 }

	 @Override
	 public List<FaultDefectDTO> curYearFault(LocalDateTime start, List<String> statusList, String tenantId) {
		 return baseMapper.curYearFault(start, statusList, tenantId);
	 }


 }
