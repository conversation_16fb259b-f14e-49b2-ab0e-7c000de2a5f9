package com.snszyk.simas.fault.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.fault.dto.FaultDefectCaseDTO;
import com.snszyk.simas.fault.service.IFaultDefectCaseService;
import com.snszyk.simas.fault.vo.FaultDefectCaseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/fault-defect/case")
@Api(value = "故障缺陷案例库管理", tags = "故障缺陷案例库管理表接口")
public class FaultDefectCaseController extends SzykController {

	private final IFaultDefectCaseService faultDefectCaseService;

	@GetMapping("/view")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查看", notes = "传入no")
	public R<FaultDefectCaseDTO> view(String no) {
		return R.data(faultDefectCaseService.view(no));
	}

	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryStartTime", value = "查询-开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "queryEndTime", value = "查询-结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "no", value = "编码", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "faultName", value = "故障缺陷名称", paramType = "query", dataType = "string"),
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入faultDefectCase")
	public R<IPage<FaultDefectCaseDTO>> page(FaultDefectCaseVO faultDefectCase, Query query) {
		return R.data(faultDefectCaseService.page(Condition.getPage(query), faultDefectCase));
	}


}
