/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.fault.vo.FaultDefectVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备故障缺陷表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface FaultDefectMapper extends BaseMapper<FaultDefect> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param faultDefect
	 * @return
	 */
	List<FaultDefectDTO> page(IPage page, @Param("faultDefect") FaultDefectVO faultDefect);

	/**
	 * 统计报表-导出故障缺陷统计
	 *
	 * @param search
	 * @return
	 */
	List<FaultDefectDTO> exportList(@Param("search") StatisticSearchVO search);


	List<FaultDefectDTO> curYearFault(@Param("start") LocalDateTime start, @Param("statusList") List<String> statusList, String tenantId);
}
