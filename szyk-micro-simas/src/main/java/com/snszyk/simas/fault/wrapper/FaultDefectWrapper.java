/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.wrapper;

import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
import com.snszyk.simas.fault.enums.FaultDefectTypeEnum;
import com.snszyk.simas.fault.enums.FaultSourceEnum;
import com.snszyk.simas.fault.vo.FaultDefectVO;
import com.snszyk.simas.inspect.vo.InspectRecordVO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备故障缺陷表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public class FaultDefectWrapper extends BaseEntityWrapper<FaultDefect, FaultDefectVO> {

	public static FaultDefectWrapper build() {
		return new FaultDefectWrapper();
	}

	@Override
	public FaultDefectVO entityVO(FaultDefect faultDefect) {
		FaultDefectVO faultDefectVO = Objects.requireNonNull(BeanUtil.copy(faultDefect, FaultDefectVO.class));

		User reportUser = UserCache.getUser(faultDefect.getReportUser());
		if (Func.isNotEmpty(reportUser)) {
			faultDefectVO.setReportUserName(reportUser.getRealName());
		}
		User operateUser = UserCache.getUser(faultDefect.getOperateUser());
		if (Func.isNotEmpty(operateUser)) {
			faultDefectVO.setOperateUserName(operateUser.getRealName());
		}

		return faultDefectVO;
	}

	public FaultDefectDTO entityDTO(FaultDefect faultDefect) {
		FaultDefectDTO faultDefectDTO = Objects.requireNonNull(BeanUtil.copy(faultDefect, FaultDefectDTO.class));
		faultDefectDTO.setSourceName(DictBizCache.getValue(DictBizEnum.FAULT_SOURCE, faultDefect.getSource()))
			.setStatusName(FaultBizStatusEnum.getByCode(faultDefect.getStatus()).getName());
		// 上报人
		if (Func.isNotEmpty(faultDefect.getReportUser())) {
			User reportUser = UserCache.getUser(faultDefect.getReportUser());
			if (Func.isNotEmpty(reportUser)) {
				faultDefectDTO.setReportUserName(reportUser.getRealName());
			}
		}
		// 上报部门
		if (Func.isNotEmpty(faultDefect.getReportDept())) {
			Dept reportDept = SysCache.getDept(faultDefect.getReportDept());
			if(Func.isNotEmpty(reportDept)){
				faultDefectDTO.setReportDeptName(reportDept.getDeptName());
			}
		}
		// 处理结果
		if(Func.isNotEmpty(faultDefect.getResult())){
			faultDefectDTO.setResultName(DictBizCache.getValue(DictBizEnum.FAULT_HANDLE_RESULT, faultDefect.getResult()));
		} else {
			if(FaultBizStatusEnum.IS_REPAIR == FaultBizStatusEnum.getByCode(faultDefect.getStatus())){
				faultDefectDTO.setResultName(FaultBizStatusEnum.IS_REPAIR.getName());
			}
		}
		// 处理人
		if (Func.isNotEmpty(faultDefect.getOperateUser())) {
			User operateUser = UserCache.getUser(faultDefect.getOperateUser());
			if (Func.isNotEmpty(operateUser)) {
				faultDefectDTO.setOperateUserName(operateUser.getRealName());
			}
		}
		// 处理部门
		if (Func.isNotEmpty(faultDefect.getOperateDept())) {
			Dept operateDept = SysCache.getDept(faultDefect.getOperateDept());
			if(Func.isNotEmpty(operateDept)){
				faultDefectDTO.setReportDeptName(operateDept.getDeptName());
			}
		}
		// 故障缺陷等级
		if(Func.isNotEmpty(faultDefect.getLevel())){
			faultDefectDTO.setLevelName(DefectLevelEnum.getByCode(faultDefect.getLevel()).getName());
		}
		// 维修上报的数据的故障类型
		if(Func.isNotEmpty(faultDefect.getType())){
			faultDefectDTO.setTypeName(FaultDefectTypeEnum.getByCode(faultDefect.getType()).getName());
		}
		return faultDefectDTO;
	}

	public List<FaultDefectDTO> listDTO(List<FaultDefect> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

	public FaultDefectVO inspectEntityVO(InspectRecordVO inspectRecord) {
		FaultDefectVO faultDefectVO = Objects.requireNonNull(BeanUtil.copy(inspectRecord, FaultDefectVO.class));
		faultDefectVO.setBizId(inspectRecord.getOrderId());
		//faultDefectVO.setLevel(inspectRecord.getAbnormalLevel());
		faultDefectVO.setSource(FaultSourceEnum.INSPECT.getCode());
		faultDefectVO.setSourceNo(inspectRecord.getOrderNo());
		faultDefectVO.setStatus(FaultBizStatusEnum.WAIT_HANDLED.getCode());
		faultDefectVO.setReportUser(inspectRecord.getInspectUser());
		faultDefectVO.setReportTime(DateUtil.now());
		if (inspectRecord.getIsHandled() != null && inspectRecord.getIsHandled() == 1) {
			faultDefectVO.setOperateUser(inspectRecord.getInspectUser()).setOperateTime(faultDefectVO.getReportTime())
				.setResult(1).setStatus(FaultBizStatusEnum.IS_HANDLED.getCode());
		}
		//faultDefectVO.setComment(inspectRecord.getAbnormalComment());
		faultDefectVO.setCreateUser(faultDefectVO.getReportUser());
		User user = UserCache.getUser(faultDefectVO.getCreateUser());
		if(Func.isNotEmpty(user)){
			faultDefectVO.setCreateDept(Func.firstLong(user.getDeptId()));
		}
		faultDefectVO.setCreateTime(faultDefectVO.getReportTime());
		return faultDefectVO;
	}

	public FaultDefectVO maintainEntityVO(MaintainRecordVO maintainRecord) {
		FaultDefectVO faultDefectVO = Objects.requireNonNull(BeanUtil.copy(maintainRecord, FaultDefectVO.class));
		faultDefectVO.setBizId(maintainRecord.getOrderId());
		//faultDefectVO.setLevel(maintainRecord.getAbnormalLevel());
		faultDefectVO.setSource(FaultSourceEnum.MAINTAIN.getCode());
		faultDefectVO.setSourceNo(maintainRecord.getOrderNo());
		faultDefectVO.setStatus(FaultBizStatusEnum.WAIT_HANDLED.getCode());
		faultDefectVO.setReportUser(maintainRecord.getMaintainUser());
		faultDefectVO.setReportTime(DateUtil.now());
		if (maintainRecord.getIsHandled() == 1) {
			faultDefectVO.setOperateUser(faultDefectVO.getReportUser()).setOperateTime(faultDefectVO.getReportTime())
				.setResult(1).setStatus(FaultBizStatusEnum.IS_HANDLED.getCode());
		}
		//faultDefectVO.setComment(maintainRecord.getAbnormalComment());
		faultDefectVO.setCreateUser(faultDefectVO.getReportUser());
		User user = UserCache.getUser(faultDefectVO.getCreateUser());
		if(Func.isNotEmpty(user)){
			faultDefectVO.setCreateDept(Func.firstLong(user.getDeptId()));
		}
		faultDefectVO.setCreateTime(faultDefectVO.getReportTime());
		return faultDefectVO;
	}

	/**
	 * <AUTHOR>
	 * @Description 润滑的缺陷
	 * @Date 下午4:29 2025/3/24
	 * @Param [lubricateOrder]
	 * @return com.snszyk.simas.fault.vo.FaultDefectVO
	 **/
	public FaultDefectVO lubricateEntityVO(LubricateOrder lubricateOrder) {
		FaultDefectVO faultDefectVO = Objects.requireNonNull(BeanUtil.copy(lubricateOrder, FaultDefectVO.class));
		faultDefectVO.setBizId(lubricateOrder.getId());
		//部位
		if (Func.isNotEmpty(lubricateOrder.getStandardsInfo())) {
			JSONObject jsonObject = JSONObject.parseObject(lubricateOrder.getStandardsInfo());
			if (jsonObject != null) {
				faultDefectVO.setMonitorId(Long.valueOf(jsonObject.getString("equipmentMonitorId")));
				faultDefectVO.setMonitorName(jsonObject.getString("equipmentMonitorName"));
			}
		}
		faultDefectVO.setStandardId(lubricateOrder.getStandardsId());
		//faultDefectVO.setLevel(lubricateOrder.getAbnormalLevel());
		faultDefectVO.setSource(FaultSourceEnum.LUBRICATE.getCode());
		faultDefectVO.setSourceNo(lubricateOrder.getNo());
		faultDefectVO.setStatus(FaultBizStatusEnum.WAIT_HANDLED.getCode());
		faultDefectVO.setReportUser(lubricateOrder.getExecuteUser());
		faultDefectVO.setReportTime(DateUtil.now());
		//faultDefectVO.setComment(lubricateOrder.getAbnormalComment());
		faultDefectVO.setAttachId(lubricateOrder.getAttachIds());
		if (lubricateOrder.getIsHandled() == 1) {
			faultDefectVO.setOperateUser(faultDefectVO.getReportUser()).setOperateTime(faultDefectVO.getReportTime())
				.setResult(1).setStatus(FaultBizStatusEnum.IS_HANDLED.getCode());
		}
		faultDefectVO.setCreateUser(faultDefectVO.getReportUser());
		User user = UserCache.getUser(faultDefectVO.getCreateUser());
		if(Func.isNotEmpty(user)){
			faultDefectVO.setCreateDept(Func.firstLong(user.getDeptId()));
		}
		faultDefectVO.setCreateTime(faultDefectVO.getReportTime());
		return faultDefectVO;
	}


}
