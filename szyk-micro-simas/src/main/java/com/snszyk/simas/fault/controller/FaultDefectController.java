 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.fault.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.simas.fault.dto.FaultDefectDTO;
 import com.snszyk.simas.fault.service.IFaultDefectService;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiImplicitParam;
 import io.swagger.annotations.ApiImplicitParams;
 import io.swagger.annotations.ApiOperation;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.validation.Valid;

 /**
  * 设备故障缺陷表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/fault-defect")
 @Api(value = "设备故障缺陷表", tags = "设备故障缺陷表接口")
 public class FaultDefectController extends SzykController {

	 private final IFaultDefectService faultDefectService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入id")
	 public R<FaultDefectDTO> detail(Long id) {
		 return R.data(faultDefectService.detail(id));
	 }

	 /**
	  * 查看
	  */
	 @GetMapping("/view")
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "查看", notes = "传入no")
	 public R<FaultDefectDTO> view(String no) {
		 return R.data(faultDefectService.view(no));
	 }

	 /**
	  * 自定义分页 设备故障缺陷表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "equipmentCode", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "level", value = "等级", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "source", value = "来源", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "equipmentSn", value = "设备编码", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "分页", notes = "传入faultDefect")
	 public R<IPage<FaultDefectDTO>> page(@ApiIgnore FaultDefectVO faultDefect, Query query) {
		 return R.data(faultDefectService.page(Condition.getPage(query), faultDefect));
	 }

	 /**
	  * 新增 设备故障缺陷表
	  */
	 @PostMapping("/add")
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "新增", notes = "传入faultDefect")
	 public R add(@Valid @RequestBody FaultDefectVO faultDefect) {
		 return R.status(faultDefectService.add(faultDefect));
	 }

	 /**
	  * 修改 设备故障缺陷表
	  */
	 @PostMapping("/modify")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "修改", notes = "传入faultDefect")
	 public R modify(@Valid @RequestBody FaultDefectVO faultDefect) {
		 return R.status(faultDefectService.modify(faultDefect));
	 }

	 /**
	  * 处理 设备故障缺陷表
	  */
	 @PostMapping("/handle")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "处理", notes = "传入faultDefect")
	 public R handle(@Valid @RequestBody FaultDefectVO faultDefect) {
		 return R.status(faultDefectService.handle(faultDefect));
	 }

	 /**
	  * 异常上报
	  */
 	@PostMapping("/report-abnormal")
 	@ApiOperationSupport(order = 7)
 	@ApiOperation(value = "异常上报", notes = "传入faultDefect")
 	public R reportAbnormal(@Valid @RequestBody FaultDefectVO faultDefect) {
 		return R.status(faultDefectService.reportAbnormal(faultDefect));
 	}


 }
