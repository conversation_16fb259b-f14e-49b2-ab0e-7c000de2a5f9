package com.snszyk.simas.fault.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.utils.PageUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.ai.service.AiToolsService;
import com.snszyk.simas.fault.dto.FaultDefectCaseDTO;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.fault.entity.FaultDefectCase;
import com.snszyk.simas.overhaul.entity.OverhaulRecord;
import com.snszyk.simas.fault.mapper.FaultDefectCaseMapper;
import com.snszyk.simas.common.service.IComponentMaterialService;
import com.snszyk.simas.fault.service.IFaultDefectCaseService;
import com.snszyk.simas.overhaul.service.IOverhaulRecordService;
import com.snszyk.simas.fault.vo.FaultDefectCaseVO;
import com.snszyk.simas.common.wrapper.ComponentMaterialWrapper;
import com.snszyk.simas.fault.wrapper.FaultDefectCaseWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class FaultDefectCaseServiceImpl extends BaseServiceImpl<FaultDefectCaseMapper, FaultDefectCase> implements IFaultDefectCaseService {

	private final IComponentMaterialService componentMaterialService;
	private final IOverhaulRecordService overhaulRecordService;


	@Override
	public boolean submit(FaultDefectCaseVO faultDefectCase) {
		faultDefectCase.setNo(BizCodeUtil.generate(SimasConstant.BizCode.FAULT_DEFECT_CASE));
		boolean save = this.save(faultDefectCase);
		String no = faultDefectCase.getNo();
		SpringUtil.getBean(AiToolsService.class).handleOneDefectSync(no);
		return save;
	}

	@Override
	public FaultDefectCaseDTO view(String no) {
		FaultDefectCase faultDefectCase = this.getOne(Wrappers.<FaultDefectCase>lambdaQuery().eq(FaultDefectCase::getNo, no));
		FaultDefectCaseDTO dto = FaultDefectCaseWrapper.build().entityDTO(faultDefectCase);
		// 更换备件情况：1.维修工单上报时 2.检修工单上报时填写
		List<ComponentMaterial> materialList = componentMaterialService.list(Wrappers.<ComponentMaterial>lambdaQuery().eq(ComponentMaterial::getBizNo, faultDefectCase.getRepairNo()));
		if (Func.isEmpty(materialList)){
			OverhaulRecord overhaulRecord = overhaulRecordService.getOne(Wrappers.<OverhaulRecord>lambdaQuery().eq(OverhaulRecord::getRepairId, faultDefectCase.getRepairId()));
			if (Func.isNotEmpty(overhaulRecord)){
				materialList = componentMaterialService.list(Wrappers.<ComponentMaterial>lambdaQuery().eq(ComponentMaterial::getBizNo, overhaulRecord.getId()));
			}
		}
		if (Func.isNotEmpty(materialList)){
			dto.setMaterialList(ComponentMaterialWrapper.build().listVO(materialList));
		}
		return dto;
	}

	@Override
	public IPage<FaultDefectCaseDTO> page(IPage<FaultDefectCase> page, FaultDefectCaseVO faultDefectCase) {
		IPage<FaultDefectCase> ipage = this.page(page, Wrappers.<FaultDefectCase>lambdaQuery()
			.eq(Func.isNotEmpty(faultDefectCase.getFaultType()), FaultDefectCase::getFaultType, faultDefectCase.getFaultType())
			.like(Func.isNotEmpty(faultDefectCase.getNo()), FaultDefectCase::getNo, faultDefectCase.getNo())
			.like(Func.isNotEmpty(faultDefectCase.getFaultName()), FaultDefectCase::getFaultName, faultDefectCase.getFaultName())
			.orderByDesc(FaultDefectCase::getCreateTime));
		return PageUtil.toPage(ipage, FaultDefectCaseWrapper.build().listDTO(ipage.getRecords()));
	}
}
