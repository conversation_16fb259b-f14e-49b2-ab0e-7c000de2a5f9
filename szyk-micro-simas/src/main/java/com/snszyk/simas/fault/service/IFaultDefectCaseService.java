package com.snszyk.simas.fault.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.fault.dto.FaultDefectCaseDTO;
import com.snszyk.simas.fault.entity.FaultDefectCase;
import com.snszyk.simas.fault.vo.FaultDefectCaseVO;

public interface IFaultDefectCaseService extends BaseService<FaultDefectCase> {

	/**
	 * 新增故障缺陷案例
	 * @param faultDefectCase 数据
	 * @return
	 */
	boolean submit(FaultDefectCaseVO faultDefectCase);

	/**
	 * 详情
	 * @param no 编码
	 * @return
	 */
	FaultDefectCaseDTO view(String no);

	/**
	 * 分页查询
	 * @param page 分页信息
	 * @param faultDefectCase 查询条件
	 * @return
	 */
	IPage<FaultDefectCaseDTO> page(IPage<FaultDefectCase> page, FaultDefectCaseVO faultDefectCase);
}
