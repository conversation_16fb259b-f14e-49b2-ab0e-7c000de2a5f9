<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.fault.mapper.FaultDefectCaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="faultDefectCaseResultMap" type="com.snszyk.simas.fault.entity.FaultDefectCase">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="repair_id" property="repairId"/>
        <result column="equipment_id" property="repairId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="equipment_name" property="repairId"/>
        <result column="monitor_name" property="repairId"/>
        <result column="equipment_model" property="repairId"/>
        <result column="equipment_category_id" property="repairId"/>
        <result column="equipment_category_name" property="repairId"/>
        <result column="equipment_sn" property="repairId"/>
        <result column="fault_level" property="repairId"/>
        <result column="fault_name" property="repairId"/>
        <result column="fault_type" property="repairId"/>
        <result column="fault_desc" property="repairId"/>
        <result column="fault_reason" property="repairId"/>
        <result column="solution" property="repairId"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


</mapper>
