<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.fault.mapper.FaultDefectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="faultDefectResultMap" type="com.snszyk.simas.fault.entity.FaultDefect">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="biz_id" property="bizId"/>
        <result column="source" property="source"/>
        <result column="source_no" property="sourceNo"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="level" property="level"/>
        <result column="comment" property="comment"/>
        <result column="result" property="result"/>
        <result column="attach_id" property="attachId"/>
        <result column="abnormal_info" property="abnormalInfo"/>
        <result column="remark" property="remark"/>
        <result column="repair_no" property="repairNo"/>
        <result column="report_user" property="reportUser"/>
        <result column="report_dept" property="reportDept"/>
        <result column="report_time" property="reportTime"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_dept" property="operateDept"/>
        <result column="operate_remark" property="operateRemark"/>
        <result column="operate_time" property="operateTime"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="faultDefectDTOResultMap" type="com.snszyk.simas.fault.dto.FaultDefectDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="equipment_sn" property="equipmentSn"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_model" property="equipmentModel"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="location_id" property="locationId"/>
        <result column="biz_id" property="bizId"/>
        <result column="source" property="source"/>
        <result column="source_no" property="sourceNo"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="level" property="level"/>
        <result column="comment" property="comment"/>
        <result column="result" property="result"/>
        <result column="attach_id" property="attachId"/>
        <result column="abnormal_info" property="abnormalInfo"/>
        <result column="remark" property="remark"/>
        <result column="repair_no" property="repairNo"/>
        <result column="report_user" property="reportUser"/>
        <result column="report_dept" property="reportDept"/>
        <result column="report_time" property="reportTime"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_dept" property="operateDept"/>
        <result column="operate_remark" property="operateRemark"/>
        <result column="operate_time" property="operateTime"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="page" resultMap="faultDefectDTOResultMap">
        select d.*, e.`code` as equipment_code, e.`sn` as equipment_sn, e.`name` as equipment_name,
        e.`model` as equipment_model, e.location_id
        from simas_fault_defect d
        left join device_account e on d.equipment_id = e.id
        where e.is_deleted = 0
        <if test="faultDefect.reportUser != null">
            and d.report_user = #{faultDefect.reportUser}
        </if>
        <if test="faultDefect.equipmentCode != null and faultDefect.equipmentCode != ''">
            and e.code like concat('%',#{faultDefect.equipmentCode},'%')
        </if>
        <if test="faultDefect.equipmentSn != null and faultDefect.equipmentSn != ''">
            and e.sn like concat('%',#{faultDefect.equipmentSn},'%')
        </if>
        <if test="faultDefect.level != null">
            and d.`level` = #{faultDefect.level}
        </if>
        <if test="faultDefect.source != null and faultDefect.source != ''">
            and d.`source` = #{faultDefect.source}
        </if>
        <if test="faultDefect.status != null">
            and d.status = #{faultDefect.status}
        </if>
        order by d.report_time desc
    </select>

    <select id="exportList" resultMap="faultDefectDTOResultMap">
        SELECT `name`, `type`, count(*) as count FROM simas_fault_defect
        where repair_no is not null
        <if test="search.queryDate == 1">
            AND report_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND report_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(report_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and report_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and report_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        group by `name`,`type`
        order by count desc
    </select>

    <select id="curYearFault" resultType="com.snszyk.simas.fault.dto.FaultDefectDTO">
        select t.* from simas_fault_defect t left join device_account e on t.equipment_id = e.id
        where t.report_time >= #{start}
        <if test="statusList != null and statusList.size() > 0">
            and t.status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and e.tenant_id = #{tenantId}
    </select>

</mapper>
