 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.fault.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.simas.fault.dto.FaultDefectDTO;
 import com.snszyk.simas.fault.entity.FaultDefect;
 import com.snszyk.simas.fault.vo.FaultDefectVO;

 import java.time.LocalDateTime;
 import java.util.List;

 /**
  * 设备故障缺陷表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 public interface IFaultDefectService extends BaseService<FaultDefect> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param faultDefect
	  * @return
	  */
	 IPage<FaultDefectDTO> page(IPage<FaultDefectDTO> page, FaultDefectVO faultDefect);

	 /**
	  * 详情
	  *
	  * @param id
	  * @return
	  */
	 FaultDefectDTO detail(Long id);

	 /**
	  * 查看
	  *
	  * @param no
	  * @return
	  */
	 FaultDefectDTO view(String no);

	 /**
	  * 新增
	  *
	  * @param faultDefect
	  * @return
	  */
	 boolean add(FaultDefectVO faultDefect);

	 /**
	  * 修改
	  *
	  * @param faultDefect
	  * @return
	  */
	 boolean modify(FaultDefectVO faultDefect);

	 /**
	  * 保存
	  *
	  * @param list
	  * @return
	  */
	 boolean submit(List<FaultDefectVO> list);

	 /**
	  * 处理
	  *
	  * @param faultDefect
	  * @return
	  */
	 boolean handle(FaultDefectVO faultDefect);

	 /**
	  * 异常上报
	  *
	  * @param faultDefect
	  * @return
	  */
	 boolean reportAbnormal(FaultDefectVO faultDefect);

	 /**
	  * 统计报表-故障缺陷统计
	  *
	  * @param page
	  * @param search
	  * @return
	  */
	 // IPage<FaultDefectDTO> statisticalReport(IPage<FaultDefectDTO> page, StatisticSearchVO search);

	 List<FaultDefectDTO> curYearFault(LocalDateTime start, List<String> statusList, String tenantId);
 }
