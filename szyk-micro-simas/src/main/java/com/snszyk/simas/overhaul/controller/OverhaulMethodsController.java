package com.snszyk.simas.overhaul.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.overhaul.dto.OverhaulMethodsDTO;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import com.snszyk.simas.overhaul.service.IOverhaulMethodsService;
import com.snszyk.simas.overhaul.wrapper.OverhaulMethodsWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/overhaul/methods")
@Api(value = "检修方式", tags = "检修方式接口")
public class OverhaulMethodsController extends SzykController {

	private final IOverhaulMethodsService overhaulMethodsService;

	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "编辑", notes = "传入OverhaulMethods")
	public R<Boolean> submit(@RequestBody OverhaulMethods overhaulMethods) {
		if (StringUtils.isEmpty(overhaulMethods.getName())) {
			return R.fail("检修方式不能为空");
		}
		overhaulMethods.setCreateTime(new Date());
		overhaulMethods.setTenantId(AuthUtil.getTenantId());
		boolean res = overhaulMethodsService.saveOrUpdate(overhaulMethods);
		if (!res) {
			return R.fail("更新失败");
		}
		return R.data(res);
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<OverhaulMethods> detail(Long id) {
		return R.data(overhaulMethodsService.getById(id));
	}

	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入overhaulMethods")
	public R<List<OverhaulMethods>> list(OverhaulMethods overhaulMethods) {
		return R.data(overhaulMethodsService.list(Condition.getQueryWrapper(overhaulMethods)));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入overhaulMethods")
	public R<IPage<OverhaulMethodsDTO>> page(OverhaulMethods overhaulMethods, Query query) {
		IPage<OverhaulMethods> page = overhaulMethodsService.page(Condition.getPage(query), Condition.getQueryWrapper(overhaulMethods).orderByDesc("create_time"));
		IPage<OverhaulMethodsDTO> convert = page.convert(e -> OverhaulMethodsWrapper.build().entityDTO(e));
		return R.data(convert);
	}

}
