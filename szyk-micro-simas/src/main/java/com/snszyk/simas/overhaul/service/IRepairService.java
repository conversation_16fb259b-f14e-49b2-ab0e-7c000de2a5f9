 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.excel.RepairExternalExcel;
 import com.snszyk.simas.common.excel.RepairInternalExcel;
 import com.snszyk.simas.common.excel.RepairStatisticsExcel;
 import com.snszyk.simas.common.vo.EquipmentRepairVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.fault.dto.FaultDefectDTO;
 import com.snszyk.simas.overhaul.dto.RepairDTO;
 import com.snszyk.simas.overhaul.entity.Repair;
 import com.snszyk.simas.overhaul.vo.RepairRecordVO;
 import com.snszyk.simas.overhaul.vo.RepairVO;

 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Map;

 /**
  * 设备维修单表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 public interface IRepairService extends BaseService<Repair> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param repair
	  * @return
	  */
	 IPage<RepairDTO> page(IPage<RepairDTO> page, RepairVO repair);

	 /**
	  * 详情
	  *
	  * @param no
	  * @return
	  */
	 RepairDTO detail(String no);

	 /**
	  * 查看
	  *
	  * @param no
	  * @return
	  */
	 RepairDTO view(String no);

	 /**
	  * 提交
	  *
	  * @param repair
	  * @return
	  */
	 Repair submit(RepairVO repair);

	 /**
	  * 派单
	  *
	  * @param repair
	  * @return
	  */
	 boolean dispatch(RepairVO repair);

	 /**
	  * 内部转外委
	  *
	  * @param repair
	  * @return
	  */
	 String toExternal(RepairVO repair);

	 /**
	  * 维修
	  *
	  * @param equipmentRepair
	  * @return
	  */
	 boolean repair(EquipmentRepairVO equipmentRepair);

	 /**
	  * 验证
	  *
	  * @param repairRecord
	  * @return
	  */
	 boolean verify(RepairRecordVO repairRecord);

	 /**
	  * 关闭
	  *
	  * @param id
	  * @return
	  */
	 boolean close(Long id);

	 /**
	  * 导出内部维修单
	  *
	  * @param repair
	  * @return
	  */
	 List<RepairInternalExcel> exportInternalOrder(RepairVO repair);

	 /**
	  * 导出外部维修单
	  *
	  * @param repair
	  * @return
	  */
	 List<RepairExternalExcel> exportExternalOrder(RepairVO repair);

	 /**
	  * 即将超期分页
	  *
	  * @param page
	  * @param repair
	  * @return
	  */
	 IPage<RepairDTO> timeoutPage(IPage<RepairDTO> page, RepairVO repair);

	 /**
	  * 即将超期工单数量
	  *
	  * @return
	  */
	 Integer expireSoonCount();

	 /**
	  * 发送消息提醒
	  *
	  * @param list
	  * @param messageBizType
	  */
	 void sendMessage(List<Repair> list, MessageBizTypeEnum messageBizType);

	 /**
	  * 维修耗时统计列表
	  *
	  * @param bizType
	  * @param queryDate
	  * @return
	  */
	 List<RepairDTO> timeTakeStatisticsList(String bizType, Integer queryDate, LocalDateTime startTime, LocalDateTime endTime);

	 /**
	  * 统计报表-维修统计
	  *
	  * @param page
	  * @param search
	  * @return
	  */
	 IPage<RepairDTO> statisticalReport(IPage<RepairDTO> page, StatisticSearchVO search);

	 /**
	  * 统计报表-维修统计导出
	  *
	  * @param search
	  * @return
	  */
	 List<RepairStatisticsExcel> exportStatisticalReport(StatisticSearchVO search);

	 /**
	  * 维修工单列表
	  *
	  * @param repair
	  * @return
	  */
	 List<RepairDTO> queryList(RepairVO repair);

	 /**
	  * 大屏-维修情况统计
	  *
	  * @param repair
	  * @param queryDate
	  * @return
	  */
	 List<RepairDTO> repairStatistics(RepairVO repair, int queryDate);


	 /**
	  * 检修转维修
	  *
	  * @param repair
	  * @return
	  */
	 Repair overhaulToRepair(RepairVO repair);

	 /**
	  * 大屏超期
	  *
	  * @return
	  */
	 List<BigScreenMessageDTO> overdueList(String tenantId);

	 /**
	  * @param bizType
	  * @return
	  */
	 List<RepairDTO> repair30day(String bizType);

	 /**
	  * @param bizType
	  * @param startTime
	  * @param endTime
	  * @return
	  */
	 List<RepairDTO> finishList(String bizType, LocalDateTime startTime, LocalDateTime endTime);

	 /**
	  * @param vo
	  * @return
	  */
	 Integer handleRepairCount(RepairVO vo);

	 /**
	  * @param start
	  * @param statusList
	  * @param tenantId
	  * @return
	  */
	 List<RepairDTO> curYearRepair(LocalDateTime start, List<Integer> statusList, String tenantId);

	 /**
	  * 设备维修统计-故障缺陷统计
	  *
	  * @param page
	  * @param vo
	  * @return
	  */
	 IPage<FaultDefectDTO> calculateStatisticsByFaultDefect(IPage<FaultDefectDTO> page, StatisticSearchVO vo);

	 /**
	  *
	  * @param receiveDept
	  * @param followDept
	  * @param receiveUserIds
	  * @param followUsers
	  * @param equipmentIds
	  * @param bizType
	  * @param startDateTime
	  * @param endDateTime
	  * @param neStatus
	  * @return
	  */
	 List<Repair> listBy(Long receiveDept, Long followDept, List<Long> receiveUserIds, List<Long> followUsers, List<Long> equipmentIds, String bizType, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus);

	 /**
	  * 维修设备统计
	  *
	  * @param search
	  * @param deviceMap
	  * @return
	  */
	 List<EquipmentStatisticsDTO> repairDeviceStatistics(StatisticSearchVO search, Map<Long, DeviceAccountVO> deviceMap);


 }
