<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.overhaul.mapper.RepairMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="repairResultMap" type="com.snszyk.simas.overhaul.entity.Repair">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="biz_type" property="bizType"/>
        <result column="source_no" property="sourceNo"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="source" property="source"/>
        <result column="repair_type" property="repairType"/>
        <result column="fault_level" property="faultLevel"/>
        <result column="fault_name" property="faultName"/>
        <result column="report_dept" property="reportDept"/>
        <result column="report_user" property="reportUser"/>
        <result column="tel" property="tel"/>
        <result column="report_time" property="reportTime"/>
        <result column="dispatch_user" property="dispatchUser"/>
        <result column="receive_dept" property="receiveDept"/>
        <result column="receive_user" property="receiveUser"/>
        <result column="external_org" property="externalOrg"/>
        <result column="external_contact" property="externalContact"/>
        <result column="external_tel" property="externalTel"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="follow_user" property="followUser"/>
        <result column="follow_dept" property="followDept"/>
        <result column="follow_user_tel" property="followUserTel"/>
        <result column="complete_time" property="completeTime"/>
        <result column="component" property="component"/>
        <result column="problem_comment" property="problemComment"/>
        <result column="repair_suggest" property="repairSuggest"/>
        <result column="attach_id" property="attachId"/>
        <result column="remark" property="remark"/>
        <result column="dispatch_time" property="dispatchTime"/>
        <result column="submit_time" property="submitTime"/>
        <result column="actual_complete_time" property="actualCompleteTime"/>
        <result column="time_take" property="timeTake"/>
        <result column="is_expired" property="isExpired"/>
        <result column="is_need_approval" property="isNeedApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="repairDTOResultMap" type="com.snszyk.simas.overhaul.dto.RepairDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="biz_type" property="bizType"/>
        <result column="source_no" property="sourceNo"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="source" property="source"/>
        <result column="repair_type" property="repairType"/>
        <result column="fault_level" property="faultLevel"/>
        <result column="fault_name" property="faultName"/>
        <result column="report_dept" property="reportDept"/>
        <result column="report_user" property="reportUser"/>
        <result column="tel" property="tel"/>
        <result column="report_time" property="reportTime"/>
        <result column="dispatch_user" property="dispatchUser"/>
        <result column="receive_dept" property="receiveDept"/>
        <result column="receive_user" property="receiveUser"/>
        <result column="external_org" property="externalOrg"/>
        <result column="external_contact" property="externalContact"/>
        <result column="external_tel" property="externalTel"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="follow_user" property="followUser"/>
        <result column="follow_dept" property="followDept"/>
        <result column="follow_user_tel" property="followUserTel"/>
        <result column="complete_time" property="completeTime"/>
        <result column="component" property="component"/>
        <result column="problem_comment" property="problemComment"/>
        <result column="repair_suggest" property="repairSuggest"/>
        <result column="attach_id" property="attachId"/>
        <result column="remark" property="remark"/>
        <result column="dispatch_time" property="dispatchTime"/>
        <result column="submit_time" property="submitTime"/>
        <result column="actual_complete_time" property="actualCompleteTime"/>
        <result column="time_take" property="timeTake"/>
        <result column="is_expired" property="isExpired"/>
        <result column="is_need_approval" property="isNeedApproval"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_sn" property="equipmentSn"/>
        <result column="equipment_model" property="equipmentModel"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="equipment_category" property="equipmentCategory"/>
        <result column="monitor_name" property="monitorName"/>
    </resultMap>


    <select id="internalPage" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.code as equipment_code, equipment.sn as
        equipment_sn, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        left join szyk_user u on u.id = repair.receive_user and u.is_deleted = 0
        where repair.is_deleted = 0 and equipment.is_deleted = 0
        <if test="repair.bizType != null and repair.bizType != ''">
            and repair.biz_type = #{repair.bizType}
        </if>
        <if test="repair.equipmentId != null">
            and repair.equipment_id = #{repair.equipmentId}
        </if>
        <if test="repair.receiveUser!=null">
            <choose>
                <when test="repair.receiveUser == 0">
                    and repair.receive_user is null
                </when>
                <otherwise>
                    and repair.receive_user = #{repair.receiveUser}
                </otherwise>
            </choose>
        </if>
        <if test="repair.followUser != null">
            <choose>
                <when test="repair.followUser == 0">
                    and repair.follow_user is null
                </when>
                <otherwise>
                    and repair.follow_user = #{repair.followUser}
                </otherwise>
            </choose>
        </if>
        <if test="repair.no != null and repair.no != ''">
            AND repair.`no` LIKE CONCAT('%', #{repair.no}, '%')
        </if>
        <if test="repair.repairType != null">
            and repair.repair_type = #{repair.repairType}
        </if>
        <if test="repair.reportUser != null">
            and repair.report_user= #{repair.reportUser}
        </if>
        <if test="repair.status != null">
            and repair.status = #{repair.status}
        </if>
        <if test="repair.keywords != null and repair.keywords != ''">
            and (repair.`no` like concat('%',#{repair.keywords},'%') or
            equipment.`sn` like concat('%',#{repair.keywords},'%') or
            equipment.`code` = #{repair.keywords})
        </if>
        <if test="repair.startDate!=null and repair.startDate!=''">
            and repair.report_time <![CDATA[ >= ]]> #{repair.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="repair.endDate!=null and repair.endDate!=''">
            and repair.report_time <![CDATA[ <= ]]> #{repair.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="repair.neStatus != null">
            and repair.status <![CDATA[ <> ]]> #{repair.neStatus}
        </if>
        <if test="repair.receiveUserName!=null and repair.receiveUserName != ''">
            and u.real_name like concat('%',#{repair.receiveUserName},'%')
        </if>
        <if test="repair.statusList != null and repair.statusList.size() > 0">
            AND repair.status in
            <foreach collection="repair.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by repair.create_time desc
    </select>

    <select id="externalPage" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.code as equipment_code, equipment.sn as
        equipment_sn, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0
        <if test="repair.bizType != null and repair.bizType != ''">
            and repair.biz_type = #{repair.bizType}
        </if>
        <if test="repair.equipmentId != null">
            and repair.equipment_id = #{repair.equipmentId}
        </if>
        <if test="repair.receiveUser!=null">
            <choose>
                <when test="repair.receiveUser == 0">
                    and repair.receive_user is null
                </when>
                <otherwise>
                    and repair.receive_user = #{repair.receiveUser}
                </otherwise>
            </choose>
        </if>
        <if test="repair.followUser != null">
            <choose>
                <when test="repair.followUser == 0">
                    and repair.follow_user is null
                </when>
                <otherwise>
                    and repair.follow_user = #{repair.followUser}
                </otherwise>
            </choose>
        </if>
        <if test="repair.no != null and repair.no != ''">
            AND repair.`no` LIKE CONCAT('%', #{repair.no}, '%')
        </if>
        <if test="repair.repairType != null">
            and repair.repair_type = #{repair.repairType}
        </if>
        <if test="repair.reportUser != null">
            and repair.report_user= #{repair.reportUser}
        </if>
        <if test="repair.status != null">
            and repair.status = #{repair.status}
        </if>
        <if test="repair.keywords != null and repair.keywords != ''">
            and (repair.`no` like concat('%',#{repair.keywords},'%') or
            equipment.`sn` like concat('%',#{repair.keywords},'%') or
            equipment.`code` = #{repair.keywords})
        </if>
        <if test="repair.startDate!=null and repair.startDate!=''">
            and repair.report_time <![CDATA[ >= ]]> #{repair.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="repair.endDate!=null and repair.endDate!=''">
            and repair.report_time <![CDATA[ <= ]]> #{repair.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="repair.neStatus != null">
            and repair.status <![CDATA[ <> ]]> #{repair.neStatus}
        </if>
        <if test="repair.statusList != null and repair.statusList.size() > 0">
            AND repair.status in
            <foreach collection="repair.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by repair.create_time desc
    </select>

    <select id="timeoutPage" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0 and repair.biz_type = #{repair.bizType} and
        (repair.status = 1 or repair.status = 4)
        <if test="repair.receiveDept != null">
            and repair.receive_dept = #{repair.receiveDept}
        </if>
        <if test="repair.bizType == 'INTERNAL'">
            and repair.receive_user = #{repair.receiveUser}
        </if>
        <if test="repair.bizType == 'EXTERNAL'">
            and repair.follow_user = #{repair.followUser}
        </if>
        <if test="repair.keywords != null and repair.keywords != ''">
            and (repair.`no` like concat('%',#{repair.keywords},'%') or
            equipment.`sn` like concat('%',#{repair.keywords},'%') or
            equipment.`code` = #{repair.keywords})
        </if>
        <![CDATA[ AND NOW() >= DATE_SUB(repair.complete_time, INTERVAL #{repair.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < repair.complete_time ]]>
        order by repair.id desc
    </select>

    <select id="exportList" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0 and repair.biz_type = #{repair.bizType}
        <if test="repair.receiveUser != null">
            AND repair.receive_user = #{repair.receiveUser}
        </if>
        <if test="repair.followUser != null">
            AND repair.follow_user = #{repair.followUser}
        </if>
        <if test="repair.no != null and repair.no != ''">
            AND repair.`no` LIKE CONCAT('%', #{repair.no}, '%')
        </if>
        <if test="repair.repairType != null">
            and repair.repair_type = #{repair.repairType}
        </if>
        <if test="repair.reportUser != null">
            and repair.report_user= #{repair.reportUser}
        </if>
        <if test="repair.status != null">
            and repair.status = #{repair.status}
        </if>
        <if test="repair.startDate!=null and repair.startDate!=''">
            and repair.report_time <![CDATA[ >= ]]> #{repair.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="repair.endDate!=null and repair.endDate!=''">
            and repair.report_time <![CDATA[ <= ]]> #{repair.endDate, jdbcType=TIMESTAMP}
        </if>
        order by repair.create_time desc
    </select>

    <select id="expireSoonCount" resultType="java.lang.Integer">
        select count(*)
        from simas_repair o
        LEFT JOIN device_account e ON o.equipment_id = e.id
        where 1 = 1
          and e.is_deleted = 0
          and (o.status = 1 or o.status = 4)
            <if test="repair.receiveUser != null">
                and (o.receive_user = #{repair.receiveUser} or o.follow_user = #{repair.followUser})
            </if>
        <![CDATA[ AND NOW() >= DATE_SUB(o.complete_time, INTERVAL #{repair.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < o.complete_time
        ]]>
    </select>

    <select id="timeTakeStatisticsList" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        left join simas_repair_record record on repair.id = record.repair_id
        where repair.is_deleted = 0 and equipment.is_deleted = 0 and (repair.status = 2 or repair.status = 7)
        <if test="bizType != null and bizType != ''">
            and repair.biz_type = #{bizType}
        </if>
        <if test="queryDate == 0">
            and repair.create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="queryDate == 1">
            and repair.create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="queryDate == 2">
            and repair.create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="startTime != null">
            and repair.create_time &gt; #{startTime, jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and repair.create_time &lt; #{endTime, jdbcType=TIMESTAMP}
        </if>
        ORDER BY repair.time_take DESC limit 20
    </select>

    <select id="statisticalReport" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model, equipment.category_id as equipment_category
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0
        <if test="search.status!=null">
            and repair.status = #{search.status}
        </if>
        <if test="search.queryDate == 1">
            and repair.create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            and repair.create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(repair.create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and complete_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and complete_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        ORDER BY repair.id DESC
    </select>

    <select id="queryList" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0
        <if test="repair.receiveUser != null">
            AND repair.receive_user = #{repair.bizType}
        </if>
        and repair.biz_type = #{repair.bizType}
        <if test="repair.receiveUser != null">
            AND repair.receive_user = #{repair.receiveUser}
        </if>
        <if test="repair.followUser != null">
            AND repair.follow_user = #{repair.followUser}
        </if>
        <if test="repair.no != null and repair.no != ''">
            AND repair.`no` LIKE CONCAT('%', #{repair.no}, '%')
        </if>
        <if test="repair.repairType != null">
            and repair.repair_type = #{repair.repairType}
        </if>
        <if test="repair.reportUser != null">
            and repair.report_user= #{repair.reportUser}
        </if>
        <if test="repair.status != null">
            and repair.status = #{repair.status}
        </if>
    </select>

    <select id="repairStatistics" resultMap="repairDTOResultMap">
        select repair.*, equipment.name as equipment_name, equipment.model as equipment_model
        from simas_repair repair
        left join device_account equipment on repair.equipment_id = equipment.id
        where repair.is_deleted = 0 and equipment.is_deleted = 0
        <if test="repair.bizType != null and repair.bizType != ''">
            AND repair.biz_type = #{repair.bizType}
        </if>
        <if test="repair.tenantId != null and repair.tenantId !=''">
            and t.tenant_id = #{repair.tenantId}
        </if>
        <if test="queryDate == 1">
            AND repair.create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="queryDate == 2">
            AND repair.create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="queryDate == 3">
            AND TO_DAYS(repair.create_time) = TO_DAYS(NOW())
        </if>
        and repair.status in (1,3,4,6)
        order by repair.create_time desc
    </select>

    <select id="statisticsByEquipment" resultType="com.snszyk.simas.common.dto.EquipmentStatisticsDTO">
        SELECT equipment_id as id,status FROM `simas_repair` where is_deleted = 0
        <if test="search.queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and complete_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and complete_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
    </select>
    <select id="overdueList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        select t.no as orderNo, t.equipment_id, e.name as equipmentName, t.biz_type
        from simas_repair t
                 left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.is_deleted = 0
          and t.status = 3
            <if test="tenantId!=null and tenantId !=''">
                and t.tenant_id = #{tenantId}
            </if>
        order by t.create_time desc

    </select>
    <select id="repair30day" resultType="com.snszyk.simas.overhaul.dto.RepairDTO">
        select t.*,e.name as equipment_name from simas_repair t
        left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.is_deleted = 0
          and t.biz_type = #{bizType}
          and t.create_time >= CURDATE() - INTERVAL 30 DAY
            <if test="bizType!=null and bizType !=''">
                and t.biz_type = #{bizType}
            </if>
        order by t.create_time desc

    </select>
    <select id="finishList" resultType="com.snszyk.simas.overhaul.dto.RepairDTO">
        select t.*, r.duration as time_take
        from simas_repair t
        left join  simas_repair_record r on t.id = r.repair_id
        where t.is_deleted = 0 and t.status in (2,7)
        <if test="bizType!=null and bizType !=''">
            and t.biz_type = #{bizType}
        </if>
        <if test="startTime != null ">
            and t.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null ">
            and t.create_time <![CDATA[ >= ]]> #{startTime}
        </if>

        order by t.create_time desc
    </select>
    <select id="handleRepairCount" resultType="java.lang.Integer"
            parameterType="com.snszyk.simas.overhaul.vo.RepairVO">
        select count(1)
        from simas_repair repair
        where repair.is_deleted = 0 and repair.biz_type = #{repair.bizType}
        <if test="repair.receiveUser != null">
            AND repair.receive_user = #{repair.receiveUser}
        </if>
        <if test="repair.followUser != null">
            AND repair.follow_user = #{repair.followUser}
        </if>
        <if test="repair.statusList != null and repair.statusList.size() > 0">
            AND repair.status in
            <foreach collection="repair.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="repair.tenantId != null and repair.tenantId !=''">
            and repair.tenant_id = #{repair.tenantId}
        </if>
        <if test="repair.startDate != null and repair.startDate != ''">
            and repair.create_time <![CDATA[ >= ]]> #{repair.startDate, jdbcType=TIMESTAMP}
        </if>

        <if test="repair.endDate != null and repair.endDate != ''">
            and repair.create_time <![CDATA[ <= ]]> #{repair.endDate, jdbcType=TIMESTAMP}
        </if>

    </select>

    <select id="curYearRepair" resultType="com.snszyk.simas.overhaul.dto.RepairDTO">
        select t.* from simas_repair t
        where t.is_deleted=0 and t.create_time>#{start}
        <if test="statusList != null and statusList.size() > 0">
            AND t.status in
            <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and t.tenant_id = #{tenantId}

    </select>

    <select id="listGroupByFaultDefect" resultType="com.snszyk.simas.fault.dto.FaultDefectDTO">
        SELECT
        r.fault_name as "name",
        r.repair_type as "type",
        COUNT(*) as "count",
        SUM( CASE WHEN r.STATUS in (2,4) THEN 1 ELSE 0 END ) AS completeCount,
        GROUP_CONCAT(CASE WHEN r.status IN (2, 4) THEN r.id ELSE NULL END ) AS completedIds
        FROM
        `simas_repair` r
        where r.is_deleted = 0 and r.status <![CDATA[ <> ]]> 7
        <if test="search.tenantId != null and search.tenantId != ''">
            AND r.tenant_id = #{search.tenantId}
        </if>
        <if test="search.queryDate == 0">
            AND r.create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND r.create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND r.create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(r.create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and r.complete_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and r.complete_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        group by r.fault_name,r.repair_type
    </select>

    <select id="equipmentStatisticsOfOrder" resultMap="repairResultMap">
        SELECT * FROM simas_repair where 1=1
        <if test="search.queryDate == 0">
            AND report_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND report_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND report_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(report_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and report_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and report_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
    </select>


</mapper>
