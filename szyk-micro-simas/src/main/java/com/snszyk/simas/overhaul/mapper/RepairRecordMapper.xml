<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.overhaul.mapper.RepairRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="repairRecordResultMap" type="com.snszyk.simas.overhaul.entity.RepairRecord">
        <id column="id" property="id"/>
        <result column="repair_id" property="repairId"/>
        <result column="result" property="result"/>
        <result column="check_method" property="checkMethod"/>
        <result column="duration" property="duration"/>
        <result column="fault_reason" property="faultReason"/>
        <result column="solution" property="solution"/>
        <result column="component" property="component"/>
        <result column="attach_id" property="attachId"/>
        <result column="verify_user" property="verifyUser"/>
        <result column="verify_time" property="verifyTime"/>
        <result column="verify_result" property="verifyResult"/>
        <result column="verify_comment" property="verifyComment"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_time" property="operateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="pageRepairDuration" resultType="com.snszyk.simas.overhaul.dto.RepairDurationDTO">
        SELECT
        ea.id AS id,
        sum(IFNULL(rr.duration,0)) AS repairDuration
        FROM device_account ea
        LEFT JOIN simas_repair r ON ea.id = r.equipment_id and r.is_deleted = 0
        <if test="vo.startDateTime!=null and vo.endDateTime!=null">
            and r.actual_complete_time BETWEEN #{vo.startDateTime} AND #{vo.endDateTime}
        </if>
        <if test="vo.repairStatusList !=null and vo.repairStatusList.size()>0">
            AND r.status IN
            <foreach collection="vo.repairStatusList" item="repairStatus" index="index" open="(" close=")"
                     separator=",">
                #{repairStatus}
            </foreach>
        </if>
        LEFT JOIN simas_repair_record rr ON r.id = rr.repair_id
        where
            ea.is_deleted = 0
            <if test="vo.tenantId!=null and vo.tenantId !=''">
                and r.tenant_id = #{vo.tenantId}
            </if>
            <if test="vo.equipmentName!=null and vo.equipmentName !=''">
                and ea.NAME LIKE concat('%',#{vo.equipmentName},'%')
            </if>
            <if test="vo.categoryId!=null">
                AND ea.category_id = ${vo.categoryId}
            </if>
            <if test="vo.useDeptList !=null and vo.useDeptList.size()>0">
                and ea.use_dept in
                <foreach collection="vo.useDeptList" item="deptId" index="index" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="vo.isLeaseBack !=null">
                and ea.is_lease_back = #{vo.isLeaseBack}
            </if>
        GROUP BY
        ea.id
        ORDER BY
        repairDuration ${vo.sortOrder}
    </select>

</mapper>
