 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service.impl;

 import cn.hutool.json.JSONUtil;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OrderStatusEnum;
 import com.snszyk.simas.common.enums.PlanCycleEnum;
 import com.snszyk.simas.common.excel.OverhaulPlanExcel;
 import com.snszyk.simas.common.vo.AuditVO;
 import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
 import com.snszyk.simas.overhaul.entity.OverhaulOrder;
 import com.snszyk.simas.overhaul.entity.OverhaulPlan;
 import com.snszyk.simas.overhaul.entity.OverhaulPlanEquipment;
 import com.snszyk.simas.overhaul.enums.OverhaulPlanStatusEnum;
 import com.snszyk.simas.overhaul.mapper.OverhaulPlanMapper;
 import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
 import com.snszyk.simas.overhaul.service.IOverhaulPlanEquipmentService;
 import com.snszyk.simas.overhaul.service.IOverhaulPlanService;
 import com.snszyk.simas.overhaul.vo.OverhaulPlanVO;
 import com.snszyk.simas.overhaul.wrapper.OverhaulPlanWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.ArrayList;
 import java.util.Date;
 import java.util.List;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备检修计划表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @AllArgsConstructor
 @Service
 public class OverhaulPlanServiceImpl extends BaseServiceImpl<OverhaulPlanMapper, OverhaulPlan> implements IOverhaulPlanService {

	 // 	private final IEquipmentAccountService equipmentAccountService;
	 private final IOverhaulPlanEquipmentService planEquipmentService;
	 private final IOverhaulOrderService overhaulOrderService;
	 private final IDeviceAccountClient accountClient;

	 @Override
	 public IPage<OverhaulPlanDTO> page(IPage<OverhaulPlanDTO> page, OverhaulPlanVO vo) {
		 if (Func.isNotEmpty(vo.getQueryStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getQueryEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<OverhaulPlan> list = baseMapper.page(page, vo);
		 if (Func.isNotEmpty(list)) {
			 List<OverhaulPlanDTO> resultList = list.stream().map(OverhaulPlan -> {
				 OverhaulPlanDTO dto = OverhaulPlanWrapper.build().entityDTO(OverhaulPlan);
				 dto.setEquipmentCount(planEquipmentService.count(Wrappers.<OverhaulPlanEquipment>query().lambda()
					 .eq(OverhaulPlanEquipment::getPlanId, dto.getId())));
				 return dto;
			 }).collect(Collectors.toList());
			 return page.setRecords(resultList);
		 }
		 return page.setRecords(null);
	 }

	 @Override
	 public OverhaulPlanDTO detail(String no) {
		 OverhaulPlan plan = this.getOne(Wrappers.<OverhaulPlan>query().lambda().eq(OverhaulPlan::getNo, no));
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 OverhaulPlanDTO detail = OverhaulPlanWrapper.build().entityDTO(plan);
		 detail.setEquipmentCount(planEquipmentService.count(Wrappers.<OverhaulPlanEquipment>query().lambda()
			 .eq(OverhaulPlanEquipment::getPlanId, detail.getId())));
		 // 所选设备
		 List<OverhaulPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<OverhaulPlanEquipment>query().lambda()
			 .eq(OverhaulPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(planEquipmentList)) {
			 detail.setEquipmentIds(planEquipmentList.stream()
				 .map(OverhaulPlanEquipment::getEquipmentId).collect(Collectors.toList()));
			 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
			 deviceAccountVO.setDeviceIds(detail.getEquipmentIds());

			 R<List<DeviceAccountVO>> listR = accountClient.deviceListByParams(deviceAccountVO);
			 List<DeviceAccountVO> equipmentList = listR.getData();
//			List<EquipmentAccount> equipmentList = equipmentAccountService.list(Wrappers.<EquipmentAccount>query().lambda()
// 				.in(EquipmentAccount::getId, detail.getEquipmentIds()));
			 if (Func.isNotEmpty(equipmentList)) {
				 List<DeviceAccountVO> list = equipmentList.stream().map(vo -> {
// 					EquipmentAccountVO vo = EquipmentAccountWrapper.build().entityVO(equipment);
					 OverhaulOrder order = overhaulOrderService.getOne(Wrappers.<OverhaulOrder>query().lambda()
						 .eq(OverhaulOrder::getEquipmentId, vo.getId())
						 .eq(OverhaulOrder::getPlanId, plan.getId()));
					 if (Func.isNotEmpty(order)) {
						 vo.setOrderNo(order.getNo())
							 .setBizStatus(OrderStatusEnum.getByCode(order.getStatus()).getName());
					 }
					 return vo;
				 }).collect(Collectors.toList());
				 detail.setEquipmentList(list);
			 }
		 }
		 return detail;
	 }

	 @Override
	 public OverhaulPlanDTO view(String no) {
		 OverhaulPlan plan = this.getOne(Wrappers.<OverhaulPlan>query().lambda().eq(OverhaulPlan::getNo, no));
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 OverhaulPlanDTO detail = OverhaulPlanWrapper.build().entityDTO(plan);
		 detail.setEquipmentCount(planEquipmentService.count(Wrappers.<OverhaulPlanEquipment>query().lambda()
			 .eq(OverhaulPlanEquipment::getPlanId, detail.getId())));
		 // 所选设备
		 List<OverhaulPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<OverhaulPlanEquipment>query().lambda()
			 .eq(OverhaulPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(planEquipmentList)) {
			 detail.setEquipmentIds(planEquipmentList.stream()
				 .map(OverhaulPlanEquipment::getEquipmentId).collect(Collectors.toList()));
			 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
			 deviceAccountVO.setDeviceIds(detail.getEquipmentIds());

			 R<List<DeviceAccountVO>> listR = accountClient.deviceListByParams(deviceAccountVO);
			 List<DeviceAccountVO> equipmentList = listR.getData();
// 			List<EquipmentAccount> equipmentList = equipmentAccountService.list(Wrappers.<EquipmentAccount>query().lambda()
// 				.in(EquipmentAccount::getId, detail.getEquipmentIds()));
			 if (Func.isNotEmpty(equipmentList)) {
				 detail.setEquipmentList(equipmentList);
				 List<DeviceAccountVO> equipmentAccountList = new ArrayList<>();
				 for (DeviceAccountVO equipment : equipmentList) {
					 List<OverhaulOrder> orderList = overhaulOrderService.list(Wrappers.<OverhaulOrder>query().lambda()
						 .eq(OverhaulOrder::getPlanId, plan.getId()).eq(OverhaulOrder::getEquipmentId, equipment.getId()));
					 if (Func.isNotEmpty(orderList)) {
						 for (OverhaulOrder order : orderList) {
// 							EquipmentAccountVO equipmentAccount = EquipmentAccountWrapper.build().entityVO(equipment);
							 DeviceAccountVO bean = JSONUtil.toBean(JSONUtil.toJsonStr(equipment), DeviceAccountVO.class);
							 bean.setOrderNo(order.getNo())
								 .setBizStatus(OrderStatusEnum.getByCode(order.getStatus()).getName());
							 equipmentAccountList.add(bean);
						 }
					 }
				 }
				 if (Func.isNotEmpty(equipmentAccountList)) {
					 detail.setEquipmentList(equipmentAccountList);
				 }
			 }
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean add(OverhaulPlanVO vo) {
		 OverhaulPlan plan = Objects.requireNonNull(BeanUtil.copy(vo, OverhaulPlan.class));
		 plan.setNo(BizCodeUtil.generate("OP"))
			 .setStatus(OverhaulPlanStatusEnum.NO_START.getCode());
		 // 时间设置
		 switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(vo.getByWeekSet().stream().collect(Collectors.joining(",")));
				 break;
			 case MONTH:
				 plan.setExecuteTime(vo.getByMonthSet().stream().collect(Collectors.joining(",")));
				 break;
			 default:
		 }
		 boolean ret = this.save(plan);
		 // 所选设备
		 if (Func.isNotEmpty(vo.getEquipmentIds())) {
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<OverhaulPlanEquipment> planEquipmentList = Func.toLongList(vo.getEquipmentIds()).stream().map(equipmentId -> {
				 OverhaulPlanEquipment planEquipment = new OverhaulPlanEquipment();
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(equipmentId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean modify(OverhaulPlanVO vo) {
		 OverhaulPlan plan = this.getById(vo.getId());
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 BeanUtil.copy(vo, plan);
		 // 时间设置
		 switch (PlanCycleEnum.getByCode(vo.getCycleType())) {
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(vo.getByWeekSet().stream().collect(Collectors.joining(",")));
				 break;
			 case MONTH:
				 plan.setExecuteTime(vo.getByMonthSet().stream().collect(Collectors.joining(",")));
				 break;
			 default:
		 }
		 // 所选设备
		 planEquipmentService.remove(Wrappers.<OverhaulPlanEquipment>query().lambda()
			 .eq(OverhaulPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(vo.getEquipmentIds())) {
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<OverhaulPlanEquipment> planEquipmentList = Func.toLongList(vo.getEquipmentIds()).stream().map(equipmentId -> {
				 OverhaulPlanEquipment planEquipment = new OverhaulPlanEquipment();
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(equipmentId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 if (Func.isEmpty(vo.getExecuteUser())) {
			 plan.setExecuteUser(null);
		 }
		 plan.setStatus(OverhaulPlanStatusEnum.UNDER_REVIEW.getCode());
		 return this.updateById(plan);
	 }

	 @Override
	 public List<OverhaulPlanDTO> getTheDayPlans(Date currentDate) {
		 return baseMapper.getTheDayPlans(currentDate);
	 }

	 @Override
	 public List<OverhaulPlanExcel> exportPlan(OverhaulPlanVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<OverhaulPlan> list = baseMapper.selectList(Wrappers.<OverhaulPlan>query().lambda()
			 .eq(Func.isNotEmpty(vo.getCycleType()), OverhaulPlan::getCycleType, vo.getCycleType())
			 .eq(Func.isNotEmpty(vo.getExecuteDept()), OverhaulPlan::getExecuteDept, vo.getExecuteDept())
			 .like(Func.isNotEmpty(vo.getName()), OverhaulPlan::getName, vo.getName())
			 .ge(Func.isNotEmpty(vo.getQueryStartDate()), OverhaulPlan::getStartDate, vo.getQueryStartDate())
			 .le(Func.isNotEmpty(vo.getQueryEndDate()), OverhaulPlan::getStartDate, vo.getQueryEndDate())
			 .orderByDesc(OverhaulPlan::getCreateTime));
		 if (Func.isNotEmpty(list)) {
			 List<OverhaulPlanDTO> planList = OverhaulPlanWrapper.build().listDTO(list);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return planList.stream().map(plan -> {
				 OverhaulPlanExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, OverhaulPlanExcel.class));
				 if (Func.isNotEmpty(plan.getStartDate())) {
					 planExcel.setStartDateStr(DateUtil.formatDate(plan.getStartDate()));
				 }
				 if (Func.isNotEmpty(plan.getEndDate())) {
					 planExcel.setEndDateStr(DateUtil.formatDate(plan.getEndDate()));
				 }
				 planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 planExcel.setEquipmentCount(Func.toStr(planEquipmentService.count(Wrappers.<OverhaulPlanEquipment>query().lambda()
					 .eq(OverhaulPlanEquipment::getPlanId, plan.getId()))));
				 return planExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean audit(AuditVO auditVO) {
		 OverhaulPlan plan = this.getById(auditVO.getId());
		 if (plan == null) {
			 throw new ServiceException("计划已删除");
		 }
		 if (!OverhaulPlanStatusEnum.UNDER_REVIEW.getCode().equals(plan.getStatus())) {
			 throw new ServiceException("计划已被处理");
		 }
		 if (auditVO.getStatus() == 1) {
			 plan.setStatus(OverhaulPlanStatusEnum.NO_START.getCode());
			 // 计划开始时间小于当前审核时间，则更新为当前时间
			 if (plan.getStartDate().before(DateUtil.now())) {
				 plan.setStartDate(DateUtil.now());
			 }
		 }
		 if (auditVO.getStatus() == 2) {
			 plan.setStatus(OverhaulPlanStatusEnum.REJECTED.getCode());
			 plan.setRejectReason(auditVO.getRejectReason());
		 }
		 return updateById(plan);
	 }

	 @Override
	 public boolean closeExpirePlan() {
		 List<OverhaulPlan> list = baseMapper.selectList(Wrappers.<OverhaulPlan>query().lambda()
			 .notIn(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IS_TERMINATED.getCode(), OverhaulPlanStatusEnum.IS_COMPLETED.getCode())
			 .le(OverhaulPlan::getEndDate, DateUtil.format(new Date(), DateUtil.PATTERN_DATE)));
		 if (Func.isNotEmpty(list)) {
			 return this.update(Wrappers.<OverhaulPlan>lambdaUpdate().set(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IS_TERMINATED.getCode())
				 .in(OverhaulPlan::getId, list.stream().map(OverhaulPlan::getId).collect(Collectors.toList())));
		 }
		 return false;
	 }
 }
