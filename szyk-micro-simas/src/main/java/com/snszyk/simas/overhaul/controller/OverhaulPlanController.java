 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
 import com.snszyk.simas.overhaul.entity.OverhaulPlan;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.overhaul.enums.OverhaulPlanStatusEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.OverhaulPlanExcel;
 import com.snszyk.simas.overhaul.service.IOverhaulPlanService;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.vo.AuditVO;
 import com.snszyk.simas.overhaul.vo.OverhaulPlanVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;

 /**
  * 设备检修计划表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/overhaul-plan")
 @Api(value = "设备检修计划表", tags = "设备检修计划表接口")
 public class OverhaulPlanController extends SzykController {

 	private final IOverhaulPlanService overhaulPlanService;
 	private final IOperateLogService operateLogService;

 	/**
 	 * 详情
 	 */
 	@GetMapping("/detail")
 	@ApiOperationSupport(order = 1)
 	@ApiOperation(value = "详情", notes = "传入id")
 	public R<OverhaulPlanDTO> detail(String no) {
 		OverhaulPlanDTO detail = overhaulPlanService.detail(no);
 		OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.RETRIEVE);
 		operateLogService.submit(operateLog);
 		return R.data(detail);
 	}

 	/**
 	 * 查看
 	 */
 	@GetMapping("/view")
 	@ApiOperationSupport(order = 1)
 	@ApiOperation(value = "查看", notes = "传入no")
 	public R<OverhaulPlanDTO> view(String no) {
 		OverhaulPlanDTO detail = overhaulPlanService.view(no);
 		OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.RETRIEVE);
 		operateLogService.submit(operateLog);
 		return R.data(detail);
 	}

 	/**
 	 * 自定义分页 设备检修计划表
 	 */
 	@GetMapping("/page")
 	@ApiImplicitParams({
 		@ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
 		@ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
 	})
 	@ApiOperationSupport(order = 2)
 	@ApiOperation(value = "分页", notes = "传入OverhaulPlan")
 	public R<IPage<OverhaulPlanDTO>> page(@ApiIgnore OverhaulPlanVO overhaulPlan, Query query) {
 		OperateLogVO operateLog = new OperateLogVO(null, SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.RETRIEVE);
 		operateLogService.submit(operateLog);
 		return R.data(overhaulPlanService.page(Condition.getPage(query), overhaulPlan));
 	}

 	/**
 	 * 新增 设备检修计划表
 	 */
 	@PostMapping("/save")
 	@ApiOperationSupport(order = 3)
 	@ApiOperation(value = "新增", notes = "传入OverhaulPlan")
 	public R save(@Valid @RequestBody OverhaulPlanVO overhaulPlan) {
 		boolean ret = overhaulPlanService.add(overhaulPlan);
 		OperateLogVO operateLog = new OperateLogVO(overhaulPlan.getId(), SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.CREATE);
 		operateLogService.submit(operateLog);
 		return R.status(ret);
 	}

 	/**
 	 * 修改 设备检修计划表
 	 */
 	@PostMapping("/update")
 	@ApiOperationSupport(order = 4)
 	@ApiOperation(value = "修改", notes = "传入OverhaulPlan")
 	public R update(@Valid @RequestBody OverhaulPlanVO OverhaulPlan) {
 		boolean ret = overhaulPlanService.modify(OverhaulPlan);
 		OperateLogVO operateLog = new OperateLogVO(OverhaulPlan.getId(), SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.UPDATE);
 		operateLogService.submit(operateLog);
 		return R.status(ret);
 	}

 	/**
 	 * 删除 设备检修计划表
 	 */
 	@PostMapping("/remove")
 	@ApiOperationSupport(order = 5)
 	@ApiOperation(value = "逻辑删除", notes = "传入ids")
 	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
 		List<Long> list = Func.toLongList(ids);
 		list.forEach(id -> {
 			OperateLogVO operateLog = new OperateLogVO(id, SystemModuleEnum.OVERHAUL_PLAN, OperateTypeEnum.DELETE);
 			operateLogService.submit(operateLog);
 		});
 		return R.status(overhaulPlanService.deleteLogic(list));
 	}

 	/**
 	 * 手动开始 设备检修计划表
 	 */
 	@PostMapping("/manual-start")
 	@ApiOperationSupport(order = 6)
 	@ApiOperation(value = "手动开始", notes = "传入id")
 	public R manualStart(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
 		return R.status(overhaulPlanService.update(Wrappers.<OverhaulPlan>update().lambda()
 			.set(OverhaulPlan::getStartDate, DateUtil.now())
 			.set(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IN_PROGRESS.getCode())
 			.eq(OverhaulPlan::getId, id)));
 	}

 	/**
 	 * 手动停止 设备检修计划表
 	 */
 	@PostMapping("/manual-stop")
 	@ApiOperationSupport(order = 7)
 	@ApiOperation(value = "手动停止", notes = "传入id")
 	public R manualStop(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
 		return R.status(overhaulPlanService.update(Wrappers.<OverhaulPlan>update().lambda()
 			.set(OverhaulPlan::getEndDate, DateUtil.now())
 			.set(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IS_TERMINATED.getCode())
 			.eq(OverhaulPlan::getId, id)));
 	}

 	/**
 	 * 导出 设备检修计划表
 	 */
 	@GetMapping("/export-plan")
 	@ApiImplicitParams({
 		@ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
 	})
 	@ApiOperationSupport(order = 8)
 	@ApiOperation(value = "导出", notes = "传入OverhaulPlan")
 	public void exportPlan(@ApiIgnore OverhaulPlanVO OverhaulPlan, HttpServletResponse response) {
 		List<OverhaulPlanExcel> list = overhaulPlanService.exportPlan(OverhaulPlan);
 		ExcelUtil.export(response, "检修计划" + DateUtil.time(), "检修计划", list, OverhaulPlanExcel.class);
 	}

 	@PostMapping("/audit")
 	@ApiOperationSupport(order = 9)
 	@ApiOperation(value = "审核", notes = "传入auditVO")
 	public R audit(@RequestBody AuditVO auditVO){
 		return R.status(overhaulPlanService.audit(auditVO));
 	}

 }
