/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.overhaul.entity.OverhaulStandard;

import java.util.List;

/**
 * 设备保养标准表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface IOverhaulStandardService extends IService<OverhaulStandard> {

	IPage<DeviceAccountVO> devicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount);

	/**
	 * 详情
	 *
	 * @param equipmentId
	 * @return
	 */
	EquipmentStandardVO detail(Long equipmentId);

	/**
	 * 保存
	 *
	 * @param equipmentStandardVO
	 * @return
	 */
	boolean submit(EquipmentStandardVO equipmentStandardVO);

	/**
	 * 清空标准
	 *
	 * @param equipmentId
	 * @return
	 */
	boolean clear(Long equipmentId);

	/**
	 * 保存导入数据
	 * @param list
	 * @return
	 */
	boolean saveImportData(List<OverhaulStandard> list);

}
