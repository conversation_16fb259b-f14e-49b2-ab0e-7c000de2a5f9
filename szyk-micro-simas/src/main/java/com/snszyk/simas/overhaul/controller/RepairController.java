 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.excel.RepairExternalExcel;
 import com.snszyk.simas.common.excel.RepairInternalExcel;
 import com.snszyk.simas.common.vo.EquipmentRepairVO;
 import com.snszyk.simas.overhaul.dto.RepairDTO;
 import com.snszyk.simas.overhaul.entity.Repair;
 import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
 import com.snszyk.simas.overhaul.service.IRepairService;
 import com.snszyk.simas.overhaul.vo.RepairRecordVO;
 import com.snszyk.simas.overhaul.vo.RepairVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;

 /**
  * 设备维修单表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/repair")
 @Api(value = "设备维修单表", tags = "设备维修单表接口")
 public class RepairController extends SzykController {

	 private final IRepairService repairService;

	 /**
	  * 编辑-详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "编辑-详情", notes = "传入no")
	 public R<RepairDTO> detail(@ApiParam(value = "维修单号", required = true) @RequestParam String no) {
		 return R.data(repairService.detail(no));
	 }

	 /**
	  * 查看-详情
	  */
	 @GetMapping("/view")
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "查看-详情", notes = "传入no")
	 public R<RepairDTO> view(@ApiParam(value = "维修单号", required = true) @RequestParam String no) {
		 return R.data(repairService.view(no));
	 }

	 /**
	  * 内部维修分页 设备维修单表
	  */
	 @GetMapping("/internalPage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "no", value = "单号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "repairType", value = "报修类型", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "reportUser", value = "报修人", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "receiveUser", value = "维修人id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "neStatus", value = "不等于", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "receiveUserName", value = "维修人姓名", paramType = "query", dataType = "String"),
	 })
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "内部维修分页", notes = "传入repair")
	 public R<IPage<RepairDTO>> internalPage(@ApiIgnore RepairVO repair, Query query) {
		 repair.setBizType(RepairBizTypeEnum.INTERNAL.getCode());
		 IPage<RepairDTO> pages = repairService.page(Condition.getPage(query), repair);
		 return R.data(pages);
	 }


	 /**
	  * 外委维修分页 设备维修单表
	  */
	 @GetMapping("/externalPage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "no", value = "单号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "repairType", value = "报修类型", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "reportUser", value = "报修人", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "followUser", value = "跟进人id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "neStatus", value = "不等于", paramType = "query", dataType = "Integer")
	 })
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "外委维修分页", notes = "传入repair")
	 public R<IPage<RepairDTO>> externalPage(@ApiIgnore RepairVO repair, Query query) {
		 repair.setBizType(RepairBizTypeEnum.EXTERNAL.getCode());
		 IPage<RepairDTO> pages = repairService.page(Condition.getPage(query), repair);
		 return R.data(pages);
	 }

	 /**
	  * 新增或修改 设备维修单表
	  */
	 @PostMapping("/submit")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "新增或修改", notes = "传入repair")
	 public R<Repair> submit(@Valid @RequestBody RepairVO repair) {
		 return R.data(repairService.submit(repair));
	 }

	 /**
	  * 删除 设备维修单表
	  */
	 @PostMapping("/remove")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "逻辑删除", notes = "传入ids")
	 public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		 return R.status(repairService.deleteLogic(Func.toLongList(ids)));
	 }

	 /**
	  * 派单 设备维修单表
	  */
	 @PostMapping("/dispatch")
	 @ApiOperationSupport(order = 7)
	 @ApiOperation(value = "派单", notes = "传入repair")
	 public R dispatch(@RequestBody RepairVO repair) {
		 return R.status(repairService.dispatch(repair));
	 }

	 /**
	  * 提交维修 设备维修单表
	  */
	 @PostMapping("/repair")
	 @ApiOperationSupport(order = 8)
	 @ApiOperation(value = "提交维修", notes = "传入equipmentRepair")
	 public R repair(@RequestBody EquipmentRepairVO equipmentRepair) {
		 return R.status(repairService.repair(equipmentRepair));
	 }

	 /**
	  * 验证 设备维修单表
	  */
	 @PostMapping("/verify")
	 @ApiOperationSupport(order = 9)
	 @ApiOperation(value = "验证", notes = "传入repairRecord")
	 public R verify(@RequestBody RepairRecordVO repairRecord) {
		 return R.status(repairService.verify(repairRecord));
	 }

	 /**
	  * 内部转外委 设备维修单表
	  */
	 @PostMapping("/toExternal")
	 @ApiOperationSupport(order = 10)
	 @ApiOperation(value = "内部转外委", notes = "传入repair")
	 public R<String> toExternal(@RequestBody RepairVO repair) {
		 return R.data(repairService.toExternal(repair));
	 }

	 /**
	  * 关闭 设备维修单表
	  */
	 @PostMapping("/close")
	 @ApiOperationSupport(order = 11)
	 @ApiOperation(value = "关闭", notes = "传入id")
	 public R close(Long id) {
		 return R.status(repairService.close(id));
	 }

	 /**
	  * 即将超时分页 设备维修单表
	  */
	 @GetMapping("/timeoutPage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "bizType", value = "维修单类型", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 12)
	 @ApiOperation(value = "即将超时分页", notes = "传入repair")
	 public R<IPage<RepairDTO>> timeoutPage(@ApiIgnore RepairVO repair, Query query) {
		 return R.data(repairService.timeoutPage(Condition.getPage(query), repair));
	 }

	 /**
	  * 导出 设备维修单表
	  */
	 @GetMapping("/export-repair")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "no", value = "单号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "repairType", value = "报修类型", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "reportUser", value = "报修人", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "bizType", value = "维修方式（INTERNAL：内部维修，EXTERNAL：外委维修）", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 13)
	 @ApiOperation(value = "导出", notes = "传入repair")
	 public void exportRepair(@ApiIgnore RepairVO repair, HttpServletResponse response) {
		 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
			 List<RepairInternalExcel> list = repairService.exportInternalOrder(repair);
			 ExcelUtil.export(response, "内部维修工单列表" + DateUtil.time(), "维修工单", list, RepairInternalExcel.class);
		 } else {
			 List<RepairExternalExcel> list = repairService.exportExternalOrder(repair);
			 ExcelUtil.export(response, "外委维修工单列表" + DateUtil.time(), "维修工单", list, RepairExternalExcel.class);
		 }
	 }

	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "no", value = "单号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "repairType", value = "报修类型", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "reportUser", value = "报修人", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "bizType", value = "维修方式（INTERNAL：内部维修，EXTERNAL：外委维修）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "long")
	 })
	 @ApiOperationSupport(order = 14)
	 @ApiOperation(value = "分页（内、外部）", notes = "传入repair")
	 public R<IPage<RepairDTO>> page(@ApiIgnore RepairVO repair, Query query) {
		 IPage<RepairDTO> pages = repairService.page(Condition.getPage(query), repair);
		 return R.data(pages);
	 }


 }
