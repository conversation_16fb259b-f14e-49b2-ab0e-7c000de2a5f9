/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.overhaul.dto.OverhaulMethodsDTO;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import com.snszyk.simas.overhaul.vo.OverhaulMethodsVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备点巡检计划表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class OverhaulMethodsWrapper extends BaseEntityWrapper<OverhaulMethods, OverhaulMethodsVO> {

	public static OverhaulMethodsWrapper build() {
		return new OverhaulMethodsWrapper();
	}

	@Override
	public OverhaulMethodsVO entityVO(OverhaulMethods overhaulPlan) {
		OverhaulMethodsVO overhaulPlanVO = Objects.requireNonNull(BeanUtil.copy(overhaulPlan, OverhaulMethodsVO.class));

		//User createUser = UserCache.getUser(OverhaulPlan.getCreateUser());
		//User updateUser = UserCache.getUser(OverhaulPlan.getUpdateUser());
		//OverhaulPlanVO.setCreateUserName(createUser.getName());
		//OverhaulPlanVO.setUpdateUserName(updateUser.getName());

		return overhaulPlanVO;
	}

	public OverhaulMethodsDTO entityDTO(OverhaulMethods overhaulMethods) {
		OverhaulMethodsDTO dto = Objects.requireNonNull(BeanUtil.copy(overhaulMethods, OverhaulMethodsDTO.class));
		//SET updateUserName
		Long updateUserId = overhaulMethods.getUpdateUser();
		if (updateUserId != null) {
			User updateUser = UserCache.getUser(updateUserId);
			if (updateUser != null) {
				dto.setUpdateUserName(updateUser.getName());
			}
		}
		//SET createUserName
		Long createUserId = overhaulMethods.getCreateUser();
		if (createUserId != null) {
			User createUser = UserCache.getUser(createUserId);
			if (createUser != null) {
				dto.setCreateUserName(createUser.getName());
			}
		}
		return dto;

	}

	public List<OverhaulMethodsDTO> listDTO(List<OverhaulMethods> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
