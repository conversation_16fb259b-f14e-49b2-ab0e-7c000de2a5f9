/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.overhaul.dto.RepairDurationDTO;
import com.snszyk.simas.overhaul.entity.RepairRecord;
import com.snszyk.simas.overhaul.vo.RepairDurationPageVO;
import org.apache.ibatis.annotations.Param;

/**
 * 设备维修记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface RepairRecordMapper extends BaseMapper<RepairRecord> {

	/**
	 * 设备维修率
	 *
	 * @param vo
	 * @return
	 */
	IPage<RepairDurationDTO> pageRepairDuration(@Param("vo") RepairDurationPageVO vo);


}
