/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
import com.snszyk.simas.overhaul.entity.OverhaulPlan;
import com.snszyk.simas.overhaul.enums.OverhaulPlanStatusEnum;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.common.enums.WeekDateEnum;
import com.snszyk.simas.common.vo.ByDaySetVO;
import com.snszyk.simas.overhaul.vo.OverhaulPlanVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备点巡检计划表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class OverhaulPlanWrapper extends BaseEntityWrapper<OverhaulPlan, OverhaulPlanVO> {

	public static OverhaulPlanWrapper build() {
		return new OverhaulPlanWrapper();
 	}

	@Override
	public OverhaulPlanVO entityVO(OverhaulPlan overhaulPlan) {
		OverhaulPlanVO overhaulPlanVO = Objects.requireNonNull(BeanUtil.copy(overhaulPlan, OverhaulPlanVO.class));

		//User createUser = UserCache.getUser(OverhaulPlan.getCreateUser());
		//User updateUser = UserCache.getUser(OverhaulPlan.getUpdateUser());
		//OverhaulPlanVO.setCreateUserName(createUser.getName());
		//OverhaulPlanVO.setUpdateUserName(updateUser.getName());

		return overhaulPlanVO;
	}

	public OverhaulPlanDTO entityDTO(OverhaulPlan overhaulPlan) {
		OverhaulPlanDTO overhaulPlanDTO = Objects.requireNonNull(BeanUtil.copy(overhaulPlan, OverhaulPlanDTO.class));
		overhaulPlanDTO.setCycleTypeName(DictBizCache.getValue(DictBizEnum.PLAN_CYCLE, overhaulPlan.getCycleType()))
			.setStatusName(OverhaulPlanStatusEnum.getByCode(overhaulPlan.getStatus()).getName());
		if(Func.isNotEmpty(overhaulPlan.getExecuteDept())){
			Dept executeDept = SysCache.getDept(overhaulPlan.getExecuteDept());
			if(Func.isNotEmpty(executeDept)){
				overhaulPlanDTO.setExecuteDeptName(executeDept.getDeptName());
			}
		}
		if(Func.isNotEmpty(overhaulPlan.getExecuteUser())){
			User executeUser = UserCache.getUser(overhaulPlan.getExecuteUser());
			if(Func.isNotEmpty(executeUser)){
				overhaulPlanDTO.setExecuteUserName(executeUser.getRealName());
			}
		}
		// 时间设置
		switch (PlanCycleEnum.getByCode(overhaulPlan.getCycleType())){
			case DAY:
				overhaulPlanDTO.setByDaySet(JSONUtil.toList(overhaulPlan.getExecuteTime(), ByDaySetVO.class));
				break;
			case WEEK:
				overhaulPlanDTO.setByWeekSet(Func.toStrList(overhaulPlan.getExecuteTime()));
				overhaulPlanDTO.setExecuteTimeStr(Func.toStrList(overhaulPlan.getExecuteTime()).stream()
					.map(date -> WeekDateEnum.getByCode(date).getName()).collect(Collectors.joining(",")));
				break;
			case MONTH:
				overhaulPlanDTO.setByMonthSet(Func.toStrList(overhaulPlan.getExecuteTime()))
					.setExecuteTimeStr(overhaulPlan.getExecuteTime());
				break;
			default:
		}
		User createUser = UserCache.getUser(overhaulPlan.getCreateUser());
		if(Func.isNotEmpty(createUser)){
			overhaulPlanDTO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(overhaulPlan.getUpdateUser());
		if(Func.isNotEmpty(updateUser)){
			overhaulPlanDTO.setUpdateUserName(updateUser.getRealName());
		}
		return overhaulPlanDTO;
	}

	public List<OverhaulPlanDTO> listDTO(List<OverhaulPlan> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
