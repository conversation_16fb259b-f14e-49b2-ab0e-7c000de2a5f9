<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.overhaul.mapper.OverhaulRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="overhaulRecordResultMap" type="com.snszyk.simas.overhaul.entity.OverhaulRecord">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="standard_id" property="standardId"/>
        <result column="standard_info" property="standardInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
    </resultMap>


    <select id="selectOverhaulRecordPage" resultMap="overhaulRecordResultMap">
        select * from simas_overhaul_record where is_deleted = 0
    </select>

</mapper>
