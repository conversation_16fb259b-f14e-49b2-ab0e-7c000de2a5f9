/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.excel.OverhaulOrderExcel;
import com.snszyk.simas.common.vo.EquipmentOverhaulVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备保养工单表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface IOverhaulOrderService extends BaseService<OverhaulOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param overhaulOrder
	 * @return
	 */
	IPage<OverhaulOrderDTO> page(IPage<OverhaulOrderDTO> page, OverhaulOrderVO overhaulOrder);

	/**
	 * 详情
	 *
	 * @param no
	 * @return
	 */
	OverhaulOrderDTO detail(String no);

	/**
	 * 设备检修
	 *
	 * @param equipmentOverhaul
	 * @return
	 */
	boolean overhaul(EquipmentOverhaulVO equipmentOverhaul);

	/**
	 * 工单确认
	 *
	 * @param overhaulOrder
	 * @return
	 */
	boolean confirm(OverhaulOrderVO overhaulOrder);

	/**
	 * 批量工单确认
	 *
	 * @param overhaulOrder
	 * @return
	 */
	boolean confirmBatch(OverhaulOrderVO overhaulOrder);

	/**
	 * 导出
	 *
	 * @param maintainOrder
	 * @return
	 */
	List<OverhaulOrderExcel> exportOrder(OverhaulOrderVO maintainOrder);

	/**
	 * 即将超期分页
	 *
	 * @param page
	 * @param overhaulOrder
	 * @return
	 */
	IPage<OverhaulOrderDTO> timeoutPage(IPage<OverhaulOrderDTO> page, OverhaulOrderVO overhaulOrder);

	/**
	 * 统计-工单完成情况
	 *
	 * @param queryDate
	 * @return
	 */
	List<OverhaulOrderDTO> overhaulOrderStatistics(Integer queryDate);

	/**
	 * 即将超期工单数量
	 *
	 * @return
	 */
	Integer expireSoonCount();

	/**
	 * 发送消息提醒
	 *
	 * @param list
	 * @param messageBizType
	 */
	void sendMessage(List<OverhaulOrder> list, MessageBizTypeEnum messageBizType);

	/**
	 * 统计报表-点巡检统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	IPage<OverhaulOrderDTO> statisticalReport(IPage<OverhaulOrderDTO> page, StatisticSearchVO search);

//	/**
//	 * 统计报表-保养统计导出
//	 *
//	 * @param search
//	 * @return
//	 */
//	List<OverhaulOrderStatisticsExcel> exportStatisticalReport(StatisticSearchVO search);

	/**
	 * 保养工单列表
	 *
	 * @param overhaulOrder
	 * @return
	 */
	List<OverhaulOrderDTO> queryList(OverhaulOrderVO overhaulOrder);


	/**
	 * 大屏 未完成特种设备
	 * @return
	 */
	List<BigScreenMessageDTO> specialNoFinishList(String tenantId);

	/**
	 *大屏 其他 超时
	 * @return
	 */
	List<BigScreenMessageDTO> noSpecialOverdueList(String tenantId);

	Integer handleOverhaulCount(OverhaulOrderVO overhaulOrder);

	List<OverhaulOrder> listBy(Long executeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus);
}
