// package com.snszyk.simas.overhaul.schedule;
//
// import com.baomidou.mybatisplus.core.toolkit.Wrappers;
// import com.snszyk.common.constant.SimasConstant;
// import com.snszyk.core.tool.utils.DateUtil;
// import com.snszyk.core.tool.utils.Func;
// import com.snszyk.message.enums.MessageBizTypeEnum;
// import com.snszyk.simas.overhaul.entity.Repair;
// import com.snszyk.simas.common.entity.TimeoutRemindSet;
// import com.snszyk.simas.common.enums.BizTypeEnum;
// import com.snszyk.simas.common.enums.OrderStatusEnum;
// import com.snszyk.simas.common.service.IBizLogService;
// import com.snszyk.simas.overhaul.service.IRepairService;
// import com.snszyk.simas.common.service.ITimeoutRemindSetService;
// import com.snszyk.simas.common.vo.BizLogVO;
// import com.snszyk.simas.overhaul.wrapper.RepairWrapper;
// import lombok.AllArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.scheduling.annotation.EnableScheduling;
// import org.springframework.scheduling.annotation.Scheduled;
//
// import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.stream.Collectors;
//
// /**
//  * 维修工单任务定时器
//  *
//  * <AUTHOR>
//  * @date 2024/08/29 15:16
//  **/
// @Slf4j
// @AllArgsConstructor
// @Configuration
// @EnableScheduling
// public class RepairSchedule {
//
// 	private final IRepairService repairService;
// 	private final ITimeoutRemindSetService timeoutRemindSetService;
// 	private final IBizLogService bizLogService;
//
//
// 	/**
// 	 * 维修单超时(每分钟执行一次)
// 	 *
// 	 * @return void
// 	 * <AUTHOR>
// 	 * @date 2024/8/29 16:16
// 	 */
// 	@Scheduled(cron = "0 0/1 * * * ?")
// 	public void repairOverdueSchedule() {
// 		log.info("################维修单超期定时任务-START-################");
// 		List<Repair> list = repairService.list(Wrappers.<Repair>query().lambda()
// 			.eq(Repair::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
// 		List<Repair> orderList = new ArrayList<>();
// 		if (Func.isNotEmpty(list)) {
// 			for (Repair repair : list) {
// 				String currentTime = DateUtil.format(DateUtil.now(), "yyyy-MM-dd HH:mm");
// 				if(Func.isNotEmpty(repair.getCompleteTime())){
// 					String endTime = DateUtil.format(repair.getCompleteTime(), "yyyy-MM-dd HH:mm");
// 					if (currentTime.compareTo(endTime) > 0) {
// 						orderList.add(repair);
// 					}
// 				}
// 			}
// 		}
// 		if (Func.isNotEmpty(orderList)) {
// 			repairService.update(Wrappers.<Repair>update().lambda()
// 				.set(Repair::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
// 				.in(Repair::getId, orderList.stream().map(Repair::getId).collect(Collectors.toList())));
// 			// 业务日志
// 			bizLogService.submitBatch(orderList.stream().map(repair -> {
// 				repair.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
// 				BizLogVO bizLog = new BizLogVO(RepairWrapper.build().entityVO(repair));
// 				bizLog.setContent("维修单超时");
// 				return bizLog;
// 			}).collect(Collectors.toList()));
// 			// 发送消息提醒
// 			repairService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_REPAIR_OVERDUE);
// 		}
// 		log.info("################维修单超期定时任务-END-################");
// 	}
//
// 	/**
// 	 * 即将超时-发送消息
// 	 *
// 	 * @return void
// 	 * <AUTHOR>
// 	 * @date 2024/9/15 17:31
// 	 */
// 	@Scheduled(cron = "0 0/1 * * * ?")
// 	public void expireSoonSchedule() {
// 		log.info("################维修工单即将超期发送消息-START-################");
// 		List<Repair> list = repairService.list(Wrappers.<Repair>query().lambda()
// 			.eq(Repair::getIsExpired, 0)
// 			.and(wrapper -> wrapper.eq(Repair::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
// 				.or()
// 				.eq(Repair::getStatus, OrderStatusEnum.IS_REJECTED.getCode())));
// 		List<Repair> orderList = new ArrayList<>();
// 		BigDecimal timeInterval = new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL);
// 		if (Func.isNotEmpty(list)) {
// 			for (Repair order : list) {
// 				if(Func.isNotEmpty(order.getCompleteTime())){
// 					if(Func.isNotEmpty(order.getReceiveUser())){
// 						TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
// 							.eq(TimeoutRemindSet::getUserId, order.getReceiveUser())
// 							.eq(TimeoutRemindSet::getBizType, BizTypeEnum.REPAIR.getCode()));
// 						if(Func.isNotEmpty(timeoutRemindSet)){
// 							timeInterval = timeoutRemindSet.getTimeInterval();
// 						}
// 					}
// 					if(Func.isNotEmpty(order.getFollowUser())){
// 						TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
// 							.eq(TimeoutRemindSet::getUserId, order.getFollowUser())
// 							.eq(TimeoutRemindSet::getBizType, BizTypeEnum.REPAIR.getCode()));
// 						if(Func.isNotEmpty(timeoutRemindSet)){
// 							timeInterval = timeoutRemindSet.getTimeInterval();
// 						}
// 					}
// 					Long seconds = order.getCompleteTime().getTime() -  System.currentTimeMillis();
// 					if (seconds <= timeInterval.multiply(new BigDecimal(3600)).multiply(new BigDecimal(1000)).longValue()) {
// 						order.setIsExpired(1);
// 						repairService.updateById(order);
// 						orderList.add(order);
// 					}
// 				}
// 			}
// 		}
// 		// 发送消息提醒
// 		if(Func.isNotEmpty(orderList)){
// 			repairService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_REPAIR_EXPIRE);
// 		}
// 		log.info("################维修工单即将超期发送消息-END-################");
// 	}
//
//
// }
