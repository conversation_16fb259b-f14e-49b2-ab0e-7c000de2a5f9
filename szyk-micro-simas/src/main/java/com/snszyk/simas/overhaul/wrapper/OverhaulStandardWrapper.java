 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.wrapper;

 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.core.mp.support.BaseEntityWrapper;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.cache.SimasCache;
 import com.snszyk.simas.overhaul.entity.OverhaulMethods;
 import com.snszyk.simas.overhaul.entity.OverhaulStandard;
 import com.snszyk.simas.overhaul.vo.OverhaulStandardVO;
 import com.snszyk.system.cache.DictBizCache;
 import com.snszyk.user.cache.UserCache;
 import com.snszyk.user.entity.User;

 import java.util.Objects;

 /**
  * 设备保养标准表包装类,返回视图层所需的字段
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 public class OverhaulStandardWrapper extends BaseEntityWrapper<OverhaulStandard, OverhaulStandardVO> {

	 public static OverhaulStandardWrapper build() {
		 return new OverhaulStandardWrapper();
	 }

	 @Override
	 public OverhaulStandardVO entityVO(OverhaulStandard overhaulStandard) {
		 OverhaulStandardVO overhaulStandardVO = Objects.requireNonNull(BeanUtil.copy(overhaulStandard, OverhaulStandardVO.class));
		 DeviceMonitor equipmentMonitor = CommonCache.getMonitor(overhaulStandard.getMonitorId());
		 if (Func.isNotEmpty(equipmentMonitor)) {
			 overhaulStandardVO.setMonitorName(equipmentMonitor.getName());
			 overhaulStandardVO.setMonitorType(equipmentMonitor.getType());
			 if (Func.isNotEmpty(equipmentMonitor.getType())) {
				 String monitorTypeName = DictBizCache.getValue("monitor_type", equipmentMonitor.getType());
				 overhaulStandardVO.setMonitorTypeName(monitorTypeName);
			 }
		 }
		 OverhaulMethods overhaulMethods = SimasCache.getOverhaulMethods(overhaulStandard.getMethodsId());
		 if (Func.isNotEmpty(overhaulMethods)) {
			 overhaulStandardVO.setMethodsName(overhaulMethods.getName());
		 }
		 User createUser = UserCache.getUser(overhaulStandard.getCreateUser());
		 if (Func.isNotEmpty(createUser)) {
			 overhaulStandardVO.setCreateUserName(createUser.getName());
		 }
		 return overhaulStandardVO;
	 }

 }
