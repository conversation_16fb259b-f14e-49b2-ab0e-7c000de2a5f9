/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
import com.snszyk.simas.overhaul.entity.OverhaulPlan;
import com.snszyk.simas.overhaul.vo.OverhaulPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备点巡检计划表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface OverhaulPlanMapper extends BaseMapper<OverhaulPlan> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectPlan
	 * @return
	 */
	List<OverhaulPlan> page(IPage page, @Param("overhaulPlan") OverhaulPlanVO overhaulPlan);

	/**
	 * 查询当天点检计划
	 *
	 * @param currentDate yyyy-MM-dd
	 * @return
	 */
	List<OverhaulPlanDTO> getTheDayPlans(@Param("currentDate") Date currentDate);

}
