 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
 import com.snszyk.simas.overhaul.entity.OverhaulPlan;
 import com.snszyk.simas.common.excel.OverhaulPlanExcel;
 import com.snszyk.simas.common.vo.AuditVO;
 import com.snszyk.simas.overhaul.vo.OverhaulPlanVO;

 import java.util.Date;
 import java.util.List;

 /**
  * 设备点巡检计划表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 public interface IOverhaulPlanService extends BaseService<OverhaulPlan> {

 	/**
 	 * 自定义分页
 	 *
 	 * @param page
 	 * @param overhaulPlan
 	 * @return
 	 */
 	IPage<OverhaulPlanDTO> page(IPage<OverhaulPlanDTO> page, OverhaulPlanVO overhaulPlan);

 	/**
 	 * 详情
 	 *
 	 * @param no
 	 * @return
 	 */
 	OverhaulPlanDTO detail(String no);

 	/**
 	 * 查看
 	 *
 	 * @param no
 	 * @return
 	 */
 	OverhaulPlanDTO view(String no);

 	/**
 	 * 新增
 	 *
 	 * @param overhaulPlan
 	 * @return
 	 */
 	boolean add(OverhaulPlanVO overhaulPlan);

 	/**
 	 * 修改
 	 *
 	 * @param overhaulPlan
 	 * @return
 	 */
 	boolean modify(OverhaulPlanVO overhaulPlan);

 	/**
 	 * 查询当天点检计划
 	 *
 	 * @param currentDate
 	 * @return
 	 */
 	List<OverhaulPlanDTO> getTheDayPlans(Date currentDate);

 	/**
 	 * 导出
 	 *
 	 * @param overhaulPlan
 	 * @return
 	 */
 	List<OverhaulPlanExcel> exportPlan(OverhaulPlanVO overhaulPlan);

 	/**
 	 * 审核
 	 * @param auditVO
 	 * @return
 	 */
 	boolean audit(AuditVO auditVO);

 	/**
 	 * 关闭到期计划
 	 * @return
 	 */
 	boolean closeExpirePlan();
 }
