/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.vo.RepairVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备维修单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface RepairMapper extends BaseMapper<Repair> {

	/**
	 * 内部维修分页
	 *
	 * @param page
	 * @param repair
	 * @return
	 */
	List<RepairDTO> internalPage(IPage page, @Param("repair") RepairVO repair);

	/**
	 * 外委维修分页
	 *
	 * @param page
	 * @param repair
	 * @return
	 */
	List<RepairDTO> externalPage(IPage page, @Param("repair") RepairVO repair);

	/**
	 * 即将超时分页
	 *
	 * @param page
	 * @param repair
	 * @return
	 */
	List<RepairDTO> timeoutPage(IPage page, @Param("repair") RepairVO repair);

	/**
	 * 导出列表
	 *
	 * @param repair
	 * @return
	 */
	List<RepairDTO> exportList(@Param("repair") RepairVO repair);

	/**
	 * 即将超时工单数量
	 *
	 * @param repair
	 * @return
	 */
	Integer expireSoonCount(@Param("repair") RepairVO repair);

	/**
	 * 维修耗时统计列表
	 *
	 * @param bizType
	 * @param queryDate
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<RepairDTO> timeTakeStatisticsList(@Param("bizType") String bizType, @Param("queryDate") Integer queryDate,
										   @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

	/**
	 * 统计报表-维修统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<RepairDTO> statisticalReport(IPage page, @Param("search") StatisticSearchVO search);

	/**
	 * 维修工单列表
	 *
	 * @param repair
	 * @return
	 */
	List<RepairDTO> queryList(@Param("repair") RepairVO repair);

	/**
	 * 大屏-维修情况统计
	 *
	 * @param repair
	 * @param queryDate
	 * @return
	 */
	List<RepairDTO> repairStatistics(@Param("repair") RepairVO repair, @Param("queryDate") int queryDate);

	/**
	 * 统计报表-维修按设备统计
	 *
	 * @param search
	 * @return
	 */
	List<EquipmentStatisticsDTO> statisticsByEquipment(@Param("search") StatisticSearchVO search);


	List<BigScreenMessageDTO> overdueList(@Param("tenantId") String tenantId);

	List<RepairDTO> repair30day(@Param("bizType") String bizType);

	List<RepairDTO> finishList(@Param("bizType") String bizType, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

	Integer handleRepairCount(@Param("repair") RepairVO repair);

	List<RepairDTO> curYearRepair(LocalDateTime start, @Param("statusList") List<Integer> statusList, String tenantId);

	/**
	 * 设备维修统计-故障缺陷统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	IPage<FaultDefectDTO> listGroupByFaultDefect(@Param("page") IPage<FaultDefectDTO> page, @Param("search") StatisticSearchVO search);

	/**
	 * 工单统计
	 *
	 * @param search
	 * @return
	 */
	List<Repair> equipmentStatisticsOfOrder(@Param("search") StatisticSearchVO search);


}
