<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.overhaul.mapper.OverhaulOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="overhaulOrderResultMap" type="com.snszyk.simas.overhaul.entity.OverhaulOrder">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="status" property="status"/>
        <result column="submit_time" property="submitTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="create_time" property="createTime"/>
        <result column="reject_reason" property="rejectReason"/>
    </resultMap>

    <resultMap id="overhaulOrderDTOResultMap" type="com.snszyk.simas.overhaul.dto.OverhaulOrderDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="status" property="status"/>
        <result column="submit_time" property="submitTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="create_time" property="createTime"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_category" property="equipmentCategory"/>
    </resultMap>


    <select id="page" resultMap="overhaulOrderResultMap">
        select o.* from simas_overhaul_order o left join device_account e on o.equipment_id = e.id
        where  o.is_deleted = 0
        <if test="overhaulOrder.equipmentIdList != null">
            and e.id in
            <foreach collection="overhaulOrder.equipmentIdList" item="equipmentId" index="index" open="(" close=")"
                     separator=",">
                #{equipmentId}
            </foreach>
        </if>
        <if test="overhaulOrder.executeDept != null">
            <choose>
                <when test="overhaulOrder.executeDept==0">
                    and o.execute_dept is null
                </when>
                <otherwise>
                    and o.execute_dept = #{overhaulOrder.executeDept}
                </otherwise>
            </choose>
        </if>
        <if test="overhaulOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{overhaulOrder.executeUser})
        </if>
        <if test="overhaulOrder.no != null and overhaulOrder.no != ''">
            and o.`no` like concat('%',#{overhaulOrder.no},'%')
        </if>
        <if test="overhaulOrder.orderName != null and overhaulOrder.orderName != ''">
            and o.order_name like concat('%',#{overhaulOrder.orderName},'%')
        </if>
        <if test="overhaulOrder.equipmentId != null">
            and o.equipment_id = #{overhaulOrder.equipmentId}
        </if>
        <if test="overhaulOrder.equipmentCode != null and overhaulOrder.equipmentCode != ''">
            and o.equipment_code = #{overhaulOrder.equipmentCode}
        </if>
        <if test="overhaulOrder.status != null">
            and o.status = #{overhaulOrder.status}
        </if>
        <if test="overhaulOrder.statusList != null and overhaulOrder.statusList.size() > 0 ">
            and o.status in
            <foreach collection="overhaulOrder.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="overhaulOrder.keywords != null and overhaulOrder.keywords != ''">
            and (o.`no` like concat('%',#{overhaulOrder.keywords},'%') or
            e.`sn` like concat('%',#{overhaulOrder.keywords},'%') or e.`code` like concat('%',#{overhaulOrder.keywords},'%'))
        </if>
        <if test="overhaulOrder.startDate != null and overhaulOrder.startDate != ''">
            and o.start_time <![CDATA[ >= ]]> #{overhaulOrder.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="overhaulOrder.endDate != null and overhaulOrder.endDate != ''">
            and o.start_time <![CDATA[ <= ]]> #{overhaulOrder.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="overhaulOrder.statuses != null">
            and o.status in
            <foreach collection="overhaulOrder.statuses" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="overhaulOrder.neStatus != null">
            and o.status <![CDATA[ <> ]]> #{overhaulOrder.neStatus}
        </if>
        <if test="overhaulOrder.onlyQueryExecuteUser!=null">
            <choose>
                <when test="overhaulOrder.onlyQueryExecuteUser == 0">
                    and o.execute_user is null
                </when>
                <otherwise>
                    and o.execute_user = #{overhaulOrder.onlyQueryExecuteUser}
                </otherwise>
            </choose>
        </if>
        order by o.id desc
    </select>

    <select id="timeoutPage" resultMap="overhaulOrderResultMap">
        select *
        from simas_overhaul_order
        where execute_dept = #{overhaulOrder.executeDept} and is_deleted = 0
          and (execute_user is null or execute_user = #{overhaulOrder.executeUser})
          and (status = 1 or status = 6)
        <![CDATA[ AND NOW() >= DATE_SUB(end_time, INTERVAL #{overhaulOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < end_time ]]>
        order by id desc
    </select>

    <select id="expireSoonCount" resultType="java.lang.Integer">
        select count(*)
        from simas_overhaul_order o
        LEFT JOIN device_account e ON o.equipment_id = e.id
        where e.is_deleted = 0 and o.is_deleted = 0
        <if test="overhaulOrder.executeDept != null">
            and o.execute_dept = #{overhaulOrder.executeDept}
        </if>
        <if test="overhaulOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{overhaulOrder.executeUser})
        </if>
        and (o.status = 1 or o.status = 6)
        <![CDATA[ AND NOW() >= DATE_SUB(o.end_time, INTERVAL #{overhaulOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < o.end_time
        ]]>
    </select>

    <select id="overhaulOrderStatistics" resultMap="overhaulOrderResultMap">
        SELECT * FROM simas_overhaul_order WHERE 1=1 and is_deleted = 0
        <if test="queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
    </select>

    <select id="statisticalReport" resultMap="overhaulOrderResultMap">
        SELECT * FROM simas_overhaul_order WHERE 1=1 and is_deleted = 0
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.deptIdList != null">
            and execute_dept in
            <foreach collection="search.deptIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        order by id desc
    </select>

    <select id="queryList" resultMap="overhaulOrderDTOResultMap">
        select o.*, e.name as equipment_name, e.category_id as equipment_category from simas_overhaul_order o
        left join device_account e on o.equipment_id = e.id
        where e.is_deleted = 0 and o.is_deleted = 0
        <if test="overhaulOrder.executeDept != null">
            and o.execute_dept = #{overhaulOrder.executeDept}
        </if>
        <if test="overhaulOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{overhaulOrder.executeUser})
        </if>
        <if test="overhaulOrder.no != null and overhaulOrder.no != ''">
            and o.`no` like concat('%',#{overhaulOrder.no},'%')
        </if>
        <if test="overhaulOrder.orderName != null and overhaulOrder.orderName != ''">
            and o.plan_info like concat('%',#{overhaulOrder.orderName},'%')
        </if>
        <if test="overhaulOrder.equipmentId != null">
            and o.equipment_id = #{overhaulOrder.equipmentId}
        </if>
        <if test="overhaulOrder.equipmentCode != null and overhaulOrder.equipmentCode != ''">
            and o.equipment_code = #{overhaulOrder.equipmentCode}
        </if>
        <if test="overhaulOrder.status != null">
            and o.status = #{overhaulOrder.status}
        </if>
    </select>

    <select id="statisticsByEquipment" resultType="com.snszyk.simas.common.dto.EquipmentStatisticsDTO">
        SELECT equipment_id as id, count(*) as count FROM `simas_overhaul_order` where 1=1
                                                                                   and is_deleted = 0
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        group by equipment_id
    </select>
    <select id="specialNoFinishList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        select t.equipment_id, e.name as equipment_name, t.no as order_no
        from simas_overhaul_order t
                 left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.status in (1, 3, 5, 6) and t.is_deleted = 0
          and e.special_type is not null
          and e.special_type != ''
        <if test="tenantId != null and tenantId != ''">
            and e.tenant_id = #{tenantId}
        </if>
        group by t.equipment_id


    </select>
    <select id="noSpecialOverdueList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        select t.equipment_id, e.name as equipment_name, t.no as order_no
        from simas_overhaul_order t
                 left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.status = 3
          and t.is_deleted = 0
          and (e.special_type is null
           or e.special_type = '')
            <if test="tenantId != null and tenantId != ''">
            and e.tenant_id = #{tenantId}
        </if>
        order by t.create_time desc

    </select>
    <select id="handleOverhaulCount" resultType="java.lang.Integer"
            parameterType="com.snszyk.simas.overhaul.vo.OverhaulOrderVO">

        select count(1) from simas_overhaul_order o
        <where>
            and o.is_deleted = 0
            <if test="overhaulOrder.queryAuthRole != null and overhaulOrder.queryAuthRole == 1">
                and (o.execute_user = #{overhaulOrder.executeUser} or ( o.execute_dept = #{overhaulOrder.executeDept} and o.execute_user is null))
            </if>
            <if test="overhaulOrder.queryAuthRole != null and overhaulOrder.queryAuthRole == 2">
                and  o.execute_dept = #{overhaulOrder.executeDept} and o.execute_user is null
            </if>

            <if test="overhaulOrder.statusList != null and overhaulOrder.statusList.size() > 0 ">
                and o.status in
                <foreach collection="overhaulOrder.statusList" item="status" index="index" open="(" close=")"
                         separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="overhaulOrder.tenantId != null and overhaulOrder.tenantId != ''">
                and o.tenant_id = #{overhaulOrder.tenantId}
            </if>
            <if test="overhaulOrder.startDate != null and overhaulOrder.startDate != ''">
                and o.create_time <![CDATA[ >= ]]> #{overhaulOrder.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="overhaulOrder.endDate != null and overhaulOrder.endDate != ''">
                and o.create_time <![CDATA[ <= ]]> #{overhaulOrder.endDate, jdbcType=TIMESTAMP}
            </if>

        </where>

    </select>

</mapper>
