/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.overhaul.entity.OverhaulRecord;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.fault.enums.FaultDefectTypeEnum;
import com.snszyk.simas.overhaul.vo.OverhaulRecordVO;

import java.util.Objects;

/**
 * 设备检修记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class OverhaulRecordWrapper extends BaseEntityWrapper<OverhaulRecord, OverhaulRecordVO> {

	public static OverhaulRecordWrapper build() {
		return new OverhaulRecordWrapper();
 	}

	@Override
	public OverhaulRecordVO entityVO(OverhaulRecord overhaulRecord) {
		OverhaulRecordVO record = Objects.requireNonNull(BeanUtil.copy(overhaulRecord, OverhaulRecordVO.class));
		if (record.getIsAbnormal() == 1){
			if (Func.isNotEmpty(record.getFaultLevel())){
				record.setFaultLevelName(DefectLevelEnum.getByCode(record.getFaultLevel()).getName());
			}
			if (Func.isNotEmpty(record.getFaultType())){
				record.setFaultTypeName(FaultDefectTypeEnum.getByCode(record.getFaultType()).getName());
			}
		}
		return record;
	}

}
