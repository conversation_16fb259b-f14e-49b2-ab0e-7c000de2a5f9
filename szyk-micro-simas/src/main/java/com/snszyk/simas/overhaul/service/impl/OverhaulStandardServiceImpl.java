 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service.impl;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
 import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
 import com.snszyk.common.equipment.feign.FeignPage;
 import com.snszyk.common.equipment.feign.ICommonClient;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.vo.EquipmentStandardVO;
 import com.snszyk.simas.inspect.service.IInspectStandardService;
 import com.snszyk.simas.overhaul.entity.OverhaulStandard;
 import com.snszyk.simas.overhaul.mapper.OverhaulStandardMapper;
 import com.snszyk.simas.overhaul.mapper.RepairMapper;
 import com.snszyk.simas.overhaul.service.IOverhaulStandardService;
 import com.snszyk.simas.overhaul.vo.OverhaulStandardVO;
 import com.snszyk.simas.overhaul.wrapper.OverhaulStandardWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备保养标准表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @AllArgsConstructor
 @Service
 public class OverhaulStandardServiceImpl extends ServiceImpl<OverhaulStandardMapper, OverhaulStandard> implements IOverhaulStandardService {

	 private final ICommonClient commonClient;
	 private final RepairMapper repairMapper;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final IInspectStandardService stdService;



	 @Override
	 public IPage<DeviceAccountVO> devicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount) {
		 DeviceAccountPageVO vo = BeanUtil.copy(deviceAccount, DeviceAccountPageVO.class);
		 R<FeignPage<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageListScope(vo,
			 Func.toInt(page.getCurrent()), Func.toInt(page.getSize()), EquipmentDataScopeEnum.OVERHAUL_STANDARD.getCode());
		 if (!deviceResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isNotEmpty(deviceResult.getData())&&Func.isNotEmpty(deviceResult.getData().getRecords())) {
			 deviceResult.getData().getRecords().forEach(data -> {
				 // 检修标准数量
				 data.setOverhaulStandardCount(this.count(Wrappers.<OverhaulStandard>query().lambda()
					 .eq(OverhaulStandard::getEquipmentId, data.getId())));
			 });
		 }
		 IPage<DeviceAccountVO> result= new Page<>();
		 result.setTotal(deviceResult.getData().getTotal());
		 result.setRecords(deviceResult.getData().getRecords());
		 return result;

	 }

	 @Override
	 public EquipmentStandardVO detail(Long equipmentId) {
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 EquipmentStandardVO detail = new EquipmentStandardVO(equipmentId);
		 detail.setEquipmentAccount(deviceAccountResult.getData());
		 List<OverhaulStandard> standardList = this.list(Wrappers.<OverhaulStandard>query().lambda()
			 .eq(OverhaulStandard::getEquipmentId, equipmentId).orderByAsc(OverhaulStandard::getSort));
		 if (Func.isNotEmpty(standardList)) {
			 detail.setOverhaulStandardList(OverhaulStandardWrapper.build().listVO(standardList));
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean submit(EquipmentStandardVO vo) {

		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(vo.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 //添加生成的部位
		 Map<String, Integer> monitorMap
			 = vo.getOverhaulStandardList().stream().filter(e ->
			 e.getMonitorId() == null).collect(Collectors.toMap(OverhaulStandardVO::getMonitorName, OverhaulStandardVO::getMonitorType));
		 Long equipmentId = vo.getEquipmentId();
//		 stdService.genMonitor(equipmentId, monitorMap);
		 boolean ret;
		 // 1. 判断是否已经存在该标准，如果存在则更新，不存在则新增
		 if (Func.isNotEmpty(vo.getOverhaulStandardList())) {
			 List<Long> updateIds = vo.getOverhaulStandardList().stream()
				 .filter(standardVO -> Func.isNotEmpty(standardVO.getId()))
				 .map(standardVO -> standardVO.getId()).collect(Collectors.toList());
			 // 删除标准
			 if (Func.isNotEmpty(updateIds)) {
				 this.remove(Wrappers.<OverhaulStandard>query().lambda()
					 .eq(OverhaulStandard::getEquipmentId, vo.getEquipmentId()).notIn(OverhaulStandard::getId, updateIds));
			 }else{
				 this.remove(Wrappers.<OverhaulStandard>query().lambda()
					 .eq(OverhaulStandard::getEquipmentId, vo.getEquipmentId()));
			 }
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<OverhaulStandard> list = vo.getOverhaulStandardList().stream().map(standardVO -> {
				 OverhaulStandard overhaulStandard = Objects.requireNonNull(BeanUtil.copy(standardVO, OverhaulStandard.class));
				 if (Func.isEmpty(standardVO.getId())) {
					 overhaulStandard.setEquipmentId(vo.getEquipmentId())
						 .setCreateUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now());
					 //如果是新增的
					 if(standardVO.getMonitorId()==null) {
						 Long monitorId = stdService.genMonitor(vo.getEquipmentId(), standardVO.getMonitorName(), standardVO.getMonitorType());
						 overhaulStandard.setMonitorId(monitorId);
					 }
				 }
				 overhaulStandard.setUpdateTime(DateUtil.now());
				 overhaulStandard.setSort(sort.getAndSet(sort.get() + 1));
				 return overhaulStandard;
			 }).collect(Collectors.toList());
			 // 2. 保存数据
			 ret = this.saveOrUpdateBatch(list);
		 } else {
			 ret = this.clear(vo.getEquipmentId());
		 }
		 // 3. 返回结果
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean clear(Long equipmentId) {
		 // 清空标准，同步删除设备关联的工单
//		 boolean ret = repairMapper.delete(Wrappers.<Repair>query().lambda()
//			 .eq(Repair::getEquipmentId, equipmentId)
//			 .eq(Repair::getSource, RepairSourceEnum.PLANNING_OVERHAUL.getCode())
//			 .notIn(Repair::getStatus,
//				 OrderStatusEnum.IS_COMPLETED.getCode(),
//				 OrderStatusEnum.IS_CLOSED.getCode(),
//				 OrderStatusEnum.OVERDUE_COMPLETED.getCode())) >= 0;
//		 // 清空标准
		 boolean ret = this.remove(Wrappers.<OverhaulStandard>query().lambda()
			 .eq(OverhaulStandard::getEquipmentId, equipmentId));
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean saveImportData(List<OverhaulStandard> list) {
		 updateSort(list);
		 return this.saveOrUpdateBatch(list);
	 }

	 /**
	  * 添加排序
	  * @param list
	  */
	 private void updateSort(List<OverhaulStandard> list) {
		 // 获取设备id分组
		 Map<Long, List<OverhaulStandard>> map = list.stream().collect(Collectors.groupingBy(OverhaulStandard::getEquipmentId));
		 map.keySet().forEach(equipmentId -> {
			 OverhaulStandard standard = this.getOne(Wrappers.<OverhaulStandard>query().lambda().eq(OverhaulStandard::getEquipmentId, equipmentId).orderByDesc(OverhaulStandard::getSort).last("LIMIT 1"));
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 if (Func.isNotEmpty(standard) && !Func.isNull(standard.getSort())) {
				 sort.set(standard.getSort());
			 }
			 map.get(equipmentId).forEach(item -> {
				 OverhaulStandard entity = this.getOne(Wrappers.<OverhaulStandard>query().lambda().eq(OverhaulStandard::getEquipmentId, item.getEquipmentId()).eq(OverhaulStandard::getMonitorId, item.getMonitorId()));
				 if (Func.isNotEmpty(entity)) {
					 item.setUpdateTime(DateUtil.now())
						 .setSort(entity.getSort())
						 .setId(entity.getId());
				 } else {
					 item.setSort(sort.getAndSet(sort.get() + 1))
						 .setCreateUser(AuthUtil.getUserId())
						 .setCreateTime(DateUtil.now());
				 }
			 });
		 });
	 }
 }
