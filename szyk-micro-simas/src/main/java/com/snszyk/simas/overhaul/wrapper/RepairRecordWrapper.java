/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.overhaul.entity.RepairRecord;
import com.snszyk.simas.overhaul.vo.RepairRecordVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备维修记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public class RepairRecordWrapper extends BaseEntityWrapper<RepairRecord, RepairRecordVO> {

	public static RepairRecordWrapper build() {
		return new RepairRecordWrapper();
 	}

	@Override
	public RepairRecordVO entityVO(RepairRecord repairRecord) {
		RepairRecordVO repairRecordVO = Objects.requireNonNull(BeanUtil.copy(repairRecord, RepairRecordVO.class));
		if(Func.isNotEmpty(repairRecord.getOperateUser())){
			User operateUser = UserCache.getUser(repairRecord.getOperateUser());
			if(Func.isNotEmpty(operateUser)){
				repairRecordVO.setOperateUserName(operateUser.getRealName());
			}
		}
		if(Func.isNotEmpty(repairRecord.getVerifyUser())){
			User verifyUser = UserCache.getUser(repairRecord.getVerifyUser());
			if(Func.isNotEmpty(verifyUser)){
				repairRecordVO.setVerifyUserName(verifyUser.getRealName());
			}
		}
		if(Func.isNotEmpty(repairRecord.getResult())){
			repairRecordVO.setResultName(repairRecord.getResult() == 1 ? "已修复" : "未完全修复，可运行");
		}
		return repairRecordVO;
	}

}
