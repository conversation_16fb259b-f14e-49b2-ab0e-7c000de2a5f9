/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.overhaul.dto.RepairDurationDTO;
import com.snszyk.simas.overhaul.entity.RepairRecord;
import com.snszyk.simas.overhaul.vo.RepairDurationPageVO;

/**
 * 设备维修记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface IRepairRecordService extends IService<RepairRecord> {

	/**
	 * 设备维修率
	 *
	 * @param vo
	 * @return
	 */
	IPage<RepairDurationDTO> pageRepairDuration(RepairDurationPageVO vo);

}
