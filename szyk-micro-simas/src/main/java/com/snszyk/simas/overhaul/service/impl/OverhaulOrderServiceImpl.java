 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service.impl;

 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceCategory;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.location.entity.Location;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.*;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.message.enums.MessageTypeEnum;
 import com.snszyk.message.enums.ReceiverTypeEnum;
 import com.snszyk.message.enums.YesNoEnum;
 import com.snszyk.message.feign.IMessageClient;
 import com.snszyk.message.vo.MessageVo;
 import com.snszyk.message.vo.ReceiverInfoVo;
 import com.snszyk.resource.entity.Attach;
 import com.snszyk.resource.feign.IAttachClient;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.entity.BizLog;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.excel.OverhaulOrderExcel;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.service.IBizLogService;
 import com.snszyk.simas.common.service.IComponentMaterialService;
 import com.snszyk.simas.common.vo.EquipmentOverhaulVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
 import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
 import com.snszyk.simas.overhaul.dto.OverhaulStandardDTO;
 import com.snszyk.simas.overhaul.entity.*;
 import com.snszyk.simas.overhaul.mapper.OverhaulOrderMapper;
 import com.snszyk.simas.overhaul.mapper.OverhaulPlanMapper;
 import com.snszyk.simas.overhaul.mapper.OverhaulStandardMapper;
 import com.snszyk.simas.overhaul.service.IOverhaulMethodsService;
 import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
 import com.snszyk.simas.overhaul.service.IOverhaulRecordService;
 import com.snszyk.simas.overhaul.service.IRepairService;
 import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
 import com.snszyk.simas.overhaul.vo.OverhaulRecordVO;
 import com.snszyk.simas.overhaul.vo.RepairVO;
 import com.snszyk.simas.overhaul.wrapper.OverhaulOrderWrapper;
 import com.snszyk.simas.overhaul.wrapper.OverhaulRecordWrapper;
 import com.snszyk.simas.overhaul.wrapper.RepairWrapper;
 import com.snszyk.simas.spare.vo.ComponentMaterialVO;
 import com.snszyk.system.feign.ISysClient;
 import com.snszyk.system.vo.RoleVO;
 import com.snszyk.user.entity.User;
 import com.snszyk.user.feign.IUserClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.math.BigDecimal;
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备保养工单表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @Slf4j
 @AllArgsConstructor
 @Service
 public class OverhaulOrderServiceImpl extends BaseServiceImpl<OverhaulOrderMapper, OverhaulOrder> implements IOverhaulOrderService {

	 private final IRepairService repairService;
	 private final IBizLogService bizLogService;
	 private final OverhaulPlanMapper overhaulPlanMapper;
	 private final IOverhaulRecordService overhaulRecordService;
	 private final IOverhaulMethodsService overhaulMethodsService;
	 private final OverhaulStandardMapper overhaulStandardMapper;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	 private final IComponentMaterialService componentMaterialService;
	 private final ISysClient sysClient;
	 private final IUserClient userClient;
	 private final IMessageClient messageClient;
	 private final IAttachClient attachClient;
	 private final IDeviceAccountClient deviceAccountClient;

	 @Override
	 public IPage<OverhaulOrderDTO> page(IPage<OverhaulOrderDTO> page, OverhaulOrderVO overhaulOrder) {
// 		Long simasAdmin = null;
// 		Long user = null;
// 		Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
// 		if (Func.isNotEmpty(role)) {
// 			simasAdmin = role.getId();
// 		}
// 		role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.REPAIR_USER);
// 		if (Func.isNotEmpty(role)) {
// 			user = role.getId();
// 		}
		 // 数据权限
// 		String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
// 		if (simasAdmin != null && !roleId.contains(Func.toStr(simasAdmin))) {
// 			// 只能查看本部门的
// 			overhaulOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
// 			if (user != null && roleId.contains(Func.toStr(user))) {
// 				// 只能查看派发给本人的工单
// 				overhaulOrder.setExecuteUser(AuthUtil.getUserId());
// 			}
// 		}
		 if (Func.isNotEmpty(overhaulOrder.getStartDate())) {
			 overhaulOrder.setStartDate(overhaulOrder.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(overhaulOrder.getEndDate())) {
			 overhaulOrder.setEndDate(overhaulOrder.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 // 只查询特种设备类型
		 if (Func.isNotEmpty(overhaulOrder.getOnlyQuerySpecialType()) && overhaulOrder.getOnlyQuerySpecialType()) {
// 			EquipmentAccountVO equipmentAccountVO = new EquipmentAccountVO();
// 			equipmentAccountVO.setIsSpecialType(Boolean.TRUE);
			 DeviceAccountVO vo = new DeviceAccountVO();
			 vo.setIsSpecial(1);
			 R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(vo);
			 List<DeviceAccountVO> specialTypeList = listR.getData();
//			List<EquipmentAccount> specialTypeList = equipmentAccountMapper.page(new Page<>(page.getCurrent(), -1L), equipmentAccountVO);
			 if (ObjectUtil.isEmpty(specialTypeList)) {
				 overhaulOrder.setEquipmentIdList(Arrays.asList(-1L));
			 } else {
				 overhaulOrder.setEquipmentIdList(ListUtil.map(specialTypeList, DeviceAccountVO::getId));
			 }
		 }
		 List<OverhaulOrder> list = baseMapper.page(page, overhaulOrder);


		 list.stream().forEach(order -> {
			 if (Func.isEmpty(order.getOrderName())) {
				 DeviceAccountVO equipment = deviceAccountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(order.getEquipmentId()).getData();
// 				EquipmentAccount equipment = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda()
// 					.eq(EquipmentAccount::getId, order.getEquipmentId()).select(EquipmentAccount::getName));
				 OverhaulPlan plan = overhaulPlanMapper.selectById(order.getPlanId());
				 order.setOrderName(equipment.getName() + "(" + plan.getName() + ")");
				 updateById(order);
			 }
		 });
		 List<OverhaulOrderDTO> resultList = OverhaulOrderWrapper.build().listDTO(list);
		 // 是否需要审核v1.2.1
		 resultList.forEach(dto -> {
			 DeviceAccountVO equipment = deviceAccountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(dto.getEquipmentId()).getData();

			 if (ObjectUtil.isNotEmpty(equipment)) {
				 dto.setEquipmentName(equipment.getName()).setEquipmentSn(equipment.getSn());
				 dto.setSpecialType(String.valueOf(equipment.getCategoryId()));
				 // 地点
				 if (Func.isNotEmpty(equipment.getLocationId())) {
					 Location location = CommonCache.getLocation(equipment.getLocationId());
					 if (Func.isNotEmpty(location)) {
						 dto.setLocationPath(location.getPath().replace(",", "/"));
					 }
				 }
			 }
			 List<OverhaulRecord> recordList = overhaulRecordService.list(Wrappers.<OverhaulRecord>query().lambda()
				 .eq(OverhaulRecord::getOrderId, dto.getId()));
			 if (Func.isNotEmpty(recordList)) {
				 List<Integer> abnormalList = recordList.stream().map(OverhaulRecord::getIsAbnormal).collect(Collectors.toList());
				 if (abnormalList.contains(Func.toInt(StringPool.ONE))) {
					 dto.setOverhaulResult("异常");
				 } else {
					 dto.setOverhaulResult("正常");
				 }
			 }

// 			dto.setSpecialTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_SPECIAL_TYPE, equipment.getSpecialType()));
			 // 特种设备没有超期、超期完成状态，超期=执行中；超期完成=完成
			 // SpecialTypeHandle.handleStatus(dto, equipment.getSpecialType());
			 // 重新处理状态名称
			 // dto.setStatusName(OrderStatusEnum.getByCode(dto.getStatus()).getName());

			 // 工单名称
			 OverhaulPlan plan = overhaulPlanMapper.selectById(dto.getPlanId());
			 if (Func.isNotEmpty(plan)) {
				 dto.setCycleTypeName(PlanCycleEnum.getByCode(plan.getCycleType()).getName());
			 }
		 });
		 return page.setRecords(resultList);
	 }

	 @Override
	 public OverhaulOrderDTO detail(String no) {
		 OverhaulOrder overhaulOrder = this.getOne(Wrappers.<OverhaulOrder>query().lambda().eq(OverhaulOrder::getNo, no));
		 if (overhaulOrder == null) {
			 throw new ServiceException("当前工单不存在");
		 }
		 OverhaulOrderDTO detail = OverhaulOrderWrapper.build().entityDTO(overhaulOrder);
		 R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(detail.getEquipmentId());
		 if (!deviceAccountVOR.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountVOR.getData())) {
			 throw new ServiceException("当前设备台账不存在！");
		 }

		 DeviceAccountVO equipmentAccount = deviceAccountVOR.getData();
//		EquipmentAccount equipmentAccount = equipmentAccountService.getById(detail.getEquipmentId());
		 detail.setEquipmentAccount(equipmentAccount)
			 .setOverhaulPlan(JSONUtil.toBean(detail.getPlanInfo(), OverhaulPlanDTO.class));
		 detail.setEquipmentCode(equipmentAccount.getCode());

		 // 地点
		 if (Func.isNotEmpty(equipmentAccount.getLocationId())) {
			 Location location = CommonCache.getLocation(equipmentAccount.getLocationId());

//			Location location = SimasCache.getLocation(equipmentAccount.getLocationId());
			 if (Func.isNotEmpty(location)) {
				 detail.setLocationPath(location.getPath().replace(",", "/"));
			 }
		 }
		 // 未完成的工单，明细查询standard表
		 if (OrderStatusEnum.IS_COMPLETED != OrderStatusEnum.getByCode(overhaulOrder.getStatus())
			 && OrderStatusEnum.OVERDUE_COMPLETED != OrderStatusEnum.getByCode(overhaulOrder.getStatus())) {
			 // 标准列表
// 			List<OverhaulStandard> standardList = overhaulStandardMapper.selectList(Wrappers.<OverhaulStandard>query().lambda()
// 				.eq(OverhaulStandard::getEquipmentId, overhaulOrder.getEquipmentId()));
			 List<OverhaulStandard> standardList = JSONUtil.toList(detail.getStandardInfo(), OverhaulStandard.class);
			 if (Func.isNotEmpty(standardList)) {
				 detail.setStandardList(standardList.stream().map(standard -> {
					 OverhaulStandardDTO standDTO = Objects.requireNonNull(BeanUtil.copy(standard, OverhaulStandardDTO.class));
					 DeviceMonitor monitor = CommonCache.getMonitor(standard.getMonitorId());
					 standDTO.setMonitorName(monitor.getName());
					 OverhaulMethods methods = overhaulMethodsService.getById(standard.getMethodsId());
					 if (methods != null) {
						 standDTO.setMethodsName(methods.getName());
					 }
					 OverhaulRecord overhaulRecord = overhaulRecordService.getOne(Wrappers.<OverhaulRecord>query().lambda()
						 .eq(OverhaulRecord::getOrderId, overhaulOrder.getId())
						 .eq(OverhaulRecord::getMonitorId, standard.getMonitorId())
						 .eq(OverhaulRecord::getStandardId, standard.getId()));
					 if (Func.isNotEmpty(overhaulRecord)) {
						 OverhaulRecordVO recordVO = OverhaulRecordWrapper.build().entityVO(overhaulRecord);
						 // 异常图片
						 if (Func.isNotEmpty(recordVO.getAttachId())) {
							 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(recordVO.getAttachId()));
							 if (attachListR.isSuccess()) {
								 recordVO.setAttachList(attachListR.getData());
							 }
						 }
						 if (Func.isNotEmpty(recordVO.getComponent())) {
							 recordVO.setMaterialList(JSONUtil.toList(recordVO.getComponent(), ComponentMaterialVO.class));
						 }

						 standDTO.setIsAbnormal(overhaulRecord.getIsAbnormal())
							 .setAbnormalStatus(overhaulRecord.getIsAbnormal() == 0 ? "正常" : "异常")
							 .setRecord(recordVO);
					 }
					 return standDTO;
				 }).collect(Collectors.toList()));
			 }
		 } else {
			 // 完成的工单，明细查询record表
			 List<OverhaulRecord> recordList = overhaulRecordService.list(Wrappers.<OverhaulRecord>query().lambda()
				 .eq(OverhaulRecord::getOrderId, overhaulOrder.getId()));
			 if (Func.isNotEmpty(recordList)) {
				 detail.setStandardList(recordList.stream().map(record -> {
					 OverhaulRecordVO recordVO = OverhaulRecordWrapper.build().entityVO(record);
					 // 异常图片
					 if (Func.isNotEmpty(recordVO.getAttachId())) {
						 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(recordVO.getAttachId()));
						 if (attachListR.isSuccess()) {
							 recordVO.setAttachList(attachListR.getData());
						 }
					 }
					 if (Func.isNotEmpty(record.getComponent())) {
						 recordVO.setMaterialList(JSONUtil.toList(record.getComponent(), ComponentMaterialVO.class));
					 }

					 OverhaulStandard standard = JSONUtil.toBean(record.getStandardInfo(), OverhaulStandard.class);
					 OverhaulStandardDTO standDTO = Objects.requireNonNull(BeanUtil.copy(standard, OverhaulStandardDTO.class));

					 OverhaulMethods methods = overhaulMethodsService.getById(standard.getMethodsId());
					 if (methods != null) {
						 standDTO.setMethodsName(methods.getName());
					 }
					 standDTO.setMonitorName(recordVO.getMonitorName()).setIsAbnormal(recordVO.getIsAbnormal())
						 .setAbnormalStatus(recordVO.getIsAbnormal() == 0 ? "正常" : "异常")
						 .setRecord(recordVO);
					 return standDTO;
				 }).collect(Collectors.toList()));
			 }
		 }
		 return detail;
	 }

	 /**
	  * 提交
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean overhaul(EquipmentOverhaulVO vo) {
		 OverhaulOrder overhaulOrder = this.baseMapper.selectById(vo.getOrderId());
		 String standardInfo = overhaulOrder.getStandardInfo();
		 if (Func.isEmpty(standardInfo)) {
			 throw new ServiceException("当前工单没有标准！");
		 }
		 List<OverhaulStandard> standardList = JSONUtil.toList(standardInfo, OverhaulStandard.class);
		 Map<Long, OverhaulStandard> standardMap = standardList.stream().collect(Collectors.toMap(OverhaulStandard::getId, e -> e, (k, v) -> v));
		 Integer oldStatus = overhaulOrder.getStatus();
		 if (OrderStatusEnum.getByCode(oldStatus) == OrderStatusEnum.IS_COMPLETED
			 || OrderStatusEnum.getByCode(oldStatus) == OrderStatusEnum.OVERDUE_COMPLETED) {
			 throw new ServiceException("当前工单已完成，不能提交！");
		 }
		 Date now = DateUtil.now();
		 AtomicReference<Integer> isAbnormal = new AtomicReference<>(0);
		 overhaulRecordService.remove(Wrappers.<OverhaulRecord>query().lambda()
			 .eq(OverhaulRecord::getOrderId, overhaulOrder.getId()));
		 List<OverhaulRecord> recordList = null;
		 if (Func.isNotEmpty(vo.getOverhaulRecordList())) {
			 recordList = vo.getOverhaulRecordList().stream().map(overhaulRecordVO -> {
				 OverhaulRecord overhaulRecord = Objects.requireNonNull(BeanUtil.copy(overhaulRecordVO, OverhaulRecord.class));
				 DeviceMonitor monitor = CommonCache.getMonitor(overhaulRecord.getMonitorId());
				 overhaulRecord.setOrderId(overhaulOrder.getId()).setEquipmentId(vo.getEquipmentId())
					 .setOverhaulUser(AuthUtil.getUserId()).setOverhaulTime(now)
					 .setMonitorName(monitor.getName());
// 				OverhaulStandard standard = overhaulStandardMapper.selectById(overhaulRecord.getStandardId());
				 OverhaulStandard standard = standardMap.get(overhaulRecord.getStandardId());
				 overhaulRecord.setStandardInfo(JSONUtil.toJsonStr(standard));
				 if (overhaulRecord.getIsAbnormal() == 1) {
					 isAbnormal.set(1);
				 }
				 // 备品备件耗材
				 if (Func.isNotEmpty(overhaulRecordVO.getMaterialList())) {
					 overhaulRecordVO.getMaterialList().forEach(material -> {
						 material.setBizModule(SystemModuleEnum.OVERHAUL_ORDER.getCode());
						 material.setBizNo(Func.toStr(overhaulRecord.getId()));
					 });
					 overhaulRecord.setComponent(JSONUtil.toJsonStr(overhaulRecordVO.getMaterialList()));
				 }
				 return overhaulRecord;
			 }).collect(Collectors.toList());
			 overhaulRecordService.saveBatch(recordList);
		 }
		 overhaulOrder.setIsAbnormal(isAbnormal.get());
		 Boolean needApproval = overhaulOrder.getIsNeedApproval();
		 overhaulOrder.setIsNeedApproval(needApproval);
		 if (needApproval != null && needApproval) {
			 // 工单需确认
			 overhaulOrder.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
		 }
		 overhaulOrder.setSubmitTime(now);
		 // 实际执行人
		 overhaulOrder.setExecuteUser(AuthUtil.getUserId());
		 OverhaulOrderVO order = OverhaulOrderWrapper.build().entityVO(overhaulOrder);
		 if (Func.isNotEmpty(recordList)) {
			 order.setOverhaulRecordList(OverhaulRecordWrapper.build().listVO(recordList));
		 }
		 this.updateById(overhaulOrder);
		 if (!needApproval) {
			 // 审核通过
			 SpringUtil.getBean(OverhaulOrderServiceImpl.class).approvalPass(overhaulOrder);
		 }
		 // 如果之前的状态是驳回,则为再次提交
		 if (OrderStatusEnum.IS_REJECTED.getCode().equals(oldStatus)) {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER,
				 JSON.parseObject(JSON.toJSONString(overhaulOrder)), OrderActionEnum.RE_SUBMIT);
		 } else {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER,
				 JSON.parseObject(JSON.toJSONString(overhaulOrder)), OrderActionEnum.INIT);
		 }
		 return true;
	 }

	 /**
	  * 审核
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirm(OverhaulOrderVO vo) {
		 OverhaulOrder overhaulOrder = this.getById(vo.getId());
		 if (overhaulOrder == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 overhaulOrder.setCheckUser(AuthUtil.getUserId());
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(overhaulOrder.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(overhaulOrder.getStatus())) {
			 throw new ServiceException("当前检修工单已确认！");
		 }
		 // 审核拒绝
		 if (OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(vo.getStatus())) {
			 overhaulOrder.setStatus(OrderStatusEnum.IS_REJECTED.getCode());
			 overhaulOrder.setRejectReason(vo.getRejectReason());
			 boolean ret = this.updateById(overhaulOrder);
			 if (!ret) {
				 throw new ServiceException("工单更新失败");
			 }
			 this.sendMessage(Arrays.asList(overhaulOrder), MessageBizTypeEnum.SIMAS_OVERHAUL_REJECT);

			 OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER, JSON.parseObject(JSON.toJSONString(overhaulOrder)),
				 OrderActionEnum.AUDIT_FAIL, vo.getRejectReason());

		 } else {
			 // 审核通过
			 SpringUtil.getBean(OverhaulOrderServiceImpl.class).approvalPass(overhaulOrder);
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER, JSON.parseObject(JSON.toJSONString(overhaulOrder)), OrderActionEnum.AUDIT_PASS);

		 }
		 // 审核驳回，发送消息提醒


		 return true;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirmBatch(OverhaulOrderVO vo) {
		 if (Func.isNotEmpty(vo.getOrderIds())) {
			 vo.getOrderIds().forEach(id -> {
				 vo.setId(id);
				 this.confirm(vo);
			 });
		 }
		 return true;
	 }

	 /**
	  * 审核通过
	  *
	  * @param overhaulOrder
	  */
	 @Transactional(rollbackFor = Exception.class)
	 public void approvalPass(OverhaulOrder overhaulOrder) {
		 Date now = DateUtil.now();
		 overhaulOrder.setCompleteTime(now);
		 BizLog bizLog = bizLogService.getOne(Wrappers.<BizLog>query().lambda()
			 .eq(BizLog::getBizId, overhaulOrder.getId())
			 .eq(BizLog::getBizStatus, OrderStatusEnum.IS_OVERDUE.getCode()));
		 // 特种设备没有超期状态
		 DeviceAccountVO equipmentAccount = deviceAccountClient.deviceInfoById(overhaulOrder.getEquipmentId()).getData();

		 if (equipmentAccount == null) {
			 throw new ServiceException("设备不存在");
		 }
//		EquipmentAccount equipmentAccount = equipmentAccountService.getById(overhaulOrder.getEquipmentId());
		 boolean isSpacialEquipment = equipmentAccount.getIsSpecial() != null && equipmentAccount.getIsSpecial() == 1;

		 if (!isSpacialEquipment && Func.isNotEmpty(bizLog)) {
			 overhaulOrder.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		 } else {
			 overhaulOrder.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
		 }
		 // 生成内部维修工单
		 // 现场未处理，需要上报，生成待派单状态的维修工单
		 // 默认现场已处理，生成维修待验证状态的维修工单。
		 List<OverhaulRecord> recordList = overhaulRecordService.list(Wrappers.<OverhaulRecord>query().lambda()
			 .eq(OverhaulRecord::getOrderId, overhaulOrder.getId()));
		 recordList.forEach(record -> {
			 if (record.getIsAbnormal() == 1) {
				 RepairVO recordVO = RepairWrapper.build().overhaulEntityVO(record, overhaulOrder);
				 Repair repair = repairService.overhaulToRepair(recordVO);
				 record.setRepairId(repair.getId());
			 }
			 if (Func.isNotEmpty(record.getComponent())) {
				 List<ComponentMaterialVO> materialList = JSONUtil.toList(record.getComponent(), ComponentMaterialVO.class);
				 componentMaterialService.submitBatch(Func.toStr(record.getId()), SystemModuleEnum.OVERHAUL_ORDER.getCode(), materialList);
			 }
		 });
		 // 更新记录(repair_id)
		 overhaulRecordService.updateBatchById(recordList);
		 overhaulOrder.setCheckUser(AuthUtil.getUserId());
		 boolean ret = this.updateById(overhaulOrder);
		 if (!ret) {
			 throw new ServiceException("工单更新失败");
		 }
		 this.sendMessage(Arrays.asList(overhaulOrder), MessageBizTypeEnum.SIMAS_OVERHAUL_PASS);

	 }


	 @Override
	 public List<OverhaulOrderExcel> exportOrder(OverhaulOrderVO vo) {
		 final IPage<OverhaulOrderDTO> page = this.page(new Page<>(1, -1L), vo);
		 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			 return Collections.emptyList();
		 }
		 AtomicReference<Integer> sn = new AtomicReference<>(1);
		 return page.getRecords()
			 .stream()
			 .map(e -> {
				 OverhaulOrderExcel excel = BeanUtil.copy(e, OverhaulOrderExcel.class);
				 excel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 excel.setStartTimeStr(DateUtil.format(e.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
				 excel.setEndTimeStr(DateUtil.format(e.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
				 return excel;
			 }).collect(Collectors.toList());
	 }

	 // @Override
	 // public List<OverhaulOrderExcel> exportOrder(OverhaulOrderVO vo) {
	 // 	Long simasAdmin = null;
	 // 	Long overhaulUser = null;
	 // 	Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
	 // 	if (Func.isNotEmpty(role)) {
	 // 		simasAdmin = role.getId();
	 // 	}
	 // 	role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.REPAIR_USER);
	 // 	if (Func.isNotEmpty(role)) {
	 // 		overhaulUser = role.getId();
	 // 	}
	 // 	// 数据权限
	 // 	String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
	 // 	if (simasAdmin != null && !roleId.contains(Func.toStr(simasAdmin))) {
	 // 		// 只能查看本部门的
	 // 		vo.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
	 // 		if (overhaulUser != null && roleId.contains(Func.toStr(overhaulUser))) {
	 // 			// 检修人员只能查看派发给本人的工单
	 // 			vo.setExecuteUser(AuthUtil.getUserId());
	 // 		}
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getStartDate())) {
	 // 		vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getEndDate())) {
	 // 		vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
	 // 	}
	 // 	QueryWrapper<OverhaulOrder> queryWrapper = Wrappers.query();
	 // 	if (Func.isNotEmpty(vo.getExecuteDept())) {
	 // 		queryWrapper.lambda().eq(OverhaulOrder::getExecuteDept, Func.toLongList(AuthUtil.getDeptId()).get(0));
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getExecuteUser())) {
	 // 		queryWrapper.lambda().and(wrapper -> {
	 // 			wrapper.isNull(OverhaulOrder::getExecuteUser).or().eq(OverhaulOrder::getExecuteUser, AuthUtil.getUserId());
	 // 		});
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getStatus())) {
	 // 		queryWrapper.lambda().eq(OverhaulOrder::getStatus, vo.getStatus());
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getNo())) {
	 // 		queryWrapper.lambda().like(OverhaulOrder::getNo, vo.getNo());
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getOrderName())) {
	 // 		queryWrapper.lambda().like(OverhaulOrder::getPlanInfo, vo.getOrderName());
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getStartDate())) {
	 // 		queryWrapper.lambda().ge(OverhaulOrder::getStartTime, vo.getStartDate());
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getEndDate())) {
	 // 		queryWrapper.lambda().le(OverhaulOrder::getStartTime, vo.getEndDate());
	 // 	}
	 // 	queryWrapper.lambda().orderByDesc(OverhaulOrder::getCreateTime);
	 // 	List<OverhaulOrder> list = baseMapper.selectList(queryWrapper);
	 // 	if (Func.isNotEmpty(list)) {
	 // 		AtomicReference<Integer> sn = new AtomicReference<>(1);
	 // 		return list.stream().map(order -> {
	 // 			if (Func.isEmpty(order.getPlanInfo())) {
	 // 				OverhaulPlan plan = overhaulPlanMapper.selectById(order.getPlanId());
	 // 				order.setPlanInfo(JSONUtil.toJsonStr(plan));
	 // 			}
	 // 			OverhaulOrderDTO dto = OverhaulOrderWrapper.build().entityDTO(order);
	 // 			dto.setEquipmentName(equipmentAccountService.getById(dto.getEquipmentId()).getName());
	 // 			OverhaulOrderExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(dto, OverhaulOrderExcel.class));
	 // 			orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
	 // 			List<OverhaulRecord> recordList = overhaulRecordService.list(Wrappers.<OverhaulRecord>query().lambda()
	 // 				.eq(OverhaulRecord::getOrderId, order.getId()));
	 // 			if (Func.isNotEmpty(recordList)) {
	 // 				List<Integer> abnormalList = recordList.stream().map(OverhaulRecord::getIsAbnormal).collect(Collectors.toList());
	 // 				if (abnormalList.contains(Func.toInt(StringPool.ONE))) {
	 // 					orderExcel.setOverhaulResult("异常");
	 // 				} else {
	 // 					orderExcel.setOverhaulResult("正常");
	 // 				}
	 // 			}
	 // 			orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
	 // 			orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
	 // 			return orderExcel;
	 // 		}).collect(Collectors.toList());
	 // 	}
	 // 	return null;
	 // }

	 @Override
	 public IPage<OverhaulOrderDTO> timeoutPage(IPage<OverhaulOrderDTO> page, OverhaulOrderVO overhaulOrder) {
		 overhaulOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.OVERHAUL.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 overhaulOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 overhaulOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			 .setExecuteUser(AuthUtil.getUserId());
		 List<OverhaulOrder> list = baseMapper.timeoutPage(page, overhaulOrder);
		 List<OverhaulOrderDTO> resultList = OverhaulOrderWrapper.build().listDTO(list);
		 resultList.forEach(dto -> {
			 DeviceAccountVO equipmentAccount = deviceAccountClient.deviceInfoById(dto.getEquipmentId()).getData();

			 dto.setEquipmentName(equipmentAccount.getName());
		 });
		 return page.setRecords(resultList);
	 }

	 @Override
	 public List<OverhaulOrderDTO> overhaulOrderStatistics(Integer queryDate) {
		 return OverhaulOrderWrapper.build().listDTO(baseMapper.overhaulOrderStatistics(queryDate));
	 }

	 @Override
	 public Integer expireSoonCount() {
		 OverhaulOrderVO overhaulOrder = new OverhaulOrderVO();
		 overhaulOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.OVERHAUL.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 overhaulOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 overhaulOrder.setExecuteDept(Func.firstLong(AuthUtil.getDeptId()))
			 .setExecuteUser(AuthUtil.getUserId());
		 return baseMapper.expireSoonCount(overhaulOrder);
	 }

	 @Override
	 public void sendMessage(List<OverhaulOrder> list, MessageBizTypeEnum messageBizType) {
		 log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		 list.forEach(order -> {
			 // 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
			 ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			 MessageVo messageVo = new MessageVo();
			 messageVo.setAppKey("SIMAS");
			 messageVo.setSender("SIMAS");
			 messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			 messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			 messageVo.setTitle(messageBizType.getMessage());
			 messageVo.setBizType(messageBizType.getCode());
			 messageVo.setBizId(order.getNo());
			 messageVo.setContent(JSONUtil.toJsonStr(order));
			 messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			 if (Func.isNotEmpty(order.getExecuteUser())) {
				 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				 userVo.setId(order.getExecuteUser());
				 receiverInfoVo.setUserList(Arrays.asList(userVo));
			 } else {
				 // 部门下具有保养员角色的人
				 R<RoleVO> roleResult = sysClient.getRoleByAlias(order.getTenantId(), SimasConstant.SimasRole.REPAIR_USER);
				 if (roleResult.isSuccess() && Func.isNotEmpty(roleResult.getData())) {
					 R<List<User>> userListResult = userClient.userListByDeptRole(order.getExecuteDept(),
						 roleResult.getData().getId());
					 if (userListResult.isSuccess() && Func.isNotEmpty(userListResult.getData())) {
						 receiverInfoVo.setUserList(userListResult.getData().stream().map(user -> {
							 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
							 userVo.setId(user.getId());
							 return userVo;
						 }).collect(Collectors.toList()));
					 }
				 }
			 }
			 messageVo.setReceiverInfoVo(receiverInfoVo);
			 messageClient.pushMessage(messageVo);
		 });
		 log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	 }

	 @Override
	 public IPage<OverhaulOrderDTO> statisticalReport(IPage<OverhaulOrderDTO> page, StatisticSearchVO search) {
		 List<OverhaulOrder> list = baseMapper.statisticalReport(page, search);
		 if (Func.isNotEmpty(list)) {
			 List<OverhaulOrderDTO> orderList = OverhaulOrderWrapper.build().listDTO(list);
			 orderList.forEach(dto -> {
				 DeviceAccountVO equipmentAccount = deviceAccountClient.deviceInfoById(dto.getEquipmentId()).getData();

//				EquipmentAccount equipmentAccount = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda()
// 					.eq(EquipmentAccount::getId, dto.getEquipmentId())
// 					.select(EquipmentAccount::getName, EquipmentAccount::getCategoryId));
				 DeviceCategory equipmentCategory = CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId());
				 dto.setEquipmentName(equipmentAccount.getName())
					 .setEquipmentCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
				 if (Func.isNotEmpty(dto.getIsAbnormal())) {
					 if (Func.equals(Func.toInt(StringPool.ONE), dto.getIsAbnormal())) {
						 dto.setOverhaulResult("异常");
					 } else {
						 dto.setOverhaulResult("正常");
					 }
				 }
			 });
			 return page.setRecords(orderList);
		 }
		 return page.setRecords(null);
	 }
	 //
	 //	@Override
	 //	public List<OverhaulOrderStatisticsExcel> exportStatisticalReport(StatisticSearchVO vo) {
	 //		QueryWrapper<OverhaulOrder> queryWrapper = Wrappers.query();
	 //		if(vo.getQueryDate() == 1){
	 //			queryWrapper.lambda().ge(OverhaulOrder::getCreateTime, LocalDate.now().minusDays(30));
	 //		}
	 //		if(vo.getQueryDate() == 2){
	 //			queryWrapper.lambda().ge(OverhaulOrder::getCreateTime, LocalDate.now().minusDays(7));
	 //		}
	 //		if(vo.getQueryDate() == 3){
	 //			queryWrapper.lambda().ge(OverhaulOrder::getCreateTime,
	 //					DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_START_TIME)
	 //				.le(OverhaulOrder::getCreateTime,
	 //					DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME);
	 //		}
	 //		if (Func.isNotEmpty(vo.getStartDate())) {
	 //			queryWrapper.lambda().ge(OverhaulOrder::getStartTime,vo.getStartDate() + DateUtils.DAY_START_TIME);
	 //		}
	 //		if (Func.isNotEmpty(vo.getEndDate())) {
	 //			queryWrapper.lambda().le(OverhaulOrder::getStartTime,vo.getEndDate() + DateUtils.DAY_END_TIME);
	 //		}
	 //		List<Long> deptIdList = new ArrayList<>();
	 //		if(Func.isNotEmpty(vo.getDeptId())){
	 //			List<Long> deptIds = Func.toLongList(vo.getDeptId());
	 //			deptIdList.addAll(deptIds);
	 //			Func.toLongList(vo.getDeptId()).forEach(deptId -> {
	 //				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
	 //				if (Func.isNotEmpty(deptChildIds)) {
	 //					deptIdList.addAll(deptChildIds);
	 //				}
	 //			});
	 //			queryWrapper.lambda().in(OverhaulOrder::getExecuteDept,
	 //				deptIdList.stream().distinct().collect(Collectors.toList()));
	 //		}
	 //		List<OverhaulOrder> list = this.list(queryWrapper);
	 //		if(Func.isNotEmpty(list)){
	 //			List<OverhaulOrderDTO> orderList = OverhaulOrderWrapper.build().listDTO(list);
	 //			AtomicReference<Integer> sn = new AtomicReference<>(1);
	 //			return orderList.stream().map(order -> {
	 //				OverhaulOrderStatisticsExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, OverhaulOrderStatisticsExcel.class));
	 //				orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
	 //				EquipmentAccount equipmentAccount = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda()
	 //					.eq(EquipmentAccount::getId, order.getEquipmentId())
	 //					.select(EquipmentAccount::getName, EquipmentAccount::getCategoryId));
	 //				orderExcel.setEquipmentName(equipmentAccount.getName())
	 //					.setEquipmentCategoryName(SimasCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
	 //				if(Func.isNotEmpty(order.getIsAbnormal())){
	 //					if(Func.equals(Func.toInt(StringPool.ONE), order.getIsAbnormal())){
	 //						order.setOverhaulResult("异常");
	 //					} else {
	 //						order.setOverhaulResult("正常");
	 //					}
	 //				}
	 //				OverhaulPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), OverhaulPlanDTO.class);
	 //				switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
	 //					case DAY:
	 //						orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm"));
	 //						orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm"));
	 //						break;
	 //					case WEEK:
	 //					case MONTH:
	 //						orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd"));
	 //						orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd"));
	 //						break;
	 //					default:
	 //				}
	 //				if(Func.isNotEmpty(order.getCompleteTime())){
	 //					orderExcel.setCompleteTimeStr(DateUtil.format(order.getCompleteTime(), "yyyy-MM-dd HH:mm:ss"));
	 //				}
	 //				return orderExcel;
	 //			}).collect(Collectors.toList());
	 //		}
	 //		return null;
	 //	}

	 @Override
	 public List<OverhaulOrderDTO> queryList(OverhaulOrderVO vo) {
		 return baseMapper.queryList(vo);
	 }

	 @Override
	 public List<BigScreenMessageDTO> specialNoFinishList(String tenantId) {
		 return baseMapper.specialNoFinishList(tenantId);
	 }

	 @Override
	 public List<BigScreenMessageDTO> noSpecialOverdueList(String tenantId) {
		 return baseMapper.noSpecialOverdueList(tenantId);

	 }

	 @Override
	 public Integer handleOverhaulCount(OverhaulOrderVO overhaulOrder) {
		 return baseMapper.handleOverhaulCount(overhaulOrder);
	 }

	 @Override
	 public List<OverhaulOrder> listBy(Long executeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus) {
		 return this.lambdaQuery()
			 .eq(ObjectUtil.isNotEmpty(executeDeptId), OverhaulOrder::getExecuteDept, executeDeptId)
			 .in(ObjectUtil.isNotEmpty(executeUserIds), OverhaulOrder::getExecuteUser, executeUserIds)
			 .in(ObjectUtil.isNotEmpty(equipmentIds), OverhaulOrder::getEquipmentId, equipmentIds)
			 .ge(ObjectUtil.isNotEmpty(startDateTime), OverhaulOrder::getStartTime, startDateTime)
			 .lt(ObjectUtil.isNotEmpty(endDateTime), OverhaulOrder::getStartTime, endDateTime)
			 .ne(ObjectUtil.isNotEmpty(neStatus), OverhaulOrder::getStatus, neStatus)
			 .list();
	 }


 }
