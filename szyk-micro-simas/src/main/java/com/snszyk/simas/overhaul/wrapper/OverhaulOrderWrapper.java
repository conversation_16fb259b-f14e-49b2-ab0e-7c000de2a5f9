/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.OverhaulPlan;
import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备检修工单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class OverhaulOrderWrapper extends BaseEntityWrapper<OverhaulOrder, OverhaulOrderVO> {

	public static OverhaulOrderWrapper build() {
		return new OverhaulOrderWrapper();
	}

	@Override
	public OverhaulOrderVO entityVO(OverhaulOrder overhaulOrder) {
		OverhaulOrderVO overhaulOrderVO = Objects.requireNonNull(BeanUtil.copy(overhaulOrder, OverhaulOrderVO.class));
		return overhaulOrderVO;
	}

	public OverhaulOrderDTO entityDTO(OverhaulOrder overhaulOrder) {
		OverhaulOrderDTO overhaulOrderDTO = Objects.requireNonNull(BeanUtil.copy(overhaulOrder, OverhaulOrderDTO.class));
		// 负责部门
		if (Func.isNotEmpty(overhaulOrder.getExecuteDept())) {
			Dept dept = SysCache.getDept(overhaulOrder.getExecuteDept());
			if (Func.isNotEmpty(dept)) {
				overhaulOrderDTO.setExecuteDeptName(dept.getDeptName());
			}
		}
		// 检修人员
		if (Func.isNotEmpty(overhaulOrder.getExecuteUser())) {
			User user = UserCache.getUser(overhaulOrder.getExecuteUser());
			if (Func.isNotEmpty(user)) {
				overhaulOrderDTO.setExecuteUserName(user.getRealName());
			}
		}
		// 审核人员
		if (Func.isNotEmpty(overhaulOrder.getCheckUser())) {
			User user = UserCache.getUser(overhaulOrder.getCheckUser());
			if (Func.isNotEmpty(user)) {
				overhaulOrderDTO.setCheckUserName(user.getRealName());
				overhaulOrderDTO.setApprovalUserName(user.getRealName());
			}
		}

		// 状态
		overhaulOrderDTO.setStatusName(OrderStatusEnum.getByCode(overhaulOrder.getStatus()).getName());
		if (Func.isNotEmpty(overhaulOrder.getPlanInfo())) {
			OverhaulPlan plan = JSONUtil.toBean(overhaulOrder.getPlanInfo(), OverhaulPlan.class);
			// 计划周期
			overhaulOrderDTO.setCycleTypeName(PlanCycleEnum.getByCode(plan.getCycleType()).getName());
			// 开始、结束时间
			overhaulOrderDTO.setStartTimeStr(DateUtil.format(overhaulOrder.getStartTime(), "yyyy-MM-dd"))
				.setEndTimeStr(DateUtil.format(overhaulOrder.getEndTime(), "yyyy-MM-dd"));
		}

		//createUserName
		if (Func.isNotEmpty(overhaulOrder.getCreateUser())) {
			User user = UserCache.getUser(overhaulOrder.getCreateUser());
			if (Func.isNotEmpty(user)) {
				overhaulOrderDTO.setCreateUserName(user.getRealName());
			}
		}
		//updateUserName
		if (Func.isNotEmpty(overhaulOrder.getUpdateUser())) {
			User user = UserCache.getUser(overhaulOrder.getUpdateUser());
			if (Func.isNotEmpty(user)) {
				overhaulOrderDTO.setUpdateUserName(user.getRealName());
			}
		}
		return overhaulOrderDTO;
	}

	public List<OverhaulOrderDTO> listDTO(List<OverhaulOrder> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
