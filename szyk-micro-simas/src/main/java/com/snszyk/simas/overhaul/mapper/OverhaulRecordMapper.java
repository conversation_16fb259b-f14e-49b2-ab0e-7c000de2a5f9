/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.overhaul.entity.OverhaulRecord;
import com.snszyk.simas.overhaul.vo.OverhaulRecordVO;

import java.util.List;

/**
 * 设备保养记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface OverhaulRecordMapper extends BaseMapper<OverhaulRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param overhaulRecord
	 * @return
	 */
	List<OverhaulRecordVO> selectOverhaulRecordPage(IPage page, OverhaulRecordVO overhaulRecord);

}
