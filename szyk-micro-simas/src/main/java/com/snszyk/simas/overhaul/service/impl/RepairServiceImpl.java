 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.service.impl;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.json.JSONUtil;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.dto.MeasureUnitDto;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.feign.ICommonClient;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.equipment.vo.DeviceMonitorVO;
 import com.snszyk.common.supplier.dto.SupplierDto;
 import com.snszyk.common.supplier.feign.ISupplierClient;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.*;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.message.enums.MessageTypeEnum;
 import com.snszyk.message.enums.ReceiverTypeEnum;
 import com.snszyk.message.enums.YesNoEnum;
 import com.snszyk.message.feign.IMessageClient;
 import com.snszyk.message.vo.MessageVo;
 import com.snszyk.message.vo.ReceiverInfoVo;
 import com.snszyk.resource.entity.Attach;
 import com.snszyk.resource.feign.IAttachClient;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.entity.BizLog;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.excel.RepairExternalExcel;
 import com.snszyk.simas.common.excel.RepairInternalExcel;
 import com.snszyk.simas.common.excel.RepairStatisticsExcel;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.RepairLogProcessor;
 import com.snszyk.simas.common.service.IBizLogService;
 import com.snszyk.simas.common.service.IComponentMaterialService;
 import com.snszyk.simas.common.service.logic.EquipmentFaultRepairMessageRuleLogicService;
 import com.snszyk.simas.common.util.ApprovalUtil;
 import com.snszyk.simas.common.vo.BizLogVO;
 import com.snszyk.simas.common.vo.EquipmentRepairVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.common.wrapper.BizLogWrapper;
 import com.snszyk.simas.common.wrapper.ComponentMaterialWrapper;
 import com.snszyk.simas.fault.dto.FaultDefectDTO;
 import com.snszyk.simas.fault.entity.FaultDefect;
 import com.snszyk.simas.fault.enums.FaultDefectTypeEnum;
 import com.snszyk.simas.fault.enums.FaultSourceEnum;
 import com.snszyk.simas.fault.mapper.FaultDefectMapper;
 import com.snszyk.simas.fault.service.IFaultDefectCaseService;
 import com.snszyk.simas.fault.vo.FaultDefectAbnormalVO;
 import com.snszyk.simas.fault.wrapper.FaultDefectWrapper;
 import com.snszyk.simas.inspect.entity.InspectRecord;
 import com.snszyk.simas.inspect.mapper.InspectRecordMapper;
 import com.snszyk.simas.inspect.mapper.InspectStandardMapper;
 import com.snszyk.simas.lubricate.entity.LubricateOrder;
 import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
 import com.snszyk.simas.maintain.entity.MaintainRecord;
 import com.snszyk.simas.maintain.mapper.MaintainRecordMapper;
 import com.snszyk.simas.maintain.mapper.MaintainStandardMapper;
 import com.snszyk.simas.overhaul.dto.RepairDTO;
 import com.snszyk.simas.overhaul.entity.OverhaulRecord;
 import com.snszyk.simas.overhaul.entity.Repair;
 import com.snszyk.simas.overhaul.entity.RepairRecord;
 import com.snszyk.simas.overhaul.enums.RepairActionEnum;
 import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
 import com.snszyk.simas.overhaul.enums.RepairSourceEnum;
 import com.snszyk.simas.overhaul.mapper.OverhaulRecordMapper;
 import com.snszyk.simas.overhaul.mapper.RepairMapper;
 import com.snszyk.simas.overhaul.service.IRepairRecordService;
 import com.snszyk.simas.overhaul.service.IRepairService;
 import com.snszyk.simas.overhaul.vo.RepairRecordVO;
 import com.snszyk.simas.overhaul.vo.RepairVO;
 import com.snszyk.simas.overhaul.wrapper.RepairRecordWrapper;
 import com.snszyk.simas.overhaul.wrapper.RepairWrapper;
 import com.snszyk.simas.spare.entity.ComponentMaterial;
 import com.snszyk.simas.spare.mapper.SparePartsDictMapper;
 import com.snszyk.simas.spare.vo.ComponentMaterialVO;
 import com.snszyk.system.cache.DictBizCache;
 import com.snszyk.system.cache.SysCache;
 import com.snszyk.system.enums.DictBizEnum;
 import com.snszyk.user.cache.UserCache;
 import com.snszyk.user.entity.User;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.math.BigDecimal;
 import java.math.RoundingMode;
 import java.time.LocalDate;
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备维修单表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-27
  */
 @Slf4j
 @AllArgsConstructor
 @Service
 public class RepairServiceImpl extends BaseServiceImpl<RepairMapper, Repair> implements IRepairService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final ICommonClient commonClient;
	 private final ISupplierClient supplierClient;
	 private final IRepairRecordService repairRecordService;
	 private final InspectStandardMapper inspectStandardMapper;
	 private final InspectRecordMapper inspectRecordMapper;
	 private final MaintainStandardMapper maintainStandardMapper;
	 private final MaintainRecordMapper maintainRecordMapper;
	 private final OverhaulRecordMapper overhaulRecordMapper;
	 private final LubricateOrderMapper lubricateOrderMapper;
	 private final IComponentMaterialService componentMaterialService;
	 private final SparePartsDictMapper sparePartsDictMapper;
	 private final RepairMapper repairMapper;
	 private final FaultDefectMapper faultDefectMapper;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	 private final IFaultDefectCaseService faultDefectCaseService;
	 private final EquipmentFaultRepairMessageRuleLogicService equipmentFaultRepairMessageRuleLogicService;
	 private final IBizLogService bizLogService;
	 private final IMessageClient messageClient;
	 private final IAttachClient attachClient;

	 @Override
	 public IPage<RepairDTO> page(IPage<RepairDTO> page, RepairVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<RepairDTO> list;
		 if (Func.isNotEmpty(vo.getBizType())) {
			 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(vo.getBizType())) {
				 list = baseMapper.internalPage(page, vo);
			 } else {
				 list = baseMapper.externalPage(page, vo);
			 }
		 } else {
			 vo.setBizType(null);
			 list = baseMapper.internalPage(page, vo);
		 }
		 if (Func.isNotEmpty(list)) {
			 // 获取部位名称Map
			 Map<Long, String> monitorNameMap = this.getMonitorNameMap(ListUtil.map(list, RepairDTO::getMonitorId));
			 list.forEach(dto -> {
				 if (monitorNameMap.containsKey(dto.getMonitorId())) {
					 dto.setMonitorName(monitorNameMap.get(dto.getMonitorId()));
				 }
				 if (Func.isNotEmpty(dto.getReportUser())) {
					 User reportUser = UserCache.getUser(dto.getReportUser());
					 if (Func.isNotEmpty(reportUser)) {
						 dto.setReportUserName(reportUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getReceiveUser())) {
					 User receiveUser = UserCache.getUser(dto.getReceiveUser());
					 if (Func.isNotEmpty(receiveUser)) {
						 dto.setReceiveUserName(receiveUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getFollowUser())) {
					 User followUser = UserCache.getUser(dto.getFollowUser());
					 if (Func.isNotEmpty(followUser)) {
						 dto.setFollowUserName(followUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getSupplierId())) {
					 R<SupplierDto> supplierR = supplierClient.getById(dto.getSupplierId());
					 if (supplierR.isSuccess() && Func.isNotEmpty(supplierR.getData())) {
						 dto.setSupplierName(supplierR.getData().getName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getCreateUser())) {
					 User createUser = UserCache.getUser(dto.getCreateUser());
					 if (Func.isNotEmpty(createUser)) {
						 dto.setCreateUserName(createUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getUpdateUser())) {
					 User updateUser = UserCache.getUser(dto.getUpdateUser());
					 if (Func.isNotEmpty(updateUser)) {
						 dto.setUpdateUserName(updateUser.getRealName());
					 }
				 }
				 if (Func.isNotEmpty(dto.getApprovalUser())) {
					 User approvalUser = UserCache.getUser(dto.getApprovalUser());
					 if (Func.isNotEmpty(approvalUser)) {
						 dto.setApprovalUserName(approvalUser.getRealName());
					 }
				 }
				 // 处理时间
				 dto.setHandleTime(dto.getSubmitTime());
				 // 驳回状态-驳回原因
				 if (OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(dto.getStatus())) {
					 BizLog bizLog = bizLogService.getOne(Wrappers.<BizLog>query().lambda()
						 .eq(BizLog::getBizId, dto.getId()).eq(BizLog::getBizStatus, OrderStatusEnum.IS_REJECTED.getCode())
						 .orderByDesc(BizLog::getOperateTime).last(" limit 1"));
					 if (Func.isNotEmpty(bizLog)) {
						 RepairVO repairVO = JSONUtil.toBean(bizLog.getBizInfo(), RepairVO.class);
						 RepairRecordVO repairRecord = repairVO.getRepairRecord();
						 if (repairRecord != null) {
							 dto.setRejectReason(repairRecord.getVerifyComment());
						 }
					 }
				 }
				 // 工单来源
				 Optional.ofNullable(RepairSourceEnum.getByCode(dto.getSource()))
					 .ifPresent(source -> dto.setSourceName(source.getName()));
				 // 报修类型
				 Optional.ofNullable(FaultDefectTypeEnum.getByCode(dto.getRepairType()))
					 .ifPresent(repairType -> dto.setRepairTypeName(repairType.getName()));
				 dto.setBizTypeName(RepairBizTypeEnum.getByCode(dto.getBizType()).getName())
					 .setStatusName(OrderStatusEnum.getByCode(dto.getStatus()).getName());
				 // 备品备件
				 if (Func.isNotEmpty(dto.getComponent())) {
					 List<ComponentMaterialVO> repairComponentList = JSONUtil.toList(dto.getComponent(), ComponentMaterialVO.class);
					 repairComponentList.forEach(componentMaterialVO -> {
						 R<MeasureUnitDto> measureUnitResult = commonClient.getMeasureUnit(componentMaterialVO.getMeasureUnitId());
						 if (measureUnitResult.isSuccess() && Func.isNotEmpty(measureUnitResult.getData())) {
							 // 精度
							 componentMaterialVO.setMeasureUnitPrecision(measureUnitResult.getData().getAccuracy());
						 }
					 });
					 dto.setRepairComponentList(repairComponentList);
				 }
			 });
		 }
		 return page.setRecords(list);
	 }

	 /**
	  * 部位名称
	  *
	  * @param monitorIds
	  * @return java.util.Map<java.lang.Long, java.lang.String>
	  * <AUTHOR>
	  * @date 2025/3/23 8:52
	  */
	 private Map<Long, String> getMonitorNameMap(List<Long> monitorIds) {
		 if (Func.isEmpty(monitorIds)) {
			 return MapUtil.empty();
		 }
		 // 部位信息
		 DeviceMonitorVO deviceMonitorVO = new DeviceMonitorVO();
		 deviceMonitorVO.setMonitorIds(monitorIds.stream().map(String::valueOf).collect(Collectors.joining(StringPool.COMMA)));
		 R<List<DeviceMonitor>> deviceMonitorListResult = commonClient.deviceMonitorByParams(deviceMonitorVO);
		 if (!deviceMonitorListResult.isSuccess()) {
			 throw new ServiceException("查询部位信息错误！");
		 }
		 if (Func.isEmpty(deviceMonitorListResult.getData())) {
			 return MapUtil.empty();
		 }
		 final List<DeviceMonitor> equipmentMonitors = deviceMonitorListResult.getData();
		 return ListUtil.toMap(equipmentMonitors, DeviceMonitor::getId, DeviceMonitor::getName);
	 }

	 @Override
	 public RepairDTO detail(String no) {
		 Repair repair = this.getOne(Wrappers.<Repair>query().lambda().eq(Repair::getNo, no));
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 RepairDTO detail = RepairWrapper.build().entityDTO(repair);
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (deviceAccountResult.isSuccess() && Func.isNotEmpty(deviceAccountResult.getData())) {
			 detail.setEquipmentAccount(deviceAccountResult.getData());
		 }
		 DeviceMonitor deviceMonitor = CommonCache.getMonitor(repair.getMonitorId());
		 if (Func.isNotEmpty(deviceMonitor)) {
			 detail.setEquipmentMonitor(Objects.requireNonNull(BeanUtil.copy(deviceMonitor, DeviceMonitorVO.class)));
		 }
		 if (Func.isNotEmpty(repair.getAttachId())) {
			 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(repair.getAttachId()));
			 if (attachListR.isSuccess()) {
				 detail.setAttachList(attachListR.getData());
			 }
		 }
		 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda()
			 .eq(RepairRecord::getRepairId, repair.getId()));
		 if (Func.isNotEmpty(repairRecord)) {
			 RepairRecordVO repairRecordVO = RepairRecordWrapper.build().entityVO(repairRecord);
			 if (Func.isNotEmpty(repairRecord.getComponent())) {
				 repairRecordVO.setMaterialList(JSONUtil.toList(repairRecord.getComponent(), ComponentMaterialVO.class));
			 }
			 if (Func.isNotEmpty(repairRecord.getAttachId())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(repairRecordVO.getAttachId()));
				 if (attachListR.isSuccess()) {
					 repairRecordVO.setAttachList(attachListR.getData());
				 }
			 }
			 detail.setRepairRecord(repairRecordVO);
		 }
		 if (Func.isNotEmpty(repair.getSupplierId())) {
			 R<SupplierDto> supplierR = supplierClient.getById(repair.getSupplierId());
			 if (supplierR.isSuccess() && Func.isNotEmpty(supplierR.getData())) {
				 detail.setSupplierName(supplierR.getData().getName());
			 }
		 }
		 // 备品备件
		 if (Func.isNotEmpty(detail.getComponent())) {
			 List<ComponentMaterialVO> repairComponentList = JSONUtil.toList(detail.getComponent(), ComponentMaterialVO.class);
			 repairComponentList.forEach(componentMaterialVO -> {
				 R<MeasureUnitDto> measureUnitResult = commonClient.getMeasureUnit(componentMaterialVO.getMeasureUnitId());
				 if (measureUnitResult.isSuccess() && Func.isNotEmpty(measureUnitResult.getData())) {
					 // 精度
					 componentMaterialVO.setMeasureUnitPrecision(measureUnitResult.getData().getAccuracy());
				 }
			 });
			 detail.setRepairComponentList(repairComponentList);
		 }
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())) {
			 List<ComponentMaterial> materialList = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda()
				 .eq(ComponentMaterial::getBizNo, detail.getNo()).orderByAsc(ComponentMaterial::getSort));
			 if (Func.isNotEmpty(materialList)) {
				 detail.setRepairComponentList(ComponentMaterialWrapper.build().listVO(materialList));
			 }
		 }
		 return detail;
	 }

	 @Override
	 public RepairDTO view(String no) {
		 Repair repair = this.getOne(Wrappers.<Repair>query().lambda().eq(Repair::getNo, no));
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 // 维修单信息
		 RepairDTO detail = RepairWrapper.build().entityDTO(repair);
		 // 设备信息
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (deviceAccountResult.isSuccess() && Func.isNotEmpty(deviceAccountResult.getData())) {
			 detail.setEquipmentAccount(deviceAccountResult.getData());
		 }
		 // 部位信息
		 if (Func.isNotEmpty(repair.getMonitorId())) {
			 final DeviceMonitor deviceMonitor = CommonCache.getMonitor(repair.getMonitorId());
			 if (Func.isNotEmpty(deviceMonitor)) {
				 detail.setEquipmentMonitor(Objects.requireNonNull(BeanUtil.copy(deviceMonitor, DeviceMonitorVO.class)));
				 detail.setMonitorName(deviceMonitor.getName());
			 }
		 }
		 // 缺陷信息
		 FaultDefect faultDefect = faultDefectMapper.selectOne(Wrappers.<FaultDefect>query().lambda()
			 .eq(FaultDefect::getRepairNo, repair.getNo()));
		 if (Func.isNotEmpty(faultDefect)) {
			 FaultDefectDTO faultDefectDTO = FaultDefectWrapper.build().entityDTO(faultDefect);
			 String abnormalAttachIds;
			 faultDefectDTO.setAttachList(new ArrayList<>());
			 switch (FaultSourceEnum.getByCode(faultDefect.getSource())) {
				 case INSPECT:
					 Optional.ofNullable(inspectStandardMapper.selectById(faultDefect.getStandardId()))
						 .ifPresent(inspectStandard -> faultDefectDTO.setStandardName(inspectStandard.getStandard()));
					 // 点检上报的异常图片
					 InspectRecord inspectRecord = inspectRecordMapper.selectOne(Wrappers.<InspectRecord>query().lambda()
						 .eq(InspectRecord::getOrderId, faultDefect.getBizId()).eq(InspectRecord::getStandardId, faultDefect.getStandardId())
						 .orderByDesc(InspectRecord::getInspectTime).last(" limit 1"));
					 abnormalAttachIds = inspectRecord.getAbnormalImage();
					 break;
				 case MAINTAIN:
					 Optional.ofNullable(maintainStandardMapper.selectById(faultDefect.getStandardId()))
						 .ifPresent(maintainStandard -> faultDefectDTO.setStandardName(maintainStandard.getStandard()));
					 // 保养上报的异常图片
					 MaintainRecord maintainRecord = maintainRecordMapper.selectOne(Wrappers.<MaintainRecord>query().lambda()
						 .eq(MaintainRecord::getOrderId, faultDefect.getBizId()).eq(MaintainRecord::getStandardId, faultDefect.getStandardId())
						 .orderByDesc(MaintainRecord::getMaintainTime).last(" limit 1"));
					 abnormalAttachIds = maintainRecord.getAbnormalImage();
					 break;
				 case LUBRICATE:
					 // 润滑上报的异常图片
					 LubricateOrder lubricateOrder = lubricateOrderMapper.selectById(faultDefect.getBizId());
					 abnormalAttachIds = lubricateOrder.getAttachIds();
					 break;
				 default:
					 FaultDefectAbnormalVO faultDefectAbnormal = JSONUtil.toBean(faultDefect.getAbnormalInfo(), FaultDefectAbnormalVO.class);
					 abnormalAttachIds = faultDefectAbnormal.getAbnormalImage();
			 }
			 if (Func.isNotEmpty(abnormalAttachIds)) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(abnormalAttachIds));
				 if (attachListR.isSuccess()) {
					 faultDefectDTO.getAttachList().addAll(attachListR.getData());
				 }
			 }
			 if (Func.isNotEmpty(faultDefect.getAttachId())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(faultDefect.getAttachId()));
				 if (attachListR.isSuccess()) {
					 faultDefectDTO.getAttachList().addAll(attachListR.getData());
				 }
			 }
			 detail.setFaultDefect(faultDefectDTO);
			 detail.setSourceNo(faultDefect.getNo());
		 }
		 // 检修
		 if (RepairSourceEnum.PLANNING_OVERHAUL == RepairSourceEnum.getByCode(detail.getSource())) {
			 FaultDefectDTO faultDefectDTO = new FaultDefectDTO();
			 // 检修记录
			 OverhaulRecord overhaulRecord = overhaulRecordMapper.selectOne(Wrappers.<OverhaulRecord>query().lambda()
				 .eq(OverhaulRecord::getRepairId, detail.getId()));
			 if (Func.isNotEmpty(overhaulRecord)) {
				 faultDefectDTO.setName(overhaulRecord.getFaultName()).setType(overhaulRecord.getFaultType())
					 .setLevel(overhaulRecord.getFaultLevel()).setComment(overhaulRecord.getFaultRemark());
				 Optional.ofNullable(inspectStandardMapper.selectById(overhaulRecord.getStandardId()))
					 .ifPresent(overhaulStandard -> faultDefectDTO.setStandardName(overhaulStandard.getStandard()));
				 if (Func.isNotEmpty(overhaulRecord.getAttachId())) {
					 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(overhaulRecord.getAttachId()));
					 if (attachListR.isSuccess()) {
						 faultDefectDTO.setAttachList(attachListR.getData());
					 }
				 }
			 }
			 detail.setFaultDefect(faultDefectDTO);
		 }
		 // 自定义新增
		 if (RepairSourceEnum.MANUAL_ADD == RepairSourceEnum.getByCode(detail.getSource())) {
			 FaultDefectDTO faultDefectDTO = new FaultDefectDTO();
			 faultDefectDTO.setComment(detail.getProblemComment());
			 // 维修单附件
			 if (Func.isNotEmpty(detail.getAttachId())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(repair.getAttachId()));
				 if (attachListR.isSuccess()) {
					 faultDefectDTO.setAttachList(attachListR.getData());
				 }
			 }
			 detail.setFaultDefect(faultDefectDTO);
		 }
		 // 处理时间
		 detail.setHandleTime(detail.getSubmitTime());
		 // 业务日志列表
		 List<BizLog> bizLogList = bizLogService.list(Wrappers.<BizLog>query().lambda()
			 .eq(BizLog::getBizId, repair.getId())
			 .ne(BizLog::getBizStatus, OrderStatusEnum.IS_OVERDUE).orderByDesc(BizLog::getId));
		 detail.setBizLogList(BizLogWrapper.build().listVO(bizLogList));
		 // 维修记录
		 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda().eq(RepairRecord::getRepairId, repair.getId()));
		 if (Func.isNotEmpty(repairRecord)) {
			 RepairRecordVO repairRecordVO = RepairRecordWrapper.build().entityVO(repairRecord);
			 if (Func.isNotEmpty(repairRecord.getComponent())) {
				 repairRecordVO.setMaterialList(JSONUtil.toList(repairRecord.getComponent(), ComponentMaterialVO.class));
			 }
			 if (Func.isNotEmpty(repairRecord.getAttachId())) {
				 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(repairRecordVO.getAttachId()));
				 if (attachListR.isSuccess()) {
					 repairRecordVO.setAttachList(attachListR.getData());
				 }
			 }
			 detail.setRepairRecord(repairRecordVO);
		 }
		 // 外部维修商
		 if (Func.isNotEmpty(repair.getSupplierId())) {
			 R<SupplierDto> supplierR = supplierClient.getById(repair.getSupplierId());
			 if (supplierR.isSuccess() && Func.isNotEmpty(supplierR.getData())) {
				 detail.setSupplierName(supplierR.getData().getName());
			 }
		 }
		 // 备品备件
		 if (Func.isNotEmpty(detail.getComponent())) {
			 List<ComponentMaterialVO> repairComponentList = JSONUtil.toList(detail.getComponent(), ComponentMaterialVO.class);
			 repairComponentList.forEach(componentMaterialVO -> {
				 R<MeasureUnitDto> measureUnitResult = commonClient.getMeasureUnit(componentMaterialVO.getMeasureUnitId());
				 if (measureUnitResult.isSuccess() && Func.isNotEmpty(measureUnitResult.getData())) {
					 // 精度
					 componentMaterialVO.setMeasureUnitPrecision(measureUnitResult.getData().getAccuracy());
				 }
			 });
			 detail.setRepairComponentList(repairComponentList);
		 }
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())) {
			 List<ComponentMaterial> materialList = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda()
				 .eq(ComponentMaterial::getBizNo, detail.getNo()).orderByAsc(ComponentMaterial::getSort));
			 if (Func.isNotEmpty(materialList)) {
				 detail.setRepairComponentList(ComponentMaterialWrapper.build().listVO(materialList));
			 }
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public Repair submit(RepairVO vo) {
		 // 报修类型为“故障”时填写故障等级，故障名称
		 Repair repair = Objects.requireNonNull(BeanUtil.copy(vo, Repair.class));
		 if (Func.isEmpty(repair.getId())) {
			 repair.setNo(BizCodeUtil.generate("BX"));
			 // 新增时发送维修消息提醒
			 equipmentFaultRepairMessageRuleLogicService
				 .pushFaultDefectOrRepairMessage(repair.getEquipmentId(), repair.getNo(), MessageContentTypeEnum.SIMAS_REPAIR, TicketStatusEnum.GENERATED);
		 }
		 // 如果来源为空，则是自定义新增的维修单，因为已经指定了维修人，所以状态直接就是执行中
		 if (Func.isEmpty(vo.getSource())) {
			 repair.setSource(RepairSourceEnum.MANUAL_ADD.getCode());
			 repair.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 }
		 // 报修人、保修部门、报修时间
		 if (Func.isEmpty(vo.getReportUser())) {
			 repair.setReportUser(AuthUtil.getUserId()).setReportDept(Func.firstLong(AuthUtil.getDeptId()));
		 } else {
			 User user = UserCache.getUser(vo.getReportUser());
			 if (Func.isNotEmpty(user)) {
				 repair.setReportDept(Func.firstLong(user.getDeptId()));
			 }
		 }
		 repair.setReportTime(DateUtil.now());
		 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(vo.getBizType())) {
			 // 内部维修-维修人、维修部门
			 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.INTERNAL_REPAIR.name());
			 repair.setIsNeedApproval(needApproval);
			 if (Func.isNotEmpty(vo.getReceiveUser())) {
				 User user = UserCache.getUser(vo.getReceiveUser());
				 if (Func.isNotEmpty(user)) {
					 repair.setReceiveDept(Func.firstLong(user.getDeptId()));
				 }
			 }
		 } else {
			 // 外委维修-跟进人、跟进部门
			 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.EXTERNAL_REPAIR.name());
			 repair.setIsNeedApproval(needApproval);
			 if (Func.isNotEmpty(vo.getFollowUser())) {
				 User user = UserCache.getUser(vo.getFollowUser());
				 if (Func.isNotEmpty(user)) {
					 repair.setReceiveDept(Func.firstLong(user.getDeptId()));
				 }
			 }
		 }
		 if (Func.isEmpty(repair.getAttachId())) {
			 repair.setAttachId(null);
		 }
		 if (Func.isNotEmpty(vo.getMaterialList())) {
			 repair.setComponent(JSONUtil.toJsonStr(vo.getMaterialList()));
		 } else {
			 repair.setComponent(null);
		 }
		 this.saveOrUpdate(repair);
		 // 保存业务日志,非待派单状态才记录业务日志，待派单的状态是由故障缺陷转化，已记录
		 if (!OrderStatusEnum.WAIT.getCode().equals(repair.getStatus())) {
			 final String createLogContent = RepairLogProcessor.getCreateLogContent(RepairBizTypeEnum.getByCode(vo.getBizType()));
			 RepairLogProcessor.saveBizLog(RepairActionEnum.CREATE, RepairBizTypeEnum.getByCode(vo.getBizType()),
				 repair.getId(), JSONUtil.toJsonStr(repair), createLogContent);
		 }
		 // 更新设备状态为维修
		 // 恢复设备状态
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 equipmentAccount.setStatus(EquipmentStatusEnum.IN_REPAIR.getCode());
		 deviceAccountClient.updateDeviceAccount(equipmentAccount);
		 // 发送消息提醒
		 this.sendMessage(Arrays.asList(repair), MessageBizTypeEnum.SIMAS_REPAIR_ADD);
		 return repair;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean dispatch(RepairVO vo) {
		 // 指定维修人员，设置预计完成时间，添加备注说明
		 Repair repair = this.getById(vo.getId());
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (OrderStatusEnum.IN_PROCESS == OrderStatusEnum.getByCode(repair.getStatus())) {
			 throw new ServiceException("当前维修单已派单！");
		 }
		 repair.setDispatchUser(AuthUtil.getUserId())
			 .setReceiveUser(vo.getReceiveUser())
			 .setReceiveDept(Func.firstLong(UserCache.getUser(vo.getReceiveUser()).getDeptId()))
			 .setReceiveUserTel(vo.getReceiveUserTel())
			 .setFollowUserTel(vo.getFollowUserTel())
			 .setCompleteTime(vo.getCompleteTime())
			 .setDispatchTime(DateUtil.now())
			 .setRemark(vo.getRemark())
			 //添加维修建议
			 .setRepairSuggest(vo.getRepairSuggest())
			 .setComponent(Func.isNotEmpty(vo.getMaterialList()) ? JSONUtil.toJsonStr(vo.getMaterialList()) : null)
			 .setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 boolean ret = this.updateById(repair);
		 // 业务日志
		 final String receiveUserName = Optional.ofNullable(UserCache.getUser(vo.getReceiveUser()))
			 .map(User::getRealName)
			 .orElse(null);
		 final String logContent = RepairLogProcessor.getInternalDispatchLogContent(receiveUserName);
		 RepairLogProcessor.saveBizLog(RepairActionEnum.INTERNAL_DISPATCH, RepairBizTypeEnum.INTERNAL,
			 repair.getId(), JSONUtil.toJsonStr(repair), logContent);
		 // 发送消息提醒
		 this.sendMessage(Arrays.asList(repair), MessageBizTypeEnum.SIMAS_REPAIR_ADD);
		 return ret;
	 }

	 @Override
	 //@Transactional(rollbackFor = Exception.class)
	 public String toExternal(RepairVO vo) {
		 // 转换后，维修单不在内部维修列表展示，在外委维修列表展示，状态为处理中
		 // 指定承修单位，跟进人，设置预计完成时间，添加备品备件，备注说明
		 Repair repair = this.getById(vo.getId());
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (RepairBizTypeEnum.EXTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
			 throw new ServiceException("当前维修单已转为外委维修单！");
		 }
		 repair.setBizType(RepairBizTypeEnum.EXTERNAL.getCode())
			 .setSupplierId(vo.getSupplierId())
			 .setRepairSuggest(vo.getRepairSuggest())
			 .setFollowUser(vo.getFollowUser())
			 .setFollowUserTel(vo.getFollowUserTel())
			 .setFollowDept(Func.firstLong(UserCache.getUser(vo.getFollowUser()).getDeptId()))
			 .setCompleteTime(vo.getCompleteTime())
			 .setRemark(vo.getRemark())
			 .setComponent(Func.isNotEmpty(vo.getMaterialList()) ? JSONUtil.toJsonStr(vo.getMaterialList()) : null)
			 .setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 this.updateById(repair);
		 // 业务日志
		 final String logContent = RepairLogProcessor.getExternalDispatchLogContent(repair.getNo());
		 RepairLogProcessor.saveBizLog(RepairActionEnum.EXTERNAL_DISPATCH,
			 RepairBizTypeEnum.EXTERNAL, repair.getId(), JSONUtil.toJsonStr(repair), logContent);
		 // 发送消息提醒
		 this.sendMessage(Arrays.asList(repair), MessageBizTypeEnum.SIMAS_EXTERNAL_REPAIR_ADD);
		 // 返回外委维修单号
		 return repair.getNo();
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean repair(EquipmentRepairVO vo) {
		 Repair repair = this.getById(vo.getRepairId());
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (OrderStatusEnum.WAIT_CONFIRM == OrderStatusEnum.getByCode(repair.getStatus())) {
			 throw new ServiceException("当前维修单已提交！");
		 }
		 Date now = DateUtil.now();
		 // 维修内容（记录）
		 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda().eq(RepairRecord::getRepairId, vo.getRepairId()));
		 if (Func.isNotEmpty(repairRecord)) {
			 Long recordId = repairRecord.getId();
			 BeanUtil.copy(vo.getRepairRecord(), repairRecord);
			 repairRecord.setId(recordId).setRepairId(repair.getId());
			 repairRecord.setVerifyUser(null).setVerifyTime(null).setVerifyResult(null).setVerifyComment(null);
		 } else {
			 if (Func.isEmpty(vo.getRepairRecord())) {
				 throw new ServiceException("未提交有效内容，请查验后重新操作！");
			 }
			 repairRecord = Objects.requireNonNull(BeanUtil.copy(vo.getRepairRecord(), RepairRecord.class));
			 repairRecord.setRepairId(repair.getId());
		 }
		 if (Func.isNotEmpty(vo.getRepairRecord().getMaterialList())) {
			 repairRecord.setComponent(JSONUtil.toJsonStr(vo.getRepairRecord().getMaterialList()));
		 } else {
			 repairRecord.setComponent(null);
		 }
		 repairRecord.setOperateUser(AuthUtil.getUserId()).setOperateTime(now);
		 repairRecordService.saveOrUpdate(repairRecord);
		 // 提交时间
		 repair.setSubmitTime(now);
		 // 验证是否需要审批
		 Boolean needApproval = repair.getIsNeedApproval();
		 if (needApproval) {
			 repair.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
			 boolean ret = this.updateById(repair);
			 if (!ret) {
				 throw new ServiceException("维修单更新错误");
			 }
		 } else {
			 // 不需要审批
			 SpringUtil.getBean(RepairServiceImpl.class).approvalPass(repair, repairRecord);
		 }
		 // 业务日志
		 final String logContent = RepairLogProcessor.getRepairedSubmitLogContent();
		 RepairLogProcessor.saveBizLog(RepairActionEnum.REPAIRED_SUBMIT,
			 RepairBizTypeEnum.getByCode(repair.getBizType()), repair.getId(), JSONUtil.toJsonStr(repair), logContent);
		 return true;
	 }

	 /**
	  * 审核
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean verify(RepairRecordVO vo) {
		 Repair repair = this.getById(vo.getRepairId());
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(repair.getStatus())
			 || OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(repair.getStatus())) {
			 throw new ServiceException("当前维修单已验证！");
		 }
		 Date now = DateUtil.now();
		 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda()
			 .eq(RepairRecord::getRepairId, vo.getRepairId()));
		 repairRecord.setVerifyResult(vo.getVerifyResult()).setVerifyUser(AuthUtil.getUserId()).
			 setVerifyComment(vo.getVerifyComment()).setVerifyTime(now);
		 String logContent;
		 RepairActionEnum actionEnum;
		 repair.setApprovalUser(AuthUtil.getUserId());
		 if (vo.getVerifyResult() == 1) {
			 SpringUtil.getBean(RepairServiceImpl.class).approvalPass(repair, repairRecord);
			 actionEnum = RepairActionEnum.AUDIT_PASS;
			 logContent = RepairLogProcessor.getAuditPassLogContent();
		 } else {
			 // 验证不通过
			 repair.setStatus(OrderStatusEnum.IS_REJECTED.getCode());
			 actionEnum = RepairActionEnum.AUDIT_FAIL;
			 logContent = RepairLogProcessor.getAuditRejectLogContent(vo.getVerifyComment());
			 this.updateById(repair);
		 }
		 RepairLogProcessor.saveBizLog(actionEnum, RepairBizTypeEnum.getByCode(repair.getBizType()),
			 repair.getId(), JSONUtil.toJsonStr(repair), logContent);
		 repairRecordService.updateById(repairRecord);
		 // 验证不通过，发送消息提醒
		 if (vo.getVerifyResult() != 1) {
			 this.sendMessage(Arrays.asList(repair), MessageBizTypeEnum.SIMAS_REPAIR_REJECT);
		 }
		 return true;
	 }

	 /**
	  * 处理审核通过的逻辑
	  */
	 @Transactional(rollbackFor = Exception.class)
	 public void approvalPass(Repair repair, RepairRecord repairRecord) {
		 Date now = DateUtil.now();
		 repair.setActualCompleteTime(now);
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 BizLog bizLog = bizLogService.getOne(Wrappers.<BizLog>query().lambda().eq(BizLog::getBizId, repair.getId()).eq(BizLog::getBizStatus, OrderStatusEnum.IS_OVERDUE.getCode()));
		 if (Func.isNotEmpty(bizLog)) {
			 repair.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		 } else {
			 repair.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
		 }
		 // 恢复设备状态
		 if (Func.isNotEmpty(equipmentAccount.getUseDept())) {
			 equipmentAccount.setStatus(EquipmentStatusEnum.IN_USE.getCode());
		 } else {
			 equipmentAccount.setStatus(EquipmentStatusEnum.IDLE.getCode());
		 }
		 deviceAccountClient.updateDeviceAccount(equipmentAccount);
		 // 备品备件入库
		 List<ComponentMaterialVO> materials = JSONUtil.toList(repairRecord.getComponent(), ComponentMaterialVO.class);
		 if (Func.isNotEmpty(materials)) {
			 String bizModule = SystemModuleEnum.INTERNAL_REPAIR.getCode();
			 if (RepairBizTypeEnum.EXTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
				 bizModule = SystemModuleEnum.EXTERNAL_REPAIR.getCode();
			 }
			 componentMaterialService.submitBatch(repair.getNo(), bizModule, materials);
		 }
		 // 耗时
		 repair.setTimeTake(DateUtils.getDurationHours(repair.getCreateTime(), now, 2));
		 // 维修完成后发送消息提醒
		 equipmentFaultRepairMessageRuleLogicService.pushFaultDefectOrRepairMessage(equipmentAccount.getId(), repair.getNo(), MessageContentTypeEnum.SIMAS_REPAIR, TicketStatusEnum.COMPLETED);
		 // 验证通过生成故障案例
		 FaultDefect faultDefect = faultDefectMapper.selectOne(Wrappers.<FaultDefect>lambdaQuery().eq(FaultDefect::getRepairNo, repair.getNo()));
		 if (Func.isNotEmpty(faultDefect)) {
			 RepairVO repairVO = RepairWrapper.build().entityVO(repair);
			 repairVO.setRepairRecord(RepairRecordWrapper.build().entityVO(repairRecord));
			 repairVO.setProblemComment(faultDefect.getRemark());
			 faultDefectCaseService.submit(RepairWrapper.build().toFaultDefectCase(repairVO, equipmentAccount));
		 }
		 boolean b = this.updateById(repair);
		 if (!b) {
			 throw new ServiceException("维修单更新错误");
		 }
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean close(Long id) {
		 Repair repair = this.getById(id);
		 if (repair == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 repair.setStatus(OrderStatusEnum.IS_CLOSED.getCode());
		 boolean ret = this.updateById(repair);
		 // 恢复设备状态
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 if (Func.isNotEmpty(equipmentAccount.getUseDept())) {
			 equipmentAccount.setStatus(EquipmentStatusEnum.IN_USE.getCode());
		 } else {
			 equipmentAccount.setStatus(EquipmentStatusEnum.IDLE.getCode());
		 }
		 deviceAccountClient.updateDeviceAccount(equipmentAccount);
		 // 业务日志
		 RepairLogProcessor.getCloseLogContent();
		 RepairLogProcessor.saveBizLog(RepairActionEnum.CLOSE, RepairBizTypeEnum.getByCode(repair.getBizType()),
			 repair.getId(), JSONUtil.toJsonStr(repair), RepairLogProcessor.getCloseLogContent());
		 // 发送维修消息提醒
		 equipmentFaultRepairMessageRuleLogicService.pushFaultDefectOrRepairMessage(repair.getEquipmentId(), repair.getNo(), MessageContentTypeEnum.SIMAS_REPAIR, TicketStatusEnum.CLOSED);
		 return ret;
	 }

	 @Override
	 public List<RepairInternalExcel> exportInternalOrder(RepairVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<RepairDTO> list = baseMapper.exportList(vo);
		 if (Func.isNotEmpty(list)) {
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return list.stream().map(repair -> {
				 User reportUser = UserCache.getUser(repair.getReportUser());
				 if (Func.isNotEmpty(reportUser)) {
					 repair.setReportUserName(reportUser.getRealName());
				 }
				 User receiveUser = UserCache.getUser(repair.getReceiveUser());
				 if (Func.isNotEmpty(receiveUser)) {
					 repair.setReceiveUserName(receiveUser.getRealName());
				 }
				 repair.setBizTypeName(RepairBizTypeEnum.getByCode(repair.getBizType()).getName()).setRepairTypeName(DictBizCache.getValue(DictBizEnum.REPAIR_TYPE, repair.getRepairType())).setSourceName(DictBizCache.getValue(DictBizEnum.REPAIR_SOURCE, repair.getSource())).setStatusName(OrderStatusEnum.getByCode(repair.getStatus()).getName());
				 RepairInternalExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(repair, RepairInternalExcel.class));
				 orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda().eq(RepairRecord::getRepairId, repair.getId()));
				 if (Func.isNotEmpty(repairRecord)) {
					 orderExcel.setHandleTimeStr(DateUtil.format(repairRecord.getOperateTime(), DateUtil.PATTERN_DATETIME));
				 }
				 if (Func.isNotEmpty(repair.getCreateTime())) {
					 orderExcel.setReportTimeStr(DateUtil.format(repair.getCreateTime(), DateUtil.PATTERN_DATETIME));
				 }
				 if (Func.isNotEmpty(repair.getCompleteTime())) {
					 orderExcel.setCompleteTimeStr(DateUtil.format(repair.getCompleteTime(), DateUtil.PATTERN_DATETIME));
				 }
				 return orderExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 public List<RepairExternalExcel> exportExternalOrder(RepairVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<RepairDTO> list = baseMapper.exportList(vo);
		 if (Func.isNotEmpty(list)) {
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return list.stream().map(repair -> {
				 User reportUser = UserCache.getUser(repair.getReportUser());
				 if (Func.isNotEmpty(reportUser)) {
					 repair.setReportUserName(reportUser.getRealName());
				 }
				 User followUser = UserCache.getUser(repair.getFollowUser());
				 if (Func.isNotEmpty(followUser)) {
					 repair.setFollowUserName(followUser.getRealName());
				 }
				 repair.setBizTypeName(RepairBizTypeEnum.getByCode(repair.getBizType()).getName()).setRepairTypeName(DictBizCache.getValue(DictBizEnum.REPAIR_TYPE, repair.getRepairType())).setSourceName(DictBizCache.getValue(DictBizEnum.REPAIR_SOURCE, repair.getSource())).setStatusName(OrderStatusEnum.getByCode(repair.getStatus()).getName());
				 RepairExternalExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(repair, RepairExternalExcel.class));
				 orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 RepairRecord repairRecord = repairRecordService.getOne(Wrappers.<RepairRecord>query().lambda().eq(RepairRecord::getRepairId, repair.getId()));
				 if (Func.isNotEmpty(repairRecord)) {
					 orderExcel.setHandleTimeStr(DateUtil.format(repairRecord.getOperateTime(), DateUtil.PATTERN_DATETIME));
				 }
				 orderExcel.setReportTimeStr(DateUtil.format(repair.getCreateTime(), DateUtil.PATTERN_DATETIME)).setCompleteTimeStr(DateUtil.format(repair.getCompleteTime(), DateUtil.PATTERN_DATETIME));
				 return orderExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 public IPage<RepairDTO> timeoutPage(IPage<RepairDTO> page, RepairVO vo) {
		 vo.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda().eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId()).eq(TimeoutRemindSet::getBizType, BizTypeEnum.REPAIR.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 vo.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(vo.getBizType())) {
			 vo.setReceiveUser(AuthUtil.getUserId());
		 } else {
			 vo.setFollowUser(AuthUtil.getUserId());
		 }
		 List<RepairDTO> list = baseMapper.timeoutPage(page, vo);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 User reportUser = UserCache.getUser(dto.getReportUser());
				 if (Func.isNotEmpty(reportUser)) {
					 dto.setReportUserName(reportUser.getRealName());
				 }
				 User receiveUser = UserCache.getUser(dto.getReceiveUser());
				 if (Func.isNotEmpty(receiveUser)) {
					 dto.setReceiveUserName(reportUser.getRealName());
				 }
				 dto.setRepairTypeName(DictBizCache.getValue(DictBizEnum.REPAIR_TYPE, dto.getRepairType())).setSourceName(DictBizCache.getValue(DictBizEnum.REPAIR_SOURCE, dto.getSource())).setStatusName(OrderStatusEnum.getByCode(dto.getStatus()).getName());
			 });
		 }
		 return page.setRecords(list);
	 }

	 @Override
	 public Integer expireSoonCount() {
		 RepairVO repair = new RepairVO();
		 repair.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda().eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId()).eq(TimeoutRemindSet::getBizType, BizTypeEnum.REPAIR.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 repair.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 repair.setReceiveUser(AuthUtil.getUserId()).setFollowUser(AuthUtil.getUserId());
		 return baseMapper.expireSoonCount(repair);
	 }

	 @Override
	 public void sendMessage(List<Repair> list, MessageBizTypeEnum messageBizType) {
		 log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		 list.forEach(order -> {
			 // 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
			 ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			 MessageVo messageVo = new MessageVo();
			 messageVo.setAppKey("SIMAS");
			 messageVo.setSender("SIMAS");
			 messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			 messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			 messageVo.setTitle(messageBizType.getMessage());
			 messageVo.setBizType(messageBizType.getCode());
			 messageVo.setBizId(order.getNo());
			 messageVo.setContent(JSONUtil.toJsonStr(order));
			 messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
			 if (Func.isNotEmpty(order.getFollowUser())) {
				 userVo.setId(order.getFollowUser());
			 } else {
				 userVo.setId(order.getReceiveUser());
			 }
			 receiverInfoVo.setUserList(Arrays.asList(userVo));
			 messageVo.setReceiverInfoVo(receiverInfoVo);
			 messageClient.pushMessage(messageVo);
		 });
		 log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	 }

	 @Override
	 public List<RepairDTO> timeTakeStatisticsList(String bizType, Integer queryDate, LocalDateTime startTime, LocalDateTime endTime) {
		 return baseMapper.timeTakeStatisticsList(bizType, queryDate, startTime, endTime);
	 }

	 @Override
	 public IPage<RepairDTO> statisticalReport(IPage<RepairDTO> page, StatisticSearchVO search) {
		 List<RepairDTO> list = baseMapper.statisticalReport(page, search);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 dto.setEquipmentCategoryName(CommonCache.getEquipmentCategory(dto.getEquipmentCategory()).getCategoryName()).setStatusName(OrderStatusEnum.getByCode(dto.getStatus()).getName());
				 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(dto.getBizType())) {
					 dto.setBizTypeName(RepairBizTypeEnum.INTERNAL.getName());
					 if (Func.isNotEmpty(dto.getReceiveUser())) {
						 User user = UserCache.getUser(dto.getReceiveUser());
						 if (Func.isNotEmpty(user)) {
							 dto.setReceiveUserName(user.getRealName());
						 }
					 }
				 } else {
					 dto.setBizTypeName(RepairBizTypeEnum.EXTERNAL.getName());
					 if (Func.isNotEmpty(dto.getFollowUser())) {
						 User user = UserCache.getUser(dto.getFollowUser());
						 if (Func.isNotEmpty(user)) {
							 dto.setFollowUserName(user.getRealName());
						 }
					 }
				 }
			 });
		 }
		 return page.setRecords(list);
	 }

	 /**
	  * 获取设备Map
	  */
	 private Map<Long, DeviceAccountVO> getEquipmentVOMap(List<Long> equipmentIdList) {
		 if (Func.isEmpty(equipmentIdList)) {
			 return MapUtil.empty();
		 }
		 // 根据设备ids查询设备信息
		 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		 deviceAccountVO.setDeviceIds(equipmentIdList);
		 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		 if (!equipmentListResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(equipmentListResult.getData())) {
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(equipmentListResult.getData(), DeviceAccountVO::getId, e -> Objects.requireNonNull(BeanUtil.copy(e, DeviceAccountVO.class)));
	 }

	 @Override
	 public List<RepairStatisticsExcel> exportStatisticalReport(StatisticSearchVO vo) {
		 QueryWrapper<Repair> queryWrapper = Wrappers.query();
		 queryWrapper.lambda().eq(Func.isNotEmpty(vo.getStatus()), Repair::getStatus, vo.getStatus());
		 if (vo.getQueryDate() == 1) {
			 queryWrapper.lambda().ge(Repair::getCreateTime, LocalDate.now().minusDays(30));
		 }
		 if (vo.getQueryDate() == 2) {
			 queryWrapper.lambda().ge(Repair::getCreateTime, LocalDate.now().minusDays(7));
		 }
		 if (vo.getQueryDate() == 3) {
			 queryWrapper.lambda().ge(Repair::getCreateTime,
					 DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_START_TIME)
				 .le(Repair::getCreateTime,
					 DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME);
		 }
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 queryWrapper.lambda().ge(Repair::getActualCompleteTime, vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 queryWrapper.lambda().le(Repair::getActualCompleteTime, vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<Repair> list = this.list(queryWrapper);
		 if (Func.isNotEmpty(list)) {
			 List<RepairDTO> orderList = RepairWrapper.build().listDTO(list);
			 List<Long> equipmentIdList = list.stream().map(Repair::getEquipmentId).collect(Collectors.toList());
			 // 查询设备Map
			 final Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(equipmentIdList);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return orderList.stream().map(order -> {
				 RepairStatisticsExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, RepairStatisticsExcel.class));
				 orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 DeviceAccountVO equipmentAccount = equipmentVOMap.get(order.getEquipmentId());
				 if (Func.isNotEmpty(equipmentAccount)) {
					 orderExcel.setEquipmentName(equipmentAccount.getName())
						 .setEquipmentCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
				 }
				 DeviceMonitor equipmentMonitor = CommonCache.getMonitor(order.getMonitorId());
				 if (Func.isNotEmpty(equipmentMonitor)) {
					 orderExcel.setMonitorName(equipmentMonitor.getName());
				 }
				 orderExcel.setCreateTime(order.getCreateTime());
				 if (Func.isNotEmpty(order.getActualCompleteTime())) {
					 orderExcel.setActualCompleteTime(order.getActualCompleteTime());
				 }
				 return orderExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 public List<RepairDTO> queryList(RepairVO vo) {
		 return baseMapper.queryList(vo);
	 }

	 @Override
	 public List<RepairDTO> repairStatistics(RepairVO vo, int queryDate) {
		 return baseMapper.repairStatistics(vo, queryDate);
	 }

	 @Override
	 public Repair overhaulToRepair(RepairVO vo) {
		 // 报修类型为“故障”时填写故障等级，故障名称
		 Repair repair = Objects.requireNonNull(BeanUtil.copy(vo, Repair.class));
		 if (Func.isEmpty(repair.getNo())) {
			 repair.setNo(BizCodeUtil.generate("BX"));
		 }
		 if (Func.isEmpty(repair.getAttachId())) {
			 repair.setAttachId(null);
		 }
		 // 是否需要审核
		 if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
			 repair.setIsNeedApproval(ApprovalUtil.isNeedApproval(OrderTypeEnum.INTERNAL_REPAIR.name()));
		 } else {
			 repair.setIsNeedApproval(ApprovalUtil.isNeedApproval(OrderTypeEnum.EXTERNAL_REPAIR.name()));
		 }
		 this.save(repair);

		 // 更新设备状态为维修
		 // 恢复设备状态
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(repair.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 equipmentAccount.setStatus(EquipmentStatusEnum.IN_REPAIR.getCode());
		 deviceAccountClient.updateDeviceAccount(equipmentAccount);
		 // 保存维修记录
		 if (Func.isNotEmpty(vo.getRepairRecord())) {
			 RepairRecord repairRecord = Objects.requireNonNull(BeanUtil.copy(vo.getRepairRecord(), RepairRecord.class));
			 repairRecord.setRepairId(repair.getId());
			 repairRecordService.save(repairRecord);
			 // 不需要审核的的操作
			 if (!repair.getIsNeedApproval()) {
				 SpringUtil.getBean(RepairServiceImpl.class).approvalPass(repair, repairRecord);
			 }
		 }
		 // 业务日志
		 BizLogVO bizLog = new BizLogVO(RepairWrapper.build().entityVO(repair));
		 bizLog.setContent("检修转维修单");
		 bizLog.setOperateUser(AuthUtil.getUserId()).setOperateTime(repair.getReportTime());
		 bizLogService.submit(bizLog);
		 // 发送消息提醒
		 this.sendMessage(Arrays.asList(repair), MessageBizTypeEnum.SIMAS_REPAIR_ADD);
		 return repair;
	 }

	 @Override
	 public List<BigScreenMessageDTO> overdueList(String tenantId) {
		 return baseMapper.overdueList(tenantId);

	 }

	 @Override
	 public List<RepairDTO> repair30day(String bizType) {
		 return baseMapper.repair30day(bizType);
	 }

	 @Override
	 public List<RepairDTO> finishList(String bizType, LocalDateTime startTime, LocalDateTime endTime) {
		 return baseMapper.finishList(bizType, startTime, endTime);
	 }

	 @Override
	 public Integer handleRepairCount(RepairVO vo) {
		 return baseMapper.handleRepairCount(vo);
	 }

	 @Override
	 public List<RepairDTO> curYearRepair(LocalDateTime start, List<Integer> statusList, String tenantId) {
		 return baseMapper.curYearRepair(start, statusList, tenantId);
	 }

	 @Override
	 public IPage<FaultDefectDTO> calculateStatisticsByFaultDefect(IPage<FaultDefectDTO> page, StatisticSearchVO vo) {
		 // 根据故障名称和故障类型查询故障统计信息
		 final IPage<FaultDefectDTO> faultDefectPage = baseMapper.listGroupByFaultDefect(page, vo);
		 if (Func.isEmpty(faultDefectPage) || Func.isEmpty(faultDefectPage.getRecords())) {
			 return page;
		 }
		 // 已完成的维修工单id列表
		 final List<Long> completeRepairIdList = faultDefectPage.getRecords()
			 .stream()
			 .flatMap(faultDefect -> Arrays.stream(Func.split(faultDefect.getCompletedIds(), StringPool.COMMA)))
			 .map(Long::valueOf)
			 .collect(Collectors.toList());

		 // 获取每个维修工单的耗时
		 final Map<Long, BigDecimal> averageTimeTakeMap = getAverageTimeTakeMap(completeRepairIdList);

		 final List<FaultDefectDTO> defectDTOList = faultDefectPage.getRecords()
			 .stream()
			 .peek(faultDefect -> {
				 // 初始化完成率
				 faultDefect.setCompleteRate(BigDecimal.ZERO);
				 faultDefect.setAverageTimeTake(BigDecimal.ZERO);
				 // 类型名称
				 Optional.ofNullable(DictBizCache.getValue(DictBizEnum.REPAIR_TYPE, faultDefect.getType()))
					 .ifPresent(typeName -> faultDefect.setTypeName(typeName));
				 // 维修次数和总次数相同
				 faultDefect.setRepairCount(faultDefect.getCount());
				 // 计算平均耗时
				 List<Long> completedIds = Arrays.stream(Func.split(faultDefect.getCompletedIds(), StringPool.COMMA))
					 .map(Long::valueOf)
					 .collect(Collectors.toList());
				 if (Func.isNotEmpty(completedIds)) {
					 // 计算平均耗时
					 BigDecimal averageTime = completedIds.stream()
						 .map(averageTimeTakeMap::get)  // 获取每个工单的耗时
						 .filter(Objects::nonNull)  // 过滤掉 null 值
						 .reduce(BigDecimal.ZERO, BigDecimal::add)  // 累加总耗时
						 .divide(new BigDecimal(completedIds.size()), 2, RoundingMode.HALF_UP);  // 计算平均值

					 // 设置平均耗时到 DTO 中
					 faultDefect.setAverageTimeTake(averageTime);
					 // 计算完成率
					 BigDecimal completeRate = calculateCompleteRate(faultDefect.getCompleteCount(), faultDefect.getCount());
					 faultDefect.setCompleteRate(completeRate);
				 }
			 }).sorted(Comparator.comparing(FaultDefectDTO::getCount).reversed())
			 .collect(Collectors.toList());
		 faultDefectPage.setRecords(defectDTOList);
		 return faultDefectPage;
	 }

	 @Override
	 public List<Repair> listBy(Long receiveDept, Long followDept, List<Long> receiveUserIds, List<Long> followUsers, List<Long> equipmentIds, String bizType, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus) {
		 return this.lambdaQuery()
			 .eq(ObjectUtil.isNotEmpty(receiveDept), Repair::getReceiveDept, receiveDept)
			 .in(ObjectUtil.isNotEmpty(receiveUserIds), Repair::getReceiveUser, receiveUserIds)
			 .in(ObjectUtil.isNotEmpty(followUsers), Repair::getFollowUser, followUsers)
			 .in(ObjectUtil.isNotEmpty(equipmentIds), Repair::getEquipmentId, equipmentIds)
			 .eq(ObjectUtil.isNotEmpty(bizType), Repair::getBizType, bizType)
			 .ge(ObjectUtil.isNotEmpty(startDateTime), Repair::getReportTime, startDateTime)
			 .lt(ObjectUtil.isNotEmpty(endDateTime), Repair::getReportTime, endDateTime)
			 .ne(ObjectUtil.isNotEmpty(neStatus), Repair::getStatus, neStatus)
			 .list();
	 }

	 @Override
	 public List<EquipmentStatisticsDTO> repairDeviceStatistics(StatisticSearchVO search, Map<Long, DeviceAccountVO> deviceMap) {
		 List<EquipmentStatisticsDTO> list = repairMapper.statisticsByEquipment(search);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 DeviceAccountVO equipment = deviceMap.get(dto.getId());
				 dto.setName(equipment.getName()).setSn(equipment.getSn())
					 .setCategoryName(CommonCache.getEquipmentCategory(equipment.getCategoryId()).getCategoryName());
				 if (Func.isNotEmpty(equipment.getUseDept())) {
					 dto.setUseDeptName(SysCache.getDept(equipment.getUseDept()).getDeptName());
				 }
				 dto.setCompleteCount(0).setCompleteRate(BigDecimal.ZERO);
				 StatisticSearchVO searchVO = new StatisticSearchVO(search.getQueryDate(), search.getStartDate(), search.getEndDate(),
					 Func.toLongList(Func.toStr(dto.getId())));
				 List<Repair> orderList = repairMapper.equipmentStatisticsOfOrder(searchVO);
				 for (Repair order : orderList) {
					 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())
						 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())) {
						 dto.setCompleteCount(dto.getCompleteCount() + 1);
					 }
				 }
				 // 完成率
				 if (dto.getCount() != 0) {
					 dto.setCompleteRate(new BigDecimal(dto.getCompleteCount())
						 .divide(new BigDecimal(dto.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
				 }
			 });
		 }
		 return list;
	 }

	 /**
	  * 计算完成率
	  * <p>
	  * 该方法用于计算已完成项目的比例，以百分比形式表示完成情况
	  * 它通过将完成的数量除以总数量，然后乘以100来计算完成率
	  *
	  * @param completeCount 完成的数量如果总数量为0，此参数应为0
	  * @param totalCount    总数量不应为负数
	  * @return 返回完成率的百分比，如果总数量为0，则返回0
	  */
	 private BigDecimal calculateCompleteRate(long completeCount, long totalCount) {
		 if (totalCount == 0) {
			 return BigDecimal.ZERO;
		 }
		 return new BigDecimal(completeCount)
			 .divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP)
			 .multiply(BigDecimal.valueOf(100));
	 }

	 /**
	  * 获取每个维修工单的平均耗时
	  *
	  * @param completeRepairIdList 已完成的维修工单ids
	  */
	 private Map<Long, BigDecimal> getAverageTimeTakeMap(List<Long> completeRepairIdList) {
		 if (Func.isEmpty(completeRepairIdList)) {
			 return MapUtil.empty();
		 }
		 // 查询维修记录
		 final List<RepairRecord> repairRecordList = repairRecordService.list(Wrappers.lambdaQuery(RepairRecord.class)
			 .in(RepairRecord::getRepairId, completeRepairIdList)
			 .eq(RepairRecord::getVerifyResult, 1));
		 if (Func.isEmpty(repairRecordList)) {
			 return MapUtil.empty();
		 }
		 // 根据维修记录分组统计维修时长
		 Map<Long, BigDecimal> repairDurationMap = repairRecordList.stream()
			 .filter(r -> cn.hutool.core.util.NumberUtil.isNumber(r.getDuration()))
			 .collect(Collectors.groupingBy(RepairRecord::getRepairId, Collectors.reducing(BigDecimal.ZERO, r -> new BigDecimal(r.getDuration()), BigDecimal::add)));

		 return repairDurationMap;
	 }


 }
