<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.overhaul.mapper.OverhaulPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="overhaulPlanResultMap" type="com.snszyk.simas.overhaul.entity.OverhaulPlan">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="overhaulPlanDTOResultMap" type="com.snszyk.simas.overhaul.dto.OverhaulPlanDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="page" resultMap="overhaulPlanDTOResultMap">
        select * from simas_overhaul_plan where is_deleted = 0
        <if test="overhaulPlan.name != null and overhaulPlan.name != ''">
            and `name` like concat('%', #{overhaulPlan.name}, '%')
        </if>
        <if test="overhaulPlan.keywords!=null and overhaulPlan.keywords != ''">
            AND (`no` like concat('%',#{overhaulPlan.keywords},'%') or `name` like
            concat('%',#{overhaulPlan.keywords},'%'))
        </if>
        <if test="overhaulPlan.cycleType != null and overhaulPlan.cycleType != ''">
            and cycle_type = #{overhaulPlan.cycleType}
        </if>
        <if test="overhaulPlan.executeDept != null and overhaulPlan.executeDept != ''">
            and execute_dept = #{overhaulPlan.executeDept}
        </if>
        <if test="overhaulPlan.status != null">
            and status = #{overhaulPlan.status}
        </if>
        <if test="overhaulPlan.queryStartDate != null and overhaulPlan.queryStartDate != ''">
            and start_date <![CDATA[ >= ]]> #{overhaulPlan.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="overhaulPlan.queryEndDate != null and overhaulPlan.queryEndDate != ''">
            and start_date <![CDATA[ <= ]]> #{overhaulPlan.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

    <!--查询当天点检计划-->
    <select id="getTheDayPlans" resultMap="overhaulPlanDTOResultMap">
        SELECT
            id,
            tenant_id,
            `name`,
            `no`,
            cycle_type,
            cycle_interval,
            execute_dept,
            execute_user,
            start_date,
            end_date,
            execute_time,
            remark,
            `status`
        FROM
            simas_overhaul_plan
        WHERE is_deleted = 0
            AND(`status` = 2 OR `status` = 3)
            AND start_date <![CDATA[ <= ]]> #{currentDate, jdbcType=DATE}
            AND end_date <![CDATA[ >= ]]> #{currentDate, jdbcType=DATE}
    </select>

</mapper>
