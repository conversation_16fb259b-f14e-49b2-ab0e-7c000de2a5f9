/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备保养工单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface OverhaulOrderMapper extends BaseMapper<OverhaulOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param overhaulOrder
	 * @return
	 */
	List<OverhaulOrder> page(IPage page, @Param("overhaulOrder") OverhaulOrderVO overhaulOrder);

	/**
	 * 即将超时分页
	 *
	 * @param page
	 * @param overhaulOrder
	 * @return
	 */
	List<OverhaulOrder> timeoutPage(IPage page, @Param("overhaulOrder") OverhaulOrderVO overhaulOrder);

	/**
	 * 即将超时工单数量
	 *
	 * @param overhaulOrder
	 * @return
	 */
	Integer expireSoonCount(@Param("overhaulOrder") OverhaulOrderVO overhaulOrder);

	/**
	 * 统计-工单完成情况
	 *
	 * @param queryDate
	 * @return
	 */
	List<OverhaulOrder> overhaulOrderStatistics(@Param("queryDate")Integer queryDate);

	/**
	 * 统计报表-保养统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<OverhaulOrder> statisticalReport(IPage page, @Param("search") StatisticSearchVO search);

	/**
	 * 保养工单列表
	 *
	 * @param overhaulOrder
	 * @return
	 */
	List<OverhaulOrderDTO> queryList(@Param("overhaulOrder") OverhaulOrderVO overhaulOrder);

	/**
	 * 统计报表-点巡检按设备统计
	 *
	 * @param search
	 * @return
	 */
	List<EquipmentStatisticsDTO> statisticsByEquipment(@Param("search") StatisticSearchVO search);


    List<BigScreenMessageDTO> specialNoFinishList(@Param("tenantId") String tenantId);

	List<BigScreenMessageDTO> noSpecialOverdueList(@Param("tenantId") String tenantId);

	Integer handleOverhaulCount(@Param("overhaulOrder") OverhaulOrderVO overhaulOrder);
}
