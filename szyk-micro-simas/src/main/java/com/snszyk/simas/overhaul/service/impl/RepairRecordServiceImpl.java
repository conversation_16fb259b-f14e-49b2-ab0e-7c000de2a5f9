/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.simas.overhaul.dto.RepairDurationDTO;
import com.snszyk.simas.overhaul.entity.RepairRecord;
import com.snszyk.simas.overhaul.mapper.RepairRecordMapper;
import com.snszyk.simas.overhaul.service.IRepairRecordService;
import com.snszyk.simas.overhaul.vo.RepairDurationPageVO;
import org.springframework.stereotype.Service;

/**
 * 设备维修记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
public class RepairRecordServiceImpl extends ServiceImpl<RepairRecordMapper, RepairRecord> implements IRepairRecordService {


	@Override
	public IPage<RepairDurationDTO> pageRepairDuration(RepairDurationPageVO vo) {
		return baseMapper.pageRepairDuration(vo);
	}


}
