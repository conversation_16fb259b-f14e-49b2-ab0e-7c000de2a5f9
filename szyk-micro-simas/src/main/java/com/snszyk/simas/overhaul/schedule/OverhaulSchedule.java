package com.snszyk.simas.overhaul.schedule;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.enums.*;
import com.snszyk.simas.common.processor.OrderLogProcessor;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.service.logic.GeneralLogicService;
import com.snszyk.simas.common.util.ApprovalUtil;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.overhaul.dto.OverhaulPlanDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.OverhaulPlan;
import com.snszyk.simas.overhaul.entity.OverhaulPlanEquipment;
import com.snszyk.simas.overhaul.entity.OverhaulStandard;
import com.snszyk.simas.overhaul.enums.OverhaulPlanStatusEnum;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IOverhaulPlanEquipmentService;
import com.snszyk.simas.overhaul.service.IOverhaulPlanService;
import com.snszyk.simas.overhaul.service.IOverhaulStandardService;
import com.snszyk.simas.overhaul.wrapper.OverhaulOrderWrapper;
import com.snszyk.simas.spare.schedule.SimasPlanSchedule;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检修任务定时器
 *
 * <AUTHOR>
 * @date 2024/08/16 15:56
 **/
@Slf4j
@AllArgsConstructor
@Service
//@Configuration
//@EnableScheduling
public class OverhaulSchedule {
	private final IDeviceAccountClient deviceAccountClient;
	private final IOverhaulPlanEquipmentService planEquipmentService;
	private final IOverhaulOrderService overhaulOrderService;
	private final IOverhaulPlanService planService;
	private final IBizLogService bizLogService;
	private final GeneralLogicService generalLogicService;

	private final IOverhaulStandardService standardService;

	//	@Scheduled(cron = "0 0 5 * * ?")
	@XxlJob("overhaulStopPlanJobHandler")
	public ReturnT<String> stopPlan(String param) {
		log.info("检修工单-，计划停止定时任务执行了");

		// 查询计划结束日期小于当前日期且状态为执行中的计划
		List<OverhaulPlan> planList = planService.list(Wrappers.lambdaQuery(OverhaulPlan.class)
			.lt(OverhaulPlan::getEndDate, DateUtil.now())
			.eq(OverhaulPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode()));
		if (ObjectUtil.isEmpty(planList)) {
			return ReturnT.SUCCESS;
		}
		// 将计划状态改为已终止
		planList.forEach(plan -> plan.setStatus(PlanStatusEnum.IS_COMPLETED.getCode()));
		planService.updateBatchById(planList);
		log.info("检修工单-，计划停止定时任务执行了");
		XxlJobLogger.log("################检修工单-，计划停止定时任务执行了-END-################");
		return ReturnT.SUCCESS;
	}

	/**
	 * 日检、周检、月检（每日01:00进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/8/19 09:16
	 */
//	@Scheduled(cron = "0 0 6 * * ?")

//	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob("overhaulOrderJobHandler")
	public ReturnT<String> overhaulSchedule(String param) {
		log.info("################设备检修周检、月检任务生成-START-################");
		//程序正常运行情况
		List<OverhaulPlanDTO> planList = planService.getTheDayPlans(DateUtil.now());
		if (Func.isNotEmpty(planList)) {
			List<OverhaulOrder> orderList = new ArrayList<>();
			for (OverhaulPlanDTO plan : planList) {
				// 计划检修设备列表
				List<OverhaulPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<OverhaulPlanEquipment>query().lambda()
					.eq(OverhaulPlanEquipment::getPlanId, plan.getId()).orderByAsc(OverhaulPlanEquipment::getSort));
				// 周期类型
				String cycleType = plan.getCycleType();
				// 周期间隔
				Integer cycleInterval = plan.getCycleInterval();
				// 计划执行时间
				String cycleExecuteTimeStr = plan.getExecuteTime();
				if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(cycleType)) {
//					boolean match = SimasPlanSchedule.matchWeek(plan.getStartDate().getTime(),
//						plan.getEndDate().getTime(), cycleInterval, cycleExecuteTimeStr);
//					if (match) {
//						generateOrders(plan, planEquipmentList, orderList);
//					}

					Boolean isExecuted = false;
					List<String> list = Func.toStrList(cycleExecuteTimeStr);
					for (String cycleExecuteTime : list) {
						isExecuted = SimasPlanSchedule.executeWeek(plan.getStartDate().getTime(),
							plan.getEndDate().getTime(), cycleInterval, cycleExecuteTime);
						if (isExecuted) {
							generateOrders(plan, planEquipmentList, orderList);
						}
					}
				}
				if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(cycleType)) {
					boolean match = SimasPlanSchedule.matchMonth(plan.getStartDate().getTime(), plan.getEndDate().getTime(),
						cycleInterval, cycleExecuteTimeStr);
					if (match) {
						//工单生成
						generateOrders(plan, planEquipmentList, orderList);
					}
				}
				// 更新计划状态
				updatePlanStatus(plan);
			}
			if (Func.isNotEmpty(orderList)) {
				sendMessage(orderList);
			}
		}
		// 关闭过期计划
		planService.closeExpirePlan();
		log.info("################设备检修周检、月检任务生成-END-################");
		XxlJobLogger.log("################设备检修周检、月检任务生成-END-################");
		return ReturnT.SUCCESS;
	}

	/**
	 * 工单超时(每分钟执行一次)
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/8/23 15:16
	 */
//	@XxlJob("overhaulJobHandler")
//	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob("overhaulExpireJobHandler")
	public ReturnT<String> orderOverdueSchedule(String param) {
		log.info("################检修工单超期定时任务-START-################");
		List<OverhaulOrder> list = overhaulOrderService.list(Wrappers.<OverhaulOrder>query().lambda()
			.eq(OverhaulOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
		List<OverhaulOrder> orderList = new ArrayList<>();
		if (Func.isNotEmpty(list)) {
			for (OverhaulOrder order : list) {
				// 特种设备没有超期状态
				DeviceAccountVO equipmentAccount = deviceAccountClient.deviceInfoById(order.getEquipmentId()).getData();
// 				EquipmentAccount equipmentAccount = equipmentAccountService.getById(order.getEquipmentId());
				if (Func.isEmpty(equipmentAccount) || Objects.equals(equipmentAccount.getIsSpecial(), 1)) {
					continue;
				}
				if (DateUtil.now().after(DateUtils.addDays(order.getEndTime(), order.getFloatDate()))) {
					orderList.add(order);
				}
			}
		}
		if (Func.isNotEmpty(orderList)) {
			overhaulOrderService.update(Wrappers.<OverhaulOrder>update().lambda()
				.set(OverhaulOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
				.in(OverhaulOrder::getId, orderList.stream().map(OverhaulOrder::getId).collect(Collectors.toList())));
			// 业务日志
			bizLogService.submitBatch(orderList.stream().map(order -> {
				order.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
				BizLogVO bizLog = new BizLogVO(OverhaulOrderWrapper.build().entityVO(order));
				bizLog.setContent("工单超时").setOperateTime(DateUtil.now());
				return bizLog;
			}).collect(Collectors.toList()));
			// 发送消息提醒
			overhaulOrderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_OVERHAUL_OVERDUE);
		}
		log.info("################检修工单超期定时任务-END-################");
		XxlJobLogger.log("################检修工单超期定时任务-END-################");
		return ReturnT.SUCCESS;
	}

	/**
	 * 即将超时-发送消息
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/9/15 18:31
	 */
//	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob("overhaulExpireSoonJobHandler")
	public ReturnT<String> expireSoonSchedule(String param) {
		log.info("################检修工单即将超期发送消息-START-################");
		List<OverhaulOrder> list = overhaulOrderService.list(Wrappers.<OverhaulOrder>query().lambda()
			.eq(OverhaulOrder::getIsExpired, 0)
			.and(wrapper -> wrapper.eq(OverhaulOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
				.or()
				.eq(OverhaulOrder::getStatus, OrderStatusEnum.IS_REJECTED.getCode())));
		if (Func.isNotEmpty(list)) {
			for (OverhaulOrder order : list) {
				// 特种设备不发送即将超时消息
				DeviceAccountVO equipmentAccount = deviceAccountClient.deviceInfoById(order.getEquipmentId()).getData();
//				EquipmentAccount equipmentAccount = equipmentAccountService.getById(order.getEquipmentId());
				if (Func.isEmpty(equipmentAccount) || Objects.equals(equipmentAccount.getIsSpecial(), 1)) {
					continue;
				}

				List<Long> userIds = generalLogicService.soonTimeoutUser(BizTypeEnum.OVERHAUL, order.getEndTime(),
					order.getFloatDate(),
					order.getExecuteUser(),
					order.getExecuteDept(),
					SimasConstant.SimasRole.REPAIR_USER);
				if (Func.isNotEmpty(userIds)) {
					// 发送消息提醒
					generalLogicService.sendMessage(order.getNo(), JSONUtil.toJsonStr(order), userIds, MessageBizTypeEnum.SIMAS_OVERHAUL_EXPIRE);
				}
			}
		}
		log.info("################检修工单即将超期发送消息-END-################");
		XxlJobLogger.log("#################检修工单即将超期发送消息-END-################");
		return ReturnT.SUCCESS;

	}

	/**
	 * 生成检修工单
	 * @param plan 计划
	 * @param planEquipmentList 计划设备列表
	 * @param orderList 工单列表
	 */
	private void generateOrders(OverhaulPlanDTO plan, List<OverhaulPlanEquipment> planEquipmentList, List<OverhaulOrder> orderList) {
		String tenantId = plan.getTenantId();
		Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.OVERHAUL_ORDER.name(), tenantId);

		for (OverhaulPlanEquipment planEquipment : planEquipmentList) {
			R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
			if (!deviceAccountVOR.isSuccess() || deviceAccountVOR.getData() == null) {
				continue;
			}
			DeviceAccountVO account = deviceAccountVOR.getData();
//			EquipmentAccount account = equipmentAccountService.getById(planEquipment.getEquipmentId());

			LambdaQueryWrapper<OverhaulOrder> query = Wrappers.<OverhaulOrder>lambdaQuery();
			query.eq(OverhaulOrder::getPlanId, plan.getId())
				.eq(OverhaulOrder::getEquipmentId, planEquipment.getEquipmentId())
				.apply("date_format(create_time,'%Y-%m-%d') = date_format({0},'%Y-%m-%d')", DateUtil.now());

			// 判断是否已生成工单
			List<OverhaulOrder> orders = overhaulOrderService.list(query);
			if (Func.isNotEmpty(orders)) {
				continue;
			}
			List<OverhaulStandard> overhaulStandardList = standardService.lambdaQuery().eq(OverhaulStandard::getEquipmentId, planEquipment.getEquipmentId()).list();
			// 生成工单
			OverhaulOrder order = new OverhaulOrder();
			//v1.0添加多个标准的信息
			order.setStandardInfo(JSONUtil.toJsonStr(overhaulStandardList));
			order.setTenantId(plan.getTenantId());
			order.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
			//生成检修工单编号
			order.setNo(BizCodeUtil.generate("OO"));
			order.setOrderName(account.getName() + "(" + plan.getName() + ")");
			order.setEquipmentId(planEquipment.getEquipmentId());
			order.setPlanId(plan.getId());
			order.setEquipmentCode(account.getCode());
			order.setExecuteDept(plan.getExecuteDept());
			order.setExecuteUser(plan.getExecuteUser());
			String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
			String endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 23:59:59";
			order.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
			order.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
			order.setCreateTime(DateUtil.now());
			order.setFloatDate(plan.getFloatDate());
			order.setIsNeedApproval(needApproval);

			overhaulOrderService.saveOrUpdate(order);
			OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
			orderList.add(order);
		}
	}

	/**
	 * 更新计划状态
	 * @param plan
	 */
	private void updatePlanStatus(OverhaulPlanDTO plan) {
		// 更新计划状态（未开始 —> 执行中）
		if (OverhaulPlanStatusEnum.NO_START == OverhaulPlanStatusEnum.getByCode(plan.getStatus())) {
			planService.update(Wrappers.<OverhaulPlan>update().lambda()
				.set(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IN_PROGRESS.getCode())
				.set(OverhaulPlan::getUpdateTime, DateUtil.now()).eq(OverhaulPlan::getId, plan.getId()));
		}
		// 更新计划状态（执行中 —> 已完成）
		if (OverhaulPlanStatusEnum.IN_PROGRESS == OverhaulPlanStatusEnum.getByCode(plan.getStatus())
			&& Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
			DateUtil.format(plan.getEndDate(), DateUtil.PATTERN_DATE))) {
			planService.update(Wrappers.<OverhaulPlan>update().lambda()
				.set(OverhaulPlan::getStatus, OverhaulPlanStatusEnum.IS_COMPLETED.getCode())
				.set(OverhaulPlan::getUpdateTime, DateUtil.now()).eq(OverhaulPlan::getId, plan.getId()));
		}
	}

	/**
	 * 发送生成工单消息
	 * @param orderList
	 */
	private void sendMessage(List<OverhaulOrder> orderList) {

		// 发送消息提醒
		overhaulOrderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_OVERHAUL_ADD);
	}


}
