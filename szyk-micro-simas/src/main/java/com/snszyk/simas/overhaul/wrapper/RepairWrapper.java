/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.wrapper;

import com.snszyk.common.equipment.entity.DeviceAccount;
import com.snszyk.common.equipment.entity.DeviceCategory;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.fault.vo.FaultDefectCaseVO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.OverhaulRecord;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.enums.RepairSourceEnum;
import com.snszyk.simas.overhaul.vo.RepairRecordVO;
import com.snszyk.simas.overhaul.vo.RepairVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备维修单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public class RepairWrapper extends BaseEntityWrapper<Repair, RepairVO> {
	private static final ICommonClient commonClient;

	static {
		commonClient = SpringUtil.getBean(ICommonClient.class);
	}

	public static RepairWrapper build() {
		return new RepairWrapper();
	}

	@Override
	public RepairVO entityVO(Repair repair) {
		return Objects.requireNonNull(BeanUtil.copy(repair, RepairVO.class));
	}

	public RepairVO overhaulEntityVO(OverhaulRecord overhaulRecord, OverhaulOrder overhaulOrder) {
		RepairVO vo = new RepairVO();
		vo.setBizType(RepairBizTypeEnum.INTERNAL.getCode());
		vo.setSourceNo(overhaulOrder.getNo());
		vo.setEquipmentId(overhaulRecord.getEquipmentId());
		vo.setMonitorId(overhaulRecord.getMonitorId());
		vo.setMonitorName(overhaulRecord.getMonitorName());
		vo.setSource(RepairSourceEnum.PLANNING_OVERHAUL.getCode());
		vo.setRepairType(overhaulRecord.getFaultType());
		vo.setFaultName(overhaulRecord.getFaultName());
		vo.setFaultLevel(overhaulRecord.getFaultLevel());
		vo.setProblemComment(overhaulRecord.getFaultRemark());
		vo.setReportDept(overhaulOrder.getExecuteDept());
		vo.setReportUser(overhaulOrder.getExecuteUser());
		vo.setReportTime(DateUtil.now());
		vo.setComponent(overhaulRecord.getComponent());
		vo.setAttachId(overhaulRecord.getAttachId());
		Optional.ofNullable(UserCache.getUser(overhaulOrder.getExecuteUser()))
			.ifPresent(user -> vo.setTel(user.getPhone()));
		// 现场已处理，生成维修待验证状态的维修工单
		if (overhaulRecord.getIsReport() == 0) {
			RepairRecordVO repairRecord = new RepairRecordVO();
			vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
			vo.setReceiveUser(overhaulOrder.getExecuteUser());
			vo.setSubmitTime(DateUtil.now());
			User user = UserCache.getUser(vo.getReceiveUser());
			if (Func.isNotEmpty(user)) {
				vo.setReceiveDept(Func.toLongList(user.getDeptId()).get(0));
			}
			repairRecord.setResult(overhaulRecord.getResult());
			repairRecord.setCheckMethod(overhaulRecord.getCheckMethod());
			repairRecord.setDuration(overhaulRecord.getDuration());
			repairRecord.setFaultReason(overhaulRecord.getFaultReason());
			repairRecord.setSolution(overhaulRecord.getSolution());
			repairRecord.setComponent(overhaulRecord.getComponent());
			repairRecord.setAttachId(overhaulRecord.getAttachId());
			repairRecord.setOperateUser(overhaulRecord.getOverhaulUser());
			repairRecord.setOperateTime(overhaulRecord.getOverhaulTime());
			vo.setRepairRecord(repairRecord);
		}
		// 现场未处理，需要上报，生成待派单状态的维修工单
		if (overhaulRecord.getIsReport() == 1) {
			vo.setStatus(OrderStatusEnum.WAIT.getCode());
		}
		return vo;
	}

	public FaultDefectCaseVO toFaultDefectCase(RepairVO repair, DeviceAccount equipmentAccount) {
		FaultDefectCaseVO faultDefectCaseVO = new FaultDefectCaseVO();
		faultDefectCaseVO.setRepairId(repair.getId());
		faultDefectCaseVO.setRepairNo(repair.getNo());
		faultDefectCaseVO.setEquipmentId(repair.getEquipmentId());
		faultDefectCaseVO.setEquipmentName(equipmentAccount.getName());
		faultDefectCaseVO.setEquipmentModel(equipmentAccount.getModel());
		faultDefectCaseVO.setEquipmentSn(equipmentAccount.getSn());
		faultDefectCaseVO.setEquipmentCategoryId(equipmentAccount.getCategoryId());

		Optional.ofNullable(equipmentAccount.getCategoryId())
			.map(commonClient::getEquipmentCategory)
			.map(R::getData)
			.map(DeviceCategory::getCategoryName)
			.ifPresent(faultDefectCaseVO::setEquipmentCategoryName);


		faultDefectCaseVO.setMonitorId(repair.getMonitorId());
		faultDefectCaseVO.setMonitorName(repair.getMonitorName());
		// EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(repair.getMonitorId());
		// if (Func.isNotEmpty(equipmentMonitor)) {
		// 	faultDefectCaseVO.setMonitorName(equipmentMonitor.getName());
		// }
		// 来源：1.自定义新增维修单；2.故障缺陷单转维修单；3.计划性检修单异常上报时填写的内容
		faultDefectCaseVO.setFaultType(repair.getRepairType());
		faultDefectCaseVO.setFaultName(repair.getFaultName());
		faultDefectCaseVO.setFaultLevel(repair.getFaultLevel());
		// 来源：1.异常上报时填写的异常描述；2.自定义新增维修填写的故障描述；3.检修工单上报异常时填写的故障缺陷描述
		faultDefectCaseVO.setFaultDesc(repair.getProblemComment());
		// 来源：1.维修工单上报时 2.检修工单上报时填写的故障缺陷原因
		if (Func.isNotEmpty(repair.getRepairRecord())) {
			faultDefectCaseVO.setFaultReason(repair.getRepairRecord().getFaultReason());
			// 来源：1.维修工单上报时 2.检修工单上报时填写的解决方案
			faultDefectCaseVO.setSolution(repair.getRepairRecord().getSolution());
		}
		return faultDefectCaseVO;
	}

	public RepairDTO entityDTO(Repair repair) {
		RepairDTO repairDTO = Objects.requireNonNull(BeanUtil.copy(repair, RepairDTO.class));
		// 报修人
		if (Func.isNotEmpty(repair.getReportUser())) {
			User reportUser = UserCache.getUser(repair.getReportUser());
			if (Func.isNotEmpty(reportUser)) {
				repairDTO.setReportUserName(reportUser.getRealName());
			}
		}
		// 报修部门
		if (Func.isNotEmpty(repair.getReportDept())) {
			Dept dept = SysCache.getDept(repair.getReportDept());
			if (Func.isNotEmpty(dept)) {
				repairDTO.setReportDeptName(dept.getDeptName());
			}
		}
		// 维修人
		if (Func.isNotEmpty(repair.getReceiveUser())) {
			User receiveUser = UserCache.getUser(repair.getReceiveUser());
			if (Func.isNotEmpty(receiveUser)) {
				repairDTO.setReceiveUserName(receiveUser.getRealName());
			}
		}
		// 维修部门
		if (Func.isNotEmpty(repair.getReceiveDept())) {
			User receiveUser = UserCache.getUser(repair.getReceiveUser());
			if (Func.isNotEmpty(receiveUser)) {
				repairDTO.setReceiveUserName(receiveUser.getRealName());
			}
		}
		// 跟进人
		if (Func.isNotEmpty(repair.getFollowUser())) {
			User followUser = UserCache.getUser(repair.getFollowUser());
			if (Func.isNotEmpty(followUser)) {
				repairDTO.setFollowUserName(followUser.getRealName());
			}
		}
		// 跟进人
		if (Func.isNotEmpty(repair.getFollowDept())) {
			User followUser = UserCache.getUser(repair.getFollowUser());
			if (Func.isNotEmpty(followUser)) {
				repairDTO.setFollowUserName(followUser.getRealName());
			}
		}
		// 派单人
		if (Func.isNotEmpty(repair.getDispatchUser())) {
			User dispatchUser = UserCache.getUser(repair.getDispatchUser());
			if (Func.isNotEmpty(dispatchUser)) {
				repairDTO.setDispatchUserName(dispatchUser.getRealName());
			}
		}
		// 创建人
		User createUser = UserCache.getUser(repair.getCreateUser());
		if (Func.isNotEmpty(createUser)) {
			repairDTO.setCreateUserName(createUser.getRealName());
		}
		// 修改人
		User updateUser = UserCache.getUser(repair.getUpdateUser());
		if (Func.isNotEmpty(updateUser)) {
			repairDTO.setUpdateUserName(updateUser.getRealName());
		}
		// 故障等级
		if (Func.isNotEmpty(repair.getFaultLevel())) {
			repairDTO.setFaultLevelName(DefectLevelEnum.getByCode(repair.getFaultLevel()).getName());
		}
		// 使用部门
		repairDTO.setRepairTypeName(DictBizCache.getValue(DictBizEnum.REPAIR_TYPE, repair.getRepairType()))
			.setSourceName(DictBizCache.getValue(DictBizEnum.REPAIR_SOURCE, repair.getSource()))
			.setStatusName(OrderStatusEnum.getByCode(repair.getStatus()).getName())
			.setBizTypeName(RepairBizTypeEnum.getByCode(repair.getBizType()).getName());
		return repairDTO;
	}

	public List<RepairDTO> listDTO(List<Repair> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
