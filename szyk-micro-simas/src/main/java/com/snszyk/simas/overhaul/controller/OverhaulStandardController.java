 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.overhaul.controller;

 import com.alibaba.excel.EasyExcel;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.listener.OverhaulStandardListener;
 import com.snszyk.simas.common.excel.support.EasyExcelUtil;
 import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
 import com.snszyk.simas.common.excel.template.OverhaulStandardExcelTemplate;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
 import com.snszyk.simas.common.vo.EquipmentStandardVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.inspect.service.IInspectStandardService;
 import com.snszyk.simas.maintain.service.IMaintainStandardService;
 import com.snszyk.simas.overhaul.service.IOverhaulStandardService;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import org.springframework.web.multipart.MultipartFile;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.io.IOException;
 import java.util.ArrayList;
 import java.util.LinkedHashMap;
 import java.util.List;
 import java.util.Map;
 import java.util.stream.Collectors;

 /**
  * 设备检修标准表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/overhaul-standard")
 @Api(value = "设备检修标准表", tags = "设备检修标准表接口")
 public class OverhaulStandardController extends SzykController {

	 private final IOverhaulStandardService overhaulStandardService;
	 private final IOperateLogService operateLogService;
	 private ImportDataValidLogicService importDataValidLogicService;
	 private final IMaintainStandardService maintainStandardService;
	 private final IInspectStandardService stdService;

	 /**
	  * 自定义分页 设备点巡检标准表
	  */
	 @GetMapping("/devicePage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "useDept", value = "使用部门id", required = true, paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "code", value = "设备编号", required = true, paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "设备的分页", notes = "传入useDept")
	 public R<IPage<DeviceAccountVO>> devicePage(@ApiIgnore DeviceAccountVO deviceAccount, Query query) {
		 return R.data(overhaulStandardService.devicePage(Condition.getPage(query), deviceAccount));
	 }

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入equipmentId")
	 public R<EquipmentStandardVO> detail(Long equipmentId) {
		 OperateLogVO operateLog = new OperateLogVO(equipmentId, SystemModuleEnum.OVERHAUL_STANDARD, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(overhaulStandardService.detail(equipmentId));
	 }

	 /**
	  * 新增或修改 设备检修标准表
	  */
	 @PostMapping("/submit")
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "新增或修改", notes = "传入equipmentStandard")
	 public R submit(@Valid @RequestBody EquipmentStandardVO equipmentStandard) {
		 return R.status(overhaulStandardService.submit(equipmentStandard));
	 }

	 /**
	  * 清空标准 设备检修标准表
	  */
	 @PostMapping("/clear")
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "清空标准", notes = "传入equipmentId")
	 public R clear(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId) {
		 return R.status(overhaulStandardService.clear(equipmentId));
	 }

	 @GetMapping("/export-template")
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "下载导入模版")
	 public void exportEquipmentAccount(HttpServletResponse response, @RequestParam(required = false) String deptId) {
		 List<BaseStandardTemplate> list = maintainStandardService.generateExcelData(deptId);
		 Map<String, Long> map = list.stream().collect(Collectors.groupingBy(BaseStandardTemplate::getCode, Collectors.counting()));
		 LinkedHashMap<String, Integer> mergeMap = new LinkedHashMap<>();
		 List<OverhaulStandardExcelTemplate> templateList = new ArrayList<>();
		 for (BaseStandardTemplate account : list) {
			 templateList.add(BeanUtil.copyProperties(account, OverhaulStandardExcelTemplate.class));
			 if (!mergeMap.containsKey(account.getCode())) {
				 mergeMap.put(account.getCode(), map.get(account.getCode()).intValue());
			 }
		 }
		 EasyExcelUtil.writerTwo(response, "检修标准导入模板", "检修标准", templateList, OverhaulStandardExcelTemplate.class, mergeMap, new int[]{0, 1, 2}, 2);
	 }

	 @PostMapping("/import-data")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "导入标准", notes = "传入excel")
	 public R importData(MultipartFile file) {
		 OverhaulStandardListener listener = new OverhaulStandardListener(2, overhaulStandardService, importDataValidLogicService);
		 try {//监听器读取内容
			 EasyExcel.read(file.getInputStream(), OverhaulStandardExcelTemplate.class, listener)
				 .sheet()
				 .headRowNumber(2) // 忽略表头行数
				 .doRead();
		 } catch (IOException e) {
			 e.printStackTrace();
		 }
		 List<String> errorMsg = listener.getFailReasonList();
		 if (Func.isNotEmpty(errorMsg)) {
			 return R.data(errorMsg, "导入异常！");
		 }
		 return R.success("导入成功");
	 }
 }
