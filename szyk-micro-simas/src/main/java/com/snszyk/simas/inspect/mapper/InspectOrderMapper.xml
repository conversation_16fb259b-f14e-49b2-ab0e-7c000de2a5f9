<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inspect.mapper.InspectOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectOrderResultMap" type="com.snszyk.simas.inspect.entity.InspectOrder">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="standard_info" property="standardInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="remark" property="remark"/>
        <result column="submit_time" property="submitTime"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_sn" property="equipmentSn"/>
        <result column="equipment_category" property="equipmentCategory"/>
    </resultMap>

    <resultMap id="inspectOrderDTOResultMap" type="com.snszyk.simas.inspect.dto.InspectOrderDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="remark" property="remark"/>
        <result column="submit_time" property="submitTime"/>
        <result column="equipment_category" property="equipmentCategory"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_sn" property="equipmentSn"/>
    </resultMap>


    <select id="page" resultMap="inspectOrderDTOResultMap">
        select o.*,d.`name` as equipment_name,d.`sn` as equipment_sn
        from simas_inspect_order o left join device_account d on o.equipment_id = d.id and d.is_deleted = 0
        left join simas_inspect_plan p on o.plan_id = p.id and p.is_deleted = 0
        where o.is_deleted = 0
        <if test="inspectOrder.onlyQueryExecuteUser!=null">
            <choose>
                <when test="inspectOrder.onlyQueryExecuteUser == 0">
                    and o.execute_user is null
                </when>
                <otherwise>
                    and o.execute_user = #{inspectOrder.onlyQueryExecuteUser}
                </otherwise>
            </choose>
        </if>
        <if test="inspectOrder.executeDept != null">
            <choose>
                <when test="inspectOrder.executeDept==0">
                    and o.execute_dept is null
                </when>
                <otherwise>
                    and o.execute_dept = #{inspectOrder.executeDept}
                </otherwise>
            </choose>
        </if>
        <if test="inspectOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{inspectOrder.executeUser})
        </if>
        <if test="inspectOrder.no != null and inspectOrder.no != ''">
            and o.`no` like concat('%',#{inspectOrder.no},'%')
        </if>
        <if test="inspectOrder.planId != null">
            and o.plan_id=#{inspectOrder.planId}
        </if>
        <if test="inspectOrder.orderName != null and inspectOrder.orderName != ''">
            and p.name like concat('%',#{inspectOrder.orderName},'%')
        </if>
        <if test="inspectOrder.equipmentId != null">
            and o.equipment_id = #{inspectOrder.equipmentId}
        </if>
        <if test="inspectOrder.equipmentCode != null and inspectOrder.equipmentCode != ''">
            and o.equipment_code = #{inspectOrder.equipmentCode}
        </if>
        <if test="inspectOrder.equipmentName != null and inspectOrder.equipmentName != ''">
            and d.`name` like concat('%',#{inspectOrder.equipmentName},'%')
        </if>
        <if test="inspectOrder.status != null">
            and o.`status` = #{inspectOrder.status}
        </if>
        <if test="inspectOrder.isAbnormal != null">
            and o.is_abnormal = #{inspectOrder.isAbnormal}
        </if>
        <if test="inspectOrder.keywords != null and inspectOrder.keywords != ''">
            and (o.`no` like concat('%',#{inspectOrder.keywords},'%') or
            d.`sn` like concat('%',#{inspectOrder.keywords},'%') or d.`name` like
            concat('%',#{inspectOrder.keywords},'%')
            or d.`code` like concat('%',#{inspectOrder.keywords},'%'))
        </if>
        <if test="inspectOrder.deptIdList != null">
            and o.execute_dept in
            <foreach collection="inspectOrder.deptIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <if test="inspectOrder.startDate != null and inspectOrder.startDate != ''">
            and o.start_time <![CDATA[ >= ]]> #{inspectOrder.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="inspectOrder.endDate != null and inspectOrder.endDate != ''">
            and o.start_time <![CDATA[ <= ]]> #{inspectOrder.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="inspectOrder.neStatus != null">
            and o.status <![CDATA[ <> ]]> #{inspectOrder.neStatus}
        </if>
        <if test="inspectOrder.statusList != null and inspectOrder.statusList.size() > 0  ">
            and o.status in
            <foreach collection="inspectOrder.statusList" item="status" index="index" open="(" close=")"
                     separator=",">
                #{status}
            </foreach>
        </if>
        <if test="inspectOrder.clientType != null and inspectOrder.clientType == 'PC'">
            order by FIELD(o.`status`,1,3,2,4), o.create_time desc, o.id
        </if>
        <if test="inspectOrder.clientType == null">
            order by FIELD(o.`status`,3,1,2,4), o.create_time desc, o.start_time, d.sn
        </if>
    </select>

    <select id="exportList" resultMap="inspectOrderResultMap">
        select o.*,d.`name` as equipment_name,d.`sn` as equipment_sn
        from simas_inspect_order o left join device_account d on o.equipment_id = d.id
        where d.is_deleted = 0
        <if test="inspectOrder.executeDept != null">
            and o.execute_dept = #{inspectOrder.executeDept}
        </if>
        <if test="inspectOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{inspectOrder.executeUser})
        </if>
        <if test="inspectOrder.no != null and inspectOrder.no != ''">
            and o.`no` like concat('%',#{inspectOrder.no},'%')
        </if>
        <if test="inspectOrder.planId != null">
            and o.plan_id=#{inspectOrder.planId}
        </if>
        <if test="inspectOrder.orderName != null and inspectOrder.orderName != ''">
            and o.plan_info like concat('%',#{inspectOrder.orderName},'%')
        </if>
        <if test="inspectOrder.equipmentId != null">
            and o.equipment_id = #{inspectOrder.equipmentId}
        </if>
        <if test="inspectOrder.equipmentCode != null and inspectOrder.equipmentCode != ''">
            and o.equipment_code = #{inspectOrder.equipmentCode}
        </if>
        <if test="inspectOrder.equipmentName != null and inspectOrder.equipmentName != ''">
            and d.`name` like concat('%',#{inspectOrder.equipmentName},'%')
        </if>
        <if test="inspectOrder.status != null">
            and o.`status` = #{inspectOrder.status}
        </if>
        <if test="inspectOrder.isAbnormal != null">
            and o.is_abnormal = #{inspectOrder.isAbnormal}
        </if>
        <if test="inspectOrder.keywords != null and inspectOrder.keywords != ''">
            and (o.`no` like concat('%',#{inspectOrder.keywords},'%') or
            d.`sn` like concat('%',#{inspectOrder.keywords},'%') or d.`name` like
            concat('%',#{inspectOrder.keywords},'%')
            or d.`code` like concat('%',#{inspectOrder.keywords},'%'))
        </if>
        <if test="inspectOrder.deptIdList != null">
            and o.execute_dept in
            <foreach collection="inspectOrder.deptIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <if test="inspectOrder.startDate != null and inspectOrder.startDate != ''">
            and o.start_time <![CDATA[ >= ]]> #{inspectOrder.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="inspectOrder.endDate != null and inspectOrder.endDate != ''">
            and o.start_time <![CDATA[ <= ]]> #{inspectOrder.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="inspectOrder.clientType != null and inspectOrder.clientType == 'PC'">
            order by FIELD(o.`status`,1,3,2,4), o.create_time desc, o.id
        </if>
        <if test="inspectOrder.clientType == null">
            order by FIELD(o.`status`,3,1,2,4), o.create_time desc, o.start_time, d.sn
        </if>
    </select>

    <select id="timeoutPage" resultMap="inspectOrderResultMap">
        select o.*, d.`name` as equipment_name, d.`sn` as equipment_sn
        from simas_inspect_order o
                 left join device_account d on o.equipment_id = d.id
        where o.status = 1
          and o.execute_dept = #{inspectOrder.executeDept}
          and (o.execute_user is null or o.execute_user = #{inspectOrder.executeUser})
        <![CDATA[ AND NOW() >= DATE_SUB(o.end_time, INTERVAL #{inspectOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < o.end_time ]]>
        order by o.id desc
    </select>

    <select id="expireSoonCount" resultType="java.lang.Integer">
        select count(*)
        from simas_inspect_order
        where status = 1
          and execute_dept = #{inspectOrder.executeDept}
          and (execute_user is null or execute_user = #{inspectOrder.executeUser})
        <![CDATA[ AND NOW() >= DATE_SUB(end_time, INTERVAL #{inspectOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < end_time
        ]]>
    </select>

    <select id="inspectOrderStatistics" resultMap="inspectOrderResultMap">
        -- SELECT * FROM simas_inspect_order WHERE create_time BETWEEN DATE_FORMAT(NOW(),'%Y-%m-01') AND LAST_DAY(NOW())
        SELECT * FROM simas_inspect_order WHERE 1=1
        <if test="queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
    </select>

    <select id="statisticalReport" resultMap="inspectOrderResultMap">
        select o.*,d.`name` as equipment_name,d.`sn` as equipment_sn, d.category_id as equipment_category
        from simas_inspect_order o left join device_account d on o.equipment_id = d.id
        where d.is_deleted = 0
        <if test="search.queryDate == 1">
            AND o.start_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND o.start_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(o.start_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and o.start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and o.start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.deptIdList != null">
            and o.execute_dept in
            <foreach collection="search.deptIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <if test="search.status != null">
            AND o.status = #{search.status}
        </if>
        order by o.id desc
    </select>

    <select id="queryList" resultMap="inspectOrderDTOResultMap">
        select o.*, d.`name` as equipment_name, d.category_id as equipment_category from simas_inspect_order o
        left join device_account d on o.equipment_id = d.id
        where d.is_deleted = 0
        <if test="inspectOrder.executeDept != null">
            and o.execute_dept = #{inspectOrder.executeDept}
        </if>
        <if test="inspectOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{inspectOrder.executeUser})
        </if>
        <if test="inspectOrder.no != null and inspectOrder.no != ''">
            and o.`no` like concat('%',#{inspectOrder.no},'%')
        </if>
        <if test="inspectOrder.orderName != null and inspectOrder.orderName != ''">
            and o.plan_info like concat('%',#{inspectOrder.orderName},'%')
        </if>
        <if test="inspectOrder.equipmentId != null">
            and o.equipment_id = #{inspectOrder.equipmentId}
        </if>
        <if test="inspectOrder.equipmentCode != null and inspectOrder.equipmentCode != ''">
            and o.equipment_code = #{inspectOrder.equipmentCode}
        </if>
        <if test="inspectOrder.status != null">
            and o.status = #{inspectOrder.status}
        </if>
    </select>

    <select id="statisticsByEquipment" resultType="com.snszyk.simas.common.dto.EquipmentStatisticsDTO">
        SELECT equipment_id as id,status,is_abnormal FROM `simas_inspect_order` where 1=1
        <if test="search.queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
<!--        SELECT equipment_id as id, count(*) as count FROM `simas_inspect_order` where 1=1-->
<!--        <if test="search.queryDate == 1">-->
<!--            AND start_time >= CURDATE() - INTERVAL 30 DAY-->
<!--        </if>-->
<!--        <if test="search.queryDate == 2">-->
<!--            AND start_time >= CURDATE() - INTERVAL 7 DAY-->
<!--        </if>-->
<!--        <if test="search.queryDate == 3">-->
<!--            AND TO_DAYS(start_time) = TO_DAYS(NOW())-->
<!--        </if>-->
<!--        <if test="search.startDate != null and search.startDate != ''">-->
<!--            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--        <if test="search.endDate != null and search.endDate != ''">-->
<!--            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--        <if test="search.equipmentIds != null">-->
<!--            and equipment_id in-->
<!--            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">-->
<!--                #{ids}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="search.status != null">-->
<!--            AND status = #{search.status}-->
<!--        </if>-->
<!--        group by equipment_id-->
    </select>

    <select id="exportStatisticsByEquipment" resultType="com.snszyk.simas.common.dto.EquipmentStatisticsDTO">
        SELECT equipment_id as id, count(*) as count FROM `simas_inspect_order` where 1=1
        <if test="search.queryDate == 0">
            AND start_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND start_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND start_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(start_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        group by equipment_id
    </select>

    <select id="equipmentStatisticsOfOrder" resultMap="inspectOrderResultMap">
        SELECT * FROM simas_inspect_order where 1=1
        <if test="search.queryDate == 0">
            AND start_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND start_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND start_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(start_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
    </select>

    <select id="overdueList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        select t.no as orderNo, e.name as equipmentName, t.equipment_id
        from simas_inspect_order t
        left join simas_equipment_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.status = 3
        <if test="tenantId != null and tenantId!=''">
            and t.tenant_id = #{tenantId}
        </if>
        order by t.create_time desc
    </select>

    <select id="inspectToday" resultType="com.snszyk.core.tool.support.Kv">
        select t.status, count(1) as count
        from simas_inspect_order t
        where TO_DAYS(t.create_time) = TO_DAYS(NOW())
    </select>

    <select id="coverListToday" resultType="com.snszyk.simas.inspect.dto.InspectOrderDTO">
        select t3.id,
        t3.tenant_id,
        t3.no,
        t3.equipment_id,
        t3.status,
        t3.create_time,
        t3.execute_dept,
        t.id as analysis_dept_id
        from szyk_dept t
        left join szyk_dept t2 on find_in_set(t.id, t2.ancestors) or t.id = t2.id and t2.is_deleted = 0
        left join simas_inspect_order t3
        on t2.id = t3.execute_dept and TO_DAYS(t3.create_time) = TO_DAYS(now())
        where t.is_deleted = 0
        and t3.equipment_id is not null
        and t.parent_id!=0
        <if test="tenantId != null and tenantId !=''">
            and t.tenant_id = #{tenantId}
        </if>
    </select>
    <select id="handleInspectCount" resultType="java.lang.Integer"
            parameterType="com.snszyk.simas.inspect.vo.InspectOrderVO">
        select count(1) from simas_inspect_order o
        <where>
            <if test="inspectOrder.queryAuthRole != null and inspectOrder.queryAuthRole == 1">
                and (o.execute_user = #{inspectOrder.executeUser} or ( o.execute_dept = #{inspectOrder.executeDept} and
                o.execute_user is null))
            </if>
            <if test="inspectOrder.queryAuthRole != null and inspectOrder.queryAuthRole == 2">
                and o.execute_dept = #{inspectOrder.executeDept} and o.execute_user is null
            </if>

            <if test="inspectOrder.statusList != null and inspectOrder.statusList.size() > 0  ">
                and o.status in
                <foreach collection="inspectOrder.statusList" item="status" index="index" open="(" close=")"
                         separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="inspectOrder.tenantId != null and inspectOrder.tenantId !=''">
                and o.tenant_id = #{inspectOrder.tenantId}
            </if>
            <if test="inspectOrder.startDate != null and inspectOrder.startDate != ''">
                and o.create_time <![CDATA[ >= ]]> #{inspectOrder.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="inspectOrder.endDate != null and inspectOrder.endDate != ''">
                and o.create_time <![CDATA[ <= ]]> #{inspectOrder.endDate, jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

</mapper>
