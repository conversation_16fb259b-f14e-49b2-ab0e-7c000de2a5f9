<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inspect.mapper.InspectPlanEquipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectPlanEquipmentResultMap" type="com.snszyk.simas.inspect.entity.InspectPlanEquipment">
        <id column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <resultMap id="planDeviceVOResultMap" type="com.snszyk.simas.common.vo.DeviceInfoVO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="code" property="code"/>
        <result column="sn" property="sn"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_path" property="categoryPath"/>
        <result column="important_level" property="importantLevel"/>
        <result column="process_category" property="processCategory"/>
        <result column="measure_unit" property="measureUnit"/>
        <result column="use_dept" property="useDept"/>
        <result column="user_id" property="userId"/>
        <result column="purchase_date" property="purchaseDate"/>
        <result column="product_date" property="productDate"/>
        <result column="location_id" property="locationId"/>
        <result column="supplier" property="supplier"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="planDevicePage" resultMap="planDeviceVOResultMap">
        SELECT * FROM device_account
        WHERE
                id IN ( SELECT DISTINCT equipment_id FROM `simas_inspect_plan_equipment` WHERE plan_id = #{equipment.planId} )
        ORDER BY
            create_time DESC
    </select>

</mapper>
