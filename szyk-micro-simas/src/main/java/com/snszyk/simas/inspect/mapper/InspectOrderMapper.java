/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.vo.InspectOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点巡检工单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface InspectOrderMapper extends BaseMapper<InspectOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectOrder
	 * @return
	 */
	List<InspectOrder> page(IPage page, @Param("inspectOrder") InspectOrderVO inspectOrder);

	/**
	 * 导出列表
	 *
	 * @param inspectOrder
	 * @return
	 */
	List<InspectOrder> exportList(@Param("inspectOrder") InspectOrderVO inspectOrder);

	/**
	 * 即将超时分页
	 *
	 * @param page
	 * @param inspectOrder
	 * @return
	 */
	List<InspectOrder> timeoutPage(IPage page, @Param("inspectOrder") InspectOrderVO inspectOrder);

	/**
	 * 即将超时工单数量
	 *
	 * @param inspectOrder
	 * @return
	 */
	Integer expireSoonCount(@Param("inspectOrder") InspectOrderVO inspectOrder);

	/**
	 * 统计-工单完成情况
	 *
	 * @param queryDate
	 * @return
	 */
	List<InspectOrder> inspectOrderStatistics(@Param("queryDate")Integer queryDate);

	/**
	 * 统计报表-点巡检统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<InspectOrder> statisticalReport(IPage page, @Param("search") StatisticSearchVO search);

	/**
	 * 点巡检工单列表
	 *
	 * @param inspectOrder
	 * @return
	 */
	List<InspectOrderDTO> queryList(@Param("inspectOrder") InspectOrderVO inspectOrder);

	/**
	 * 统计报表-点巡检按设备统计
	 *
	 * @param search
	 * @return
	 */
	List<EquipmentStatisticsDTO> statisticsByEquipment(@Param("search") StatisticSearchVO search);

	/**
	 * 统计报表-点巡检按设备统计导出
	 *
	 * @param search
	 * @return
	 */
	List<EquipmentStatisticsDTO> exportStatisticsByEquipment(@Param("search") StatisticSearchVO search);

	/**
	 * 工单统计
	 *
	 * @param search
	 * @return
	 */
	List<InspectOrder> equipmentStatisticsOfOrder(@Param("search") StatisticSearchVO search);

	/**
	 *
	 *
	 * @param tenantId
	 * @return
	 */
	List<BigScreenMessageDTO> overdueList(@Param("tenantId") String tenantId);

	/**
	 *
	 *
	 * @return
	 */
	List<Kv> inspectToday();

	/**
	 *
	 *
	 * @param tenantId
	 * @return
	 */
	List<InspectOrderDTO> coverListToday(@Param("tenantId") String tenantId);

	/**
	 *
	 *
	 * @param inspectOrder
	 * @return
	 */
	Integer handleInspectCount(@Param("inspectOrder") InspectOrderVO inspectOrder);


}
