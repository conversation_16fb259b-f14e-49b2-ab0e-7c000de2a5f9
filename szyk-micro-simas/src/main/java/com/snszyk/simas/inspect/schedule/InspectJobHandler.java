 package com.snszyk.simas.inspect.schedule;

 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.StringPool;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.service.ITimeoutRemindSetService;
 import com.snszyk.simas.common.util.ApprovalUtil;
 import com.snszyk.simas.common.vo.ByDaySetVO;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.entity.InspectOrder;
 import com.snszyk.simas.inspect.entity.InspectPlan;
 import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
 import com.snszyk.simas.inspect.entity.InspectStandard;
 import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
 import com.snszyk.simas.inspect.mapper.InspectPlanMapper;
 import com.snszyk.simas.inspect.mapper.InspectStandardMapper;
 import com.snszyk.simas.inspect.service.IInspectOrderService;
 import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
 import com.snszyk.simas.inspect.service.IInspectPlanService;
 import com.snszyk.simas.inspect.vo.InspectOrderVO;
 import com.snszyk.simas.spare.schedule.SimasPlanSchedule;
 import com.xxl.job.core.biz.model.ReturnT;
 import com.xxl.job.core.handler.annotation.XxlJob;
 import com.xxl.job.core.log.XxlJobLogger;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Component;

 import java.math.BigDecimal;
 import java.util.ArrayList;
 import java.util.Date;
 import java.util.List;
 import java.util.Objects;
 import java.util.stream.Collectors;

 /**
  * XxlJob开发示例（Bean模式）
  * <p>
  * 开发步骤：
  * 1、在Spring Bean实例中，开发Job方法，方式格式要求为 "public ReturnT<String> execute(String param)"
  * 2、为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
  * 3、执行日志：需要通过 "XxlJobLogger.log" 打印执行日志；
  *
  * <AUTHOR>
  */
 @Slf4j
 @AllArgsConstructor
 @Component
 public class InspectJobHandler {

 	private final IInspectPlanService planService;
 	private final IInspectOrderService orderService;
 	private final ITimeoutRemindSetService timeoutRemindSetService;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final InspectPlanMapper planMapper;
	 private final IInspectPlanEquipmentService planEquipmentService;
	 private final InspectStandardMapper inspectStandardMapper;
	 private final InspectOrderMapper inspectOrderMapper;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;

 	/**
 	 * 日检、周检、月检（每日07:00进行）
 	 *
 	 * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
 	 * <AUTHOR>
 	 * @date 2025/3/17 9:56
 	 */
 	@XxlJob("generateInspectOrdersJobHandler")
 	public ReturnT<String> generateOrdersJobHandler(String param) {
 		XxlJobLogger.log("################点巡检任务（日检、周检、月检）生成-START-################");
 		List<InspectPlanDTO> planList = planService.getTheDayPlans(DateUtil.now());
 		List<InspectPlanDTO> executePlanList = new ArrayList<>();
 		if (Func.isNotEmpty(planList)) {
 			for (InspectPlanDTO plan : planList) {
 				// 生成点检任务时间判断
 				String cycleType = plan.getCycleType();
 				Integer cycleInterval = plan.getCycleInterval();
 				String cycleExecuteTimeStr = plan.getExecuteTime();
 				boolean isExecuted;
				// 确认当前计划第一次生成工单的日期
				InspectOrder order = orderService.getOne(Wrappers.<InspectOrder>query().lambda()
					.eq(InspectOrder::getPlanId, plan.getId()).orderByAsc(InspectOrder::getCreateTime).last("limit 1"));
				long actualStartDate = plan.getStartDate().getTime();
				if(Func.isNotEmpty(order)){
					actualStartDate = order.getCreateTime().getTime();
				}
 				if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(cycleType)) {
 					isExecuted = SimasPlanSchedule.executeDay(actualStartDate, plan.getEndDate().getTime(), cycleInterval);
 					if (isExecuted) {
						log.info("点检任务——日检工单生成：{}", cycleExecuteTimeStr);
 						executePlanList.add(plan);
 					}
 				}
 				if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(cycleType)) {
 					List<String> list = Func.toStrList(cycleExecuteTimeStr);
 					for (String cycleExecuteTime : list) {
 						isExecuted = SimasPlanSchedule.executeWeek(plan.getStartDate().getTime(),
 							plan.getEndDate().getTime(), cycleInterval, cycleExecuteTime);
 						if (isExecuted) {
							log.info("点检任务——周检工单生成：{}", cycleExecuteTimeStr);
 							executePlanList.add(plan);
 						}
 					}
 				}
 				if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(cycleType)) {
 					List<String> list = Func.toStrList(cycleExecuteTimeStr);
 					for (String cycleExecuteTime : list) {
 						isExecuted = SimasPlanSchedule.executeMonth(actualStartDate, plan.getEndDate().getTime(),
							cycleInterval, cycleExecuteTime);
 						if (isExecuted) {
							log.info("点检任务——月检工单生成：{}", cycleExecuteTimeStr);
 							executePlanList.add(plan);
 						}
 					}
 				}
 			}
		}
		if (Func.isNotEmpty(executePlanList)) {
			log.info("需要执行的计划：{}", executePlanList.stream().map(dto ->
				dto.getId() + StringPool.COLON + dto.getNo()).collect(Collectors.toList()));
			this.generateOrders(executePlanList);
		}
 		XxlJobLogger.log("################点巡检任务（日检、周检、月检）生成-END-################");
 		return ReturnT.SUCCESS;
 	}

	 /**
	  * 生成点巡检工单
	  *
	  * @param planList
	  * @return boolean
	  * <AUTHOR>
	  * @date 2025/3/20 09:56
	  */
	 public boolean generateOrders(List<InspectPlanDTO> planList) {
		 boolean ret = Boolean.FALSE;
		 List<InspectOrder> orderList = new ArrayList<>();
		 for (InspectPlanDTO plan : planList) {
			 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.INSPECT_ORDER.name(), plan.getTenantId());
			 List<InspectPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<InspectPlanEquipment>query().lambda()
				 .eq(InspectPlanEquipment::getPlanId, plan.getId()).orderByAsc(InspectPlanEquipment::getSort));
			 for(InspectPlanEquipment planEquipment : planEquipmentList){
				 log.info("计划：{}，设备：{}", planEquipment.getPlanId(), planEquipment.getEquipmentId());
				 // 正在维修的设备不再生成工单
				 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
				 if (!deviceAccountResult.isSuccess()) {
					 log.error(String.format("查询设备信息%s失败", planEquipment.getEquipmentId()));
					 return false;
				 }
				 if (Func.isNotEmpty(deviceAccountResult.getData())) {
					 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
					 log.info("设备：{}，状态：{}", equipmentAccount.getId(), equipmentAccount.getStatus());
					 if (EquipmentStatusEnum.IN_USE == EquipmentStatusEnum.getByCode(equipmentAccount.getStatus())) {
						 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
							 List<ByDaySetVO> list = JSONUtil.toList(plan.getExecuteTime(), ByDaySetVO.class);
							 for (ByDaySetVO daySet : list) {
								 InspectOrderVO inspectOrder = new InspectOrderVO(plan, planEquipment.getEquipmentId(),
									 plan.getExecuteDept(), plan.getExecuteUser());
								 inspectOrder.setEquipmentCode(equipmentAccount.getCode());
								 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + daySet.getStartTime() + ":00";
								 String endTime;
								 String[] startTimeItems = daySet.getStartTime().split(StringPool.COLON);
								 String[] endTimeItems = daySet.getEndTime().split(StringPool.COLON);
								 if (Func.toInt(startTimeItems[0]) <= Func.toInt(endTimeItems[0])) {
									 // 未跨天
									 endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + daySet.getEndTime() + ":00";
								 } else {
									 // 跨天
									 endTime = DateUtil.format(DateUtil.plusDays(DateUtil.now(), 1), DateUtil.PATTERN_DATE) + " " + daySet.getEndTime() + ":00";
								 }
								 inspectOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
								 inspectOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
								 inspectOrder.setIsNeedApproval(needApproval);
								 List<InspectStandard> inspectStandardList = inspectStandardMapper.selectList(Wrappers.<InspectStandard>query().lambda()
									 .eq(InspectStandard::getEquipmentId, planEquipment.getEquipmentId()));
								 if (Func.isNotEmpty(inspectStandardList)) {
									 inspectOrder.setStandardInfo(JSONUtil.toJsonStr(inspectStandardList));
									 orderList.add(Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrder.class)));
								 }
							 }
						 } else {
							 InspectOrderVO inspectOrder = new InspectOrderVO(plan, planEquipment.getEquipmentId(),
								 plan.getExecuteDept(), plan.getExecuteUser());
							 inspectOrder.setEquipmentCode(equipmentAccount.getCode());
							 inspectOrder.setPlanInfo(JSONUtil.toJsonStr(plan));
							 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
							 Date endDate = DateUtil.now();
							 if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(plan.getCycleType())) {
								 endDate = DateUtils.getSunDay(DateUtil.now());
							 }
							 if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(plan.getCycleType())) {
								 endDate = DateUtils.getLastDayOfMonth(DateUtil.now());
							 }
							 String endTime = DateUtil.format(endDate, DateUtil.PATTERN_DATE) + " 23:59:59";
							 inspectOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
							 inspectOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
							 inspectOrder.setIsNeedApproval(needApproval);
							 List<InspectStandard> inspectStandardList = inspectStandardMapper.selectList(Wrappers.<InspectStandard>query().lambda()
								 .eq(InspectStandard::getEquipmentId, planEquipment.getEquipmentId()));
							 if (Func.isNotEmpty(inspectStandardList)) {
								 inspectOrder.setStandardInfo(JSONUtil.toJsonStr(inspectStandardList));
								 orderList.add(Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrder.class)));
							 }
						 }
					 }
				 }
			 }
			 InspectPlan p = planMapper.selectById(plan.getId());
			 // 更新计划状态（未开始 —> 执行中）
			 if (PlanStatusEnum.NO_START == PlanStatusEnum.getByCode(p.getStatus())) {
				 p.setStatus(PlanStatusEnum.IN_PROGRESS.getCode());
				 p.setUpdateTime(DateUtil.now());
				 planMapper.updateById(p);
			 }
			 // 更新计划状态（执行中 —> 已完成）
			 if (PlanStatusEnum.IN_PROGRESS == PlanStatusEnum.getByCode(p.getStatus())
				 && Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
				 DateUtil.format(plan.getEndDate(), DateUtil.PATTERN_DATE))) {
				 p.setStatus(PlanStatusEnum.IS_COMPLETED.getCode());
				 p.setUpdateTime(DateUtil.now());
				 planMapper.updateById(p);
			 }
		 }
		 log.info("点巡检计划：{}，需要生成的工单：{}", orderList);
		 if (Func.isNotEmpty(orderList)) {
			 ret = orderService.saveBatch(orderList);
			 // 业务日志
			 orderList.forEach(order -> {
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
			 });
			 // 消息提醒
			 orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_INSPECT_ADD);
		 }
		 return ret;
	 }

 	/**
 	 * 工单超时(每分钟执行一次)
 	 *
 	 * @return void
 	 * <AUTHOR>
 	 * @date 2025/3/17 11:31
 	 */
 	@XxlJob("inspectOrderOverdueJobHandler")
 	public ReturnT<String> orderOverdueJobHandler(String param) {
 		XxlJobLogger.log("################点巡检工单超期定时任务-START-################");
 		List<InspectOrder> list = orderService.list(Wrappers.<InspectOrder>query().lambda()
 			.eq(InspectOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
 		List<InspectOrder> orderList = new ArrayList<>();
 		if (Func.isNotEmpty(list)) {
 			for (InspectOrder order : list) {
 				InspectPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), InspectPlanDTO.class);
 				if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
 					String currentTime = DateUtil.format(DateUtil.now(), "yyyy-MM-dd HH:mm");
 					String endTime = DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm");
 					Date date1 = DateUtil.parse(currentTime, "yyyy-MM-dd HH:mm");
 					Date date2 = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm");
 					if (date1.after(date2)) {
 						orderList.add(order);
 					}
 				} else {
 					String endDate = DateUtil.format(order.getEndTime(), DateUtil.PATTERN_DATE);
 					Date endTime = DateUtil.parse(endDate + " 23:59:59", DateUtil.PATTERN_DATETIME);
 					if (DateUtil.now().after(endTime)) {
 						orderList.add(order);
 					}
 				}
 			}
 		}
 		if (Func.isNotEmpty(orderList)) {
 			orderService.update(Wrappers.<InspectOrder>update().lambda()
 				.set(InspectOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
 				.in(InspectOrder::getId, orderList.stream().map(InspectOrder::getId).collect(Collectors.toList())));
 			// 业务日志
			orderList.forEach(order -> {
				OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.OVERDUE);
			});
 			// 发送消息提醒
 			orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_INSPECT_OVERDUE);
 		}
 		XxlJobLogger.log("################点巡检工单超期定时任务-END-################");
		return ReturnT.SUCCESS;
 	}

 	/**
 	 * 即将超时(每分钟执行一次)
 	 *
 	 * @return void
 	 * <AUTHOR>
 	 * @date 2025/3/17 11:35
 	 */
 	@XxlJob("inspectOrderExpireSoonJobHandler")
 	public ReturnT<String> orderExpireSoonJobHandler(String param) {
 		XxlJobLogger.log("################点巡检工单即将超期发送消息-START-################");
 		List<InspectOrder> list = orderService.list(Wrappers.<InspectOrder>query().lambda()
 			.eq(InspectOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
 			.eq(InspectOrder::getIsExpired, 0));
 		List<InspectOrder> orderList = new ArrayList<>();
 		BigDecimal timeInterval = new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL);
 		if (Func.isNotEmpty(list)) {
 			for (InspectOrder order : list) {
 				if (Func.isNotEmpty(order.getExecuteUser())) {
 					TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
 						.eq(TimeoutRemindSet::getUserId, order.getExecuteUser())
 						.eq(TimeoutRemindSet::getBizType, BizTypeEnum.INSPECT.getCode()));
 					if (Func.isNotEmpty(timeoutRemindSet)) {
 						timeInterval = timeoutRemindSet.getTimeInterval();
 					}
 				}
 				Long seconds = order.getEndTime().getTime() - System.currentTimeMillis();
 				if (seconds <= timeInterval.multiply(new BigDecimal(3600)).multiply(new BigDecimal(1000)).longValue()) {
 					order.setIsExpired(1);
 					orderService.updateById(order);
 					orderList.add(order);
 				}
 			}
 		}
 		// 发送消息提醒
 		if (Func.isNotEmpty(orderList)) {
 			orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_INSPECT_EXPIRE);
 		}
 		XxlJobLogger.log("################点巡检工单即将超期发送消息-END-################");
		return ReturnT.SUCCESS;
 	}


 }
