/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.vo.DeviceInfoVO;
import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点巡检计划关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface InspectPlanEquipmentMapper extends BaseMapper<InspectPlanEquipment> {

	/**
	 * 点检计划详情设备分页
	 *
	 * @param page
	 * @param deviceInfo
	 * @return
	 */
	List<DeviceInfoVO> planDevicePage(IPage page, @Param("equipment") DeviceInfoVO deviceInfo);


}
