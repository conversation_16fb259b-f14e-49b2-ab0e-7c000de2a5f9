/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.inspect.dto.InspectPlanDTO;
import com.snszyk.simas.inspect.entity.InspectPlan;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.common.enums.PlanStatusEnum;
import com.snszyk.simas.common.enums.WeekDateEnum;
import com.snszyk.simas.common.vo.ByDaySetVO;
import com.snszyk.simas.inspect.vo.InspectPlanVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备点巡检计划表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class InspectPlanWrapper extends BaseEntityWrapper<InspectPlan, InspectPlanVO> {

	public static InspectPlanWrapper build() {
		return new InspectPlanWrapper();
 	}

	@Override
	public InspectPlanVO entityVO(InspectPlan inspectPlan) {
		InspectPlanVO inspectPlanVO = Objects.requireNonNull(BeanUtil.copy(inspectPlan, InspectPlanVO.class));

		//User createUser = UserCache.getUser(inspectPlan.getCreateUser());
		//User updateUser = UserCache.getUser(inspectPlan.getUpdateUser());
		//inspectPlanVO.setCreateUserName(createUser.getName());
		//inspectPlanVO.setUpdateUserName(updateUser.getName());

		return inspectPlanVO;
	}

	public InspectPlanDTO entityDTO(InspectPlan inspectPlan) {
		InspectPlanDTO inspectPlanDTO = Objects.requireNonNull(BeanUtil.copy(inspectPlan, InspectPlanDTO.class));
		inspectPlanDTO.setCycleTypeName(DictBizCache.getValue(DictBizEnum.PLAN_CYCLE, inspectPlan.getCycleType()))
			.setStatusName(PlanStatusEnum.getByCode(inspectPlan.getStatus()).getName());
		if(Func.isNotEmpty(inspectPlan.getExecuteDept())){
			Dept executeDept = SysCache.getDept(inspectPlan.getExecuteDept());
			if(Func.isNotEmpty(executeDept)){
				inspectPlanDTO.setExecuteDeptName(executeDept.getDeptName());
			}
		}
		if(Func.isNotEmpty(inspectPlan.getExecuteUser())){
			User executeUser = UserCache.getUser(inspectPlan.getExecuteUser());
			if(Func.isNotEmpty(executeUser)){
				inspectPlanDTO.setExecuteUserName(executeUser.getRealName());
			}
		}
		// 时间设置
		switch (PlanCycleEnum.getByCode(inspectPlan.getCycleType())){
			case DAY:
				inspectPlanDTO.setByDaySet(JSONUtil.toList(inspectPlan.getExecuteTime(), ByDaySetVO.class));
				break;
			case WEEK:
				inspectPlanDTO.setByWeekSet(Func.toStrList(inspectPlan.getExecuteTime()));
				inspectPlanDTO.setExecuteTimeStr(Func.toStrList(inspectPlan.getExecuteTime()).stream()
					.map(date -> WeekDateEnum.getByCode(date).getName()).collect(Collectors.joining(",")));
				break;
			case MONTH:
				inspectPlanDTO.setByMonthSet(Func.toStrList(inspectPlan.getExecuteTime()))
					.setExecuteTimeStr(inspectPlan.getExecuteTime());
				break;
			default:
		}
		User createUser = UserCache.getUser(inspectPlan.getCreateUser());
		if(Func.isNotEmpty(createUser)){
			inspectPlanDTO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(inspectPlan.getUpdateUser());
		if(Func.isNotEmpty(updateUser)){
			inspectPlanDTO.setUpdateUserName(updateUser.getRealName());
		}
		return inspectPlanDTO;
	}

	public List<InspectPlanDTO> listDTO(List<InspectPlan> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
