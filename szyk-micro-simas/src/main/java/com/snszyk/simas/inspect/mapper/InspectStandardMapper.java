/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
import com.snszyk.simas.inspect.entity.InspectStandard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点巡检标准表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface InspectStandardMapper extends BaseMapper<InspectStandard> {

	/**
	 * 导入模板数据
	 *
	 * @param deptIds
	 * @return
	 */
	List<BaseStandardTemplate> generateExcelData(@Param("deptIds") List<Long> deptIds);


}
