 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.simas.common.excel.InspectPlanExcel;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.entity.InspectPlan;
 import com.snszyk.simas.inspect.vo.InspectPlanVO;

 import java.util.Date;
 import java.util.List;

 /**
  * 设备点巡检计划表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 public interface IInspectPlanService extends BaseService<InspectPlan> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param inspectPlan
	  * @return
	  */
	 IPage<InspectPlanDTO> page(IPage<InspectPlanDTO> page, InspectPlanVO inspectPlan);

	 /**
	  * 选择设备分页
	  *
	  * @param page
	  * @param deviceAccount
	  * @return
	  */
	 IPage<DeviceAccountVO> selectDevicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount);

	 /**
	  * 详情
	  *
	  * @param no
	  * @return
	  */
	 InspectPlanDTO detail(String no);

	 /**
	  * 查看
	  *
	  * @param no
	  * @return
	  */
	 InspectPlanDTO view(String no);

	 /**
	  * 新增
	  *
	  * @param inspectPlan
	  * @return
	  */
	 boolean add(InspectPlanVO inspectPlan);

	 /**
	  * 修改
	  *
	  * @param inspectPlan
	  * @return
	  */
	 boolean modify(InspectPlanVO inspectPlan);

	 /**
	  * 查询当天点检计划
	  *
	  * @param currentDate
	  * @return
	  */
	 List<InspectPlanDTO> getTheDayPlans(Date currentDate);

	 /**
	  * 导出
	  *
	  * @param inspectPlan
	  * @return
	  */
	 List<InspectPlanExcel> exportPlan(InspectPlanVO inspectPlan);

 }
