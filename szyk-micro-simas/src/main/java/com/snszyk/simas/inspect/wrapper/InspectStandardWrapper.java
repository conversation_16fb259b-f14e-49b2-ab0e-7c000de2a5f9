/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.wrapper;

import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.inspect.entity.InspectStandard;
import com.snszyk.simas.inspect.vo.InspectStandardVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备点巡检标准表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class InspectStandardWrapper extends BaseEntityWrapper<InspectStandard, InspectStandardVO> {

    public static InspectStandardWrapper build() {
        return new InspectStandardWrapper();
    }

    @Override
    public InspectStandardVO entityVO(InspectStandard inspectStandard) {
        InspectStandardVO inspectStandardVO = Objects
                .requireNonNull(BeanUtil.copy(inspectStandard, InspectStandardVO.class));
        User createUser = UserCache.getUser(inspectStandard.getCreateUser());
        if (Func.isNotEmpty(createUser)) {
            inspectStandardVO.setCreateUserName(createUser.getRealName());
        }
        Long monitorId = inspectStandard.getMonitorId();
        if (Func.isNotEmpty(monitorId)) {
            DeviceMonitor monitor = CommonCache.getMonitor(monitorId);
			if(monitor!=null){
				inspectStandardVO.setMonitorName(monitor.getName());
				inspectStandardVO.setMonitorType(monitor.getType());
				if (Func.isNotEmpty(monitor.getType())) {
					String monitorTypeName = DictBizCache.getValue("monitor_type", monitor.getType());
					inspectStandardVO.setMonitorTypeName(monitorTypeName);
				}
			}
        }
        return inspectStandardVO;
    }

}
