/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.inspect.entity.InspectRecord;
import com.snszyk.simas.inspect.vo.InspectRecordVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备点巡检记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public class InspectRecordWrapper extends BaseEntityWrapper<InspectRecord, InspectRecordVO> {

	public static InspectRecordWrapper build() {
		return new InspectRecordWrapper();
 	}

	@Override
	public InspectRecordVO entityVO(InspectRecord inspectRecord) {
		InspectRecordVO inspectRecordVO = Objects.requireNonNull(BeanUtil.copy(inspectRecord, InspectRecordVO.class));
		// 检查结果
		if(Func.isNotEmpty(inspectRecord.getIsAbnormal())){
			inspectRecordVO.setAbnormalStatus(inspectRecord.getIsAbnormal() == 0 ? "正常" : "异常");
		}
		// 异常等级
		if(Func.isNotEmpty(inspectRecord.getAbnormalLevel())){
			inspectRecordVO.setAbnormalLevelName(DefectLevelEnum.getByCode(inspectRecord.getAbnormalLevel()).getName());
		}
		// 是否现场处理
		if(Func.isNotEmpty(inspectRecord.getIsHandled())){
			inspectRecordVO.setIsHandledName(Func.equals(Func.toInt(StringPool.ONE), inspectRecord.getIsHandled()) ? "是" : "否");
		}
		// 点检人员
		if(Func.isNotEmpty(inspectRecord.getInspectUser())){
			User inspectUser = UserCache.getUser(inspectRecord.getInspectUser());
			if(Func.isNotEmpty(inspectUser)){
				inspectRecordVO.setInspectUserName(inspectUser.getName());
			}
		}
		return inspectRecordVO;
	}

}
