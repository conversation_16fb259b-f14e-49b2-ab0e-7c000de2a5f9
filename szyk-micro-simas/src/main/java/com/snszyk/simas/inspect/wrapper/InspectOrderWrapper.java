/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.entity.InspectPlan;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.inspect.vo.InspectOrderVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备点巡检工单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public class InspectOrderWrapper extends BaseEntityWrapper<InspectOrder, InspectOrderVO> {

	public static InspectOrderWrapper build() {
		return new InspectOrderWrapper();
 	}

	@Override
	public InspectOrderVO entityVO(InspectOrder inspectOrder) {
		InspectOrderVO inspectOrderVO = Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrderVO.class));

		//User createUser = UserCache.getUser(inspectPlan.getCreateUser());
		//User updateUser = UserCache.getUser(inspectPlan.getUpdateUser());
		//inspectPlanVO.setCreateUserName(createUser.getName());
		//inspectPlanVO.setUpdateUserName(updateUser.getName());

		return inspectOrderVO;
	}

	public InspectOrderDTO entityDTO(InspectOrder inspectOrder) {
		InspectOrderDTO inspectOrderDTO = Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrderDTO.class));
		// 执行部门
		if(Func.isNotEmpty(inspectOrder.getExecuteDept())){
			Dept dept = SysCache.getDept(inspectOrder.getExecuteDept());
			if(Func.isNotEmpty(dept)){
				inspectOrderDTO.setExecuteDeptName(dept.getDeptName());
			}
		}
		// 执行人
		if(Func.isNotEmpty(inspectOrder.getExecuteUser())){
			User user = UserCache.getUser(inspectOrder.getExecuteUser());
			if(Func.isNotEmpty(user)){
				inspectOrderDTO.setExecuteUserName(user.getRealName());
			}
		}
		// 审批人
		if(Func.isNotEmpty(inspectOrder.getApprovalUser())){
			User user = UserCache.getUser(inspectOrder.getApprovalUser());
			if(Func.isNotEmpty(user)){
				inspectOrderDTO.setApprovalUserName(user.getRealName());
			}
		}
		// 状态
		inspectOrderDTO.setStatusName(OrderStatusEnum.getByCode(inspectOrder.getStatus()).getName());
		if(Func.isNotEmpty(inspectOrder.getPlanInfo())){
			InspectPlan plan = JSONUtil.toBean(inspectOrder.getPlanInfo(), InspectPlan.class);
			// 计划名称
			inspectOrderDTO.setOrderName(plan.getName());
			// 计划周期
			inspectOrderDTO.setCycleTypeName(PlanCycleEnum.getByCode(plan.getCycleType()).getName());
			switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
				case DAY:
					inspectOrderDTO.setStartTimeStr(DateUtil.format(inspectOrder.getStartTime(), "yyyy-MM-dd HH:mm"))
						.setEndTimeStr(DateUtil.format(inspectOrder.getEndTime(), "yyyy-MM-dd HH:mm"));
					break;
				case WEEK:
				case MONTH:
					inspectOrderDTO.setStartTimeStr(DateUtil.format(inspectOrder.getStartTime(), "yyyy-MM-dd"))
						.setEndTimeStr(DateUtil.format(inspectOrder.getEndTime(), "yyyy-MM-dd"));
					break;
				default:
			}
		}
		return inspectOrderDTO;
	}

	public List<InspectOrderDTO> listDTO(List<InspectOrder> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
