 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.secure.SzykUser;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.InspectOrderExcel;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.service.ITimeoutRemindSetService;
 import com.snszyk.simas.common.vo.EquipmentInspectVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.common.vo.TimeoutRemindSetVO;
 import com.snszyk.simas.inspect.dto.InspectOrderDTO;
 import com.snszyk.simas.inspect.service.IInspectOrderService;
 import com.snszyk.simas.inspect.vo.InspectOrderVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.math.BigDecimal;
 import java.util.List;
 import java.util.Objects;

 /**
  * 设备点巡检工单表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-16
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/inspect-order")
 @Api(value = "设备点巡检工单表", tags = "设备点巡检工单表接口")
 public class InspectOrderController extends SzykController {

	 private static final String DEFAULT_TIME_INTERVAL = "0.5";
	 private final IInspectOrderService inspectOrderService;
	 private final ITimeoutRemindSetService timeoutRemindSetService;
	 private final IOperateLogService operateLogService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入no")
	 public R<InspectOrderDTO> detail(String no) {
		 InspectOrderDTO detail = inspectOrderService.detail(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.INSPECT_ORDER, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 自定义分页 设备点巡检工单表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "no", value = "工单编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "orderName", value = "计划名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "equipmentCode", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "planId", value = "点巡检计划id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "isAbnormal", value = "检查结果（0：正常，1：异常）", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "clientType", value = "终端类型（PC端：PC，APP端：APP）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "onlyQueryExecuteUser", value = "执行人id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "分页", notes = "传入inspectOrder")
	 public R<IPage<InspectOrderDTO>> page(@ApiIgnore InspectOrderVO inspectOrder, Query query) {
		 OperateLogVO operateLog = new OperateLogVO(null, SystemModuleEnum.INSPECT_ORDER, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(inspectOrderService.page(Condition.getPage(query), inspectOrder));
	 }

	 /**
	  * 提交点巡检 设备点巡检工单表
	  */
	 @PostMapping("/inspect")
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "提交点巡检", notes = "传入equipmentInspect")
	 public R inspect(@Valid @RequestBody EquipmentInspectVO equipmentInspect) {
		 return R.status(inspectOrderService.inspect(equipmentInspect));
	 }

	 /**
	  * 审核确认 设备点巡检工单表
	  */
	 @PostMapping("/confirm")
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "审核确认", notes = "InspectOrderVO，驳回：status=6")
	 public R confirm(@RequestBody InspectOrderVO vo) {
		 return R.status(inspectOrderService.confirm(vo));
	 }

	 /**
	  * 导出 设备点巡检计划表
	  */
	 @GetMapping("/export-order")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "orderName", value = "名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "导出", notes = "传入inspectPlan")
	 public void exportOrder(@ApiIgnore InspectOrderVO inspectOrder, HttpServletResponse response) {
		 List<InspectOrderExcel> list = inspectOrderService.exportOrder(inspectOrder);
		 ExcelUtil.export(response, "点巡检工单列表" + DateUtil.time(), "点巡检工单", list, InspectOrderExcel.class);
	 }

	 /**
	  * 超时提醒时间详情
	  */
	 @GetMapping("/timeoutRemindDetail")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "超时提醒时间详情", notes = "传入bizType")
	 public R<TimeoutRemindSetVO> timeoutRemindDetail(@ApiParam(value = "业务类型", required = true) @RequestParam String bizType
		 , SzykUser szykUser) {
		 TimeoutRemindSet set = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getBizType, bizType).eq(TimeoutRemindSet::getUserId, szykUser.getUserId()));
		 if (Func.isEmpty(set)) {
			 set = new TimeoutRemindSet();
			 set.setBizType(bizType);
			 set.setUserId(szykUser.getUserId());
			 set.setTimeInterval(new BigDecimal(DEFAULT_TIME_INTERVAL));
		 }
		 return R.data(Objects.requireNonNull(BeanUtil.copy(set, TimeoutRemindSetVO.class)));
	 }

	 /**
	  * 设置超时提醒时间
	  */
	 @PostMapping("/setTimeoutRemind")
	 @ApiOperationSupport(order = 7)
	 @ApiOperation(value = "设置超时提醒时间", notes = "传入equipment")
	 public R submit(@Valid @RequestBody TimeoutRemindSetVO timeoutRemindSet) {
		 return R.status(timeoutRemindSetService.submit(timeoutRemindSet));
	 }

	 /**
	  * 即将超时分页 设备点巡检工单表
	  */
	 @GetMapping("/timeoutPage")
	 @ApiOperationSupport(order = 8)
	 @ApiOperation(value = "即将超时分页", notes = "传入inspectOrder")
	 public R<IPage<InspectOrderDTO>> timeoutPage(@ApiIgnore InspectOrderVO inspectOrder, Query query) {
		 return R.data(inspectOrderService.timeoutPage(Condition.getPage(query), inspectOrder));
	 }

	 /**
	  * 超时说明
	  */
	 @PostMapping("/timeoutExplain")
	 @ApiOperationSupport(order = 9)
	 @ApiOperation(value = "超时说明", notes = "传入inspectOrder")
	 public R timeoutExplain(@Valid @RequestBody InspectOrderVO inspectOrder) {
		 return R.status(inspectOrderService.timeoutExplain(inspectOrder));
	 }

	 /**
	  * 批量审核确认 设备点巡检工单表
	  */
	 @PostMapping("/confirmBatch")
	 @ApiOperationSupport(order = 10)
	 @ApiOperation(value = "审核确认", notes = "InspectOrderVO，驳回：status=6")
	 public R confirmBatch(@RequestBody InspectOrderVO vo) {
		 return R.status(inspectOrderService.confirmBatch(vo));
	 }


 }
