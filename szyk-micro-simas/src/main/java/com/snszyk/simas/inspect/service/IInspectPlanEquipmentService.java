/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.common.vo.DeviceInfoVO;
import com.snszyk.simas.inspect.entity.InspectPlanEquipment;

/**
 * 设备点巡检计划关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface IInspectPlanEquipmentService extends IService<InspectPlanEquipment> {

	/**
	 * 点检计划详情设备分页
	 *
	 * @param page
	 * @param deviceInfo
	 * @return
	 */
	IPage<DeviceInfoVO> planDevicePage(IPage<DeviceInfoVO> page, DeviceInfoVO deviceInfo);


}
