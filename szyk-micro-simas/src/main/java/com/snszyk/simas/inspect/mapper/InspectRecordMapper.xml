<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inspect.mapper.InspectRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectRecordResultMap" type="com.snszyk.simas.inspect.entity.InspectRecord">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="standard_id" property="standardId"/>
        <result column="standard_info" property="standardInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_comment" property="abnormalComment"/>
        <result column="abnormal_image" property="abnormalImage"/>
        <result column="is_handled" property="isHandled"/>
        <result column="inspect_user" property="inspectUser"/>
        <result column="inspect_time" property="inspectTime"/>
    </resultMap>


</mapper>
