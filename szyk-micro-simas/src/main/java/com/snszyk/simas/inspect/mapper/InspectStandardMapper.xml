<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inspect.mapper.InspectStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectStandardResultMap" type="com.snszyk.simas.inspect.entity.InspectStandard">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="standard" property="standard"/>
        <result column="method" property="method"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="inspectStandardDTOResultMap" type="com.snszyk.simas.inspect.dto.InspectStandardDTO">
        <id column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="standard" property="standard"/>
        <result column="method" property="method"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="abnormal_status" property="abnormalStatus"/>
    </resultMap>

    <select id="generateExcelData" resultType="com.snszyk.simas.common.excel.template.BaseStandardTemplate">
        select d.`code` as code,d.`name` as name,d.`sn` as sn,m.`name` as monitorName
        from device_monitor m
        left join device_account d on m.device_id = d.id
        <where>
            d.is_deleted = 0
            <if test="deptIds != null">
                and d.use_dept in
                <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        order by d.`code`, m.sort
    </select>


</mapper>
