/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.inspect.dto.InspectPlanDTO;
import com.snszyk.simas.inspect.entity.InspectStandard;
import com.snszyk.simas.inspect.vo.InspectPlanVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 设备点巡检标准表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface IInspectStandardService extends BaseService<InspectStandard> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceAccount
	 * @return
	 */
	IPage<DeviceAccountVO> page(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount);

	/**
	 * 详情
	 *
	 * @param equipmentId
	 * @return
	 */
	EquipmentStandardVO detail(Long equipmentId);

	/**
	 * 保存
	 *
	 * @param equipmentStandardVO
	 * @return
	 */
	boolean submit(EquipmentStandardVO equipmentStandardVO);

	Long genMonitor(Long equipmentId,String monitorName,Integer monitorType);

	/**
	 * 清空标准
	 *
	 * @param equipmentId
	 * @return
	 */
	boolean clear(Long equipmentId);

	/**
	 * 生成excel数据
	 *
	 * @param deptId
	 * @return
	 */
	List<BaseStandardTemplate> generateExcelData(String deptId);

	/**
	 * 导入数据
	 * @param list
	 * @return
	 */
	boolean saveImportData(List<InspectStandard> list);

}
