 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.core.tool.support.Kv;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.excel.InspectOrderExcel;
 import com.snszyk.simas.common.vo.EquipmentInspectVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.inspect.dto.InspectOrderDTO;
 import com.snszyk.simas.inspect.entity.InspectOrder;
 import com.snszyk.simas.inspect.vo.InspectOrderVO;

 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Map;

 /**
  * 设备点巡检工单表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-16
  */
 public interface IInspectOrderService extends BaseService<InspectOrder> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param inspectOrder
	  * @return
	  */
	 IPage<InspectOrderDTO> page(IPage<InspectOrderDTO> page, InspectOrderVO inspectOrder);

	 /**
	  * 详情
	  *
	  * @param no
	  * @return
	  */
	 InspectOrderDTO detail(String no);

	 /**
	  * 设备点巡检
	  *
	  * @param equipmentInspect
	  * @return
	  */
	 boolean inspect(EquipmentInspectVO equipmentInspect);

	 /**
	  * 审核确认
	  *
	  * @param vo
	  * @return
	  */
	 boolean confirm(InspectOrderVO vo);

	 /**
	  * 审核确认
	  *
	  * @param vo
	  * @return
	  */
	 boolean confirmBatch(InspectOrderVO vo);

	 /**
	  * 导出
	  *
	  * @param inspectOrder
	  * @return
	  */
	 List<InspectOrderExcel> exportOrder(InspectOrderVO inspectOrder);

	 /**
	  * 即将超期分页
	  *
	  * @param page
	  * @param inspectOrder
	  * @return
	  */
	 IPage<InspectOrderDTO> timeoutPage(IPage<InspectOrderDTO> page, InspectOrderVO inspectOrder);

	 /**
	  * 统计-工单完成情况
	  *
	  * @param queryDate
	  * @return
	  */
	 List<InspectOrderDTO> inspectOrderStatistics(Integer queryDate);

	 /**
	  * 即将超期工单数量
	  *
	  * @return
	  */
	 Integer expireSoonCount();

	 /**
	  * 发送消息提醒
	  *
	  * @param list
	  * @param messageBizType
	  */
	 void sendMessage(List<InspectOrder> list, MessageBizTypeEnum messageBizType);

	 /**
	  * 统计报表-点巡检统计
	  *
	  * @param page
	  * @param search
	  * @return
	  */
	 IPage<InspectOrderDTO> statisticalReport(IPage<InspectOrderDTO> page, StatisticSearchVO search);

	 /**
	  * 统计报表-点巡检统计导出
	  *
	  * @param search
	  * @return
	  */
	 // List<InspectOrderStatisticsExcel> exportStatisticalReport(StatisticSearchVO search);

	 /**
	  * 点巡检工单列表
	  *
	  * @param inspectOrder
	  * @return
	  */
	 List<InspectOrderDTO> queryList(InspectOrderVO inspectOrder);

	 /**
	  * 超时说明
	  *
	  * @param inspectOrder
	  * @return
	  */
	 boolean timeoutExplain(InspectOrderVO inspectOrder);


	 /**
	  * 大屏超期
	  *
	  * @return
	  */
	 List<BigScreenMessageDTO> overdueList(String tenantId);

	 /**
	  * @return
	  */
	 List<Kv> inspectToday();

	 /**
	  * 当天的点巡检覆盖率
	  *
	  * @param tenantId
	  * @return
	  */
	 List<InspectOrderDTO> coverListToday(String tenantId);

	 /**
	  * @param inspectOrder
	  * @return
	  */
	 Integer handleInspectCount(InspectOrderVO inspectOrder);

	 List<InspectOrder> listBy(Long executeDeptId, List<Long> userIds, List<Long> equipmentIds, LocalDateTime startTime, LocalDateTime endTime, Integer neStatus);

	 /**
	  * 点检设备统计
	  *
	  * @param vo
	  * @param deviceMap
	  * @return
	  */
	 List<EquipmentStatisticsDTO> inspectStatistics(StatisticSearchVO vo, Map<Long, DeviceAccountVO> deviceMap);


 }
