 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.PlanStatusEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.InspectPlanExcel;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.vo.DeviceInfoVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.entity.InspectPlan;
 import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
 import com.snszyk.simas.inspect.service.IInspectPlanService;
 import com.snszyk.simas.inspect.vo.InspectPlanVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;

 /**
  * 设备点巡检计划表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/inspect-plan")
 @Api(value = "设备点巡检计划表", tags = "设备点巡检计划表接口")
 public class InspectPlanController extends SzykController {

	 private final IInspectPlanService inspectPlanService;
	 private final IInspectPlanEquipmentService planEquipmentService;
	 private final IOperateLogService operateLogService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入id")
	 public R<InspectPlanDTO> detail(String no) {
		 InspectPlanDTO detail = inspectPlanService.detail(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 查看
	  */
	 @GetMapping("/view")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "查看", notes = "传入no")
	 public R<InspectPlanDTO> view(String no) {
		 InspectPlanDTO detail = inspectPlanService.view(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 自定义分页 设备点巡检计划表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "分页", notes = "传入inspectPlan")
	 public R<IPage<InspectPlanDTO>> page(@ApiIgnore InspectPlanVO inspectPlan, Query query) {
		 OperateLogVO operateLog = new OperateLogVO(null, SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(inspectPlanService.page(Condition.getPage(query), inspectPlan));
	 }

	 /**
	  * 选择设备分页 设备点巡检计划表
	  */
	 @GetMapping("/selectDevicePage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "sn", value = "设备SN", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "name", value = "设备名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "code", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "useDept", value = "使用部门", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "设备状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "filterStatus", value = "选择设备参数", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "选择设备分页", notes = "传入deviceAccount")
	 public R<IPage<DeviceAccountVO>> selectDevicePage(@ApiIgnore DeviceAccountVO deviceAccount, Query query) {
		 return R.data(inspectPlanService.selectDevicePage(Condition.getPage(query), deviceAccount));
	 }

	 /**
	  * 计划详情设备分页 设备点巡检计划表
	  */
	 @GetMapping("/planDevicePage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "planId", value = "计划id", paramType = "query", dataType = "long")
	 })
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "计划详情设备分页", notes = "传入deviceInfo")
	 public R<IPage<DeviceInfoVO>> planDevicePage(@ApiIgnore DeviceInfoVO deviceInfo, Query query) {
		 return R.data(planEquipmentService.planDevicePage(Condition.getPage(query), deviceInfo));
	 }

	 /**
	  * 新增 设备点巡检计划表
	  */
	 @PostMapping("/save")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "新增", notes = "传入inspectPlan")
	 public R save(@Valid @RequestBody InspectPlanVO inspectPlan) {
		 boolean ret = inspectPlanService.add(inspectPlan);
		 OperateLogVO operateLog = new OperateLogVO(inspectPlan.getId(), SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.CREATE);
		 operateLogService.submit(operateLog);
		 return R.status(ret);
	 }

	 /**
	  * 修改 设备点巡检计划表
	  */
	 @PostMapping("/update")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "修改", notes = "传入inspectPlan")
	 public R update(@Valid @RequestBody InspectPlanVO inspectPlan) {
		 boolean ret = inspectPlanService.modify(inspectPlan);
		 OperateLogVO operateLog = new OperateLogVO(inspectPlan.getId(), SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.UPDATE);
		 operateLogService.submit(operateLog);
		 return R.status(ret);
	 }

	 /**
	  * 删除 设备点巡检计划表
	  */
	 @PostMapping("/remove")
	 @ApiOperationSupport(order = 7)
	 @ApiOperation(value = "逻辑删除", notes = "传入ids")
	 public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		 List<Long> list = Func.toLongList(ids);
		 list.forEach(id -> {
			 OperateLogVO operateLog = new OperateLogVO(id, SystemModuleEnum.INSPECT_PLAN, OperateTypeEnum.DELETE);
			 operateLogService.submit(operateLog);
		 });
		 return R.status(inspectPlanService.deleteLogic(list));
	 }

	 /**
	  * 手动开始 设备点巡检计划表
	  */
	 @PostMapping("/manual-start")
	 @ApiOperationSupport(order = 8)
	 @ApiOperation(value = "手动开始", notes = "传入id")
	 public R manualStart(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		 return R.status(inspectPlanService.update(Wrappers.<InspectPlan>update().lambda()
			 .set(InspectPlan::getStartDate, DateUtil.now())
			 .set(InspectPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode())
			 .eq(InspectPlan::getId, id)));
	 }

	 /**
	  * 手动停止 设备点巡检计划表
	  */
	 @PostMapping("/manual-stop")
	 @ApiOperationSupport(order = 9)
	 @ApiOperation(value = "手动停止", notes = "传入id")
	 public R manualStop(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		 return R.status(inspectPlanService.update(Wrappers.<InspectPlan>update().lambda()
			 .set(InspectPlan::getEndDate, DateUtil.now())
			 .set(InspectPlan::getStatus, PlanStatusEnum.IS_TERMINATED.getCode())
			 .eq(InspectPlan::getId, id)));
	 }

	 /**
	  * 导出 设备点巡检计划表
	  */
	 @GetMapping("/export-plan")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 10)
	 @ApiOperation(value = "导出", notes = "传入inspectPlan")
	 public void exportPlan(@ApiIgnore InspectPlanVO inspectPlan, HttpServletResponse response) {
		 List<InspectPlanExcel> list = inspectPlanService.exportPlan(inspectPlan);
		 ExcelUtil.export(response, "点巡检计划列表" + DateUtil.time(), "点巡检计划", list, InspectPlanExcel.class);
	 }


 }
