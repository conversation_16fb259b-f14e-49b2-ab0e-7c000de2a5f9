 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.service.impl;

 import cn.hutool.json.JSONUtil;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OrderStatusEnum;
 import com.snszyk.simas.common.enums.PlanCycleEnum;
 import com.snszyk.simas.common.enums.PlanStatusEnum;
 import com.snszyk.simas.common.excel.InspectPlanExcel;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.entity.InspectOrder;
 import com.snszyk.simas.inspect.entity.InspectPlan;
 import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
 import com.snszyk.simas.inspect.entity.InspectStandard;
 import com.snszyk.simas.inspect.mapper.InspectPlanMapper;
 import com.snszyk.simas.inspect.mapper.InspectStandardMapper;
 import com.snszyk.simas.inspect.service.IInspectOrderService;
 import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
 import com.snszyk.simas.inspect.service.IInspectPlanService;
 import com.snszyk.simas.inspect.vo.InspectPlanVO;
 import com.snszyk.simas.inspect.wrapper.InspectPlanWrapper;
 import com.snszyk.simas.lubricate.entity.LubricateStandards;
 import com.snszyk.simas.lubricate.mapper.LubricateStandardsMapper;
 import com.snszyk.simas.overhaul.entity.OverhaulStandard;
 import com.snszyk.simas.overhaul.mapper.OverhaulStandardMapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.ArrayList;
 import java.util.Date;
 import java.util.List;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备点巡检计划表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @AllArgsConstructor
 @Service
 public class InspectPlanServiceImpl extends BaseServiceImpl<InspectPlanMapper, InspectPlan> implements IInspectPlanService {


	 private final InspectStandardMapper inspectStandardMapper;
	 private final IInspectPlanEquipmentService planEquipmentService;
	 private final IInspectOrderService inspectOrderService;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final LubricateStandardsMapper lubricateStandardsMapper;
	 private final OverhaulStandardMapper overhaulStandardMapper;


	 @Override
	 public IPage<InspectPlanDTO> page(IPage<InspectPlanDTO> page, InspectPlanVO vo) {
		 if (Func.isNotEmpty(vo.getQueryStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getQueryEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<InspectPlan> list = baseMapper.page(page, vo);
		 if (Func.isNotEmpty(list)) {
			 List<InspectPlanDTO> resultList = list.stream().map(inspectPlan -> {
				 InspectPlanDTO dto = InspectPlanWrapper.build().entityDTO(inspectPlan);
				 dto.setEquipmentCount(planEquipmentService.count(Wrappers.<InspectPlanEquipment>query().lambda()
					 .eq(InspectPlanEquipment::getPlanId, dto.getId())));
				 return dto;
			 }).collect(Collectors.toList());
			 return page.setRecords(resultList);
		 }
		 return page.setRecords(null);
	 }

	 @Override
	 public IPage<DeviceAccountVO> selectDevicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount) {
		 R<List<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageList(deviceAccount,
			 Func.toInt(page.getCurrent()), Func.toInt(page.getSize()));
		 R<Integer> totalResult = deviceAccountClient.deviceCount(deviceAccount);
		 if (!deviceResult.isSuccess() || !totalResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isNotEmpty(deviceResult.getData())) {
			 deviceResult.getData().forEach(data -> {
				 // 点巡检标准数量
				 data.setStandardCount(inspectStandardMapper.selectCount(Wrappers.<InspectStandard>query().lambda()
					 .eq(InspectStandard::getEquipmentId, data.getId())));
				 // 点巡检计划是否可勾选
				 data.setCanSelect(Boolean.FALSE);
				 if (data.getStandardCount() > 0) {
					 data.setCanSelect(Boolean.TRUE);
				 }
				 // 润滑标准数量
				 data.setLubricateStandardCount(lubricateStandardsMapper.selectCount(Wrappers.<LubricateStandards>query().lambda()
					 .eq(LubricateStandards::getEquipmentId, data.getId())));
				 //润滑的计划是否可选
				 data.setCanLubricateSelect(Boolean.FALSE);
				 if (data.getLubricateStandardCount() > 0) {
					 data.setCanLubricateSelect(Boolean.TRUE);
				 }
				 //检修标准的数量
				 data.setOverhaulStandardCount(overhaulStandardMapper.selectCount(Wrappers.<OverhaulStandard>query().lambda()
					 .eq(OverhaulStandard::getEquipmentId, data.getId())));
				 data.setCanOverhaulSelect(Boolean.FALSE);
				 // 检修的计划是否可选
				 if (data.getOverhaulStandardCount() > 0) {
					 data.setCanOverhaulSelect(Boolean.TRUE);
				 }
			 });
		 }
		 IPage<DeviceAccountVO> iPage = new Page<>();
		 iPage.setCurrent(page.getCurrent());
		 iPage.setSize(page.getSize());
		 iPage.setTotal(Func.toLong(Func.toStr(totalResult.getData())));
		 iPage.setRecords(deviceResult.getData());
		 return iPage;
	 }

	 @Override
	 public InspectPlanDTO detail(String no) {
		 InspectPlan plan = this.getOne(Wrappers.<InspectPlan>query().lambda().eq(InspectPlan::getNo, no));
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 InspectPlanDTO detail = InspectPlanWrapper.build().entityDTO(plan);
		 detail.setEquipmentCount(planEquipmentService.count(Wrappers.<InspectPlanEquipment>query().lambda()
			 .eq(InspectPlanEquipment::getPlanId, detail.getId())));
		 // 所选设备
		 List<InspectPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<InspectPlanEquipment>query().lambda()
			 .eq(InspectPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(planEquipmentList)) {
			 detail.setEquipmentIds(planEquipmentList.stream()
				 .map(InspectPlanEquipment::getEquipmentId).collect(Collectors.toList()));
			 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
			 deviceAccountVO.setDeviceIds(detail.getEquipmentIds());
			 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
			 if (equipmentListResult.isSuccess() && Func.isNotEmpty(equipmentListResult.getData())) {
				 detail.setEquipmentList(equipmentListResult.getData());
			 }
		 }
		 return detail;
	 }

	 @Override
	 public InspectPlanDTO view(String no) {
		 InspectPlan plan = this.getOne(Wrappers.<InspectPlan>query().lambda().eq(InspectPlan::getNo, no));
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 InspectPlanDTO detail = InspectPlanWrapper.build().entityDTO(plan);
		 detail.setEquipmentCount(planEquipmentService.count(Wrappers.<InspectPlanEquipment>query().lambda()
			 .eq(InspectPlanEquipment::getPlanId, detail.getId())));
		 // 所选设备
		 List<InspectPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<InspectPlanEquipment>query().lambda()
			 .eq(InspectPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(planEquipmentList)) {
			 detail.setEquipmentIds(planEquipmentList.stream()
				 .map(InspectPlanEquipment::getEquipmentId).collect(Collectors.toList()));
			 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
			 deviceAccountVO.setDeviceIds(detail.getEquipmentIds());
			 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
			 if (equipmentListResult.isSuccess() && Func.isNotEmpty(equipmentListResult.getData())) {
				 detail.setEquipmentList(equipmentListResult.getData());
				 List<DeviceAccountVO> equipmentAccountList = new ArrayList<>();
				 for (DeviceAccountVO equipment : equipmentListResult.getData()) {
					 List<InspectOrder> orderList = inspectOrderService.list(Wrappers.<InspectOrder>query().lambda()
						 .eq(InspectOrder::getPlanId, plan.getId()).eq(InspectOrder::getEquipmentId, equipment.getId()));
					 if (Func.isNotEmpty(orderList)) {
						 for (InspectOrder order : orderList) {
							 equipment.setOrderNo(order.getNo())
								 .setBizStatus(OrderStatusEnum.getByCode(order.getStatus()).getName());
							 equipmentAccountList.add(equipment);
						 }
					 }
				 }
				 if (Func.isNotEmpty(equipmentAccountList)) {
					 detail.setEquipmentList(equipmentAccountList);
				 }
			 }
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean add(InspectPlanVO vo) {
		 InspectPlan plan = Objects.requireNonNull(BeanUtil.copy(vo, InspectPlan.class));
		 plan.setNo(BizCodeUtil.generate("IP")).setStatus(PlanStatusEnum.NO_START.getCode());
		 // 时间设置
		 switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(vo.getByWeekSet().stream().collect(Collectors.joining(",")));
				 break;
			 case MONTH:
				 plan.setExecuteTime(vo.getByMonthSet().stream().collect(Collectors.joining(",")));
				 break;
			 default:
		 }
		 boolean ret = this.save(plan);
		 // 所选设备
		 if (Func.isNotEmpty(vo.getEquipmentIds())) {
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<InspectPlanEquipment> planEquipmentList = Func.toLongList(vo.getEquipmentIds()).stream().map(equipmentId -> {
				 InspectPlanEquipment planEquipment = new InspectPlanEquipment();
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(equipmentId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean modify(InspectPlanVO vo) {
		 InspectPlan plan = this.getById(vo.getId());
		 if (plan == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 BeanUtil.copy(vo, plan);
		 // 时间设置
		 switch (PlanCycleEnum.getByCode(vo.getCycleType())) {
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(vo.getByWeekSet().stream().collect(Collectors.joining(",")));
				 break;
			 case MONTH:
				 plan.setExecuteTime(vo.getByMonthSet().stream().collect(Collectors.joining(",")));
				 break;
			 default:
		 }
		 // 所选设备
		 planEquipmentService.remove(Wrappers.<InspectPlanEquipment>query().lambda()
			 .eq(InspectPlanEquipment::getPlanId, plan.getId()));
		 if (Func.isNotEmpty(vo.getEquipmentIds())) {
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<InspectPlanEquipment> planEquipmentList = Func.toLongList(vo.getEquipmentIds()).stream().map(equipmentId -> {
				 InspectPlanEquipment planEquipment = new InspectPlanEquipment();
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(equipmentId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 if (Func.isEmpty(vo.getExecuteUser())) {
			 plan.setExecuteUser(null);
		 }
		 return this.updateById(plan);
	 }

	 @Override
	 public List<InspectPlanDTO> getTheDayPlans(Date currentDate) {
		 return baseMapper.getTheDayPlans(currentDate);
	 }

	 @Override
	 public List<InspectPlanExcel> exportPlan(InspectPlanVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<InspectPlan> list = baseMapper.selectList(Wrappers.<InspectPlan>query().lambda()
			 .eq(Func.isNotEmpty(vo.getCycleType()), InspectPlan::getCycleType, vo.getCycleType())
			 .eq(Func.isNotEmpty(vo.getExecuteDept()), InspectPlan::getExecuteDept, vo.getExecuteDept())
			 .like(Func.isNotEmpty(vo.getName()), InspectPlan::getName, vo.getName())
			 .ge(Func.isNotEmpty(vo.getQueryStartDate()), InspectPlan::getStartDate, vo.getQueryStartDate())
			 .le(Func.isNotEmpty(vo.getQueryEndDate()), InspectPlan::getStartDate, vo.getQueryEndDate())
			 .orderByDesc(InspectPlan::getCreateTime));
		 if (Func.isNotEmpty(list)) {
			 List<InspectPlanDTO> planList = InspectPlanWrapper.build().listDTO(list);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return planList.stream().map(plan -> {
				 InspectPlanExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, InspectPlanExcel.class));
				 if (Func.isNotEmpty(plan.getStartDate())) {
					 planExcel.setStartDateStr(DateUtil.formatDate(plan.getStartDate()));
				 }
				 if (Func.isNotEmpty(plan.getEndDate())) {
					 planExcel.setEndDateStr(DateUtil.formatDate(plan.getEndDate()));
				 }
				 planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 planExcel.setEquipmentCount(Func.toStr(planEquipmentService.count(Wrappers.<InspectPlanEquipment>query().lambda()
					 .eq(InspectPlanEquipment::getPlanId, plan.getId()))));
				 return planExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

 }
