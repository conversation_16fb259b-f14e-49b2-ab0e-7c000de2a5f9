/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.inspect.entity.InspectRecord;

import java.util.List;

/**
 * 设备点巡检记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface IInspectRecordService extends IService<InspectRecord> {

	/**
	 * 最新提交的点检
	 *
	 * @param orderIdList
	 * @return
	 */
	List<InspectRecord> listLatestGroupByEquipmentIdAndMonitorName(List<Long> orderIdList);


}
