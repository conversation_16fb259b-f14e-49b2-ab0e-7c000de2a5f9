/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.inspect.entity.InspectRecord;
import com.snszyk.simas.inspect.mapper.InspectRecordMapper;
import com.snszyk.simas.inspect.service.IInspectRecordService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备点巡检记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Service
public class InspectRecordServiceImpl extends ServiceImpl<InspectRecordMapper, InspectRecord> implements IInspectRecordService {

	/**
	 * 查询最近的点巡检记录
	 * 按照equipmentId和monitorName分组去重，
	 * 因为monitorId有可能为空
	 *
	 * @param orderIdList
	 * @return
	 */
	@Override
	public List<InspectRecord> listLatestGroupByEquipmentIdAndMonitorName(List<Long> orderIdList) {
		if (ObjectUtil.isEmpty(orderIdList)) {
			return Collections.emptyList();
		}
		// 根据工单查询
		final List<InspectRecord> recordList = this.lambdaQuery()
			.in(InspectRecord::getOrderId, orderIdList)
			.list();
		if (ObjectUtil.isEmpty(recordList)) {
			return Collections.emptyList();
		}
		// 按照 orderId、equipmentId 和 monitorName 分组，并取每组中 inspect_time 最新的记录
		Map<String, InspectRecord> latestRecordsMap = recordList.stream()
			.collect(Collectors.toMap(
				// 分组键：orderId + equipmentId + monitorName
				record -> record.getOrderId() + StringPool.DASH + record.getEquipmentId() + StringPool.DASH + record.getMonitorName(),
				// 值：记录本身
				record -> record,
				// 去重逻辑：保留 inspect_time 最新的记录
				(existingRecord, newRecord) ->
					newRecord.getInspectTime().after(existingRecord.getInspectTime()) ? newRecord : existingRecord
			));
		// 将 Map 转换为 List
		return Lists.newArrayList(latestRecordsMap.values());
	}


}
