<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inspect.mapper.InspectPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inspectPlanResultMap" type="com.snszyk.simas.inspect.entity.InspectPlan">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="inspectPlanDTOResultMap" type="com.snszyk.simas.inspect.dto.InspectPlanDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="page" resultMap="inspectPlanDTOResultMap">
        select * from simas_inspect_plan where is_deleted = 0
        <if test="inspectPlan.name != null and inspectPlan.name != ''">
            and `name` like concat('%', #{inspectPlan.name}, '%')
        </if>
        <if test="inspectPlan.keywords!=null and inspectPlan.keywords != ''">
            AND (`no` like concat('%',#{inspectPlan.keywords},'%') or `name` like
            concat('%',#{inspectPlan.keywords},'%'))
        </if>
        <if test="inspectPlan.cycleType != null and inspectPlan.cycleType != ''">
            and cycle_type = #{inspectPlan.cycleType}
        </if>
        <if test="inspectPlan.executeDept != null and inspectPlan.executeDept != ''">
            and execute_dept = #{inspectPlan.executeDept}
        </if>
        <if test="inspectPlan.status != null">
            and status = #{inspectPlan.status}
        </if>
        <if test="inspectPlan.queryStartDate != null and inspectPlan.queryStartDate != ''">
            and start_date <![CDATA[ >= ]]> #{inspectPlan.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="inspectPlan.queryEndDate != null and inspectPlan.queryEndDate != ''">
            and start_date <![CDATA[ <= ]]> #{inspectPlan.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

    <!--查询当天点检计划-->
    <select id="getTheDayPlans" resultMap="inspectPlanDTOResultMap">
        SELECT
            p.id,
            p.tenant_id,
            p.`name`,
            p.`no`,
            p.cycle_type,
            p.cycle_interval,
            p.execute_dept,
            p.execute_user,
            p.start_date,
            p.end_date,
            p.execute_time,
            p.remark,
            p.`status`
        FROM
            simas_inspect_plan p
        WHERE p.is_deleted = 0
            AND(p.`status` = 0 OR p.`status` = 1)
            AND p.start_date <![CDATA[ <= ]]> #{currentDate, jdbcType=DATE}
            AND p.end_date <![CDATA[ >= ]]> #{currentDate, jdbcType=DATE}
    </select>

</mapper>
