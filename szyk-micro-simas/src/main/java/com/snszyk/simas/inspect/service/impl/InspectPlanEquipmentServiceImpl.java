/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceCategory;
import com.snszyk.common.location.entity.Location;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.vo.DeviceInfoVO;
import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
import com.snszyk.simas.inspect.mapper.InspectPlanEquipmentMapper;
import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备点巡检计划关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Service
@AllArgsConstructor
public class InspectPlanEquipmentServiceImpl extends ServiceImpl<InspectPlanEquipmentMapper, InspectPlanEquipment> implements IInspectPlanEquipmentService {


	@Override
	public IPage<DeviceInfoVO> planDevicePage(IPage<DeviceInfoVO> page, DeviceInfoVO deviceInfo) {
		List<DeviceInfoVO> list = baseMapper.planDevicePage(page, deviceInfo);
		if(Func.isNotEmpty(list)){
			list.forEach(device -> {
				if(Func.isNotEmpty(device.getCategoryId())){
					DeviceCategory deviceCategory = CommonCache.getEquipmentCategory(device.getCategoryId());
					if(Func.isNotEmpty(deviceCategory)){
						device.setCategoryName(deviceCategory.getCategoryName());
					}
				}
				if(Func.isNotEmpty(device.getLocationId())){
					Location location = CommonCache.getLocation(device.getLocationId());
					if(Func.isNotEmpty(location)){
						device.setLocationPath(location.getPath());
					}
				}
			});
		}
		return page.setRecords(list);
	}


}
