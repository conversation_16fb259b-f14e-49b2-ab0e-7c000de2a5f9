 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.service.impl;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.support.Kv;
 import com.snszyk.core.tool.utils.*;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.message.enums.MessageTypeEnum;
 import com.snszyk.message.enums.ReceiverTypeEnum;
 import com.snszyk.message.enums.YesNoEnum;
 import com.snszyk.message.feign.IMessageClient;
 import com.snszyk.message.vo.MessageVo;
 import com.snszyk.message.vo.ReceiverInfoVo;
 import com.snszyk.resource.entity.Attach;
 import com.snszyk.resource.feign.IAttachClient;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.entity.BizLog;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.excel.InspectOrderExcel;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.service.IBizLogService;
 import com.snszyk.simas.common.vo.EquipmentInspectVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.fault.service.IFaultDefectService;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import com.snszyk.simas.fault.wrapper.FaultDefectWrapper;
 import com.snszyk.simas.inspect.dto.InspectOrderDTO;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.dto.InspectStandardDTO;
 import com.snszyk.simas.inspect.entity.InspectOrder;
 import com.snszyk.simas.inspect.entity.InspectRecord;
 import com.snszyk.simas.inspect.entity.InspectStandard;
 import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
 import com.snszyk.simas.inspect.service.IInspectOrderService;
 import com.snszyk.simas.inspect.service.IInspectRecordService;
 import com.snszyk.simas.inspect.vo.InspectOrderVO;
 import com.snszyk.simas.inspect.vo.InspectRecordVO;
 import com.snszyk.simas.inspect.wrapper.InspectOrderWrapper;
 import com.snszyk.simas.inspect.wrapper.InspectRecordWrapper;
 import com.snszyk.system.cache.SysCache;
 import com.snszyk.system.feign.ISysClient;
 import com.snszyk.system.vo.RoleVO;
 import com.snszyk.user.entity.User;
 import com.snszyk.user.feign.IUserClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.math.BigDecimal;
 import java.math.RoundingMode;
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备点巡检工单表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-16
  */
 @Slf4j
 @AllArgsConstructor
 @Service
 public class InspectOrderServiceImpl extends BaseServiceImpl<InspectOrderMapper, InspectOrder> implements IInspectOrderService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final IInspectRecordService inspectRecordService;
	 private final IFaultDefectService faultDefectService;
	 private final InspectOrderMapper inspectOrderMapper;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	 private final IBizLogService bizLogService;
	 private final ISysClient sysClient;
	 private final IUserClient userClient;
	 private final IAttachClient attachClient;
	 private final IMessageClient messageClient;


	 @Override
	 public IPage<InspectOrderDTO> page(IPage<InspectOrderDTO> page, InspectOrderVO inspectOrder) {
		 if (Func.isNotEmpty(inspectOrder.getStartDate())) {
			 inspectOrder.setStartDate(inspectOrder.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(inspectOrder.getEndDate())) {
			 inspectOrder.setEndDate(inspectOrder.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<InspectOrder> list = baseMapper.page(page, inspectOrder);
		 List<InspectOrderDTO> resultList = InspectOrderWrapper.build().listDTO(list);
		 // 获取点检结果Map
		 Map<Long, String> inspectResultMap = ObjectUtil.isEmpty(resultList) ? MapUtil.empty() : this.getInspectResultMap(ListUtil.map(resultList, InspectOrderDTO::getId));
		 resultList.forEach(dto -> {
			 // 点巡检结果
			 dto.setInspectResult(inspectResultMap.get(dto.getId()));
		 });
		 return page.setRecords(resultList);
	 }

	 /**
	  * 获取设备点巡检结果Map
	  *
	  * @param orderIdList
	  * @return
	  */
	 public Map<Long, String> getInspectResultMap(List<Long> orderIdList) {
		 if (ObjectUtil.isEmpty(orderIdList)) {
			 return MapUtil.empty();
		 }
		 // 查询最新点巡检记录
		 List<InspectRecord> recordList = inspectRecordService.listLatestGroupByEquipmentIdAndMonitorName(orderIdList);
		 if (ObjectUtil.isEmpty(recordList)) {
			 return MapUtil.empty();
		 }
		 return recordList.stream()
			 .collect(Collectors.groupingBy(InspectRecord::getOrderId,
				 Collectors.collectingAndThen(Collectors.toList(),
					 list -> {
						 // 判断是否包含 isAbnormal 为 1 的记录
						 if (list.stream().anyMatch(record -> record.getIsAbnormal() == Func.toInt(StringPool.ONE))) {
							 return "异常";
						 } else {
							 return "正常";
						 }
					 }
				 )));
	 }

	 @Override
	 public InspectOrderDTO detail(String no) {
		 InspectOrder inspectOrder = this.getOne(Wrappers.<InspectOrder>query().lambda().eq(InspectOrder::getNo, no));
		 if (inspectOrder == null) {
			 // throw new ServiceException(ResultCode.FAILURE);
			 throw new ServiceException("当前工单不存在");
		 }
		 InspectOrderDTO detail = InspectOrderWrapper.build().entityDTO(inspectOrder);
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(inspectOrder.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 detail.setEquipmentAccount(equipmentAccount).setInspectPlan(JSONUtil.toBean(detail.getPlanInfo(), InspectPlanDTO.class));
		 // 未完成的工单，点检明细查询standard表
		 if (OrderStatusEnum.IN_PROCESS == OrderStatusEnum.getByCode(inspectOrder.getStatus())
			 || OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
			 // 点巡检标准列表
			 List<InspectStandard> standardList = JSONUtil.toList(inspectOrder.getStandardInfo(), InspectStandard.class);
			 if (Func.isNotEmpty(standardList)) {
				 detail.setStandardList(standardList.stream().map(standard -> {
					 InspectStandardDTO standDTO = Objects.requireNonNull(BeanUtil.copy(standard, InspectStandardDTO.class));
					 standDTO.setMonitorName(CommonCache.getMonitor(standard.getMonitorId()).getName());
					 return standDTO;
				 }).collect(Collectors.toList()));
			 }
		 } else {
			 // 完其他状态的工单，点检明细查询record表
			 List<InspectRecord> recordList = inspectRecordService.listLatestGroupByEquipmentIdAndMonitorName(Collections.singletonList(inspectOrder.getId()));
			 if (Func.isNotEmpty(recordList)) {
				 detail.setStandardList(recordList.stream().map(record -> {
					 InspectRecordVO recordVO = InspectRecordWrapper.build().entityVO(record);
					 InspectStandard standard = JSONUtil.toBean(record.getStandardInfo(), InspectStandard.class);
					 InspectStandardDTO standDTO = Objects.requireNonNull(BeanUtil.copy(standard, InspectStandardDTO.class));
					 if (Func.isNotEmpty(recordVO.getAbnormalImage())) {
						 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(record.getAbnormalImage()));
						 if (attachListR.isSuccess()) {
							 recordVO.setAbnormalImageList(attachListR.getData());
						 }
					 }
					 standDTO.setMonitorName(recordVO.getMonitorName()).setIsAbnormal(recordVO.getIsAbnormal())
						 .setAbnormalStatus(recordVO.getAbnormalStatus()).setInspectRecord(recordVO);
					 return standDTO;
				 }).collect(Collectors.toList()));
			 }
		 }
		 return detail;
	 }

	 /**
	  * 点检工单提交
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean inspect(EquipmentInspectVO vo) {
		 InspectOrder inspectOrder = inspectOrderMapper.selectById(vo.getOrderId());
		 Integer oldStatus = inspectOrder.getStatus();
		 if (OrderStatusEnum.getByCode(inspectOrder.getStatus()) == OrderStatusEnum.IS_COMPLETED
			 || OrderStatusEnum.getByCode(inspectOrder.getStatus()) == OrderStatusEnum.OVERDUE_COMPLETED) {
			 throw new ServiceException("当前工单已完成，不能提交！");
		 }
		 Date now = DateUtil.now();
		 // 校验当前时间是否在点检时间段
		 if (OrderStatusEnum.IS_OVERDUE != OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
			 if (now.before(inspectOrder.getStartTime())) {
				 throw new ServiceException("当前未到点巡检时间，不能提交！");
			 }
		 }
		 // 点巡检记录
		 AtomicReference<Integer> isAbnormal = new AtomicReference<>(0);
		 List<InspectRecord> toFaultList = new ArrayList<>();
		 List<InspectRecord> recordList;
		 // 点检执行标准
		 List<InspectStandard> inspectStandardList = JSONUtil.toList(inspectOrder.getStandardInfo(), InspectStandard.class);
		 Map<Long, InspectStandard> inspectStandardMap = ListUtil.toMap(inspectStandardList, InspectStandard::getId,
			 e -> Objects.requireNonNull(BeanUtil.copy(e, InspectStandard.class)));
		 if (Func.isNotEmpty(vo.getInspectRecordList())) {
			 recordList = vo.getInspectRecordList().stream().map(inspectRecordVO -> {
				 InspectRecord inspectRecord = Objects.requireNonNull(BeanUtil.copy(inspectRecordVO, InspectRecord.class));
				 inspectRecord.setOrderId(inspectOrder.getId()).setEquipmentId(vo.getEquipmentId())
					 .setInspectUser(AuthUtil.getUserId()).setInspectTime(now)
					 .setMonitorName(CommonCache.getMonitor(inspectRecord.getMonitorId()).getName())
					 .setStandardInfo(JSONUtil.toJsonStr(inspectStandardMap.get(inspectRecord.getStandardId())));
				 if (inspectRecord.getIsAbnormal() == 1) {
					 isAbnormal.set(1);
					 toFaultList.add(inspectRecord);
				 }
				 return inspectRecord;
			 }).collect(Collectors.toList());
			 inspectRecordService.saveBatch(recordList);
		 }
		 inspectOrder.setIsAbnormal(isAbnormal.get());
		 inspectOrder.setSubmitTime(now);
		 inspectOrder.setRemark(vo.getRemark());
		 // 业务日志
		 // InspectOrderVO order = InspectOrderWrapper.build().entityVO(inspectOrder);
		 // if (Func.isNotEmpty(recordList)) {
		 // order.setInspectRecordList(InspectRecordWrapper.build().listVO(recordList));
		 //}
		 // 实际执行人
		 inspectOrder.setExecuteUser(AuthUtil.getUserId());
		 Boolean needApproval = inspectOrder.getIsNeedApproval();
		 if (needApproval) {
			 // 待审核
			 inspectOrder.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
		 }
		 this.updateById(inspectOrder);
		 if(!needApproval){
			 // 默认审核通过
			 SpringUtil.getBean(InspectOrderServiceImpl.class).approvalPass(inspectOrder.getId(), toFaultList);
		 }
		 // 添加日志
		 // 如果之前的状态是驳回,则为再次提交
		 if (OrderStatusEnum.IS_REJECTED.getCode().equals(oldStatus)) {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER,
				 JSON.parseObject(JSON.toJSONString(inspectOrder)), OrderActionEnum.RE_SUBMIT);
		 } else {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER,
				 JSON.parseObject(JSON.toJSONString(inspectOrder)), OrderActionEnum.INIT);
		 }
		 return true;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirm(InspectOrderVO vo) {
		 InspectOrder inspectOrder = this.getById(vo.getId());
		 if (inspectOrder == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(inspectOrder.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
			 throw new ServiceException("当前点巡检工单已确认！");
		 }
		 // 审核拒绝v1.2.1
		 if (OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(vo.getStatus())) {
			 inspectOrder.setStatus(vo.getStatus());
			 inspectOrder.setRejectReason(vo.getRejectReason());
			 inspectOrder.setApprovalUser(AuthUtil.getUserId());
			 boolean ret = this.updateById(inspectOrder);
			 if (!ret) {
				 throw new ServiceException("工单更新失败");
			 }
			 // 发送消息
			 this.sendMessage(Collections.singletonList(inspectOrder), MessageBizTypeEnum.SIMAS_LUBRICATE_REJECT);
			 // 业务日志
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER, JSON.parseObject(JSON.toJSONString(inspectOrder)),
				 OrderActionEnum.AUDIT_FAIL, vo.getRejectReason());
		 } else {
			 // 审核通过
			 // get record
			 List<InspectRecord> recordList = inspectRecordService.lambdaQuery().eq(InspectRecord::getOrderId, vo.getId()).list();
			 if (Func.isNotEmpty(recordList)) {
				 recordList = recordList.stream().filter(inspectRecord -> inspectRecord.getIsAbnormal() == 1).collect(Collectors.toList());
			 }
			 SpringUtil.getBean(InspectOrderServiceImpl.class).approvalPass(vo.getId(), recordList);
			 // log
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER, JSON.parseObject(JSON.toJSONString(inspectOrder)), OrderActionEnum.AUDIT_PASS);
		 }
		 return true;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirmBatch(InspectOrderVO vo) {
		 if (Func.isNotEmpty(vo.getOrderIds())) {
			 vo.getOrderIds().forEach(id -> {
				 vo.setId(id);
				 this.confirm(vo);
			 });
		 }
		 return true;
	 }

	 /**
	  * 审核通过
	  *
	  * @param id
	  * @param toFaultList
	  * @return
	  */
	 @Transactional(rollbackFor = Exception.class)
	 public boolean approvalPass(Long id, List<InspectRecord> toFaultList) {
		 InspectOrder inspectOrder = getById(id);
		 List<BizLog> bizLogList = bizLogService.list(Wrappers.<BizLog>query().lambda()
			 .eq(BizLog::getBizId, id)
			 .eq(BizLog::getBizStatus, OrderStatusEnum.IS_OVERDUE.getCode()));
		 if (Func.isNotEmpty(bizLogList)) {
			 inspectOrder.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		 } else {
			 inspectOrder.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
		 }
		 // 生成故障缺陷(按标准生成)
		 if (Func.isNotEmpty(toFaultList)) {
			 List<InspectRecordVO> list = InspectRecordWrapper.build().listVO(toFaultList);
			 List<FaultDefectVO> faultDefectList = list.stream().map(record -> {
				 record.setOrderNo(inspectOrder.getNo());
				 FaultDefectVO faultDefectVO = FaultDefectWrapper.build().inspectEntityVO(record);
				 return faultDefectVO;
			 }).collect(Collectors.toList());
			 faultDefectService.submit(faultDefectList);
		 }
		 inspectOrder.setApprovalUser(AuthUtil.getUserId());
		 return updateById(inspectOrder);
	 }

	 @Override
	 public List<InspectOrderExcel> exportOrder(InspectOrderVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<InspectOrder> list = baseMapper.exportList(vo);
		 if (Func.isNotEmpty(list)) {
			 List<InspectOrderDTO> orderList = InspectOrderWrapper.build().listDTO(list);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return orderList.stream().map(order -> {
				 InspectOrderExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, InspectOrderExcel.class));
				 orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 List<InspectRecord> recordList = inspectRecordService.list(Wrappers.<InspectRecord>query().lambda()
					 .eq(InspectRecord::getOrderId, order.getId()));
				 if (Func.isNotEmpty(recordList)) {
					 List<Integer> abnormalList = recordList.stream().map(InspectRecord::getIsAbnormal).collect(Collectors.toList());
					 if (abnormalList.contains(Func.toInt(StringPool.ONE))) {
						 orderExcel.setInspectResult("异常");
					 } else {
						 orderExcel.setInspectResult("正常");
					 }
				 }
				 InspectPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), InspectPlanDTO.class);
				 switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
					 case DAY:
						 orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm"));
						 orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm"));
						 break;
					 case WEEK:
					 case MONTH:
						 orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd"));
						 orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd"));
						 break;
					 default:
				 }
				 return orderExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 public IPage<InspectOrderDTO> timeoutPage(IPage<InspectOrderDTO> page, InspectOrderVO inspectOrder) {
		 inspectOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.INSPECT.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 inspectOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 inspectOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			 .setExecuteUser(AuthUtil.getUserId());
		 List<InspectOrder> list = baseMapper.timeoutPage(page, inspectOrder);
		 return page.setRecords(InspectOrderWrapper.build().listDTO(list));
	 }

	 @Override
	 public List<InspectOrderDTO> inspectOrderStatistics(Integer queryDate) {
		 return InspectOrderWrapper.build().listDTO(baseMapper.inspectOrderStatistics(queryDate));
	 }

	 @Override
	 public Integer expireSoonCount() {
		 InspectOrderVO inspectOrder = new InspectOrderVO();
		 inspectOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.INSPECT.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 inspectOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		  inspectOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
		 	.setExecuteUser(AuthUtil.getUserId());
		 return baseMapper.expireSoonCount(inspectOrder);
	 }

	 @Override
	 public void sendMessage(List<InspectOrder> list, MessageBizTypeEnum messageBizType) {
		 log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		 list.forEach(order -> {
			 // 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
			 ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			 MessageVo messageVo = new MessageVo();
			 messageVo.setAppKey("SIMAS");
			 messageVo.setSender("SIMAS");
			 messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			 messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			 messageVo.setTitle(messageBizType.getMessage());
			 messageVo.setBizType(messageBizType.getCode());
			 messageVo.setBizId(order.getNo());
			 messageVo.setContent(JSONUtil.toJsonStr(order));
			 messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			 if (Func.isNotEmpty(order.getExecuteUser())) {
				 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				 userVo.setId(order.getExecuteUser());
				 receiverInfoVo.setUserList(Arrays.asList(userVo));
			 } else {
				 // 部门下具有点检员角色的人
				 R<RoleVO> roleResult = sysClient.getRoleByAlias(order.getTenantId(), SimasConstant.SimasRole.INSPECT_USER);
				 if (roleResult.isSuccess() && Func.isNotEmpty(roleResult.getData())) {
					 R<List<User>> userListResult = userClient.userListByDeptRole(order.getExecuteDept(),
						 roleResult.getData().getId());
					 if (userListResult.isSuccess() && Func.isNotEmpty(userListResult.getData())) {
						 receiverInfoVo.setUserList(userListResult.getData().stream().map(user -> {
							 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
							 userVo.setId(user.getId());
							 return userVo;
						 }).collect(Collectors.toList()));
					 }
				 }
			 }
			 messageVo.setReceiverInfoVo(receiverInfoVo);
			 messageClient.pushMessage(messageVo);
		 });
		 log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	 }

	 @Override
	 public IPage<InspectOrderDTO> statisticalReport(IPage<InspectOrderDTO> page, StatisticSearchVO search) {
		 List<InspectOrder> list = baseMapper.statisticalReport(page, search);
		 if (Func.isNotEmpty(list)) {
			 List<InspectOrderDTO> orderList = InspectOrderWrapper.build().listDTO(list);
			 orderList.forEach(dto -> {
				 dto.setEquipmentCategoryName(CommonCache.getEquipmentCategory(dto.getEquipmentCategory()).getCategoryName());
				 if (Func.isNotEmpty(dto.getIsAbnormal())) {
					 if (Func.equals(Func.toInt(StringPool.ONE), dto.getIsAbnormal())) {
						 dto.setInspectResult("异常");
					 } else {
						 dto.setInspectResult("正常");
					 }
				 }
			 });
			 return page.setRecords(orderList);
		 }
		 return page.setRecords(null);
	 }

	 // @Override
	 // public List<InspectOrderStatisticsExcel> exportStatisticalReport(StatisticSearchVO vo) {
	 // 	QueryWrapper<InspectOrder> queryWrapper = Wrappers.query();
	 // 	queryWrapper.lambda().eq(ObjectUtil.isNotEmpty(vo.getStatus()), InspectOrder::getStatus, vo.getStatus());
	 // 	if (vo.getQueryDate() == 1) {
	 // 		queryWrapper.lambda().ge(InspectOrder::getCreateTime, LocalDate.now().minusDays(30));
	 // 	}
	 // 	if (vo.getQueryDate() == 2) {
	 // 		queryWrapper.lambda().ge(InspectOrder::getCreateTime, LocalDate.now().minusDays(7));
	 // 	}
	 // 	if (vo.getQueryDate() == 3) {
	 // 		queryWrapper.lambda().ge(InspectOrder::getCreateTime,
	 // 				DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_START_TIME)
	 // 			.le(InspectOrder::getCreateTime,
	 // 				DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME);
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getStartDate())) {
	 // 		queryWrapper.lambda().ge(InspectOrder::getStartTime, vo.getStartDate() + DateUtils.DAY_START_TIME);
	 // 	}
	 // 	if (Func.isNotEmpty(vo.getEndDate())) {
	 // 		queryWrapper.lambda().le(InspectOrder::getStartTime, vo.getEndDate() + DateUtils.DAY_END_TIME);
	 // 	}
	 // 	List<Long> deptIdList = new ArrayList<>();
	 // 	if (Func.isNotEmpty(vo.getDeptId())) {
	 // 		List<Long> deptIds = Func.toLongList(vo.getDeptId());
	 // 		deptIdList.addAll(deptIds);
	 // 		Func.toLongList(vo.getDeptId()).forEach(deptId -> {
	 // 			List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
	 // 			if (Func.isNotEmpty(deptChildIds)) {
	 // 				deptIdList.addAll(deptChildIds);
	 // 			}
	 // 		});
	 // 		queryWrapper.lambda().in(InspectOrder::getExecuteDept,
	 // 			deptIdList.stream().distinct().collect(Collectors.toList()));
	 // 	}
	 // 	List<InspectOrder> list = this.list(queryWrapper);
	 // 	if (Func.isNotEmpty(list)) {
	 // 		List<InspectOrderDTO> orderList = InspectOrderWrapper.build().listDTO(list);
	 // 		AtomicReference<Integer> sn = new AtomicReference<>(1);
	 // 		return orderList.stream().map(order -> {
	 // 			InspectOrderStatisticsExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, InspectOrderStatisticsExcel.class));
	 // 			EquipmentAccount equipmentAccount = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda()
	 // 				.eq(EquipmentAccount::getId, order.getEquipmentId())
	 // 				.select(EquipmentAccount::getName, EquipmentAccount::getCategoryId));
	 // 			orderExcel.setEquipmentName(equipmentAccount.getName())
	 // 				.setEquipmentCategoryName(SimasCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
	 // 			if (Func.isNotEmpty(order.getIsAbnormal())) {
	 // 				if (Func.equals(Func.toInt(StringPool.ONE), order.getIsAbnormal())) {
	 // 					order.setInspectResult("异常");
	 // 				} else {
	 // 					order.setInspectResult("正常");
	 // 				}
	 // 			}
	 // 			InspectPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), InspectPlanDTO.class);
	 // 			switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
	 // 				case DAY:
	 // 					orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm"));
	 // 					orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm"));
	 // 					break;
	 // 				case WEEK:
	 // 				case MONTH:
	 // 					orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd"));
	 // 					orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd"));
	 // 					break;
	 // 				default:
	 // 			}
	 // 			if (Func.isNotEmpty(order.getSubmitTime())) {
	 // 				orderExcel.setCompleteTimeStr(DateUtil.format(order.getSubmitTime(), "yyyy-MM-dd HH:mm:ss"));
	 // 			}
	 // 			orderExcel.setInspectResult(order.getInspectResult());
	 // 			return orderExcel;
	 // 		}).collect(Collectors.toList());
	 // 	}
	 // 	return null;
	 // }

	 @Override
	 public List<InspectOrderDTO> queryList(InspectOrderVO vo) {
		 return baseMapper.queryList(vo);
	 }

	 @Override
	 public boolean timeoutExplain(InspectOrderVO vo) {
		 InspectOrder inspectOrder = this.getOne(Wrappers.<InspectOrder>query().lambda().eq(InspectOrder::getNo, vo.getNo()));
		 if (inspectOrder == null) {
			 // throw new ServiceException(ResultCode.FAILURE);
			 throw new ServiceException("当前工单不存在");
		 }
		 inspectOrder.setRemark(vo.getRemark());
		 return this.updateById(inspectOrder);
	 }

	 @Override
	 public List<BigScreenMessageDTO> overdueList(String tenantId) {
		 return baseMapper.overdueList(tenantId);
	 }

	 @Override
	 public List<Kv> inspectToday() {
		 return baseMapper.inspectToday();
	 }

	 @Override
	 public List<InspectOrderDTO> coverListToday(String tenantId) {
		 return baseMapper.coverListToday(tenantId);

	 }

	 @Override
	 public Integer handleInspectCount(InspectOrderVO inspectOrder) {
		 return baseMapper.handleInspectCount(inspectOrder);
	 }

	 @Override
	 public List<InspectOrder> listBy(Long executeDeptId, List<Long> userIds, List<Long> equipmentIds, LocalDateTime startTime, LocalDateTime endTime, Integer neStatus) {
		 return this.lambdaQuery()
			 .eq(ObjectUtil.isNotEmpty(executeDeptId), InspectOrder::getExecuteDept, executeDeptId)
			 .in(ObjectUtil.isNotEmpty(userIds), InspectOrder::getExecuteUser, userIds)
			 .in(ObjectUtil.isNotEmpty(equipmentIds), InspectOrder::getEquipmentId, equipmentIds)
			 .ge(ObjectUtil.isNotEmpty(startTime), InspectOrder::getStartTime, startTime)
			 .lt(ObjectUtil.isNotEmpty(endTime), InspectOrder::getStartTime, endTime)
			 .ne(ObjectUtil.isNotEmpty(neStatus), InspectOrder::getStatus, neStatus)
			 .list();
	 }

	 @Override
	 public List<EquipmentStatisticsDTO> inspectStatistics(StatisticSearchVO search, Map<Long, DeviceAccountVO> deviceMap) {
		 List<EquipmentStatisticsDTO> list = inspectOrderMapper.statisticsByEquipment(search);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 DeviceAccountVO equipment = deviceMap.get(dto.getId());
				 dto.setName(equipment.getName()).setSn(equipment.getSn())
					 .setCategoryName(CommonCache.getEquipmentCategory(equipment.getCategoryId()).getCategoryName());
				 if (Func.isNotEmpty(equipment.getUseDept())) {
					 dto.setUseDeptName(SysCache.getDept(equipment.getUseDept()).getDeptName());
				 }
				 dto.setCompleteCount(0).setUnfinishedCount(0).setAbnormalCount(0)
					 .setCompleteRate(BigDecimal.ZERO).setAbnormalRate(BigDecimal.ZERO);
				 StatisticSearchVO searchVO = new StatisticSearchVO(search.getQueryDate(), search.getStartDate(), search.getEndDate(),
					 Func.toLongList(Func.toStr(dto.getId())));
				 List<InspectOrder> orderList = inspectOrderMapper.equipmentStatisticsOfOrder(searchVO);
				 for (InspectOrder order : orderList) {
					 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())
						 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())) {
						 dto.setCompleteCount(dto.getCompleteCount() + 1);
					 }
					 if (Func.isNotEmpty(order.getIsAbnormal()) && order.getIsAbnormal() == 1) {
						 dto.setAbnormalCount(dto.getAbnormalCount() + 1);
					 }
				 }
				 dto.setUnfinishedCount(dto.getCount() - dto.getCompleteCount());
				 // 完成率
				 if (dto.getCount() != 0) {
					 dto.setCompleteRate(new BigDecimal(dto.getCompleteCount())
						 .divide(new BigDecimal(dto.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
				 }
				 // 异常率
				 if (dto.getCompleteCount() != 0) {
					 dto.setAbnormalRate(new BigDecimal(dto.getAbnormalCount())
						 .divide(new BigDecimal(dto.getCompleteCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
				 }
			 });
		 }
		 return list;
	 }




 }
