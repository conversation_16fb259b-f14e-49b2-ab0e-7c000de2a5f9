/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.inspect.dto.InspectPlanDTO;
import com.snszyk.simas.inspect.entity.InspectPlan;
import com.snszyk.simas.inspect.vo.InspectPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备点巡检计划表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public interface InspectPlanMapper extends BaseMapper<InspectPlan> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectPlan
	 * @return
	 */
	List<InspectPlan> page(IPage page, @Param("inspectPlan") InspectPlanVO inspectPlan);

	/**
	 * 查询当天点检计划
	 *
	 * @param currentDate yyyy-MM-dd
	 * @return
	 */
	List<InspectPlanDTO> getTheDayPlans(@Param("currentDate") Date currentDate);

}
