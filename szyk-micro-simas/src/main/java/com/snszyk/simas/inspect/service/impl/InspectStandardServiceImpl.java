 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.inspect.service.impl;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
 import com.snszyk.common.equipment.feign.FeignPage;
 import com.snszyk.common.equipment.feign.ICommonClient;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.StringPool;
 import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
 import com.snszyk.simas.common.vo.EquipmentStandardVO;
 import com.snszyk.simas.inspect.entity.InspectStandard;
 import com.snszyk.simas.inspect.mapper.InspectPlanEquipmentMapper;
 import com.snszyk.simas.inspect.mapper.InspectPlanMapper;
 import com.snszyk.simas.inspect.mapper.InspectStandardMapper;
 import com.snszyk.simas.inspect.service.IInspectStandardService;
 import com.snszyk.simas.inspect.vo.InspectStandardVO;
 import com.snszyk.simas.inspect.wrapper.InspectStandardWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备点巡检标准表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 @AllArgsConstructor
 @Service
 public class InspectStandardServiceImpl extends BaseServiceImpl<InspectStandardMapper, InspectStandard> implements IInspectStandardService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final InspectPlanMapper inspectPlanMapper;
	 private final InspectPlanEquipmentMapper planEquipmentMapper;
	 private final ICommonClient iCommonClient;


	 @Override
	 public IPage<DeviceAccountVO> page(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount) {
		 //R<List<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageList(deviceAccount,
		 // Func.toInt(page.getCurrent()), Func.toInt(page.getSize()));
		 //R<Integer> totalResult = deviceAccountClient.deviceCount(deviceAccount);
		 //if(!deviceResult.isSuccess() || !totalResult.isSuccess()){
		 // throw new ServiceException("查询设备台账信息失败！");
		 //}
		 //if(Func.isNotEmpty(deviceResult.getData())){
		 // deviceResult.getData().forEach(data -> {
		 //	 // 点巡检标准数量
		 //	 data.setStandardCount(this.count(Wrappers.<InspectStandard>query().lambda()
		 //		 .eq(InspectStandard::getEquipmentId, data.getId())));
		 // });
		 //}
		 //IPage<DeviceAccountVO> iPage = new Page<>();
		 //iPage.setCurrent(page.getCurrent());
		 //iPage.setSize(page.getSize());
		 //iPage.setTotal(Func.toLong(Func.toStr(totalResult.getData())));
		 //iPage.setRecords(deviceResult.getData());
		 //return iPage;
		 DeviceAccountPageVO vo = BeanUtil.copy(deviceAccount, DeviceAccountPageVO.class);
		 R<FeignPage<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageListScope(vo,
			 Func.toInt(page.getCurrent()), Func.toInt(page.getSize()), EquipmentDataScopeEnum.INSPECT_STANDARD.getCode());
		 if (!deviceResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isNotEmpty(deviceResult.getData()) && Func.isNotEmpty(deviceResult.getData().getRecords())) {
			 deviceResult.getData().getRecords().forEach(data -> {
				 // 点巡检标准数量
				 data.setStandardCount(this.count(Wrappers.<InspectStandard>query().lambda()
					 .eq(InspectStandard::getEquipmentId, data.getId())));
			 });
		 }
		 IPage<DeviceAccountVO> result = new Page<>();
		 result.setTotal(deviceResult.getData().getTotal());
		 result.setRecords(deviceResult.getData().getRecords());
		 return result;
	 }

	 @Override
	 public EquipmentStandardVO detail(Long equipmentId) {
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 EquipmentStandardVO detail = new EquipmentStandardVO(equipmentId);
		 detail.setEquipmentAccount(deviceAccountResult.getData());
		 List<InspectStandard> standardList = this.list(Wrappers.<InspectStandard>query().lambda()
			 .eq(InspectStandard::getEquipmentId, equipmentId).orderByAsc(InspectStandard::getSort));
		 if (Func.isNotEmpty(standardList)) {
			 detail.setMonitorStandardList(InspectStandardWrapper.build().listVO(standardList));
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean submit(EquipmentStandardVO vo) {
		 Long equipmentId = vo.getEquipmentId();
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 boolean ret;
		 //添加部位
		 Map<String, Integer> monitorMap
			 = vo.getMonitorStandardList().stream().filter(e ->
			 e.getMonitorId() == null).collect(Collectors.toMap(InspectStandardVO::getMonitorName, InspectStandardVO::getMonitorType));
//		 genMonitor(equipmentId, monitorMap);
		 // 1. 判断是否已经存在该标准，如果存在则更新，不存在则新增
		 if (Func.isNotEmpty(vo.getMonitorStandardList())) {
			 List<Long> updateIds = vo.getMonitorStandardList().stream()
				 .filter(monitorStandard -> Func.isNotEmpty(monitorStandard.getId()))
				 .map(monitorStandard -> monitorStandard.getId()).collect(Collectors.toList());
			 // 删除标准
			 if (Func.isNotEmpty(updateIds)) {
				 this.remove(Wrappers.<InspectStandard>query().lambda()
					 .eq(InspectStandard::getEquipmentId, vo.getEquipmentId()).notIn(InspectStandard::getId, updateIds));
			 } else {
				 this.remove(Wrappers.<InspectStandard>query().lambda()
					 .eq(InspectStandard::getEquipmentId, vo.getEquipmentId()));
			 }
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<InspectStandard> list = vo.getMonitorStandardList().stream().map(standardVO -> {
				 InspectStandard inspectStandard = Objects.requireNonNull(BeanUtil.copy(standardVO, InspectStandard.class));
				 if (Func.isEmpty(standardVO.getId())) {
					 inspectStandard.setEquipmentId(vo.getEquipmentId()).setStatus(Func.toInt(StringPool.ONE));
					 //如果是新增的
					 if (standardVO.getMonitorId() == null) {
						 Long monitorId = genMonitor(vo.getEquipmentId(), standardVO.getMonitorName(), standardVO.getMonitorType());
						 inspectStandard.setMonitorId(monitorId);
					 }
				 } else {
				 }
				 inspectStandard.setUpdateTime(DateUtil.now());
				 inspectStandard.setSort(sort.getAndSet(sort.get() + 1));
				 return inspectStandard;
			 }).collect(Collectors.toList());
			 // 2. 保存数据
			 ret = this.saveOrUpdateBatch(list);
		 } else {
			 ret = this.clear(vo.getEquipmentId());
		 }
		 // 3. 返回结果
		 return ret;
	 }


	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public Long genMonitor(Long equipmentId, String monitorName, Integer monitorType) {
		 DeviceMonitor deviceMonitor = new DeviceMonitor();
		 deviceMonitor.setDeviceId(equipmentId);
		 deviceMonitor.setName(monitorName);
		 deviceMonitor.setType(monitorType);
		 R<Long> r = iCommonClient.addEquipmentMonitor(deviceMonitor);
		 if (!r.isSuccess()) {
			 throw new ServiceException("新增设备部位失败！");
		 }

		 return r.getData();
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean clear(Long equipmentId) {
		 // 清空标准，同步删除设备关联的工单
		 //boolean ret = inspectOrderMapper.delete(Wrappers.<InspectOrder>query().lambda()
		 // .eq(InspectOrder::getEquipmentId, equipmentId)
		 // .and(wrapper ->
		 //	 wrapper.eq(InspectOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
		 //		 .or()
		 //		 .eq(InspectOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
		 //		 .or()
		 //		 .eq(InspectOrder::getStatus, OrderStatusEnum.WAIT_CONFIRM.getCode())
		 //		 .or()
		 //		 .eq(InspectOrder::getStatus, OrderStatusEnum.IS_REJECTED.getCode()))) >= 0;
		 // 校验点检计划
		 //List<String> planNoList = new ArrayList<>();
		 //List<InspectPlanEquipment> list = planEquipmentMapper.selectList(Wrappers.<InspectPlanEquipment>query().lambda()
		 // .eq(InspectPlanEquipment::getEquipmentId, equipmentId));
		 //if(Func.isNotEmpty(list)){
		 // list.forEach(planEquipment -> {
		 //	 InspectPlan inspectPlan = inspectPlanMapper.selectById(planEquipment.getPlanId());
		 //	 if(PlanStatusEnum.IS_COMPLETED != PlanStatusEnum.getByCode(inspectPlan.getStatus())
		 //		 || PlanStatusEnum.IS_TERMINATED != PlanStatusEnum.getByCode(inspectPlan.getStatus())){
		 //		 planNoList.add(inspectPlan.getNo());
		 //	 }
		 // });
		 // if(Func.isNotEmpty(planNoList)){
		 //	 throw new ServiceException(String.format("点检计划%s中包含当前设备，无法清空标准！",
		 //		 planNoList.stream().collect(Collectors.joining(StringPool.COMMA))));
		 // }
		 //}
		 // 清空标准
		 boolean ret = this.remove(Wrappers.<InspectStandard>query().lambda()
			 .eq(InspectStandard::getEquipmentId, equipmentId));
		 return ret;
	 }

	 @Override
	 public List<BaseStandardTemplate> generateExcelData(String deptId) {
		 List<Long> deptIds = null;
		 if (Func.isNotEmpty(deptId)) {
			 deptIds = Func.toLongList(deptId);
		 }
		 return this.baseMapper.generateExcelData(deptIds);
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean saveImportData(List<InspectStandard> list) {
		 this.updateSort(list);
		 return this.saveOrUpdateBatch(list);
	 }

	 /**
	  * 添加排序
	  *
	  * @param list
	  */
	 private void updateSort(List<InspectStandard> list) {
		 // 获取设备id分组
		 Map<Long, List<InspectStandard>> map = list.stream().collect(Collectors.groupingBy(InspectStandard::getEquipmentId));
		 map.keySet().forEach(equipmentId -> {
			 InspectStandard standard = this.getOne(Wrappers.<InspectStandard>query().lambda().eq(InspectStandard::getEquipmentId, equipmentId).orderByDesc(InspectStandard::getSort).last("LIMIT 1"));
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 if (Func.isNotEmpty(standard) && !Func.isNull(standard.getSort())) {
				 sort.set(standard.getSort());
			 }
			 map.get(equipmentId).forEach(item -> {
				 InspectStandard entity = this.getOne(Wrappers.<InspectStandard>query().lambda().eq(InspectStandard::getEquipmentId, item.getEquipmentId()).eq(InspectStandard::getMonitorId, item.getMonitorId()));
				 if (Func.isNotEmpty(entity)) {
					 item.setId(entity.getId());
				 } else {
					 item.setSort(sort.getAndSet(sort.get() + 1));
					 //item.setCreateUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now()).setStatus(Func.toInt(StringPool.ONE));
				 }
			 });
		 });
	 }
 }
