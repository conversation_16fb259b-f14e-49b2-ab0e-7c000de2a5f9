/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.dashboard.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工单大屏Mapper接口
 * 负责各类工单的状态统计、超期分析和任务覆盖率统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface OrderDashboardMapper {

	/**
	 * 查询已超期的工单列表（分页）
	 * 使用优化后的UNION ALL查询统计所有类型的超期工单
	 * 只查询状态为已超期的工单
	 * MyBatis Plus分页插件会自动处理分页
	 * <p>
	 * 优化说明：
	 * - 使用CASE WHEN替代IF函数，避免JSQLParser解析问题
	 * - 使用COALESCE替代IFNULL，提高兼容性
	 * - 避免复杂嵌套函数，简化SQL表达式
	 *
	 * @param page          分页参数
	 * @param overdueStatus 已超期状态
	 * @param tenantId      租户ID
	 * @return 超期工单列表
	 */
	List<OverdueOrderStatisticsDto> getOverdueOrderStatistics(
		IPage<OverdueOrderStatisticsDto> page,
		@Param("overdueStatus") Integer overdueStatus,
		@Param("tenantId") String tenantId
	);

	/**
	 * 统计点巡检工单状态分布
	 * 使用数据库层面的聚合查询，按状态分组统计数量
	 * 支持按天数过滤，null表示当日
	 *
	 * @param days     统计天数，null表示当日
	 * @param tenantId 租户ID
	 * @return 点巡检工单状态项列表
	 */
	List<OrderStatusStatisticsDto.OrderStatusItem> getInspectOrderStatusStatistics(
		@Param("days") Integer days,
		@Param("tenantId") String tenantId
	);

	/**
	 * 统计保养工单状态分布
	 * 使用数据库层面的聚合查询，按状态分组统计数量
	 * 支持按天数过滤，null表示当日
	 *
	 * @param days     统计天数，null表示当日
	 * @param tenantId 租户ID
	 * @return 保养工单状态项列表
	 */
	List<OrderStatusStatisticsDto.OrderStatusItem> getMaintainOrderStatusStatistics(
		@Param("days") Integer days,
		@Param("tenantId") String tenantId
	);

	/**
	 * 查询近30天维修工单列表（分页）
	 * 使用数据库层面的查询，查询近30天内的维修工单
	 * MyBatis Plus分页插件会自动处理分页
	 *
	 * @param page     分页参数
	 * @param tenantId 租户ID
	 * @return 维修工单列表
	 */
	List<RepairOrderStatisticsDto> getLast30DaysRepairOrderStatistics(
		IPage<RepairOrderStatisticsDto> page,
		@Param("tenantId") String tenantId
	);

	/**
	 * 查询近一年维修耗时统计（按部位分组分页）
	 * 使用数据库层面的GROUP BY查询，按设备部位分组统计耗时
	 * 只统计已完成状态的维修工单（status = 2 已完成，status = 4 超期完成）
	 * MyBatis Plus分页插件会自动处理分页
	 *
	 * @param page     分页参数
	 * @param tenantId 租户ID
	 * @return 维修耗时统计列表（按部位分组）
	 */
	List<RepairDurationStatisticsDto> getLastYearRepairDurationStatistics(
		IPage<RepairDurationStatisticsDto> page,
		@Param("tenantId") String tenantId
	);

	/**
	 * 计算近一年维修耗时平均值
	 * 使用数据库层面的聚合查询，计算平均耗时
	 * 只统计已完成状态的维修工单（status = 2 已完成，status = 4 超期完成）
	 *
	 * @param tenantId 租户ID
	 * @return 平均耗时（小时）
	 */
	BigDecimal getLastYearRepairDurationAverage(@Param("tenantId") String tenantId);

	/**
	 * 查询当日点巡检工单基础数据
	 * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
	 * 只查询当日（当前日期）创建的点巡检工单
	 *
	 * @param tenantId 租户ID
	 * @return 点巡检工单基础数据列表
	 */
	List<InspectOrderSimpleDto> getTodayInspectOrderSimpleData(@Param("tenantId") String tenantId);

	/**
	 * 查询近30天保养工单基础数据
	 * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
	 * 查询近30天创建的保养工单
	 *
	 * @param tenantId 租户ID
	 * @return 保养工单基础数据列表
	 */
	List<InspectOrderSimpleDto> getLast30DaysMaintainOrderSimpleData(@Param("tenantId") String tenantId);

}
