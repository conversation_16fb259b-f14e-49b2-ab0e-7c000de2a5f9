/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsDto;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsResultDto;
import com.snszyk.simas.dashboard.service.logic.FaultDashboardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 故障大屏控制器
 * 负责设备故障统计和维修时长分析
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard/fault")
@Api(value = "大屏-故障信息", tags = "大屏-故障信息接口")
public class FaultDashboardController {

    private final FaultDashboardLogicService faultDashboardLogicService;

    @GetMapping("/last-30-days-defects")
    @ApiOperation(value = "近30天故障缺陷列表统计", notes = "分页查询近30天内的故障缺陷记录，包括设备名称、故障部位、故障状态、处理时间等信息。同时返回相比去年同期的增长率统计（小数形式，如0.1525表示15.25%增长）。使用MyBatis Plus分页插件优化性能，避免额外的统计查询。")
    @ApiOperationSupport(order = 1)
    public R<FaultDefectStatisticsResultDto> getLast30DaysFaultDefectStatistics(Query query) {
        IPage<FaultDefectStatisticsDto> page = Condition.getPage(query);
        FaultDefectStatisticsResultDto statistics = faultDashboardLogicService.getLast30DaysFaultDefectStatistics(page);
        return R.data(statistics);
    }

}
