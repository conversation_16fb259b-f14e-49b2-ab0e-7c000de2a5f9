/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.dashboard.dto.SparePartsConsumptionStatisticsDto;
import com.snszyk.simas.dashboard.service.logic.SparePartsDashboardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 备品备件大屏控制器
 * 负责备品备件消耗分析和排名统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard/spare-parts")
@Api(value = "大屏-备品备件", tags = "大屏-备品备件接口")
public class SparePartsDashboardController {

	private final SparePartsDashboardLogicService sparePartsDashboardLogicService;

	@GetMapping("/consumption-statistics")
	@ApiOperation(value = "备品备件消耗统计", notes = "统计备品备件的出库消耗情况，包含备品备件基础信息、计量单位、库房信息和消耗数量，按消耗数量降序排序")
	@ApiOperationSupport(order = 1)
	public R<List<SparePartsConsumptionStatisticsDto>> getSparePartsConsumptionStatistics() {
		List<SparePartsConsumptionStatisticsDto> statistics = sparePartsDashboardLogicService.getSparePartsConsumptionStatistics();
		return R.data(statistics);
	}

}
