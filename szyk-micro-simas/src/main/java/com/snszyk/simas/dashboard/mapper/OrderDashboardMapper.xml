<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.dashboard.mapper.OrderDashboardMapper">

    <!-- 查询已超期的工单列表 -->
    <!-- 方案A：分别关联后UNION ALL（当前实现） -->
    <!-- 优势：索引利用充分，JSQLParser兼容性好，易于维护 -->
    <!-- 劣势：重复关联设备表，代码略有冗余 -->
    <select id="getOverdueOrderStatistics" resultType="com.snszyk.simas.dashboard.dto.OverdueOrderStatisticsDto">
        SELECT
            o.no as orderNo,
            'INSPECT_ORDER' as orderType,
            o.equipment_id as equipmentId,
            COALESCE(da.name, '') as equipmentName,
            o.end_time as endTime,
            o.execute_dept as responsibleDeptId,
            o.execute_user as responsibleUserId,
            o.status,
            DATEDIFF(NOW(), o.end_time) as overdueDays
        FROM simas_inspect_order o
        LEFT JOIN device_account da ON o.equipment_id = da.id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        WHERE o.status = #{overdueStatus} AND o.is_deleted = 0 AND o.tenant_id = #{tenantId}

        UNION ALL

        SELECT
            o.no as orderNo,
            'MAINTAIN_ORDER' as orderType,
            o.equipment_id as equipmentId,
            COALESCE(da.name, '') as equipmentName,
            o.end_time as endTime,
            o.execute_dept as responsibleDeptId,
            o.execute_user as responsibleUserId,
            o.status,
            DATEDIFF(NOW(), o.end_time) as overdueDays
        FROM simas_maintain_order o
        LEFT JOIN device_account da ON o.equipment_id = da.id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        WHERE o.status = #{overdueStatus} AND o.is_deleted = 0 AND o.tenant_id = #{tenantId}

        UNION ALL

        SELECT
            o.no as orderNo,
            'LUBRICATE_ORDER' as orderType,
            o.equipment_id as equipmentId,
            COALESCE(da.name, '') as equipmentName,
            CASE
                WHEN o.float_time IS NULL OR o.float_time = 0 THEN o.plan_time
                ELSE DATE_ADD(o.plan_time, INTERVAL o.float_time DAY)
            END as endTime,
            o.charge_dept as responsibleDeptId,
            o.charge_user as responsibleUserId,
            o.status,
            CASE
                WHEN o.float_time IS NULL OR o.float_time = 0 THEN DATEDIFF(NOW(), o.plan_time)
                ELSE DATEDIFF(NOW(), DATE_ADD(o.plan_time, INTERVAL o.float_time DAY))
            END as overdueDays
        FROM simas_lubricate_order o
        LEFT JOIN device_account da ON o.equipment_id = da.id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        WHERE o.status = #{overdueStatus} AND o.is_deleted = 0 AND o.tenant_id = #{tenantId}

        UNION ALL

        SELECT
            o.no as orderNo,
            'OVERHAUL_ORDER' as orderType,
            o.equipment_id as equipmentId,
            COALESCE(da.name, '') as equipmentName,
            CASE
                WHEN o.float_date IS NULL OR o.float_date = 0 THEN o.end_time
                ELSE DATE_ADD(o.end_time, INTERVAL o.float_date DAY)
            END as endTime,
            o.execute_dept as responsibleDeptId,
            o.execute_user as responsibleUserId,
            o.status,
            CASE
                WHEN o.float_date IS NULL OR o.float_date = 0 THEN DATEDIFF(NOW(), o.end_time)
                ELSE DATEDIFF(NOW(), DATE_ADD(o.end_time, INTERVAL o.float_date DAY))
            END as overdueDays
        FROM simas_overhaul_order o
        LEFT JOIN device_account da ON o.equipment_id = da.id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        WHERE o.status = #{overdueStatus} AND o.is_deleted = 0 AND o.tenant_id = #{tenantId}

        UNION ALL

        SELECT
            o.no as orderNo,
            CASE WHEN o.biz_type = 'INTERNAL' THEN 'INTERNAL_REPAIR' ELSE 'EXTERNAL_REPAIR' END as orderType,
            o.equipment_id as equipmentId,
            COALESCE(da.name, '') as equipmentName,
            o.complete_time as endTime,
            CASE WHEN o.biz_type = 'INTERNAL' THEN o.receive_dept ELSE o.follow_dept END as responsibleDeptId,
            CASE WHEN o.biz_type = 'INTERNAL' THEN o.receive_user ELSE o.follow_user END as responsibleUserId,
            o.status,
            DATEDIFF(NOW(), o.complete_time) as overdueDays
        FROM simas_repair o
        LEFT JOIN device_account da ON o.equipment_id = da.id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        WHERE o.status = #{overdueStatus} AND o.is_deleted = 0 AND o.tenant_id = #{tenantId}

        ORDER BY overdueDays DESC, endTime ASC
    </select>

    <!-- 统计点巡检工单状态分布 -->
    <select id="getInspectOrderStatusStatistics" resultType="com.snszyk.simas.dashboard.dto.OrderStatusStatisticsDto$OrderStatusItem">
        SELECT
            o.status as statusCode,
            COUNT(*) as count
        FROM simas_inspect_order o
        WHERE o.is_deleted = 0 AND o.tenant_id = #{tenantId}
        <choose>
            <when test="days == null">
                AND DATE(o.create_time) = CURDATE()
            </when>
            <otherwise>
                AND o.create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
            </otherwise>
        </choose>
        GROUP BY o.status
        ORDER BY o.status
    </select>

    <!-- 统计保养工单状态分布 -->
    <select id="getMaintainOrderStatusStatistics" resultType="com.snszyk.simas.dashboard.dto.OrderStatusStatisticsDto$OrderStatusItem">
        SELECT
            o.status as statusCode,
            COUNT(*) as count
        FROM simas_maintain_order o
        WHERE o.is_deleted = 0 AND o.tenant_id = #{tenantId}
        <choose>
            <when test="days == null">
                AND DATE(o.create_time) = CURDATE()
            </when>
            <otherwise>
                AND o.create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
            </otherwise>
        </choose>
        GROUP BY o.status
        ORDER BY o.status
    </select>

    <!-- 查询近30天维修工单列表（分页） -->
    <select id="getLast30DaysRepairOrderStatistics" resultType="com.snszyk.simas.dashboard.dto.RepairOrderStatisticsDto">
        SELECT r.id           AS repairId,
               r.no           AS repairNo,
               r.equipment_id AS equipmentId,
               da.name        AS equipmentName,
               r.monitor_name AS monitorName,
               fd.status      AS faultStatus,
               r.biz_type     AS bizType,
               r.status
        FROM simas_repair r
                 LEFT JOIN device_account da ON da.id = r.equipment_id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
                 LEFT JOIN simas_fault_defect fd ON fd.repair_no = r.no AND fd.is_deleted = 0 AND fd.tenant_id = #{tenantId}
        WHERE r.is_deleted = 0 AND r.tenant_id = #{tenantId}
          AND r.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY r.create_time DESC
    </select>

    <!-- 公共WHERE条件：近一年已完成维修工单 -->
    <sql id="lastYearCompletedRepairCondition">
        WHERE r.is_deleted = 0 AND r.tenant_id = #{tenantId}
        AND r.actual_complete_time IS NOT NULL
        AND r.actual_complete_time >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
        AND r.time_take IS NOT NULL
        AND r.time_take > 0
        AND r.status IN (2, 4)
    </sql>

    <!-- 查询近一年维修耗时统计（按部位分组分页） -->
    <select id="getLastYearRepairDurationStatistics" resultType="com.snszyk.simas.dashboard.dto.RepairDurationStatisticsDto">
        SELECT
            r.monitor_id AS monitorId,
            da.name AS equipmentName,
            r.monitor_name AS monitorName,
            SUM(r.time_take) AS totalTimeTake,
            COUNT(r.id) AS repairCount
        FROM simas_repair r
        LEFT JOIN device_account da ON da.id = r.equipment_id AND da.is_deleted = 0 AND da.tenant_id = #{tenantId}
        <include refid="lastYearCompletedRepairCondition"/>
        GROUP BY r.monitor_id, da.name, r.monitor_name
        ORDER BY totalTimeTake DESC
    </select>

    <!-- 计算近一年维修耗时平均值 -->
    <select id="getLastYearRepairDurationAverage" resultType="java.math.BigDecimal">
        SELECT AVG(r.time_take)
        FROM simas_repair r
        <include refid="lastYearCompletedRepairCondition"/>
    </select>

    <!-- 查询当日点巡检工单基础数据 -->
    <select id="getTodayInspectOrderSimpleData" resultType="com.snszyk.simas.dashboard.dto.InspectOrderSimpleDto">
        SELECT
            io.id,
            io.equipment_id as equipmentId,
            io.status,
            io.execute_dept as deptId,
            COALESCE(d.dept_name, '未知部门') as deptName
        FROM simas_inspect_order io
        LEFT JOIN szyk_dept d ON io.execute_dept = d.id AND d.is_deleted = 0
        WHERE io.is_deleted = 0 AND io.tenant_id = #{tenantId}
          AND DATE(io.create_time) = CURDATE()
          AND io.execute_dept IS NOT NULL
        ORDER BY io.create_time DESC
    </select>

    <!-- 查询近30天保养工单基础数据 -->
    <select id="getLast30DaysMaintainOrderSimpleData" resultType="com.snszyk.simas.dashboard.dto.InspectOrderSimpleDto">
        SELECT
            mo.id,
            mo.equipment_id as equipmentId,
            mo.status,
            mo.execute_dept as deptId,
            COALESCE(d.dept_name, '未知部门') as deptName
        FROM simas_maintain_order mo
        LEFT JOIN szyk_dept d ON mo.execute_dept = d.id AND d.is_deleted = 0
        WHERE mo.is_deleted = 0 AND mo.tenant_id = #{tenantId}
          AND mo.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
          AND mo.execute_dept IS NOT NULL
        ORDER BY mo.create_time DESC
    </select>
</mapper>
