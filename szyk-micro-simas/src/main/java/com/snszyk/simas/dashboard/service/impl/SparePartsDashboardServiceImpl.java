/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.impl;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.simas.dashboard.dto.SparePartsOutboundSimpleDto;
import com.snszyk.simas.dashboard.mapper.SparePartsDashboardMapper;
import com.snszyk.simas.dashboard.service.ISparePartsDashboardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 备品备件大屏服务实现
 * 负责备品备件消耗分析和排名统计
 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class SparePartsDashboardServiceImpl implements ISparePartsDashboardService {

    private final SparePartsDashboardMapper sparePartsDashboardMapper;

    @Override
    public List<SparePartsOutboundSimpleDto> getSparePartsOutboundSimpleData() {
        // 获取当前租户ID
        String tenantId = AuthUtil.getTenantId();
        // 简化查询逻辑，只获取备品备件出库基础数据
        // 统计计算在业务层进行
        return sparePartsDashboardMapper.getSparePartsOutboundSimpleData(tenantId);
    }

}
