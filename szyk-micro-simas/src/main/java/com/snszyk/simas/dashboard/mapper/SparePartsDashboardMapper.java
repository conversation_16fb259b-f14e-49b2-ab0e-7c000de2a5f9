/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.mapper;

import com.snszyk.simas.dashboard.dto.SparePartsOutboundSimpleDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件大屏Mapper接口
 * 负责备品备件消耗分析和排名统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface SparePartsDashboardMapper {

    /**
     * 查询备品备件出库基础数据
     * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
     * 查询所有已完成的出库单数据
     *
     * @param tenantId 租户ID
     * @return 备品备件出库基础数据列表
     */
    List<SparePartsOutboundSimpleDto> getSparePartsOutboundSimpleData(@Param("tenantId") String tenantId);

}
