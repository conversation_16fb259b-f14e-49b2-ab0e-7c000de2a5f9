/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.dashboard.dto.*;
import com.snszyk.simas.dashboard.service.logic.OrderDashboardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工单大屏控制器
 * 负责各类工单的状态统计、超期分析和任务覆盖率统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard/order")
@Api(value = "大屏-工单", tags = "大屏-工单接口")
public class OrderDashboardController {

	private final OrderDashboardLogicService orderDashboardLogicService;

	@GetMapping("/overdue-orders")
	@ApiOperation(value = "告警工单展示", notes = "分页查询已超期的各类工单列表，包括点检、保养、润滑、检修、维修工单")
	@ApiOperationSupport(order = 1)
	public R<IPage<OverdueOrderStatisticsDto>> getOverdueOrderStatistics(Query query) {
		IPage<OverdueOrderStatisticsDto> statistics = orderDashboardLogicService.getOverdueOrderStatistics(Condition.getPage(query));
		return R.data(statistics);
	}

	@GetMapping("/inspection/daily-status")
	@ApiOperation(value = "当日点巡检工单状态统计", notes = "统计当日（当前日期）的点巡检工单数量，按工单状态分类统计")
	@ApiOperationSupport(order = 2)
	public R<OrderStatusStatisticsDto> getDailyInspectOrderStatusStatistics() {
		OrderStatusStatisticsDto statistics = orderDashboardLogicService.getDailyInspectOrderStatusStatistics();
		return R.data(statistics);
	}

	@GetMapping("/maintenance/last-30-days-status")
	@ApiOperation(value = "近30天保养工单状态统计", notes = "统计近30天（含当天）的保养工单数量，按工单状态分类统计")
	@ApiOperationSupport(order = 3)
	public R<OrderStatusStatisticsDto> getLast30DaysMaintainOrderStatusStatistics() {
		OrderStatusStatisticsDto statistics = orderDashboardLogicService.getLast30DaysMaintainOrderStatusStatistics();
		return R.data(statistics);
	}

	@GetMapping("/repair/last-30-days-orders")
	@ApiOperation(value = "近30天维修工单列表", notes = "分页查询近30天内的维修工单列表，包括设备名称、维修方式、工单状态等信息")
	@ApiOperationSupport(order = 4)
	public R<IPage<RepairOrderStatisticsDto>> getLast30DaysRepairOrderStatistics(Query query) {
		IPage<RepairOrderStatisticsDto> statistics = orderDashboardLogicService.getLast30DaysRepairOrderStatistics(Condition.getPage(query));
		return R.data(statistics);
	}

	@GetMapping("/repair/last-year-duration-statistics")
	@ApiOperation(value = "近一年维修耗时统计", notes = "按设备部位分组统计近一年内已完成维修工单的耗时，包括设备名称、部位名称、该部位总耗时、工单数量和平均耗时")
	@ApiOperationSupport(order = 5)
	public R<RepairDurationStatisticsResultDto> getLastYearRepairDurationStatistics(Query query) {
		IPage<RepairDurationStatisticsDto> page = Condition.getPage(query);
		RepairDurationStatisticsResultDto statistics = orderDashboardLogicService.getLastYearRepairDurationStatistics(page);
		return R.data(statistics);
	}

	@GetMapping("/inspection/device-statistics")
	@ApiOperation(value = "点巡检工单设备统计", notes = "按工单执行部门分组统计当日点巡检工单设备完成情况，包括部门名称、设备总量、完成量、完成率（小数形式），按完成率从高到低排序，完成率相同时按设备总量降序排列")
	@ApiOperationSupport(order = 6)
	public R<List<OrderDeviceStatisticsDto>> getInspectOrderDeviceStatistics() {
		List<OrderDeviceStatisticsDto> statistics = orderDashboardLogicService.getInspectOrderDeviceStatistics();
		return R.data(statistics);
	}

	@GetMapping("/maintain/device-statistics")
	@ApiOperation(value = "保养工单设备统计", notes = "按工单执行部门分组统计近30天保养工单设备完成情况，包括部门名称、设备总量、完成量、完成率（小数形式），按完成率从高到低排序，完成率相同时按设备总量降序排列")
	@ApiOperationSupport(order = 7)
	public R<List<OrderDeviceStatisticsDto>> getMaintainOrderDeviceStatistics() {
		List<OrderDeviceStatisticsDto> statistics = orderDashboardLogicService.getMaintainOrderDeviceStatistics();
		return R.data(statistics);
	}

}
