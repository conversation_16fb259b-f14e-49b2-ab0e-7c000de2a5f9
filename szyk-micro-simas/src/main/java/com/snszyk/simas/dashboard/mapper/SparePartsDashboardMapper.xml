<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.dashboard.mapper.SparePartsDashboardMapper">

    <!-- 查询备品备件出库基础数据 -->
    <select id="getSparePartsOutboundSimpleData" resultType="com.snszyk.simas.dashboard.dto.SparePartsOutboundSimpleDto">
        SELECT
            soi.dict_id as dictId,
            soi.outbound_quantity as outboundQuantity
        FROM simas_spare_parts_outbound_order soo
        INNER JOIN simas_spare_parts_outbound_item soi ON soo.id = soi.outbound_order_id
            AND soi.delete_time = 0 AND soi.tenant_id = #{tenantId}
        WHERE soo.delete_time = 0 AND soo.tenant_id = #{tenantId}
          AND soo.status = 1
        ORDER BY soo.create_time DESC
    </select>

</mapper>
