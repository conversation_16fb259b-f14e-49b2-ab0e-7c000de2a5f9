/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.dashboard.dto.*;
import com.snszyk.simas.dashboard.mapper.OrderDashboardMapper;
import com.snszyk.simas.dashboard.service.IOrderDashboardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 工单大屏服务实现
 * 负责各类工单的状态统计、超期分析和任务覆盖率统计
 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderDashboardServiceImpl implements IOrderDashboardService {

	private final OrderDashboardMapper orderDashboardMapper;

	@Override
	public IPage<OverdueOrderStatisticsDto> getOverdueOrderStatistics(IPage<OverdueOrderStatisticsDto> page) {
		try {
			// 获取当前租户ID
			String tenantId = AuthUtil.getTenantId();
			// 使用优化后的UNION ALL查询进行数据库层面分页
			// SQL已优化：使用CASE WHEN替代IF函数，避免JSQLParser解析问题
			List<OverdueOrderStatisticsDto> records = orderDashboardMapper.getOverdueOrderStatistics(page, OrderStatusEnum.IS_OVERDUE.getCode(), tenantId);
			return page.setRecords(records);
		} catch (Exception e) {
			log.error("查询超期工单统计失败: {}", e.getMessage(), e);
			// 如果查询失败，返回空结果
			return page.setRecords(new ArrayList<>()).setTotal(0);
		}
	}

	@Override
	public List<OrderStatusStatisticsDto.OrderStatusItem> getInspectOrderStatusStatistics(Integer days) {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用数据库层面的聚合查询，按状态分组统计数量
		// 支持按天数过滤，null表示当日
		return orderDashboardMapper.getInspectOrderStatusStatistics(days, tenantId);
	}

	@Override
	public List<OrderStatusStatisticsDto.OrderStatusItem> getMaintainOrderStatusStatistics(Integer days) {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用数据库层面的聚合查询，按状态分组统计数量
		// 支持按天数过滤，null表示当日
		return orderDashboardMapper.getMaintainOrderStatusStatistics(days, tenantId);
	}

	@Override
	public IPage<RepairOrderStatisticsDto> getLast30DaysRepairOrderStatistics(IPage<RepairOrderStatisticsDto> page) {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用MyBatis Plus分页插件进行数据库层面分页
		// 分页插件会自动处理分页和总数统计
		List<RepairOrderStatisticsDto> records = orderDashboardMapper.getLast30DaysRepairOrderStatistics(page, tenantId);
		return page.setRecords(records);
	}

	@Override
	public IPage<RepairDurationStatisticsDto> getLastYearRepairDurationStatistics(IPage<RepairDurationStatisticsDto> page) {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用MyBatis Plus分页插件进行数据库层面分页
		// 分页插件会自动处理按部位分组的分页和总数统计
		List<RepairDurationStatisticsDto> records = orderDashboardMapper.getLastYearRepairDurationStatistics(page, tenantId);
		return page.setRecords(records);
	}

	@Override
	public BigDecimal getLastYearRepairDurationAverage() {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用数据库层面的聚合查询计算平均耗时
		// 只统计已完成状态的维修工单
		return orderDashboardMapper.getLastYearRepairDurationAverage(tenantId);
	}

	@Override
	public List<InspectOrderSimpleDto> getTodayInspectOrderSimpleData() {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 简化查询逻辑，只获取当日点巡检工单基础数据
		// 统计计算在业务层进行
		return orderDashboardMapper.getTodayInspectOrderSimpleData(tenantId);
	}

	@Override
	public List<InspectOrderSimpleDto> getLast30DaysMaintainOrderSimpleData() {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 简化查询逻辑，只获取近30天保养工单基础数据
		// 统计计算在业务层进行
		return orderDashboardMapper.getLast30DaysMaintainOrderSimpleData(tenantId);
	}

}
