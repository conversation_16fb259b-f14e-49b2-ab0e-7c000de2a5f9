/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsDto;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsResultDto;
import com.snszyk.simas.dashboard.service.IFaultDashboardService;
import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 故障大屏业务逻辑服务
 * 负责故障分析的业务逻辑处理、数据转换、字典转换
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class FaultDashboardLogicService {

    private final IFaultDashboardService faultDashboardService;

    /**
     * 获取近30天故障缺陷列表统计
     * 查询近30天内的故障缺陷记录，包含设备信息和故障状态
     * 同时计算同比增长率
     *
     * @param page 分页参数
     * @return 故障缺陷统计结果（包含分页数据和同比统计）
     */
    @Transactional(readOnly = true)
    public FaultDefectStatisticsResultDto getLast30DaysFaultDefectStatistics(IPage<FaultDefectStatisticsDto> page) {
        // 获取故障缺陷统计分页数据（数据库层面查询）
        IPage<FaultDefectStatisticsDto> pageResult = faultDashboardService.getLast30DaysFaultDefectStatistics(page);

        if (ObjectUtil.isNotEmpty(pageResult.getRecords())) {
            // 填充故障状态名称
            fillFaultDefectInfo(pageResult.getRecords());
        } else {
            log.info("近30天故障缺陷统计结果为空");
        }

        // 计算同比增长率（使用分页查询的total属性，避免额外的统计查询）
        BigDecimal yearOverYearGrowthRate = calculateYearOverYearGrowthRate(pageResult.getTotal());

        // 构建结果对象
        FaultDefectStatisticsResultDto result = new FaultDefectStatisticsResultDto();
        result.setPageData(pageResult);
        result.setYearOverYearGrowthRate(yearOverYearGrowthRate);

        return result;
    }

    /**
     * 填充故障缺陷信息（字典转换）
     */
    private void fillFaultDefectInfo(java.util.List<FaultDefectStatisticsDto> records) {
        if (ObjectUtil.isEmpty(records)) {
            return;
        }

        records.forEach(dto -> {
            // 填充故障状态名称
            try {
                FaultBizStatusEnum statusEnum = FaultBizStatusEnum.getByCode(dto.getStatus());
                if (statusEnum != null) {
                    dto.setStatusName(statusEnum.getName());
                }
            } catch (Exception e) {
                log.warn("获取故障状态名称失败，状态码: {}", dto.getStatus(), e);
            }
        });
    }

    /**
     * 计算同比增长率
     * 计算公式：(今年数量 - 去年数量) / 去年数量
     * 优化：使用分页查询的total属性获取当前数量，避免额外的统计查询
     * 数据格式：返回小数形式（如0.1525表示15.25%），符合API设计规范
     *
     * @param currentCount 当前近30天故障缺陷数量（从分页查询的total获取）
     * @return 同比增长率（小数形式）
     */
    private BigDecimal calculateYearOverYearGrowthRate(Long currentCount) {
        try {
            // 当前数量（从分页查询的total属性获取，避免额外查询）
            if (currentCount == null) {
                currentCount = 0L;
            }

            // 获取去年同期故障缺陷数量
            Integer lastYearCount = faultDashboardService.getLastYearSamePeriodFaultDefectCount();
            if (lastYearCount == null) {
                lastYearCount = 0;
            }

            // 如果去年同期数量为0，则无法计算增长率
            if (lastYearCount == 0) {
                // 如果今年有数据而去年没有，返回1.00（表示100%增长）
                return currentCount > 0 ? new BigDecimal("1.00") : BigDecimal.ZERO;
            }

            // 计算增长率：(今年数量 - 去年数量) / 去年数量
            // 返回小数形式，如0.1525表示15.25%增长
            BigDecimal currentCountDecimal = new BigDecimal(currentCount);
            BigDecimal lastYearCountDecimal = new BigDecimal(lastYearCount);
            BigDecimal difference = currentCountDecimal.subtract(lastYearCountDecimal);

            return difference.divide(lastYearCountDecimal, 4, RoundingMode.HALF_UP)
                    .setScale(4, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.error("计算故障缺陷同比增长率失败", e);
            return BigDecimal.ZERO;
        }
    }

}
