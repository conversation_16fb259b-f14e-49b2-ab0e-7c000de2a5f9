/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsDto;
import com.snszyk.simas.dashboard.mapper.FaultDashboardMapper;
import com.snszyk.simas.dashboard.service.IFaultDashboardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 故障大屏服务实现
 * 负责设备故障统计和维修时长分析
 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class FaultDashboardServiceImpl implements IFaultDashboardService {

    private final FaultDashboardMapper faultDashboardMapper;

    @Override
    public IPage<FaultDefectStatisticsDto> getLast30DaysFaultDefectStatistics(IPage<FaultDefectStatisticsDto> page) {
        // 获取当前租户ID
        String tenantId = AuthUtil.getTenantId();
        // 使用MyBatis Plus分页插件进行数据库层面分页
        // 分页插件会自动处理分页和总数统计
        List<FaultDefectStatisticsDto> records = faultDashboardMapper.getLast30DaysFaultDefectStatistics(page, tenantId);
        return page.setRecords(records);
    }

    @Override
    public Integer getLastYearSamePeriodFaultDefectCount() {
        // 获取当前租户ID
        String tenantId = AuthUtil.getTenantId();
        // 使用数据库层面的聚合查询统计去年同期故障缺陷数量
        return faultDashboardMapper.getLastYearSamePeriodFaultDefectCount(tenantId);
    }

}
