/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.logic;

import com.snszyk.common.equipment.dto.MeasureUnitDto;
import com.snszyk.common.equipment.feign.IMeasureUnitClient;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.SparePartsDictStatusEnum;
import com.snszyk.simas.dashboard.dto.SparePartsConsumptionStatisticsDto;
import com.snszyk.simas.dashboard.dto.SparePartsOutboundSimpleDto;
import com.snszyk.simas.dashboard.service.ISparePartsDashboardService;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.service.ISparePartsDictService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 备品备件大屏业务逻辑服务
 * 负责备品备件统计的业务逻辑处理、数据转换
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class SparePartsDashboardLogicService {

	private final ISparePartsDashboardService sparePartsDashboardService;
	private final ISparePartsDictService sparePartsDictService;
	private final ISparePartsWarehouseService sparePartsWarehouseService;
	private final IMeasureUnitClient measureUnitClient;

	/**
	 * 获取备品备件消耗统计
	 * 查询备品备件出库数据，在Java业务层进行统计计算
	 * 按消耗数量降序排序返回结果
	 *
	 * @return 备品备件消耗统计列表
	 */
	@Transactional(readOnly = true)
	public List<SparePartsConsumptionStatisticsDto> getSparePartsConsumptionStatistics() {
		// 获取备品备件出库基础数据
		List<SparePartsOutboundSimpleDto> outboundData = sparePartsDashboardService.getSparePartsOutboundSimpleData();

		if (ObjectUtil.isEmpty(outboundData)) {
			log.info("备品备件出库数据为空");
			return Collections.emptyList();
		}

		// 使用Java Stream API进行分组统计
		return calculateConsumptionStatistics(outboundData);
	}

	/**
	 * 计算备品备件消耗统计数据
	 * 使用Java Stream API进行分组统计，提高代码可读性和维护性
	 *
	 * @param outboundData 出库基础数据
	 * @return 消耗统计结果
	 */
	private List<SparePartsConsumptionStatisticsDto> calculateConsumptionStatistics(List<SparePartsOutboundSimpleDto> outboundData) {
		// 处理出库数据，获取dictId和消耗数量的映射
		Map<Long, BigDecimal> dictConsumptionMap = processOutboundData(outboundData);

		if (dictConsumptionMap.isEmpty()) {
			log.info("处理后的消耗数据为空");
			return Collections.emptyList();
		}

		// 获取备品备件字典信息
		List<Long> dictIds = new ArrayList<>(dictConsumptionMap.keySet());
		List<SparePartsDictDTO> dictList = sparePartsDictService.listByIds(dictIds);

		if (ObjectUtil.isEmpty(dictList)) {
			log.warn("未找到对应的备品备件字典信息");
			return Collections.emptyList();
		}

		// 构建统计结果
		return buildConsumptionStatistics(dictList, dictConsumptionMap);
	}

	/**
	 * 处理出库数据，获取dictId和消耗数量的映射
	 * 直接使用SQL查询返回的dictId和outboundQuantity进行统计
	 *
	 * @param outboundData 出库基础数据
	 * @return dictId和消耗数量的映射
	 */
	private Map<Long, BigDecimal> processOutboundData(List<SparePartsOutboundSimpleDto> outboundData) {
		// 直接按dictId分组，对outboundQuantity进行求和统计
		return outboundData.stream()
			.filter(outbound -> outbound.getDictId() != null && outbound.getOutboundQuantity() != null)
			.collect(Collectors.groupingBy(
				SparePartsOutboundSimpleDto::getDictId,
				Collectors.reducing(
					BigDecimal.ZERO,
					SparePartsOutboundSimpleDto::getOutboundQuantity,
					BigDecimal::add
				)
			));
	}


	/**
	 * 构建消耗统计结果
	 * 填充备品备件基础信息和扩展信息
	 *
	 * @param dictList           备品备件字典列表
	 * @param dictConsumptionMap dictId和消耗数量的映射
	 * @return 消耗统计结果列表
	 */
	private List<SparePartsConsumptionStatisticsDto> buildConsumptionStatistics(
		List<SparePartsDictDTO> dictList, Map<Long, BigDecimal> dictConsumptionMap) {

		// 获取计量单位信息
		List<Long> measureUnitIds = dictList.stream()
			.map(SparePartsDictDTO::getMeasureUnitId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());
		Map<Long, MeasureUnitDto> measureUnitMap = getMeasureUnitMap(measureUnitIds);

		// 获取库房信息
		List<Long> warehouseIds = dictList.stream()
			.map(SparePartsDictDTO::getDefaultWarehouseId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());
		Map<Long, String> warehouseMap = getWarehouseMap(warehouseIds);

		// 构建结果列表
		return dictList.stream()
			.map(dict -> {
				SparePartsConsumptionStatisticsDto dto = new SparePartsConsumptionStatisticsDto();
				dto.setSparePartsId(dict.getId());
				dto.setNo(dict.getNo());
				dto.setName(dict.getName());
				dto.setModel(dict.getModel());
				dto.setMeasureUnitId(dict.getMeasureUnitId());
				dto.setDefaultWarehouseId(dict.getDefaultWarehouseId());
				dto.setStatus(dict.getStatus());
				dto.setSafeStockAmount(dict.getSafeStockAmount());

				// 设置状态名称
				SparePartsDictStatusEnum statusEnum = SparePartsDictStatusEnum.getByCode(dict.getStatus());
				if (statusEnum != null) {
					dto.setStatusName(statusEnum.getValue());
				}

				// 设置消耗数量
				BigDecimal consumptionAmount = dictConsumptionMap.getOrDefault(dict.getId(), BigDecimal.ZERO);
				dto.setConsumptionAmount(consumptionAmount);

				// 设置精度、计量单位名称
				MeasureUnitDto measureUnit = measureUnitMap.get(dict.getMeasureUnitId());
				if (measureUnit != null) {
					dto.setMeasureUnitPrecision(measureUnit.getAccuracy());
					dto.setMeasureUnitName(measureUnit.getName());
				}

				// 设置库房名称
				String warehouseName = warehouseMap.get(dict.getDefaultWarehouseId());
				dto.setDefaultWarehouseName(warehouseName);

				return dto;
			})
			// 按消耗数量降序排序
			.sorted(Comparator.comparing(SparePartsConsumptionStatisticsDto::getConsumptionAmount).reversed())
			.limit(5)
			.collect(Collectors.toList());
	}

	/**
	 * 获取计量单位信息映射
	 *
	 * @param measureUnitIds 计量单位ID列表
	 * @return 计量单位信息映射
	 */
	private Map<Long, MeasureUnitDto> getMeasureUnitMap(List<Long> measureUnitIds) {
		if (ObjectUtil.isEmpty(measureUnitIds)) {
			return Collections.emptyMap();
		}

		try {
			R<List<MeasureUnitDto>> result = measureUnitClient.listByIds(measureUnitIds);
			if (result != null && ObjectUtil.isNotEmpty(result.getData())) {
				return result.getData().stream()
					.collect(Collectors.toMap(MeasureUnitDto::getId, unit -> unit));
			}
		} catch (Exception e) {
			log.warn("获取计量单位信息失败: {}", e.getMessage());
		}

		return Collections.emptyMap();
	}

	/**
	 * 获取库房信息映射
	 *
	 * @param warehouseIds 库房ID列表
	 * @return 库房信息映射
	 */
	private Map<Long, String> getWarehouseMap(List<Long> warehouseIds) {
		if (ObjectUtil.isEmpty(warehouseIds)) {
			return Collections.emptyMap();
		}

		try {
			List<SparePartsWarehouse> warehouses = sparePartsWarehouseService.listByIds(warehouseIds);
			if (ObjectUtil.isNotEmpty(warehouses)) {
				return warehouses.stream()
					.collect(Collectors.toMap(SparePartsWarehouse::getId, SparePartsWarehouse::getName));
			}
		} catch (Exception e) {
			log.warn("获取库房信息失败: {}", e.getMessage());
		}

		return Collections.emptyMap();
	}

}
