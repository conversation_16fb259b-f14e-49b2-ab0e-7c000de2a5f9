/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.impl;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.EquipmentStatusEnum;
import com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto;
import com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto;
import com.snszyk.simas.dashboard.mapper.EquipmentDashboardMapper;
import com.snszyk.simas.dashboard.service.IEquipmentDashboardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.snszyk.common.equipment.enums.YesNoEnum;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备大屏展示服务实现
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
@AllArgsConstructor
public class EquipmentDashboardServiceImpl implements IEquipmentDashboardService {

	private final EquipmentDashboardMapper equipmentDashboardMapper;

	@Override
	public EquipmentTotalStatisticsDto getEquipmentTotalStatistics() {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用数据库层面的聚合查询，传递枚举值作为参数
		EquipmentTotalStatisticsDto result = equipmentDashboardMapper.getEquipmentTotalStatistics(
			EquipmentStatusEnum.IN_USE.getCode(),
			EquipmentStatusEnum.IDLE.getCode(),
			EquipmentStatusEnum.IN_REPAIR.getCode(),
			EquipmentStatusEnum.SCRAPPED.getCode(),
			tenantId,
			YesNoEnum.NO.getCode()
		);
		return result != null ? result : new EquipmentTotalStatisticsDto();
	}

	@Override
	public List<DepartmentEquipmentStatisticsDto> getDepartmentEquipmentStatistics() {
		// 获取当前租户ID
		String tenantId = AuthUtil.getTenantId();
		// 使用数据库层面的聚合查询，传递枚举值作为参数
		List<DepartmentEquipmentStatisticsDto> result = equipmentDashboardMapper.getDepartmentEquipmentStatistics(
			EquipmentStatusEnum.IN_USE.getCode(),
			EquipmentStatusEnum.IDLE.getCode(),
			EquipmentStatusEnum.IN_REPAIR.getCode(),
			EquipmentStatusEnum.SCRAPPED.getCode(),
			tenantId,
			YesNoEnum.NO.getCode()
		);
		return ObjectUtil.isEmpty(result) ? new ArrayList<>() : result;
	}

}
