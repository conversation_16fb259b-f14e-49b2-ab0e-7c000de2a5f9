<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.dashboard.mapper.FaultDashboardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="faultDefectStatisticsResultMap" type="com.snszyk.simas.dashboard.dto.FaultDefectStatisticsDto">
        <id column="fault_defect_id" property="faultDefectId"/>
        <result column="fault_defect_no" property="faultDefectNo"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="status" property="status"/>
        <result column="operate_time" property="operateTime"/>
    </resultMap>

    <!-- 公共WHERE条件 -->
    <sql id="commonWhere">
        WHERE fd.is_deleted = 0
        AND da.is_deleted = 0
        AND fd.tenant_id = #{tenantId}
    </sql>

    <!-- 近30天故障缺陷列表查询 -->
    <select id="getLast30DaysFaultDefectStatistics" resultMap="faultDefectStatisticsResultMap">
        SELECT
            fd.id AS fault_defect_id,
            fd.no AS fault_defect_no,
            fd.equipment_id,
            da.name AS equipment_name,
            fd.monitor_name,
            fd.status,
            fd.operate_time
        FROM simas_fault_defect fd
        LEFT JOIN device_account da ON fd.equipment_id = da.id AND da.is_deleted = 0
        <include refid="commonWhere"/>
        AND fd.create_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY fd.create_time DESC
    </select>



    <!-- 统计去年同期故障缺陷数量 -->
    <select id="getLastYearSamePeriodFaultDefectCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM simas_fault_defect fd
        LEFT JOIN device_account da ON fd.equipment_id = da.id AND da.is_deleted = 0
        <include refid="commonWhere"/>
        AND fd.create_time >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL 1 YEAR), INTERVAL 30 DAY)
        AND fd.create_time &lt; DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
    </select>

</mapper>
