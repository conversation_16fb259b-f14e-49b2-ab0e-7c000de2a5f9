/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.logic;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto;
import com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto;
import com.snszyk.simas.dashboard.service.IEquipmentDashboardService;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备大屏展示业务逻辑服务
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Slf4j
@Service
@AllArgsConstructor
public class EquipmentDashboardLogicService {

    private final IEquipmentDashboardService equipmentDashboardService;

    @Transactional(readOnly = true)
    public EquipmentTotalStatisticsDto getEquipmentTotalStatistics() {
        // 获取设备总数统计（数据库层面聚合查询）
        EquipmentTotalStatisticsDto statistics = equipmentDashboardService.getEquipmentTotalStatistics();

        if (statistics == null) {
            log.warn("获取设备总数统计结果为空");
            return new EquipmentTotalStatisticsDto();
        }

        return statistics;
    }

    @Transactional(readOnly = true)
    public List<DepartmentEquipmentStatisticsDto> getDepartmentEquipmentStatistics() {
        // 获取部门设备统计（数据库层面聚合查询）
        List<DepartmentEquipmentStatisticsDto> statistics = equipmentDashboardService.getDepartmentEquipmentStatistics();

        if (ObjectUtil.isEmpty(statistics)) {
            log.info("按部门统计设备结果为空");
            return new ArrayList<>();
        }

        // 填充部门信息
        fillDepartmentInfo(statistics);

        // 按部门名称排序
        return statistics.stream()
                .sorted((a, b) -> {
                    if (a.getDeptName() == null) return 1;
                    if (b.getDeptName() == null) return -1;
                    return a.getDeptName().compareTo(b.getDeptName());
                })
                .collect(Collectors.toList());
    }



    /**
     * 填充部门信息
     * 这里的try-catch是必要的，因为需要降级处理（设置默认值）
     */
    private void fillDepartmentInfo(List<DepartmentEquipmentStatisticsDto> result) {
        result.forEach(dto -> {
            try {
                Dept dept = SysCache.getDept(dto.getDeptId());
                if (dept != null) {
                    dto.setDeptName(dept.getDeptName());
                    dto.setDeptPath(dept.getFullName());
                }
            } catch (Exception e) {
                log.warn("获取部门信息失败，部门ID: {}", dto.getDeptId(), e);
                dto.setDeptName("未知部门");
            }
        });
    }

}
