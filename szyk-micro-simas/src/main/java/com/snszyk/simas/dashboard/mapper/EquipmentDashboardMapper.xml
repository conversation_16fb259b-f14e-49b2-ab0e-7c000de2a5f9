<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.dashboard.mapper.EquipmentDashboardMapper">

    <!-- 设备总数统计 -->
    <select id="getEquipmentTotalStatistics" resultType="com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto">
        SELECT
            COALESCE(COUNT(*), 0) as totalCount,
            COALESCE(SUM(CASE WHEN status = #{inUseStatus} THEN 1 ELSE 0 END), 0) as inUseCount,
            COALESCE(SUM(CASE WHEN status = #{idleStatus} THEN 1 ELSE 0 END), 0) as idleCount,
            COALESCE(SUM(CASE WHEN status = #{inRepairStatus} THEN 1 ELSE 0 END), 0) as inRepairCount,
            COALESCE(SUM(CASE WHEN status = #{scrappedStatus} THEN 1 ELSE 0 END), 0) as scrappedCount
        FROM device_account
        WHERE is_deleted = 0 AND tenant_id = #{tenantId} AND is_lease_back = #{isLeaseBack}

    </select>

    <!-- 按部门统计设备分布情况 -->
    <!-- 部门优先级：优先使用use_dept，如果为空则使用belong_dept -->
    <select id="getDepartmentEquipmentStatistics" resultType="com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto">
        SELECT
            dept_id as deptId,
            COALESCE(COUNT(*), 0) as totalCount,
            COALESCE(SUM(CASE WHEN status = #{inUseStatus} THEN 1 ELSE 0 END), 0) as inUseCount,
            COALESCE(SUM(CASE WHEN status = #{idleStatus} THEN 1 ELSE 0 END), 0) as idleCount,
            COALESCE(SUM(CASE WHEN status = #{inRepairStatus} THEN 1 ELSE 0 END), 0) as inRepairCount,
            COALESCE(SUM(CASE WHEN status = #{scrappedStatus} THEN 1 ELSE 0 END), 0) as scrappedCount
        FROM (
            SELECT
                COALESCE(use_dept, belong_dept) as dept_id,
                status
            FROM device_account
            WHERE is_deleted = 0 AND tenant_id = #{tenantId}
              AND COALESCE(use_dept, belong_dept) IS NOT NULL AND is_lease_back = #{isLeaseBack}
        ) t
        GROUP BY dept_id
        ORDER BY totalCount DESC
    </select>

</mapper>
