/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.mapper;

import com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto;
import com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备大屏展示Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface EquipmentDashboardMapper {

    /**
     * 统计设备总数概览
     * 使用数据库层面的聚合查询，提高查询性能
     *
     * @param inUseStatus    在用状态值
     * @param idleStatus     备用状态值
     * @param inRepairStatus 维修状态值
     * @param scrappedStatus 报废状态值
     * @param tenantId       租户ID
	 * @param isLeaseBack    租赁的设备是否归还
     * @return 设备总数统计信息
     */
    EquipmentTotalStatisticsDto getEquipmentTotalStatistics(
        @Param("inUseStatus") Integer inUseStatus,
        @Param("idleStatus") Integer idleStatus,
        @Param("inRepairStatus") Integer inRepairStatus,
        @Param("scrappedStatus") Integer scrappedStatus,
        @Param("tenantId") String tenantId,
		@Param("isLeaseBack") Integer isLeaseBack
    );

    /**
     * 按部门统计设备分布情况
     * 使用数据库层面的聚合查询，提高查询性能
     * 优先按使用部门统计，无使用部门则按归属部门统计
     *
     * @param inUseStatus    在用状态值
     * @param idleStatus     备用状态值
     * @param inRepairStatus 维修状态值
     * @param scrappedStatus 报废状态值
     * @param tenantId       租户ID
	 * @param isLeaseBack    租赁的设备是否归还
     * @return 部门设备统计列表
     */
    List<DepartmentEquipmentStatisticsDto> getDepartmentEquipmentStatistics(
        @Param("inUseStatus") Integer inUseStatus,
        @Param("idleStatus") Integer idleStatus,
        @Param("inRepairStatus") Integer inRepairStatus,
        @Param("scrappedStatus") Integer scrappedStatus,
        @Param("tenantId") String tenantId,
		@Param("isLeaseBack") Integer isLeaseBack
    );

}
