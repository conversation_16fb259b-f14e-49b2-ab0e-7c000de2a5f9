/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.OrderTypeEnum;
import com.snszyk.simas.dashboard.dto.*;
import com.snszyk.simas.dashboard.service.IOrderDashboardService;
import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 工单大屏业务逻辑服务
 * 负责工单统计、超期分析和任务覆盖率的业务逻辑处理、数据转换、字典转换
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderDashboardLogicService {

	private final IOrderDashboardService orderDashboardService;

	@Transactional(readOnly = true)
	public IPage<OverdueOrderStatisticsDto> getOverdueOrderStatistics(IPage<OverdueOrderStatisticsDto> page) {
		// 获取超期工单统计（数据库层面联合查询）
		IPage<OverdueOrderStatisticsDto> pageResult = orderDashboardService.getOverdueOrderStatistics(page);

		if (ObjectUtil.isEmpty(pageResult.getRecords())) {
			log.info("超期工单统计结果为空");
			return pageResult;
		}

		// 填充工单类型名称、部门名称、用户名称
		fillOverdueOrderInfo(pageResult.getRecords());

		// 数据已在数据库层面按超期天数排序，无需应用层再次排序
		return pageResult;
	}

	/**
	 * 填充超期工单信息
	 */
	private void fillOverdueOrderInfo(List<OverdueOrderStatisticsDto> statistics) {
		statistics.forEach(dto -> {
			// 填充工单类型名称
			try {
				OrderTypeEnum orderTypeEnum = OrderTypeEnum.valueOf(dto.getOrderType());
				dto.setOrderTypeName(orderTypeEnum.getDesc());
			} catch (Exception e) {
				log.warn("获取工单类型名称失败，工单类型: {}", dto.getOrderType(), e);
				dto.setOrderTypeName(dto.getOrderType());
			}

			// 填充状态名称
			try {
				OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(dto.getStatus());
				if (statusEnum != null) {
					dto.setStatusName(statusEnum.getName());
				}
			} catch (Exception e) {
				log.warn("获取工单状态名称失败，状态码: {}", dto.getStatus(), e);
			}

			// 填充部门名称
			if (dto.getResponsibleDeptId() != null) {
				try {
					Dept dept = SysCache.getDept(dto.getResponsibleDeptId());
					if (dept != null) {
						dto.setResponsibleDeptName(dept.getDeptName());
					}
				} catch (Exception e) {
					log.warn("获取部门信息失败，部门ID: {}", dto.getResponsibleDeptId(), e);
					dto.setResponsibleDeptName("未知部门");
				}
			}

			// 填充用户名称
			if (dto.getResponsibleUserId() != null) {
				try {
					User user = UserCache.getUser(dto.getResponsibleUserId());
					if (user != null) {
						dto.setResponsibleUserName(user.getRealName());
					}
				} catch (Exception e) {
					log.warn("获取用户信息失败，用户ID: {}", dto.getResponsibleUserId(), e);
					dto.setResponsibleUserName("未知用户");
				}
			}
		});
	}

	/**
	 * 获取当日点巡检工单状态统计
	 * 统计当日（当前日期）的点巡检工单数量，按工单状态分类统计
	 * 优化：使用流式处理，减少中间对象创建，提高性能
	 *
	 * @return 点巡检工单状态统计
	 */
	@Transactional(readOnly = true)
	public OrderStatusStatisticsDto getDailyInspectOrderStatusStatistics() {
		// 获取当日点巡检工单状态统计并直接构建完整结果
		return buildOrderStatusStatistics(
			() -> orderDashboardService.getInspectOrderStatusStatistics(null), "当日点巡检工单状态统计"
		);
	}

	/**
	 * 获取近30天保养工单状态统计
	 * 统计近30天（含当天）的保养工单数量，按工单状态分类统计
	 * 优化：使用流式处理，减少中间对象创建，提高性能
	 *
	 * @return 保养工单状态统计
	 */
	@Transactional(readOnly = true)
	public OrderStatusStatisticsDto getLast30DaysMaintainOrderStatusStatistics() {
		// 获取近30天保养工单状态统计并直接构建完整结果
		return buildOrderStatusStatistics(
			() -> orderDashboardService.getMaintainOrderStatusStatistics(30), "近30天保养工单状态统计"
		);
	}

	/**
	 * 构建工单状态统计结果的通用方法
	 * 提取公共逻辑，减少代码重复，提高可维护性
	 * 使用函数式编程思想，支持不同的数据源
	 *
	 * @param dataSupplier 数据供应商函数
	 * @param logContext   日志上下文
	 * @return 完整的工单状态统计结果
	 */
	private OrderStatusStatisticsDto buildOrderStatusStatistics(
		Supplier<List<OrderStatusStatisticsDto.OrderStatusItem>> dataSupplier,
		String logContext) {

		try {
			// 获取数据库统计结果
			List<OrderStatusStatisticsDto.OrderStatusItem> statusItems = dataSupplier.get();

			// 构建包含所有状态的完整列表（已包含状态名称）
			List<OrderStatusStatisticsDto.OrderStatusItem> completeStatusItems = fillCompleteOrderStatusList(statusItems);

			// 使用Stream API计算总数，避免额外的遍历
			int totalCount = completeStatusItems.stream()
				.mapToInt(OrderStatusStatisticsDto.OrderStatusItem::getCount)
				.sum();

			// 构建并返回结果
			OrderStatusStatisticsDto result = new OrderStatusStatisticsDto();
			result.setStatusList(completeStatusItems);
			result.setTotalCount(totalCount);

			return result;

		} catch (Exception e) {
			log.error("{}失败", logContext, e);
			// 发生异常时返回空结果，保证接口稳定性
			return OrderStatusStatisticsDto.createEmpty();
		}
	}


	/**
	 * 填充完整的工单状态列表
	 * 确保返回所有可能的工单状态，对于数据库中没有数据的状态，设置数量为0
	 * 使用Lambda表达式和Stream API优化性能和可读性
	 *
	 * @param dbStatusItems 数据库查询得到的状态项列表
	 * @return 包含所有工单状态的完整列表
	 */
	private List<OrderStatusStatisticsDto.OrderStatusItem> fillCompleteOrderStatusList(
		List<OrderStatusStatisticsDto.OrderStatusItem> dbStatusItems) {

		// 使用Stream API将数据库查询结果转换为Map，提高查找效率
		Map<Integer, Integer> statusCountMap = ObjectUtil.isEmpty(dbStatusItems)
			? Collections.emptyMap()
			: dbStatusItems.stream().collect(
			Collectors.toMap(
				OrderStatusStatisticsDto.OrderStatusItem::getStatusCode,
				OrderStatusStatisticsDto.OrderStatusItem::getCount
			)
		);

		// 使用Stream API和Lambda表达式创建完整状态列表，减少循环和临时变量
		return Arrays.stream(OrderStatusEnum.values())
			.map(statusEnum -> createStatusItem(statusEnum, statusCountMap))
			.collect(Collectors.toList());
	}

	/**
	 * 创建单个状态项
	 * 提取公共逻辑，提高代码复用性和可读性
	 *
	 * @param statusEnum     状态枚举
	 * @param statusCountMap 状态数量映射
	 * @return 状态项
	 */
	private OrderStatusStatisticsDto.OrderStatusItem createStatusItem(
		OrderStatusEnum statusEnum, Map<Integer, Integer> statusCountMap) {
		OrderStatusStatisticsDto.OrderStatusItem item = new OrderStatusStatisticsDto.OrderStatusItem();
		item.setStatusCode(statusEnum.getCode());
		item.setStatusName(statusEnum.getName()); // 直接设置状态名称，减少后续处理
		item.setCount(statusCountMap.getOrDefault(statusEnum.getCode(), 0));
		return item;
	}

	/**
	 * 获取近30天维修工单列表
	 * 查询近30天内的维修工单，包含设备信息和维修状态
	 *
	 * @param page 分页参数
	 * @return 维修工单分页列表
	 */
	@Transactional(readOnly = true)
	public IPage<RepairOrderStatisticsDto> getLast30DaysRepairOrderStatistics(IPage<RepairOrderStatisticsDto> page) {
		// 获取维修工单统计（数据库层面查询）
		IPage<RepairOrderStatisticsDto> pageResult = orderDashboardService.getLast30DaysRepairOrderStatistics(page);

		if (ObjectUtil.isEmpty(pageResult.getRecords())) {
			log.info("近30天维修工单统计结果为空");
			return pageResult;
		}

		// 填充维修方式名称、工单状态名称
		fillRepairOrderInfo(pageResult.getRecords());

		return pageResult;
	}

	/**
	 * 填充维修工单信息（字典转换）
	 */
	private void fillRepairOrderInfo(List<RepairOrderStatisticsDto> records) {
		if (ObjectUtil.isEmpty(records)) {
			return;
		}

		records.forEach(record -> {
			// 维修方式名称转换
			if (ObjectUtil.isNotEmpty(record.getBizType())) {
				RepairBizTypeEnum bizTypeEnum = RepairBizTypeEnum.getByCode(record.getBizType());
				if (bizTypeEnum != null) {
					record.setBizTypeName(bizTypeEnum.getName());
				}
			}

			// 故障状态名称转换
			if (ObjectUtil.isNotEmpty(record.getFaultStatus())) {
				FaultBizStatusEnum faultStatusEnum = FaultBizStatusEnum.getByCode(record.getFaultStatus());
				if (faultStatusEnum != null) {
					record.setFaultStatusName(faultStatusEnum.getName());
				}
			}

			// 工单状态名称转换
			if (ObjectUtil.isNotEmpty(record.getStatus())) {
				OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(record.getStatus());
				if (statusEnum != null) {
					record.setStatusName(statusEnum.getName());
				}
			}
		});
	}

	/**
	 * 获取近一年维修耗时统计（按部位分组）
	 * 查询近一年内已完成的维修工单耗时统计，按设备部位分组
	 * 只统计已完成状态的维修工单（status = 2 已完成，status = 4 超期完成）
	 *
	 * @param page 分页参数
	 * @return 维修耗时统计结果（包含按部位分组的分页数据和平均耗时）
	 */
	@Transactional(readOnly = true)
	public RepairDurationStatisticsResultDto getLastYearRepairDurationStatistics(IPage<RepairDurationStatisticsDto> page) {
		// 获取维修耗时统计分页数据（数据库层面按部位分组查询）
		IPage<RepairDurationStatisticsDto> pageResult = orderDashboardService.getLastYearRepairDurationStatistics(page);

		// 获取平均耗时统计（数据库层面聚合查询）
		BigDecimal averageDuration = orderDashboardService.getLastYearRepairDurationAverage();
		if (averageDuration == null) {
			averageDuration = BigDecimal.ZERO;
		}

		// 构建结果对象
		RepairDurationStatisticsResultDto result = new RepairDurationStatisticsResultDto();
		result.setPageData(pageResult);
		result.setAverageDuration(averageDuration);

		return result;
	}

	/**
	 * 获取点巡检工单设备统计
	 * 查询当日点巡检工单数据，在Java业务层进行统计计算
	 * 按完成率从高到低排序返回结果
	 *
	 * @return 点巡检工单设备统计列表
	 */
	@Transactional(readOnly = true)
	public List<OrderDeviceStatisticsDto> getInspectOrderDeviceStatistics() {
		// 获取当日点巡检工单基础数据
		List<InspectOrderSimpleDto> orderData = orderDashboardService.getTodayInspectOrderSimpleData();

		if (ObjectUtil.isEmpty(orderData)) {
			log.info("当日点巡检工单数据为空");
			return Collections.emptyList();
		}

		// 使用Java Stream API进行分组统计
		return calculateDeviceStatistics(orderData);
	}

	/**
	 * 获取近30天保养工单设备统计
	 * 查询近30天保养工单数据，在Java业务层进行统计计算
	 * 按完成率从高到低排序返回结果
	 *
	 * @return 保养工单设备统计列表
	 */
	@Transactional(readOnly = true)
	public List<OrderDeviceStatisticsDto> getMaintainOrderDeviceStatistics() {
		// 获取近30天保养工单基础数据
		List<InspectOrderSimpleDto> orderData = orderDashboardService.getLast30DaysMaintainOrderSimpleData();

		if (ObjectUtil.isEmpty(orderData)) {
			log.info("近30天保养工单数据为空");
			return Collections.emptyList();
		}

		// 使用Java Stream API进行分组统计
		return calculateDeviceStatistics(orderData);
	}

	/**
	 * 计算设备统计数据
	 * 使用Java Stream API进行分组统计，提高代码可读性和维护性
	 *
	 * @param orderData 工单基础数据
	 * @return 设备统计结果
	 */
	private List<OrderDeviceStatisticsDto> calculateDeviceStatistics(List<InspectOrderSimpleDto> orderData) {
		// 按部门ID分组，确保统计准确性
		Map<Long, List<InspectOrderSimpleDto>> deptOrderMap = orderData.stream()
			.collect(Collectors.groupingBy(InspectOrderSimpleDto::getDeptId));

		// 已完成状态集合
		Set<Integer> completedStatuses = new HashSet<>();
		completedStatuses.add(OrderStatusEnum.IS_COMPLETED.getCode());
		completedStatuses.add(OrderStatusEnum.OVERDUE_COMPLETED.getCode());

		return deptOrderMap.entrySet().stream()
			.map(entry -> {
				Long deptId = entry.getKey();
				List<InspectOrderSimpleDto> deptOrders = entry.getValue();

				// 获取部门名称（从第一个工单中获取，同一部门ID的工单部门名称应该相同）
				String deptName = deptOrders.isEmpty() ? "未知部门" : deptOrders.get(0).getDeptName();

				// 按设备分组，统计每个设备的工单完成情况
				Map<Long, List<InspectOrderSimpleDto>> deviceOrderMap = deptOrders.stream()
					.collect(Collectors.groupingBy(InspectOrderSimpleDto::getEquipmentId));

				// 设备总量
				int totalDeviceCount = deviceOrderMap.size();

				// 完成量：某个设备的所有工单全部完成时，该设备计入完成量
				int completedDeviceCount = (int) deviceOrderMap.entrySet().stream()
					.filter(deviceEntry -> {
						List<InspectOrderSimpleDto> deviceOrders = deviceEntry.getValue();
						// 检查该设备的所有工单是否都已完成
						return deviceOrders.stream()
							.allMatch(order -> completedStatuses.contains(order.getStatus()));
					}).count();

				// 计算完成率（小数形式，0.0-1.0范围）
				BigDecimal completionRate = totalDeviceCount > 0 ?
					BigDecimal.valueOf(completedDeviceCount)
						.divide(BigDecimal.valueOf(totalDeviceCount), 4, RoundingMode.HALF_UP)
						.setScale(4, RoundingMode.HALF_UP) :
					BigDecimal.ZERO;

				// 构建统计结果
				OrderDeviceStatisticsDto dto = new OrderDeviceStatisticsDto();
				dto.setDeptId(deptId);
				dto.setDeptName(deptName);
				dto.setTotalDeviceCount(totalDeviceCount);
				dto.setCompletedDeviceCount(completedDeviceCount);
				dto.setCompletionRate(completionRate);

				return dto;
			})
			// 多级排序：首先按完成率降序，如果完成率相同则按设备总量降序
			.sorted(Comparator.comparing(OrderDeviceStatisticsDto::getCompletionRate).reversed()
				.thenComparing(OrderDeviceStatisticsDto::getTotalDeviceCount, Comparator.reverseOrder()))
			.collect(Collectors.toList());
	}

}
