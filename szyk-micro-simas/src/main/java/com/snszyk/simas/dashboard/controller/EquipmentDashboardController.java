/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto;
import com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto;
import com.snszyk.simas.dashboard.service.logic.EquipmentDashboardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备大屏展示控制器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard/equipment")
@Api(value = "大屏-设备统计", tags = "大屏-设备统计接口")
public class EquipmentDashboardController {

    private final EquipmentDashboardLogicService equipmentDashboardLogicService;

    @GetMapping("/total-statistics")
    @ApiOperation(value = "设备总数统计", notes = "统计全局设备数据概览，包括设备总数、在用数量、备用数量、维修数量、报废数量")
    @ApiOperationSupport(order = 1)
    public R<EquipmentTotalStatisticsDto> getEquipmentTotalStatistics() {
        EquipmentTotalStatisticsDto statistics = equipmentDashboardLogicService.getEquipmentTotalStatistics();
        return R.data(statistics);
    }

    @GetMapping("/department-statistics")
    @ApiOperation(value = "按部门统计设备信息", notes = "按部门维度统计设备分布情况，优先按使用部门统计，如果设备无使用部门则按归属部门统计")
    @ApiOperationSupport(order = 2)
    public R<List<DepartmentEquipmentStatisticsDto>> getDepartmentEquipmentStatistics() {
        List<DepartmentEquipmentStatisticsDto> statistics = equipmentDashboardLogicService.getDepartmentEquipmentStatistics();
        return R.data(statistics);
    }

}
