/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.simas.change.dto.EquipmentChangeDto;
import com.snszyk.simas.change.entity.EquipmentChange;
import com.snszyk.simas.common.mapper.EquipmentChangeMapper;
import com.snszyk.simas.change.service.IEquipmentChangeService;
import com.snszyk.simas.change.vo.EquipmentChangePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 设备变更表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@AllArgsConstructor
@Service
public class EquipmentChangeServiceImpl extends BaseServiceImpl<EquipmentChangeMapper, EquipmentChange> implements IEquipmentChangeService {



    /**
     * 分页查询
     */
    @Override
    public IPage<EquipmentChangeDto> pageList(EquipmentChangePageVo v) {
        return baseMapper.pageList(v);
    }

    /**
     * 详情
     */
    @Override
    public EquipmentChangeDto detail(Long id) {
        return baseMapper.detail(id);
    }

}
