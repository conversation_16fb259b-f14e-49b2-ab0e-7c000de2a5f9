/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.change.service.logic.EquipmentChangeLogicService;
import com.snszyk.simas.change.vo.EquipmentChangeAVo;
import com.snszyk.simas.change.vo.EquipmentChangeAcceptVO;
import com.snszyk.simas.change.vo.EquipmentChangeOneVO;
import com.snszyk.simas.change.vo.EquipmentChangePageVo;
import com.snszyk.simas.common.vo.*;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.change.dto.EquipmentChangeDto;
import com.snszyk.simas.common.dto.EquipmentFileDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备变更表 控制器
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipmentchange")
@Api(value = "设备变更表", tags = "设备变更表接口")
public class EquipmentChangeController  extends SzykController {

	private final EquipmentChangeLogicService equipmentChangeLogicService;



	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "EquipmentChangeVo")
	public R<EquipmentChangeDto> save(@RequestBody @Valid EquipmentChangeAVo v) {
		EquipmentChangeDto baseCrudDto = equipmentChangeLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "EquipmentChangePageVo")
	public R<IPage<EquipmentChangeDto>> page(EquipmentChangePageVo v) {
		IPage<EquipmentChangeDto> pageQueryResult = equipmentChangeLogicService.pageList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/fetchById")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<EquipmentChangeDto> fetchById(Long id) {
		EquipmentChangeDto baseCrudDto = equipmentChangeLogicService.detail(id);
		return R.data(baseCrudDto);
	}

	/**
	 * 删除
	 */
	@PostMapping("/removeByIds")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除", notes = "id")
	public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
		List<CommonDeleteResultDto> result = equipmentChangeLogicService.removeByIds(ids);
		return R.data(result);
	}


	@PostMapping("/audit")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "审核", notes = "传入id")
	public R<Boolean> audit(@RequestBody AuditVO vo){
		Boolean audit = equipmentChangeLogicService.audit(vo);
		return R.status(audit);
	}

	@PostMapping("/cancel")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "撤销", notes = "传入id")
	public R<Boolean> cancel(@ApiParam(value = "主键", required = true) @RequestParam Long id){
		Boolean cancel = equipmentChangeLogicService.cancel(id);
		return R.status(cancel);

	}

	@PostMapping("/accept")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "验收", notes = "传入id")
	public R<Boolean> accept(@RequestBody @Valid EquipmentChangeAcceptVO vo){
		Boolean cancel = equipmentChangeLogicService.accept(vo);
		return R.status(cancel);

	}

	@PostMapping("/updateOneFile")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "变更一个资料", notes = "传入id")
	public R<Boolean> updateOneFile(@RequestBody @Valid EquipmentChangeOneVO vo){
		Boolean cancel = equipmentChangeLogicService.updateOneFile(vo);
		return R.status(cancel);

	}

	/**
	 * 根据设备id查询设备资料
	 */
	@GetMapping("/listFileByEquipmentId")
	@ApiOperation (value = "根据设备id查询设备资料", notes = "传入id")
	public R<List<EquipmentFileDTO>> listByEquipmentId(Long equipmentId) {
		List<EquipmentFileDTO> list = equipmentChangeLogicService.listFileByEquipmentId(equipmentId);
		return R.data(list);
	}



}
