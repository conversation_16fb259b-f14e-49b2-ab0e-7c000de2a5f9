/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.simas.change.dto.EquipmentChangeFileDto;
import com.snszyk.simas.change.entity.EquipmentChangeFile;
import com.snszyk.simas.common.mapper.EquipmentChangeFileMapper;
import com.snszyk.simas.change.service.IEquipmentChangeFileService;
import com.snszyk.simas.change.vo.EquipmentChangeFilePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备变更附件 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@AllArgsConstructor
@Service
public class EquipmentChangeFileServiceImpl extends BaseServiceImpl<EquipmentChangeFileMapper, EquipmentChangeFile> implements IEquipmentChangeFileService {

    /**
     * 名称校验
     */
    @Override
    public void checkName(Long id, String name) {
        Integer count = lambdaQuery().eq(EquipmentChangeFile::getAttachId, name).ne(id != null, EquipmentChangeFile::getId, id).count();
        if (count > 0) {
            throw new ServiceException("门类名称已存在");
        }
    }

    /**
     * 分页查询
     */
    @Override
    public IPage<EquipmentChangeFileDto> pageList(EquipmentChangeFilePageVo v) {
        return baseMapper.pageList(v);
    }

    /**
     * 详情
     */
    @Override
    public EquipmentChangeFileDto detail(Long id) {
        return baseMapper.detail(id);
    }

	@Override
	public List<EquipmentChangeFileDto> listByChangeId(Long changeId) {
		return baseMapper.listByChangeId(changeId);

	}

}
