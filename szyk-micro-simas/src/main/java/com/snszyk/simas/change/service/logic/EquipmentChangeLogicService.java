/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.service.logic;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.change.dto.EquipmentChangeDto;
import com.snszyk.simas.change.dto.EquipmentChangeFileDto;
import com.snszyk.simas.change.entity.EquipmentChange;
import com.snszyk.simas.change.entity.EquipmentChangeFile;
import com.snszyk.simas.change.service.IEquipmentChangeFileService;
import com.snszyk.simas.change.service.IEquipmentChangeService;
import com.snszyk.simas.change.vo.*;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.dto.EquipmentFileDTO;
import com.snszyk.simas.common.dto.EquipmentFileUpdateDto;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.enums.*;
import com.snszyk.simas.common.processor.EquipmentChangeLogProcessor;
import com.snszyk.simas.common.service.IEquipmentFileService;
import com.snszyk.simas.common.service.IEquipmentFileUpdateService;
import com.snszyk.simas.common.vo.AuditVO;
import com.snszyk.simas.common.vo.EquipmentFileUpdateAVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

/**
 * 设备变更表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@AllArgsConstructor
@Service
public class EquipmentChangeLogicService {

	private final IEquipmentChangeService equipmentChangeService;
	private final IEquipmentChangeFileService changeFileService;
	private final IEquipmentFileService fileService;
	private final IEquipmentFileUpdateService fileUpdatedService;
	private final IAttachClient attachClient;


	@Transactional(rollbackFor = Exception.class)
	public EquipmentChangeDto saveOrUpdate(EquipmentChangeAVo v) {
		// 初始化提交
		EquipmentChangeActionEnum actionEnum = EquipmentChangeActionEnum.INIT;
		// 1 save
		EquipmentChange copy = BeanUtil.copy(v, EquipmentChange.class);
		String changeNumber = v.getChangeNumber();
		if (StrUtil.isBlank(changeNumber)) {
			copy.setChangeNumber(BizCodeUtil.generate("BG"));
		}
		if (v.getId() != null) {
			EquipmentChange change = equipmentChangeService.getById(v.getId());
			String changeStatus = change.getChangeStatus();
			actionEnum = EquipmentChangeActionEnum.RE_SUBMIT;
			// 如果是驳回的改为待审核
			if (EquipmentChangeStatusEnum.REJECT.getCode().equals(changeStatus)) {
				copy.setChangeStatus(EquipmentChangeStatusEnum.WAIT_CHECK.getCode());
			}

		}
		boolean b = equipmentChangeService.saveOrUpdate(copy);
		// 保存业务操作日志
		EquipmentChangeLogProcessor.saveBizLog(copy, actionEnum);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		// 2.
		// 风险评估资料和数据库的数据对比,找出add和remove的数据
		// 获取数据库中的数据
		LambdaQueryChainWrapper<EquipmentChangeFile> query = changeFileService.lambdaQuery();
		List<EquipmentChangeFile> dbRiskList = query.eq(EquipmentChangeFile::getChangeId, v.getId()).list();
		List<EquipmentChangeFileAVo> riskFileList = v.getRiskFileList();
		List<Long> riskFileIds = riskFileList.stream().map(EquipmentChangeFileAVo::getId).filter(Objects::nonNull).collect(Collectors.toList());
		List<EquipmentChangeFile> addList = new ArrayList<>();
		List<EquipmentChangeFile> removeList = new ArrayList<>();
		// 只区分新增的和删除的 没有id的是新增的,数据库中有,但是参数没有的是删除的
		for (EquipmentChangeFileAVo riskFile : riskFileList) {
			if (riskFile.getId() == null) {
				// 新增
				EquipmentChangeFile add = BeanUtil.copy(riskFile, EquipmentChangeFile.class);
				// 附件的业务类型
				add.setBusinessType(EquipmentFileTypeEnum.RISK_FILE.getCode());
				add.setChangeId(copy.getId());
				addList.add(add);
			}
		}
		dbRiskList.stream().filter(e -> !riskFileIds.contains(e.getId()) && e.getBusinessType().equals(EquipmentChangeFileBusinessTypeEnum.RISK_ASSESSMENT.getCode())).forEach(removeList::add);
		// 数据库操作
		if (!addList.isEmpty()) {
			changeFileService.saveBatch(addList);
		}
		if (!removeList.isEmpty()) {
			changeFileService.removeByIds(removeList.stream().map(EquipmentChangeFile::getId).collect(Collectors.toList()));
		}
		// 3.资料更新
		List<EquipmentFileUpdateAVo> changeFileList = v.getChangeFileList();
		List<Long> changeFileIdList = changeFileList.stream().map(e -> e.getId()).filter(Objects::nonNull).collect(Collectors.toList());
		List<EquipmentFileUpdateDto> equipmentFileUpdateDtos = fileUpdatedService.listByChangeId(copy.getId());
		for (EquipmentFileUpdateAVo changeFile : changeFileList) {
			EquipmentFileUpdate fileUpdateAdd = new EquipmentFileUpdate();
			fileUpdateAdd.setEquipmentFileId(changeFile.getEquipmentFileId());
			fileUpdateAdd.setChangeId(copy.getId());
			Long changeFileId = changeFile.getId();
			fileUpdateAdd.setId(changeFileId);
			fileUpdateAdd.setUpdateStatus(EquipmentUpdateStatusEnum.WAIT_UPDATE.getCode());
			fileUpdateAdd.setName(changeFile.getName());
			fileUpdateAdd.setType(changeFile.getType());
			fileUpdateAdd.setFileCategoryId(changeFile.getFileCategoryId());
			fileUpdateAdd.setExtension(changeFile.getExtension());
			fileUpdateAdd.setAttachId(changeFile.getAttachId());
			boolean save = fileUpdatedService.saveOrUpdate(fileUpdateAdd);
			if (!save) {
				throw new ServiceException("系统异常,变更记录添加失败");
			}
		}
		List<Long> removeUpdateIdList = equipmentFileUpdateDtos.stream().filter(e -> !changeFileIdList.contains(e.getId())).map(e -> e.getId()).collect(Collectors.toList());
		if (!removeUpdateIdList.isEmpty()) {
			fileUpdatedService.removeByIds(removeUpdateIdList);
		}
		return detail(copy.getId());
	}


	public IPage<EquipmentChangeDto> pageList(EquipmentChangePageVo v) {
		// 开始时间和结束时间改为time类型的
		if (v.getStartApplyDate() != null && v.getEndApplyDate() != null) {
			v.setStartApplyTime(v.getStartApplyDate().atStartOfDay());
			v.setEndApplyTime(v.getEndApplyDate().atTime(LocalTime.MAX));
		}
		// changeStatusList转为list
		if (StringUtil.isNotBlank(v.getChangeStatusList())) {
			v.setChangeStatusListParam(Func.toStrList(",", v.getChangeStatusList()));
		}
		return equipmentChangeService.pageList(v);
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public EquipmentChangeDto detail(Long id) {
		EquipmentChangeDto dto = equipmentChangeService.detail(id);
		if (dto == null) {
			throw new ServiceException("变更单不存在");
		}

		// 查询变更相关的文件列表
		List<EquipmentChangeFileDto> equipmentChangeFileDtos = changeFileService.listByChangeId(dto.getId());

		// 分离风险评估文件和验收文件
		List<EquipmentChangeFileDto> riskList = equipmentChangeFileDtos.stream()
			.filter(e -> e.getBusinessType().equals(EquipmentChangeFileBusinessTypeEnum.RISK_ASSESSMENT.getCode()))
			.collect(Collectors.toList());

		List<EquipmentChangeFileDto> imageList = equipmentChangeFileDtos.stream()
			.filter(e -> e.getBusinessType().equals(EquipmentChangeFileBusinessTypeEnum.DEVICE_ACCEPTANCE.getCode()))
			.collect(Collectors.toList());

		// 设置风险评估文件和验收文件列表
		dto.setRiskFileList(riskList);
		dto.setImageList(imageList);

		// 查询变更相关的更新列表
		List<EquipmentFileUpdateDto> dbChangeFileList = fileUpdatedService.listByChangeId(dto.getId());
		dto.setChangeFileList(dbChangeFileList);

		// 获取所有已存在的文件 ID 集合
		List<Long> changeFileIds = dbChangeFileList.stream()
			.map(EquipmentFileUpdateDto::getEquipmentFileId)
			.collect(Collectors.toList());

		// 查询设备相关的文件列表
		List<EquipmentFileDTO> dbFileList = fileService.listByEquipmentId(dto.getEquipmentId());

		// 添加未被选择的文件到更新列表
		dbFileList.stream()
			.filter(e -> !changeFileIds.contains(e.getId()))
			.forEach(e -> {
				EquipmentFileUpdateDto equipmentFileUpdateDto = BeanUtil.copyProperties(e, EquipmentFileUpdateDto.class);
				equipmentFileUpdateDto.setEquipmentFileId(e.getId());
				equipmentFileUpdateDto.setId(null);
				equipmentFileUpdateDto.setUpdateStatus(EquipmentUpdateStatusEnum.NO_CHOOSE.getCode());
				equipmentFileUpdateDto.setUpdateStatusName(EquipmentUpdateStatusEnum.NO_CHOOSE.getName());
				equipmentFileUpdateDto.setAttachId(e.getAttachId());
				equipmentFileUpdateDto.setName(e.getName());
				equipmentFileUpdateDto.setFileCategoryId(e.getFileCategoryId());
				equipmentFileUpdateDto.setType(e.getType());
				equipmentFileUpdateDto.setExtension(e.getExtension());
				dbChangeFileList.add(equipmentFileUpdateDto);
			});

		// 按设备文件 ID 排序
		dbChangeFileList.sort(Comparator.comparing(EquipmentFileUpdateDto::getEquipmentFileId));
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			EquipmentChange data = equipmentChangeService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}

			Boolean b = equipmentChangeService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


	/**
	 * 审核
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean audit(AuditVO vo) {
		EquipmentChangeActionEnum actionEnum = null;

		EquipmentChange receive = equipmentChangeService.getById(vo.getId());
		LocalDateTime now = LocalDateTime.now();
		receive.setAuditTime(now);
		receive.setAuditPerson(AuthUtil.getUserId());
		if (AuditStatusEnum.REJECT == AuditStatusEnum.getByCode(vo.getStatus())) {
			receive.setRejectionReason(vo.getRejectReason());
			receive.setChangeStatus(EquipmentChangeStatusEnum.REJECT.getCode());
			receive.setRejectionTime(now);
			actionEnum = EquipmentChangeActionEnum.AUDIT_FAIL;
		} else {
			receive.setChangeStatus(EquipmentChangeStatusEnum.IS_COMPLETED.getCode());
			actionEnum = EquipmentChangeActionEnum.AUDIT_PASS;
		}
		// 保存通过或驳回日志
		EquipmentChangeLogProcessor.saveBizLog(receive, actionEnum, vo.getRejectReason());
		return equipmentChangeService.updateById(receive);
	}

	/**
	 * 撤销
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean cancel(Long id) {
		EquipmentChange receive = equipmentChangeService.getById(id);
		// 只有待审核和驳回的状态才能撤销
		if (EquipmentChangeStatusEnum.WAIT_CHECK != EquipmentChangeStatusEnum.getByCode(receive.getChangeStatus()) && EquipmentChangeStatusEnum.REJECT != EquipmentChangeStatusEnum.getByCode(receive.getChangeStatus())) {
			throw new ServiceException("当前状态不允许撤销");
		}
		receive.setChangeStatus(EquipmentChangeStatusEnum.CANCEL.getCode());
		// 保存撤销日志
		EquipmentChangeLogProcessor.saveBizLog(receive, EquipmentChangeActionEnum.CANCEL);
		return equipmentChangeService.updateById(receive);
	}

	/**
	 * 变更验收
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean accept(EquipmentChangeAcceptVO vo) {
		Long id = vo.getId();
		EquipmentChange change = equipmentChangeService.getById(id);
		if (change == null) {
			throw new ServiceException("数据不存在");
		}
		if (EquipmentChangeStatusEnum.IS_COMPLETED != EquipmentChangeStatusEnum.getByCode(change.getChangeStatus())) {
			throw new ServiceException("当前状态不允许验收");
		}
		// 需要更新的资料是否已经全部更新
		fileUpdatedService.listByChangeId(id).forEach(e -> {
			if (!Objects.equals(e.getUpdateStatus(), EquipmentUpdateStatusEnum.ALREADY_UPDATE.getCode())) {
				throw new ServiceException("请先更新所有附件");
			}
		});
		change.setChangeStatus(EquipmentChangeStatusEnum.CHANGE_COMPLETED.getCode());
		change.setRenovationEvaluation(vo.getRenovationEvaluation());
		// update
		boolean b = equipmentChangeService.updateById(change);
		// 保存业务日志
		EquipmentChangeLogProcessor.saveBizLog(change, EquipmentChangeActionEnum.ACCEPT);
		if (!b) {
			throw new ServiceException("更新失败");
		}
		// save imageList
		List<EquipmentChangeFile> imageList = vo.getImageList();
		// 先删除之前的图片
		changeFileService.remove(Wrappers.<EquipmentChangeFile>lambdaQuery().eq(EquipmentChangeFile::getChangeId, id));

		// save batch
		for (EquipmentChangeFile file : imageList) {
			file.setChangeId(id);
			file.setBusinessType(EquipmentFileTypeEnum.ACCEPT_IMAGE.getCode());
		}
		if (Func.isNotEmpty(imageList)) {
			boolean saveBatch = changeFileService.saveBatch(imageList);
			if (!saveBatch) {
				throw new ServiceException("保存附件失败");
			}
		}
		return true;
	}

	public List<EquipmentFileDTO> listFileByEquipmentId(Long equipmentId) {
		return fileService.listByEquipmentId(equipmentId);
	}

	/**
	 * 更新单个附件
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateOneFile(EquipmentChangeOneVO vo) {
		Long updateId = vo.getUpdateId();
		EquipmentFileUpdate dbUpdate = fileUpdatedService.getById(updateId);
		if (dbUpdate == null) {
			throw new ServiceException("数据不存在");
		}
		// 根据资料id更新资料的信息
		R<Attach> r = attachClient.attachInfoById(Long.valueOf(vo.getAttachId()));
		Attach attach = r.getData();
		if (attach == null) {
			throw new ServiceException("附件信息获取失败");
		}
		// 更新equipmentUpdate
		boolean update = fileUpdatedService.lambdaUpdate().eq(EquipmentFileUpdate::getId, updateId)
			.set(EquipmentFileUpdate::getUpdateStatus, EquipmentUpdateStatusEnum.ALREADY_UPDATE.getCode())
			.set(EquipmentFileUpdate::getAttachId, vo.getAttachId())
			.set(EquipmentFileUpdate::getName, attach.getOriginalName())
			.set(EquipmentFileUpdate::getExtension, attach.getExtension())
			.update();

		EquipmentFile equipmentFile = fileService.getById(dbUpdate.getEquipmentFileId());
		// 如果设备资料存在 则更新
		if (nonNull(equipmentFile)) {
			equipmentFile.setAttachId(vo.getAttachId());
			equipmentFile.setName(attach.getOriginalName());
			equipmentFile.setExtension(attach.getExtension());
			boolean b = fileService.updateById(equipmentFile);
			if (!b) {
				throw new ServiceException("设备资料信息更新失败");
			}
		} else {
			// 保存
			Long changeId = dbUpdate.getChangeId();

			EquipmentChange change = equipmentChangeService.getById(changeId);
			EquipmentFile file = new EquipmentFile();
			file.setAttachId(vo.getAttachId());
			file.setName(attach.getOriginalName());
			file.setExtension(attach.getExtension());
			file.setEquipmentId(change.getEquipmentId());
			// 资料类型和归类用之前的
			file.setType(dbUpdate.getType());
			file.setFileCategoryId(dbUpdate.getFileCategoryId());
			file.setNo(BizCodeUtil.generate("ZL"));

			boolean b = fileService.save(file);
			if (!b) {
				throw new ServiceException("设备资料信息保存失败");
			}
		}


		if (!update) {
			throw new ServiceException("设备资料更新状态变更是失败");
		}
		return true;
	}
}
