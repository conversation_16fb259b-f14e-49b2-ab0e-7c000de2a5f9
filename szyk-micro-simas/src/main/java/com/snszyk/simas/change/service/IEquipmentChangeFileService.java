/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.change.dto.EquipmentChangeFileDto;
import com.snszyk.simas.change.entity.EquipmentChangeFile;
import com.snszyk.simas.change.vo.EquipmentChangeFilePageVo;

import java.util.List;

/**
 * 设备变更附件 服务类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface IEquipmentChangeFileService extends BaseService<EquipmentChangeFile> {

    /**
    * 名称校验
    */
    void checkName(Long id, String name);

    /**
    * 分页查询
    */
    IPage<EquipmentChangeFileDto> pageList(EquipmentChangeFilePageVo v);

    /**
    * 详情
    */
    EquipmentChangeFileDto detail(Long id);

	/*
	* 根据变更ID查询附件列表
	 */
    List<EquipmentChangeFileDto> listByChangeId(Long changeId);
}
