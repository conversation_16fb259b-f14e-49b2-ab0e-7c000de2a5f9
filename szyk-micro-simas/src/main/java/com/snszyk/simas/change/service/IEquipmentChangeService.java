/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.change.dto.EquipmentChangeDto;
import com.snszyk.simas.change.entity.EquipmentChange;
import com.snszyk.simas.change.vo.EquipmentChangePageVo;

/**
 * 设备变更表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface IEquipmentChangeService extends BaseService<EquipmentChange> {


    /**
    * 分页查询
    */
    IPage<EquipmentChangeDto> pageList(EquipmentChangePageVo v);

    /**
    * 详情
    */
    EquipmentChangeDto detail(Long id);

}
