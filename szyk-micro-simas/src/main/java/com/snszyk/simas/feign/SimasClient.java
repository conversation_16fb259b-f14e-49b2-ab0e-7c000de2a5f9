/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.mapper.ComponentMaterialMapper;
import com.snszyk.simas.common.mapper.EquipmentFileMapper;
import com.snszyk.simas.common.mapper.EquipmentFileUpdateMapper;
import com.snszyk.simas.common.service.IEquipmentFileService;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;
import com.snszyk.simas.common.wrapper.EquipmentFileWrapper;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.fault.mapper.FaultDefectMapper;
import com.snszyk.simas.inspect.entity.*;
import com.snszyk.simas.inspect.mapper.*;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.leaseback.entity.DeviceBack;
import com.snszyk.simas.leaseback.service.IDeviceBackFileService;
import com.snszyk.simas.leaseback.service.IDeviceBackService;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.entity.LubricatePlanEquipment;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
import com.snszyk.simas.lubricate.mapper.LubricatePlanEquipmentMapper;
import com.snszyk.simas.lubricate.mapper.LubricateStandardsMapper;
import com.snszyk.simas.maintain.entity.*;
import com.snszyk.simas.maintain.mapper.*;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.entity.*;
import com.snszyk.simas.overhaul.mapper.*;
import com.snszyk.simas.overhaul.service.IOverhaulMethodsService;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.scrap.entity.ScrapDetail;
import com.snszyk.simas.scrap.mapper.ScrapDetailMapper;
import com.snszyk.simas.scrap.mapper.ScrapMapper;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.spare.entity.SparePartsDict;
import com.snszyk.simas.spare.mapper.SparePartsDictMapper;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Feign接口类
 *
 * <AUTHOR>
 * @Date 2025/3/18
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class SimasClient implements ISimasClient {

	private final IEquipmentFileService fileService;
	private final IOverhaulMethodsService overhaulMethodsService;

	private final IDeviceBackFileService deviceBackFileService;
	private final InspectStandardMapper inspectStandardMapper;
	private final MaintainStandardMapper maintainStandardMapper;
	private final InspectPlanMapper inspectPlanMapper;
	private final InspectPlanEquipmentMapper planEquipmentMapper;
	private final MaintainPlanMapper maintainPlanMapper;
	private final MaintainPlanEquipmentMapper maintainPlanEquipmentMapper;
	private final InspectOrderMapper inspectOrderMapper;
	private final InspectRecordMapper inspectRecordMapper;
	private final MaintainOrderMapper maintainOrderMapper;
	private final MaintainRecordMapper maintainRecordMapper;
	private final ScrapMapper scrapMapper;
	private final ScrapDetailMapper scrapDetailMapper;
	private final FaultDefectMapper faultDefectMapper;
	private final RepairMapper repairMapper;
	private final RepairRecordMapper repairRecordMapper;
	private final IAttachClient attachClient;
	private final IEquipmentFileService equipmentFileService;
	private final LubricateStandardsMapper lubricateStandardsMapper;
	private final LubricatePlanEquipmentMapper lubricatePlanEquipmentMapper;
	private final LubricateOrderMapper lubricateOrderMapper;
	private final OverhaulOrderMapper overhaulOrderMapper;
	private final EquipmentFileMapper equipmentFileMapper;
	private final OverhaulStandardMapper overhaulStandardMapper;
	private final OverhaulPlanEquipmentMapper overhaulPlanEquipmentMapper;
	private final IDeviceBackService deviceBackService;
	private final ComponentMaterialMapper componentMaterialMapper;
	private final SparePartsDictMapper sparePartsDictMapper;
	private final EquipmentFileUpdateMapper equipmentFileUpdateMapper;

	@Override
	@PostMapping(SUBMIT_PRE_FILE)
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "提交（前期资料管理）", notes = "传入preFile")
	public R<Boolean> submitPreFile(@Valid @RequestBody EquipmentPreFileVO preFile) {
		return R.status(fileService.submitPreFile(preFile));
	}

	/**
	 * 删除 设备资料表
	 */
	@Override
	@PostMapping(REMOVE_PRE_FILE)
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(fileService.deleteLogic(Func.toLongList(ids)));
	}

	@Override
	@PostMapping(LIST_FILE)
	@ApiOperation(value = "资料查询", notes = "")
	public R<List<EquipmentFileVO>> listFile(EquipmentFileVO fileVO) {
		List<EquipmentFile> list = fileService.lambdaQuery()
			.eq(fileVO.getEquipmentId() != null, EquipmentFile::getEquipmentId, fileVO.getEquipmentId())
			//categoryIdLike
			.like(Func.isNotBlank(fileVO.getCategoryIdLike()), EquipmentFile::getCategoryId, fileVO.getCategoryIdLike())
			.eq(fileVO.getType() != null, EquipmentFile::getType, fileVO.getType())
			.list();
		return R.data(EquipmentFileWrapper.build().listVO(list));
	}

	@Override
	public R<Integer> countByEquipmentId(Long equipmentId, String categoryCode) {
		return R.data(fileService.countByEquipmentId(equipmentId, categoryCode));

	}

	@Override
	public R<OverhaulMethods> getOverhaulMethods(Long id) {
		return R.data(overhaulMethodsService.getById(id));
	}

	@Override
	public R<List<Attach>> leaseBackFile(Long equipmentId) {
		return R.data(deviceBackFileService.listAttachByEquipmentId(equipmentId));
	}

	@Override
	public R<Boolean> deleteDeviceRelation(String equipmentIds) {
		List<Long> ids = Func.toLongList(equipmentIds);
		// 删除点检标准
		inspectStandardMapper.delete(Wrappers.<InspectStandard>query().lambda()
			.in(InspectStandard::getEquipmentId, ids));
		// 删除点检计划中的设备（只有一个设备，则连同计划一并删除）
		List<InspectPlanEquipment> list = planEquipmentMapper.selectList(Wrappers.<InspectPlanEquipment>query().lambda()
			.in(InspectPlanEquipment::getEquipmentId, ids));
		List<Long> removePlanIds = new ArrayList<>();
		if (Func.isNotEmpty(list)) {
			List<Long> planIds = list.stream().map(InspectPlanEquipment::getPlanId).distinct().collect(Collectors.toList());
			for (Long planId : planIds) {
				Integer cnt = planEquipmentMapper.selectCount(Wrappers.<InspectPlanEquipment>query().lambda()
					.in(InspectPlanEquipment::getPlanId, planId));
				if (cnt == 1) {
					removePlanIds.add(planId);
				}
			}
		}
		if (Func.isNotEmpty(removePlanIds)) {
			inspectPlanMapper.delete(Wrappers.<InspectPlan>query().lambda()
				.in(InspectPlan::getId, removePlanIds));
		}
		planEquipmentMapper.delete(Wrappers.<InspectPlanEquipment>query().lambda()
			.in(InspectPlanEquipment::getEquipmentId, ids));
		// 删除保养标准
		maintainStandardMapper.delete(Wrappers.<MaintainStandard>query().lambda()
			.in(MaintainStandard::getEquipmentId, ids));
		// 删除保养计划中的设备（只有一个设备，则连同计划一并删除）
		List<MaintainPlanEquipment> equipmentList = maintainPlanEquipmentMapper.selectList(Wrappers.<MaintainPlanEquipment>query().lambda()
			.in(MaintainPlanEquipment::getEquipmentId, ids));
		List<Long> removeMaintainPlanIds = new ArrayList<>();
		if (Func.isNotEmpty(equipmentList)) {
			List<Long> planIds = equipmentList.stream().map(MaintainPlanEquipment::getPlanId).distinct().collect(Collectors.toList());
			for (Long planId : planIds) {
				Integer cnt = maintainPlanEquipmentMapper.selectCount(Wrappers.<MaintainPlanEquipment>query().lambda()
					.in(MaintainPlanEquipment::getPlanId, planId));
				if (cnt == 1) {
					removeMaintainPlanIds.add(planId);
				}
			}
		}
		if (Func.isNotEmpty(removeMaintainPlanIds)) {
			maintainPlanMapper.delete(Wrappers.<MaintainPlan>query().lambda()
				.in(MaintainPlan::getId, removeMaintainPlanIds));
		}
		maintainPlanEquipmentMapper.delete(Wrappers.<MaintainPlanEquipment>query().lambda()
			.in(MaintainPlanEquipment::getEquipmentId, ids));
		// 删除点检工单、点检记录
		inspectOrderMapper.delete(Wrappers.<InspectOrder>query().lambda()
			.in(InspectOrder::getEquipmentId, ids));
		inspectRecordMapper.delete(Wrappers.<InspectRecord>query().lambda()
			.in(InspectRecord::getEquipmentId, ids));
		// 删除保养工单、保养记录
		maintainOrderMapper.delete(Wrappers.<MaintainOrder>query().lambda()
			.in(MaintainOrder::getEquipmentId, ids));
		maintainRecordMapper.delete(Wrappers.<MaintainRecord>query().lambda()
			.in(MaintainRecord::getEquipmentId, ids));
		// 删除故障缺陷
		faultDefectMapper.delete(Wrappers.<FaultDefect>query().lambda()
			.in(FaultDefect::getEquipmentId, ids));
		// 删除维修单、维修记录
		List<Repair> repairList = repairMapper.selectList(Wrappers.<Repair>query().lambda()
			.in(Repair::getEquipmentId, ids));
		if (Func.isNotEmpty(repairList)) {
			List<Long> repairIds = repairList.stream().map(Repair::getId).collect(Collectors.toList());
			repairMapper.deleteBatchIds(repairIds);
			repairRecordMapper.delete(Wrappers.<RepairRecord>query().lambda()
				.in(RepairRecord::getRepairId, repairIds));
		}
		// 删除报废单
		List<ScrapDetail> scrapDetailList = scrapDetailMapper.selectList(Wrappers.<ScrapDetail>query().lambda()
			.in(ScrapDetail::getEquipmentId, ids));
		if (Func.isNotEmpty(scrapDetailList)) {
			List<Long> scrapIds = scrapDetailList.stream().map(ScrapDetail::getScrapId).collect(Collectors.toList());
			scrapMapper.delete(Wrappers.<Scrap>query().lambda()
				.in(Scrap::getId, scrapIds));
			scrapDetailMapper.deleteBatchIds(scrapDetailList.stream().map(ScrapDetail::getId).collect(Collectors.toList()));
		}

		// 查询设备润滑标准
		List<LubricateStandards> lubricateStandardsList = lubricateStandardsMapper.selectList(Wrappers.<LubricateStandards>query().lambda()
			.in(LubricateStandards::getEquipmentId, ids));
		if (Func.isNotEmpty(lubricateStandardsList)) {
			// 删除润滑标准
			lubricateStandardsMapper.delete(Wrappers.<LubricateStandards>query().lambda()
				.in(LubricateStandards::getEquipmentId, ids));
			// 删除润滑计划
			lubricatePlanEquipmentMapper.delete(Wrappers.<LubricatePlanEquipment>query().lambda()
				.in(LubricatePlanEquipment::getEquipmentId, ids));
			List<Long> lubricateStandardsIds = lubricateStandardsList.stream().map(LubricateStandards::getId).collect(Collectors.toList());
			// 删除未完成的润滑工单
			lubricateOrderMapper.delete(Wrappers.<LubricateOrder>query().lambda()
				.in(LubricateOrder::getStandardsId, lubricateStandardsIds)
			);
		}

		// 查询设备检修标准
		List<OverhaulStandard> overhaulStandardList = overhaulStandardMapper.selectList(Wrappers.<OverhaulStandard>query().lambda()
			.in(OverhaulStandard::getEquipmentId, ids));
		if (Func.isNotEmpty(overhaulStandardList)) {
			// 删除检修标准
			overhaulStandardMapper.delete(Wrappers.<OverhaulStandard>query().lambda()
				.in(OverhaulStandard::getEquipmentId, ids));
			// 删除检修计划
			overhaulPlanEquipmentMapper.delete(Wrappers.<OverhaulPlanEquipment>query().lambda()
				.in(OverhaulPlanEquipment::getEquipmentId, ids));
			// 删除检修工单
			overhaulOrderMapper.delete(Wrappers.<OverhaulOrder>query().lambda()
				.in(OverhaulOrder::getEquipmentId, ids)
			);
		}
		// 删除设备资料
		equipmentFileMapper.delete(Wrappers.<EquipmentFile>query().lambda().in(EquipmentFile::getEquipmentId, ids));
		return R.data(true);
	}

	@Override
	public R<DeviceBackDto> leaseBackInfo(Long equipmentId) {
		DeviceBack one = deviceBackService.lambdaQuery().eq(DeviceBack::getDeviceId, equipmentId).last("limit 1").one();
		if (one == null) {
			return R.data(null);
		}
		DeviceBackDto detail = deviceBackService.detail(one.getId());
		return R.data(detail);
	}

	@Override
	public R<Boolean> deleteFileByEquipmentId(Long equipmentId) {
		List<EquipmentFile> fileList = fileService.lambdaQuery().eq(EquipmentFile::getEquipmentId, equipmentId).list();
		boolean remove = fileService.lambdaUpdate().eq(EquipmentFile::getEquipmentId, equipmentId).remove();
		//dify的数据同步删除
		if (Func.isNotEmpty(fileList)) {
			List<Long> ids = fileList.stream().map(EquipmentFile::getId).collect(Collectors.toList());
			fileService.syncRemoveDify(ids);
		}
		return R.data(remove);
	}

	@Override
	public R<List<String>> hasStandardsByMonitorIds(String monitorIds) {
		List<String> standardTypes = new ArrayList<>();

		if (Func.isEmpty(monitorIds)) {
			return R.data(standardTypes);
		}

		List<Long> monitorIdList = Func.toLongList(monitorIds);
		if (Func.isEmpty(monitorIdList)) {
			return R.data(standardTypes);
		}

		// 检查点巡检标准
		Integer inspectCount = inspectStandardMapper.selectCount(Wrappers.<InspectStandard>query().lambda()
			.in(InspectStandard::getMonitorId, monitorIdList));
		if (inspectCount > 0) {
			standardTypes.add("点巡检标准");
		}

		// 检查润滑标准
		Integer lubricateCount = lubricateStandardsMapper.selectCount(Wrappers.<LubricateStandards>query().lambda()
			.in(LubricateStandards::getEquipmentMonitorId, monitorIdList));
		if (lubricateCount > 0) {
			standardTypes.add("润滑标准");
		}

		// 检查检修标准
		Integer overhaulCount = overhaulStandardMapper.selectCount(Wrappers.<OverhaulStandard>query().lambda()
			.in(OverhaulStandard::getMonitorId, monitorIdList));
		if (overhaulCount > 0) {
			standardTypes.add("检修标准");
		}

		// 检查保养标准
		Integer maintainCount = maintainStandardMapper.selectCount(Wrappers.<MaintainStandard>query().lambda()
			.in(MaintainStandard::getMonitorId, monitorIdList));
		if (maintainCount > 0) {
			standardTypes.add("保养标准");
		}

		return R.data(standardTypes);
	}

	@Override
	public R<List<RepairDTO>> listRepairBySupplierIds(String supplierIds) {
		List<Repair> repairs = repairMapper.selectList(Wrappers.<Repair>query()
			.lambda()
			.in(Repair::getSupplierId, Func.toLongList(supplierIds)));
		return R.data(BeanUtil.copy(repairs, RepairDTO.class));
	}

	@GetMapping(HAS_BUSINESS_BY_MEASURE_UNIT)
	@Override
	public R<Boolean> hasBusinessByMeasureUnit(Long measureUnitId) {
		// 查询备品备件耗材表是否引用
		Integer count = componentMaterialMapper.selectCount(Wrappers.<ComponentMaterial>query().lambda()
			.eq(ComponentMaterial::getMeasureUnitId, measureUnitId));
		if (count > 0) {
			return R.data(true);
		}

		// 查询备品备件字典表是否引用
		count = sparePartsDictMapper.selectCount(Wrappers.<SparePartsDict>query().lambda()
			.eq(SparePartsDict::getMeasureUnitId, measureUnitId));
		if (count > 0) {
			return R.data(true);
		}
		//


		return R.data(false);
	}

	@GetMapping(HAS_BUSINESS_BY_FILE_CATEGORY_ID)
	@Override
	public R<Boolean> hasBusinessByFileCategoryId(Long fileCategoryId) {
		// 查詢设备资料表是否引用
		Integer count = equipmentFileMapper.selectCount(Wrappers.<EquipmentFile>query().lambda()
			.eq(EquipmentFile::getFileCategoryId, fileCategoryId));
		if (count > 0) {
			return R.data(true);
		}
		// 查询设备资料更新表是否引用
		count = equipmentFileUpdateMapper.selectCount(Wrappers.<EquipmentFileUpdate>query().lambda()
			.eq(EquipmentFileUpdate::getFileCategoryId, fileCategoryId));
		if (count > 0) {
			return R.data(true);
		}
		return R.data(false);
	}

	@Override
	public R<Boolean> hasBusinessByDeviceIds(String deviceIds) {
		List<Long> ids = Func.toLongList(deviceIds);
		// 校验点巡检标准是否引用
		Integer count = inspectStandardMapper.selectCount(Wrappers.<InspectStandard>query().lambda()
			.in(InspectStandard::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验点巡检计划是否引用
		count = planEquipmentMapper.selectCount(Wrappers.<InspectPlanEquipment>query().lambda()
			.in(InspectPlanEquipment::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验保养标准是否引用
		count = maintainStandardMapper.selectCount(Wrappers.<MaintainStandard>query().lambda()
			.in(MaintainStandard::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验保养计划是否引用
		count = maintainPlanEquipmentMapper.selectCount(Wrappers.<MaintainPlanEquipment>query().lambda()
			.in(MaintainPlanEquipment::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验润滑标准是否引用
		count = lubricateStandardsMapper.selectCount(Wrappers.<LubricateStandards>query().lambda()
			.in(LubricateStandards::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验润滑计划是否引用
		count = lubricatePlanEquipmentMapper.selectCount(Wrappers.<LubricatePlanEquipment>query().lambda()
			.in(LubricatePlanEquipment::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验检修标准是否引用
		count = overhaulStandardMapper.selectCount(Wrappers.<OverhaulStandard>query().lambda()
			.in(OverhaulStandard::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验检修计划是否引用
		count = overhaulPlanEquipmentMapper.selectCount(Wrappers.<OverhaulPlanEquipment>query().lambda()
			.in(OverhaulPlanEquipment::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验维修工单是否引用
		count = repairMapper.selectCount(Wrappers.<Repair>query().lambda()
			.in(Repair::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验故障缺陷是否引用
		count = faultDefectMapper.selectCount(Wrappers.<FaultDefect>query().lambda()
			.in(FaultDefect::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验设备资料是否引用
		count = equipmentFileMapper.selectCount(Wrappers.<EquipmentFile>query().lambda()
			.in(EquipmentFile::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}
		// 校验设备报废是否引用
		count = scrapDetailMapper.selectCount(Wrappers.<ScrapDetail>query().lambda()
			.in(ScrapDetail::getEquipmentId, ids));
		if (count > 0) {
			return R.data(true);
		}


		return R.data(false);
	}
}
