package com.snszyk.simas.lubricate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.excel.LubricateOrderExcel;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;

import java.time.LocalDateTime;
import java.util.List;

public interface ILubricateOrderService extends BaseService<LubricateOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<LubricateOrderDTO> page(IPage<LubricateOrderDTO> page, LubricateOrderVO vo);

	/**
	 * 详情
	 *
	 * @param no
	 * @return
	 */
	LubricateOrderDTO detail(String no);

	/**
	 * 提交
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(LubricateOrderVO vo);

	/**
	 * 审核确认
	 *
	 * @param vo
	 * @return
	 */
	boolean confirm(LubricateOrderVO vo);

	/**
	 * 批量审核确认
	 *
	 * @param vo
	 * @return
	 */
	boolean confirmBatch(LubricateOrderVO vo);

	/**
	 * 超时提醒
	 *
	 * @param page
	 * @param lubricateOrder
	 * @return
	 */
	IPage<LubricateOrderDTO> timeoutPage(IPage<LubricateOrderDTO> page, LubricateOrderVO lubricateOrder);


	/**
	 * 发送消息提醒
	 *
	 * @param list
	 * @param messageBizType
	 */
	void sendMessage(List<LubricateOrder> list, MessageBizTypeEnum messageBizType);


	/**
	 * 即将超期工单数量
	 *
	 * @return
	 */
	Integer expireSoonCount();

	/**
	 * 导出
	 *
	 * @param lubricateOrder
	 * @return
	 */
	List<LubricateOrderExcel> exportOrder(LubricateOrderVO lubricateOrder);

	/**
	 * 查询设备下所有的工单
	 *
	 * @param equipmentIds
	 * @return
	 */
	List<LubricateOrderVO> selectByEquipmentIds(List<Long> equipmentIds);

	/**
	 * 批量保存
	 *
	 * @param list
	 * @return
	 */
	boolean saveOrUpdateBatch(List<LubricateOrder> list);

	/**
	 * 大屏超时的
	 *
	 * @return
	 */
	List<BigScreenMessageDTO> overdueList(String tenantId);

	List<LubricateOrderDTO> lubricateStatistics(String tenantId);

	Integer handleLubricateCount(LubricateOrderVO vo);

	String parseStandardToJson(LubricateStandards standards);

	List<LubricateOrder> listBy(Long chargeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus, Boolean noUnscheduled);
}
