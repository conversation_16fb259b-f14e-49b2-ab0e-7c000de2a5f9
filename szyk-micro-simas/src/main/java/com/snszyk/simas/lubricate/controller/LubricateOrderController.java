package com.snszyk.simas.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.TimeoutRemindSet;
import com.snszyk.simas.common.excel.LubricateOrderExcel;
import com.snszyk.simas.common.service.ITimeoutRemindSetService;
import com.snszyk.simas.common.vo.TimeoutRemindSetVO;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.schedule.LubricateSchedule;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@AllArgsConstructor
@RequestMapping("/lubricate/order")
@Api(value = "润滑工单", tags = "润滑工单接口")
public class LubricateOrderController extends SzykController {

	private final ILubricateOrderService lubricateOrderService;
	private final ITimeoutRemindSetService timeoutRemindSetService;
	private final LubricateSchedule lubricateSchedule;

	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "equipmentCode", value = "设备编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "onlyQueryExecuteUser", value = "执行人id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "chargeDept", value = "执行部门", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "startCreateDate", value = "创建开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endCreateDate", value = "创建结束日期", paramType = "query", dataType = "string"),
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入lubricateOrderVO")
	public R<IPage<LubricateOrderDTO>> page(@ApiIgnore LubricateOrderVO vo, Query query) {
		return R.data(lubricateOrderService.page(Condition.getPage(query), vo));
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<LubricateOrderDTO> detail(@ApiParam(value = "工单号", required = true) @RequestParam String no) {
		return R.data(lubricateOrderService.detail(no));
	}

	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "提交", notes = "传入lubricateOrderVO")
	public R submit(@Valid @RequestBody LubricateOrderVO vo) {
		return R.status(lubricateOrderService.submit(vo));
	}

	@PostMapping("/confirm")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "审核确认", notes = "传入lubricateOrderVO，驳回：status=1")
	public R confirm(@RequestBody LubricateOrderVO vo) {
		return R.status(lubricateOrderService.confirm(vo));
	}

	/**
	 * 设置超时提醒时间
	 */
	@PostMapping("/setTimeoutRemind")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "设置超时提醒时间", notes = "传入timeoutRemindSet")
	public R setTimeoutRemind(@Valid @RequestBody TimeoutRemindSetVO timeoutRemindSet) {
		return R.status(timeoutRemindSetService.submit(timeoutRemindSet));
	}

	/**
	 * 超时提醒时间详情
	 */
	@GetMapping("/timeoutRemindDetail")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "超时提醒时间详情", notes = "传入bizType")
	public R<TimeoutRemindSetVO> timeoutRemindDetail(@ApiParam(value = "业务类型", required = true) @RequestParam String bizType
		, SzykUser szykUser) {
		TimeoutRemindSet set = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
			.eq(TimeoutRemindSet::getBizType, bizType).eq(TimeoutRemindSet::getUserId, szykUser.getUserId()));
		if (Func.isNotEmpty(set)) {
			return R.data(Objects.requireNonNull(BeanUtil.copy(set, TimeoutRemindSetVO.class)));
		}
		return R.data(null);
	}

	/**
	 * 即将超时分页 设备润滑工单表
	 */
	@GetMapping("/timeoutPage")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "即将超时分页", notes = "传入inspectOrder")
	public R<IPage<LubricateOrderDTO>> timeoutPage(@ApiIgnore LubricateOrderVO lubricateOrder, Query query) {
		return R.data(lubricateOrderService.timeoutPage(Condition.getPage(query), lubricateOrder));
	}

	/**
	 * 导出 设备润滑工单表
	 */
	@GetMapping("/export-order")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "导出", notes = "传入lubricateOrder")
	public void exportOrder(@ApiIgnore LubricateOrderVO lubricateOrder, HttpServletResponse response) {
		List<LubricateOrderExcel> list = lubricateOrderService.exportOrder(lubricateOrder);
		ExcelUtil.export(response, "润滑工单列表" + DateUtil.time(), "润滑工单", list, LubricateOrderExcel.class);
	}

	@GetMapping("/manual-start")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "手动执行工单生成任务")
	public void manualStart() {
		lubricateSchedule.lubricateSchedule(null);
	}

	@PostMapping("/confirmBatch")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "批量审核确认", notes = "传入lubricateOrderVO，驳回：status=6")
	public R confirmBatch(@RequestBody LubricateOrderVO vo) {
		return R.status(lubricateOrderService.confirmBatch(vo));
	}

}
