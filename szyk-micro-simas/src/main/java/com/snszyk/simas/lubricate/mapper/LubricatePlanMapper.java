package com.snszyk.simas.lubricate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.lubricate.dto.LubricatePlanDTO;
import com.snszyk.simas.lubricate.entity.LubricatePlan;
import com.snszyk.simas.lubricate.vo.LubricatePlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LubricatePlanMapper extends BaseMapper<LubricatePlan> {
    LubricatePlan getEquipmentPlan(Long equipmentId);

	List<LubricatePlan> page(IPage<LubricatePlanDTO> page, @Param("vo") LubricatePlanVO vo);

    int hasExecutePlan(@Param("equipmentId") Long equipmentId,@Param("id") Long id);
}
