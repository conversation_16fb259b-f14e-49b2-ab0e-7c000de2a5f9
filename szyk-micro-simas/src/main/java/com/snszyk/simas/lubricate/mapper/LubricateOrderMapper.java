package com.snszyk.simas.lubricate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LubricateOrderMapper extends BaseMapper<LubricateOrder> {
    List<LubricateOrder> page(IPage page,@Param("order") LubricateOrderVO vo);

	List<LubricateOrder> pageExcludeUnscheduled(IPage page,@Param("order") LubricateOrderVO vo);
    List<LubricateOrder> timeoutPage(IPage page,@Param("order") LubricateOrderVO vo);

	/**
	 * 即将超时工单数量
	 *
	 * @param vo
	 * @return
	 */
	Integer expireSoonCount(@Param("order") LubricateOrderVO vo);

	List<BigScreenMessageDTO> overdueList(@Param("tenantId") String tenantId);

    List<LubricateOrderDTO> lubricateStatistics(@Param("tenantId") String tenantId);

    Integer handleLubricateCount(@Param("vo") LubricateOrderVO vo);
}
