package com.snszyk.simas.lubricate.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceAccount;
import com.snszyk.common.equipment.entity.DeviceCategory;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.location.entity.Location;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.entity.TimeoutRemindSet;
import com.snszyk.simas.common.enums.BizTypeEnum;
import com.snszyk.simas.common.enums.OrderActionEnum;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.excel.LubricateOrderExcel;
import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
import com.snszyk.simas.common.processor.OrderLogProcessor;
import com.snszyk.simas.common.service.IComponentMaterialService;
import com.snszyk.simas.fault.service.IFaultDefectService;
import com.snszyk.simas.fault.vo.FaultDefectVO;
import com.snszyk.simas.fault.wrapper.FaultDefectWrapper;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.entity.LubricateMethods;
import com.snszyk.simas.lubricate.entity.LubricateOilType;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
import com.snszyk.simas.lubricate.service.ILubricateMethodsService;
import com.snszyk.simas.lubricate.service.ILubricateOilTypeService;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.simas.lubricate.wrapper.LubricateOrderWrapper;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.system.vo.RoleVO;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 润滑工单 服务实现类
 */

@Slf4j
@AllArgsConstructor
@Service
public class LubricateOrderServiceImpl extends BaseServiceImpl<LubricateOrderMapper, LubricateOrder> implements ILubricateOrderService {
	private final ILubricateStandardsService lubricateStandardsService;
	private final ILubricateOilTypeService lubricateOilTypeService;
	private final ILubricateMethodsService lubricateMethodsService;
	private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	private final IAttachClient attachClient;
	private final ISysClient sysClient;
	private final IUserClient userClient;
	private final IMessageClient messageClient;
	private final IComponentMaterialService componentMaterialService;

	private final IDeviceAccountClient accountClient;

	private final IFaultDefectService faultDefectService;

	@Override
	public IPage<LubricateOrderDTO> page(IPage<LubricateOrderDTO> page, LubricateOrderVO vo) {
//		Long simasAdmin = null;
//		Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
//		if (Func.isNotEmpty(role)) {
//			simasAdmin = role.getId();
//		}
//		Long lubricateUser = null;
//		role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.LUBRICATE_USER);
//		if (Func.isNotEmpty(role)) {
//			lubricateUser = role.getId();
//		}
//		// 数据权限
//		String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
//		if (simasAdmin != null && !roleId.contains(Func.toStr(simasAdmin))) {
//			// 只能查看本部门的
//			vo.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//			if (lubricateUser != null && roleId.contains(Func.toStr(lubricateUser))) {
//				// 润滑人员只能查看派发给本人的点检工单
//				vo.setChargeUser(AuthUtil.getUserId());
//			}
//		}
		if (Func.isNotEmpty(vo.getQueryStartDate())) {
			vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getQueryEndDate())) {
			vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		}
		List<LubricateOrder> list = baseMapper.page(page, vo);
		List<LubricateOrderDTO> resultList = LubricateOrderWrapper.build().listDTO(list);
		resultList.forEach(order -> {
			// 是否需要审核
			OrderStatusEnum status = OrderStatusEnum.getByCode(order.getStatus());
			if (status == OrderStatusEnum.IS_CLOSED || status == OrderStatusEnum.IS_COMPLETED
				|| status == OrderStatusEnum.OVERDUE_COMPLETED) {
				JSONObject jsonObject = JSONObject.parseObject(order.getStandardsInfo());
				if (jsonObject != null) {
					order.setEquipmentName(jsonObject.getString("equipmentName"))
						.setEquipmentMonitorName(jsonObject.getString("equipmentMonitorName"))
						.setLubricateCycle(jsonObject.getInteger("lubricateCycle"))
						.setFloatTime(jsonObject.getInteger("floatTime"));
					DeviceAccount equipmentAccount = accountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(jsonObject.getLong("equipmentId")).getData();
					// 地点
					if (Func.isNotEmpty(equipmentAccount)) {
						if (Func.isNotEmpty(equipmentAccount.getLocationId())) {
							Location location = CommonCache.getLocation(equipmentAccount.getLocationId());
//						Location location = SimasCache.getLocation(equipmentAccount.getLocationId());
							if (Func.isNotEmpty(location)) {
								order.setLocationName(location.getName()).setLocationPath(location.getPath().replace(",", "/"));
							}
						}

						DeviceCategory equipmentCategory = CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId());
						if (Func.isNotEmpty(equipmentCategory)) {
							order.setEquipmentCategoryName(equipmentCategory.getCategoryName());
						}
					}
//					order.setEquipmentCategoryName(SimasCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
				}
			} else {
				String standardsInfo = order.getStandardsInfo();
				if (Func.isNotEmpty(standardsInfo)) {
					LubricateStandards standards = JSONUtil.toBean(standardsInfo, LubricateStandards.class);
//					LubricateStandards standards = lubricateStandardsService.getById(order.getStandardsId());
					DeviceAccountVO equipmentAccount = accountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(standards.getEquipmentId()).getData();
//				EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
					DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
//				EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
					// 地点
					if (Func.isNotEmpty(equipmentAccount) && Func.isNotEmpty(equipmentAccount.getLocationId())) {
						Location location = CommonCache.getLocation(equipmentAccount.getLocationId());
//					Location location = SimasCache.getLocation(equipmentAccount.getLocationId());
						if (Func.isNotEmpty(location)) {
							order.setLocationName(location.getName()).setLocationPath(location.getPath().replace(",", "/"));
						}
					}
					if (Func.isNotEmpty(equipmentAccount)) {
						order.setEquipmentName(equipmentAccount.getName())
							.setEquipmentMonitorName(equipmentMonitor.getName())
							.setLubricateCycle(standards.getLubricateCycle())
							.setEquipmentCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName())
//					.setEquipmentCategoryName(SimasCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName())
							.setFloatTime(standards.getFloatTime());
					}
				}
			}
		});
		return page.setRecords(resultList);
	}

	@Override
	public LubricateOrderDTO detail(String no) {
		LubricateOrder order = baseMapper.selectOne(Wrappers.<LubricateOrder>lambdaQuery().eq(LubricateOrder::getNo, no));
		LubricateOrderDTO dto = LubricateOrderWrapper.build().entityDTO(order);
		// 取json里的标准信息
		LubricateStandards standards = JSONObject.parseObject(order.getStandardsInfo(), LubricateStandards.class);
//		LubricateStandards standards = lubricateStandardsService.getById(order.getStandardsId());
		DeviceAccount equipmentAccount = accountClient.deviceInfoByIdIgnoreDeletedAndLeaseBack(standards.getEquipmentId()).getData();
//		EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
		DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
//		EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
		LubricateOilType oilType = lubricateOilTypeService.getById(standards.getOilTypeId());
		LubricateMethods methods = lubricateMethodsService.getById(standards.getLubricateMethodsId());
		// 地点
		if (Func.isNotEmpty(equipmentAccount) && Func.isNotEmpty(equipmentAccount.getLocationId())) {
			Location location = CommonCache.getLocation(equipmentAccount.getLocationId());
//			Location location = SimasCache.getLocation(equipmentAccount.getLocationId());
			if (Func.isNotEmpty(location)) {
				dto.setLocationName(location.getName()).setLocationPath(location.getPath().replace(",", "/"));
			}
		}
		if (ObjectUtil.isNotEmpty(equipmentMonitor)) {
			dto.setEquipmentMonitorName(equipmentMonitor.getName());
		}
		if (Func.isNotEmpty(equipmentAccount)) {
			dto.setEquipmentName(equipmentAccount.getName())
				.setLubricateCycle(standards.getLubricateCycle());
			// 设备的编码
			dto.setEquipmentCode(equipmentAccount.getCode());
		}
		if (ObjectUtil.isNotEmpty(oilType)) {
			dto.setOilTypeName(oilType.getName());
		}
		if (ObjectUtil.isNotEmpty(methods)) {
			dto.setMethodsName(methods.getName());
		}

		if (StringUtil.isNotBlank(order.getAttachIds())) {
			R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(order.getAttachIds()));
			if (ObjectUtil.isNotEmpty(attachListR) && attachListR.isSuccess()) {
				dto.setAttachList(attachListR.getData());
			}
		}
		if (Func.isNotEmpty(order.getMaterial())) {
			dto.setMaterialList(JsonUtil.parseArray(order.getMaterial(), ComponentMaterialVO.class));
		}
		return dto;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveOrUpdateBatch(List<LubricateOrder> list) {
		return saveOrUpdateBatch(list, 500);
	}

	@Override
	public List<BigScreenMessageDTO> overdueList(String tenantId) {
		return baseMapper.overdueList(tenantId);

	}

	@Override
	public List<LubricateOrderDTO> lubricateStatistics(String tenantId) {
		return baseMapper.lubricateStatistics(tenantId);
	}

	@Override
	public Integer handleLubricateCount(LubricateOrderVO vo) {
		return baseMapper.handleLubricateCount(vo);
	}

	/**
	 * 提交审核
	 *
	 * @param vo
	 * @return
	 */
	@Override
	public boolean submit(LubricateOrderVO vo) {
		if (vo.getId() == null) {
			throw new ServiceException("工单id不能为空");
		}
		LubricateOrder order = baseMapper.selectById(vo.getId());
		OrderStatusEnum status = OrderStatusEnum.getByCode(order.getStatus());
		Integer oldStatus = null;
		if (status != null) {
			oldStatus = status.getCode();
		}
		if (status == OrderStatusEnum.IS_COMPLETED
			|| status == OrderStatusEnum.OVERDUE_COMPLETED
			|| status == OrderStatusEnum.IS_CLOSED) {
			throw new ServiceException("工单已结束，请勿重复提交!");
		}
		if (status == OrderStatusEnum.WAIT_CONFIRM) {
			throw new ServiceException("工单已提交，请勿重复操作!");
		}
		order.setExecuteTime(new Date());
		order.setExecuteUser(AuthUtil.getUserId());
		order.setChargeUser(order.getExecuteUser());
		order.setSubmitTime(new Date());
		order.setAttachIds(vo.getAttachIds());
		order.setIsAbnormal(vo.getIsAbnormal());
		order.setAbnormalLevel(vo.getAbnormalLevel());
		order.setAbnormalComment(vo.getAbnormalComment());
//		order.setAbnormalImage(vo.getAbnormalImage());
		order.setIsHandled(vo.getIsHandled());
		order.setRemark(vo.getRemark());
		if (Func.isEmpty(vo.getAttachIds()) && Objects.equals(vo.getIsAbnormal(), 1)) {
			throw new ServiceException("请上传附件!");
		}
		if (Func.isNotEmpty(vo.getMaterialList())) {
			order.setMaterial(JSONUtil.toJsonStr(vo.getMaterialList()));
		} else {
			order.setMaterial(null);
		}
		Boolean needApproval = order.getIsNeedApproval();
		order.setIsNeedApproval(needApproval);
		updateById(order);
		// 如果需要审核
		if (needApproval != null && needApproval) {
			order.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
			updateById(order);
		} else {
			// if 不需要审核 默认通过
			SpringUtil.getBean(LubricateOrderServiceImpl.class).lubricateApprovalPass(vo.getId());
		}
		// 添加日志
		// 如果之前的状态是驳回,则为再次提交
		if (OrderStatusEnum.IS_REJECTED.getCode().equals(oldStatus)) {
			OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER,
				JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.RE_SUBMIT);
		} else {
			OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER,
				JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.INIT);
		}

		return true;
	}

	/**
	 * 润滑工单审核
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	@Override
	public boolean confirm(LubricateOrderVO vo) {
		OrderStatusEnum status = OrderStatusEnum.getByCode(vo.getStatus());
		LubricateOrder order = baseMapper.selectById(vo.getId());
		// 审核拒绝
		if (status == OrderStatusEnum.IS_REJECTED) {
			order.setCheckUser(AuthUtil.getUserId());
			order.setStatus(vo.getStatus());
			order.setRejectReason(vo.getRejectReason());
			this.sendMessage(Collections.singletonList(order), MessageBizTypeEnum.SIMAS_LUBRICATE_REJECT);
			this.updateById(order);
			OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.AUDIT_FAIL,
				vo.getRejectReason());
		} else {
			// 审核通过
			SpringUtil.getBean(LubricateOrderServiceImpl.class).lubricateApprovalPass(vo.getId());
			OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.AUDIT_PASS);

		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean confirmBatch(LubricateOrderVO vo) {
		if (Func.isNotEmpty(vo.getOrderIds())) {
			vo.getOrderIds().forEach(id -> {
				vo.setId(id);
				this.confirm(vo);
			});
		}
		return true;
	}

	/**
	 * 审批通过
	 *
	 * @param orderId
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean lubricateApprovalPass(Long orderId) {
		LubricateOrder order = baseMapper.selectById(orderId);
		// 审核通过
		order.setCheckUser(AuthUtil.getUserId());
		// 润滑标准
		String standardsInfo = order.getStandardsInfo();
		JSONObject lubricateStandards = JSONObject.parseObject(standardsInfo);
		Integer floatTime = 0;
		if (lubricateStandards != null) {
			// 从json 里取出浮动时间
			floatTime = lubricateStandards.getInteger("floatTime");
		}
//		LubricateStandards lubricateStandards = lubricateStandardsService.getById(order.getStandardsId());
		// 浮动时间
//		Integer floatTime = lubricateStandards.getFloatTime();
		// 本次工单计划截止时间
		String planEndTime = DateUtils.formatTime(DateUtils.addDays(order.getPlanTime(), floatTime), DateUtils.DAY_PATTERN);
		// 本次工单执行时间
		String orderExecuteTime = DateUtils.formatTime(order.getExecuteTime(), DateUtils.DAY_PATTERN);
		// 判断是否超时 20250328 计划时间加浮动时间=今天,也是正常完成>=0
		if (DateUtils.compareDate(planEndTime, orderExecuteTime) >= 0) {
			order.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
		} else {
			order.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		}
		order.setCompleteTime(new Date());
		// order的润滑的标准20250326 工单生成的时候就添加
//		order.setStandardsInfo(parseStandardToJson(lubricateStandards));
		// 生成未排期工单
		createUnscheduledOrder(order, lubricateStandards);
		// 发送工单审核通过消息
		this.sendMessage(Collections.singletonList(order), MessageBizTypeEnum.SIMAS_LUBRICATE_PASS);
		if (Func.isNotEmpty(order.getMaterial())) {
			// 备品备件入库
			List<ComponentMaterialVO> materials = JSONUtil.toList(order.getMaterial(), ComponentMaterialVO.class);
			if (Func.isNotEmpty(materials)) {
				componentMaterialService.submitBatch(order.getNo(), SystemModuleEnum.LUBRICATE_ORDER.getCode(), materials);
			}
		}
		// 生成缺陷
		if (order.getIsAbnormal() == 1) {
			FaultDefectVO faultDefectVO = FaultDefectWrapper.build().lubricateEntityVO(order);
			faultDefectService.submit(Collections.singletonList(faultDefectVO));
		}
		return this.updateById(order);
	}

	@Override
	public IPage<LubricateOrderDTO> timeoutPage(IPage<LubricateOrderDTO> page, LubricateOrderVO lubricateOrder) {
		lubricateOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			.eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			.eq(TimeoutRemindSet::getBizType, BizTypeEnum.LUBRICATE.getCode()));
		if (Func.isNotEmpty(timeoutRemindSet)) {
			lubricateOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		}
		// 当前登录人和所属部门
		lubricateOrder.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			.setChargeUser(AuthUtil.getUserId());
		List<LubricateOrder> list = baseMapper.timeoutPage(page, lubricateOrder);
		List<LubricateOrderDTO> resultList = LubricateOrderWrapper.build().listDTO(list);
		resultList.forEach(dto -> {
			String standardsInfo = dto.getStandardsInfo();
			if (Func.isNotBlank(standardsInfo)) {
				LubricateStandards standards = JSONUtil.toBean(standardsInfo, LubricateStandards.class);
				if (Func.isNotEmpty(standards)) {
					//			LubricateStandards standards = lubricateStandardsService.getById(dto.getStandardsId());
					DeviceAccount equipmentAccount = accountClient.deviceInfoById(standards.getEquipmentId()).getData();
//			EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
					DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
					//			EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
					if (Func.isNotEmpty(equipmentAccount.getLocationId())) {

						Location location = CommonCache.getLocation(equipmentAccount.getLocationId());
//				Location location = SimasCache.getLocation(equipmentAccount.getLocationId());
						if (Func.isNotEmpty(location)) {
							dto.setLocationName(location.getName()).setLocationPath(location.getPath().replace(",", "/"));
						}
					}
					dto.setEquipmentName(equipmentAccount.getName())
						.setEquipmentMonitorName(equipmentMonitor.getName())
						.setLubricateCycle(standards.getLubricateCycle())
						.setFloatTime(standards.getFloatTime());
				}
			}


		});
		return page.setRecords(resultList);
	}

	@Override
	public void sendMessage(List<LubricateOrder> list, MessageBizTypeEnum messageBizType) {
		log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		list.forEach(order -> {
			// 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SIMAS");
			messageVo.setSender("SIMAS");
			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			messageVo.setTitle(messageBizType.getMessage());
			messageVo.setBizType(messageBizType.getCode());
			messageVo.setBizId(order.getNo());
			messageVo.setContent(JSONUtil.toJsonStr(order));
			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			if (Func.isNotEmpty(order.getChargeUser())) {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(order.getChargeUser());
				receiverInfoVo.setUserList(Arrays.asList(userVo));
			} else {
				// 部门下具有保养员角色的人
				R<RoleVO> roleResult = sysClient.getRoleByAlias(order.getTenantId(), SimasConstant.SimasRole.LUBRICATE_USER);
				if (roleResult.isSuccess() && Func.isNotEmpty(roleResult.getData())) {
					R<List<User>> userListResult = userClient.userListByDeptRole(order.getChargeDept(),
						roleResult.getData().getId());
					if (userListResult.isSuccess() && Func.isNotEmpty(userListResult.getData())) {
						receiverInfoVo.setUserList(userListResult.getData().stream().map(user -> {
							ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
							userVo.setId(user.getId());
							return userVo;
						}).collect(Collectors.toList()));
					}
				}
			}
			messageVo.setReceiverInfoVo(receiverInfoVo);
			messageClient.pushMessage(messageVo);
		});
		log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	}

	private void createUnscheduledOrder(LubricateOrder lastLubricateOrder, JSONObject lubricateStandards) {
		if (Func.isNotEmpty(lastLubricateOrder)) {
			OrderStatusEnum status = OrderStatusEnum.getByCode(lastLubricateOrder.getStatus());
			if (status == OrderStatusEnum.OVERDUE_COMPLETED || status == OrderStatusEnum.IS_COMPLETED) {
				LubricateOrder order = new LubricateOrder();
				// 下次计划执行时间
//				Date nextPlanTime = DateUtils.addDays(lastLubricateOrder.getExecuteTime(), lubricateStandards.getLubricateCycle());
				Date nextPlanTime = DateUtils.addDays(lastLubricateOrder.getExecuteTime(), lubricateStandards.getInteger("lubricateCycle"));
				order.setPlanTime(nextPlanTime);
				order.setNo(BizCodeUtil.generate("LO"));
				order.setName(lastLubricateOrder.getName());
				order.setEquipmentCode(lubricateStandards.getString("equipmentCode"));
				order.setChargeDept(lastLubricateOrder.getChargeDept());
				order.setChargeUser(lastLubricateOrder.getChargeUser());
				order.setPlanId(lastLubricateOrder.getPlanId());
				order.setStandardsId(lastLubricateOrder.getStandardsId());
				order.setUnscheduled(1);
				order.setLastTime(lastLubricateOrder.getExecuteTime());
				order.setFloatTime(lubricateStandards.getInteger("floatTime"));
				order.setEquipmentId(lastLubricateOrder.getEquipmentId());
				order.setStandardsInfo(JSONUtil.toJsonStr(lubricateStandards));
				this.save(order);
			}
		}
	}

	@Override
	public Integer expireSoonCount() {
		LubricateOrderVO order = new LubricateOrderVO();
		order.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			.eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			.eq(TimeoutRemindSet::getBizType, BizTypeEnum.LUBRICATE.getCode()));
		if (Func.isNotEmpty(timeoutRemindSet)) {
			order.setTimeInterval(timeoutRemindSet.getTimeInterval());
		}
		// 当前登录人和所属部门
		order.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			.setCheckUser(AuthUtil.getUserId());
		return baseMapper.expireSoonCount(order);
	}

	@Override
	public List<LubricateOrderVO> selectByEquipmentIds(List<Long> equipmentIds) {
		List<LubricateStandards> standardsList = lubricateStandardsService.list(Wrappers.<LubricateStandards>query().lambda().in(LubricateStandards::getEquipmentId, equipmentIds));
		if (Func.isNotEmpty(standardsList)) {
			List<Long> standardsIds = standardsList.stream().map(LubricateStandards::getId).collect(Collectors.toList());
			List<LubricateOrder> list = this.list(Wrappers.<LubricateOrder>query().lambda().in(LubricateOrder::getStandardsId, standardsIds).notIn(LubricateOrder::getStatus, OrderStatusEnum.completedStatus()));
			return LubricateOrderWrapper.build().listVO(list);
		}
		return null;
	}

	@Override
	public List<LubricateOrderExcel> exportOrder(LubricateOrderVO vo) {
		IPage page = page(new Page(1L, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(page.getRecords(), LubricateOrderExcel.class);
		// Long simasAdmin = null;
		// Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
		// if(Func.isNotEmpty(role)){
		// 	simasAdmin = role.getId();
		// }
		// Long lubricateUser = null;
		// role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.LUBRICATE_USER);
		// if(Func.isNotEmpty(role)){
		// 	lubricateUser = role.getId();
		// }
		// // 数据权限
		// String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
		// if(simasAdmin != null && !roleId.contains(Func.toStr(simasAdmin))){
		// 	// 只能查看本部门的
		// 	vo.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
		// 	if(lubricateUser != null && roleId.contains(Func.toStr(lubricateUser))){
		// 		// 点检人员只能查看派发给本人的点检工单
		// 		vo.setChargeUser(AuthUtil.getUserId());
		// 	}
		// }
		// if (Func.isNotEmpty(vo.getQueryStartDate())) {
		// 	vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		// }
		// if (Func.isNotEmpty(vo.getQueryEndDate())) {
		// 	vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		// }
		// QueryWrapper<LubricateOrder> queryWrapper = Wrappers.query();
		// queryWrapper.lambda().isNull(LubricateOrder::getUnscheduled);
		// if(Func.isNotEmpty(vo.getChargeDept())){
		// 	queryWrapper.lambda().eq(LubricateOrder::getChargeDept, Func.toLongList(AuthUtil.getDeptId()).get(0));
		// }
		// if(Func.isNotEmpty(vo.getChargeUser())){
		// 	queryWrapper.lambda().and(wrapper -> {
		// 		wrapper.isNull(LubricateOrder::getChargeUser).or().eq(LubricateOrder::getChargeUser, AuthUtil.getUserId());
		// 	});
		// }
		// if(Func.isNotEmpty(vo.getStatus())){
		// 	queryWrapper.lambda().eq(LubricateOrder::getStatus, vo.getStatus());
		// }
		// if(Func.isNotEmpty(vo.getNo())){
		// 	queryWrapper.lambda().like(LubricateOrder::getNo, vo.getNo());
		// }
		// if(Func.isNotEmpty(vo.getName())){
		// 	queryWrapper.lambda().like(LubricateOrder::getName, vo.getName());
		// }
		// if(Func.isNotEmpty(vo.getQueryStartDate())){
		// 	queryWrapper.lambda().ge(LubricateOrder::getPlanTime, vo.getQueryStartDate());
		// }
		// if(Func.isNotEmpty(vo.getQueryEndDate())){
		// 	queryWrapper.lambda().le(LubricateOrder::getPlanTime, vo.getQueryEndDate());
		// }
		// queryWrapper.lambda().orderByDesc(LubricateOrder::getCreateTime);
		// List<LubricateOrder> list = baseMapper.selectList(queryWrapper);
		// if (Func.isNotEmpty(list)) {
		// 	List<LubricateOrderDTO> orderList = LubricateOrderWrapper.build().listDTO(list);
		// 	AtomicReference<Integer> sn = new AtomicReference<>(1);
		// 	return orderList.stream().map(order -> {
		// 		LubricateOrderExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, LubricateOrderExcel.class));
		// 		orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
		// 		orderExcel.setPlanTimeStr(DateUtils.formatTime(order.getPlanTime(), DateUtils.DAY_PATTERN));
		// 		if (Func.isNotEmpty(order.getExecuteTime())){
		// 			orderExcel.setExecuteTimeStr(DateUtils.formatTime(order.getExecuteTime(), DateUtils.DAY_PATTERN));
		// 		}
		// 		OrderStatusEnum status = OrderStatusEnum.getByCode(order.getStatus());
		// 		orderExcel.setStatusName(status.getName());
		// 		if (status == OrderStatusEnum.IS_CLOSED || status == OrderStatusEnum.IS_COMPLETED
		// 			|| status == OrderStatusEnum.OVERDUE_COMPLETED){
		// 			JSONObject jsonObject = JSONObject.parseObject(order.getStandardsInfo());
		// 			orderExcel.setName(jsonObject.getString("equipmentName") + "(" + jsonObject.getString("equipmentMonitorName") + jsonObject.getInteger("lubricateCycle") + "天)");
		// 			orderExcel.setFloatingTimeStr(jsonObject.getString("floatTime"));
		// 		} else {
		// 			LubricateStandards standards = lubricateStandardsService.getById(order.getStandardsId());
		// 			EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
		// 			EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
		// 			orderExcel.setName(equipmentAccount.getName() + "(" + equipmentMonitor.getName() + standards.getLubricateCycle() + "天)");
		// 			orderExcel.setEquipmentName(equipmentAccount.getName());
		// 			if (Func.isNotEmpty(standards.getFloatTime())){
		// 				orderExcel.setFloatingTimeStr(standards.getFloatTime().toString());
		// 			}
		// 		}
		// 		return orderExcel;
		// 	}).collect(Collectors.toList());
		// }
		// return null;
	}

	/**
	 * 标准信息(已完成工单)
	 *
	 * @param standards 润滑标准
	 * @return
	 */
	@Override
	public String parseStandardToJson(LubricateStandards standards) {
		DeviceAccountVO equipmentAccount = accountClient.deviceInfoById(standards.getEquipmentId()).getData();
//		EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
		DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
//		EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
		LubricateOilType oilType = lubricateOilTypeService.getById(standards.getOilTypeId());
		LubricateMethods lubricateMethods = lubricateMethodsService.getById(standards.getLubricateMethodsId());
		cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(standards);
		jsonObject.putOpt("equipmentName", equipmentAccount.getName());
		jsonObject.putOpt("equipmentMonitorName", equipmentMonitor.getName());
		jsonObject.putOpt("oilTypeName", oilType.getName());
		jsonObject.putOpt("lubricateMethodsName", lubricateMethods.getName());
		return jsonObject.toString();
	}

	@Override
	public List<LubricateOrder> listBy(Long chargeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus, Boolean noUnscheduled) {
		return this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(chargeDeptId), LubricateOrder::getChargeDept, chargeDeptId)
			.in(ObjectUtil.isNotEmpty(executeUserIds), LubricateOrder::getExecuteUser, executeUserIds)
			.in(ObjectUtil.isNotEmpty(equipmentIds), LubricateOrder::getEquipmentId, equipmentIds)
			.ge(ObjectUtil.isNotEmpty(startDateTime), LubricateOrder::getCreateTime, startDateTime)
			.lt(ObjectUtil.isNotEmpty(endDateTime), LubricateOrder::getCreateTime, endDateTime)
			.ne(ObjectUtil.isNotEmpty(neStatus), LubricateOrder::getStatus, neStatus)
			.isNull(ObjectUtil.isNotEmpty(noUnscheduled), LubricateOrder::getUnscheduled)
			.list();
	}
}
