<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.lubricate.mapper.LubricatePlanMapper">


    <resultMap id="pageResultMap" type="com.snszyk.simas.lubricate.entity.LubricatePlan">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="charge_user" property="chargeUser"/>
        <result column="charge_dept" property="chargeDept"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>

    <select id="getEquipmentPlan" resultType="com.snszyk.simas.lubricate.entity.LubricatePlan">
        select p.* from simas_lubricate_plan p
        left join simas_lubricate_plan_equipment e
        on p.id = e.plan_id
        where p.is_deleted = 0 and e.is_deleted = 0 and p.status in (0,1) and e.equipment_id = #{equipmentId}
    </select>

    <select id="page" resultMap="pageResultMap">
         select * from simas_lubricate_plan where is_deleted = 0
        <if test="vo.name != null and vo.name != ''">
            and `name` like concat('%', #{vo.name}, '%')
        </if>
        <if test="vo.status != null">
            and `status` = #{vo.status}
        </if>
        <if test="vo.chargeDept != null and vo.chargeDept != ''">
            and charge_dept = #{vo.chargeDept}
        </if>
        <if test="vo.queryStartDate != null and vo.queryStartDate != ''">
            and start_time <![CDATA[ >= ]]> #{vo.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="vo.queryEndDate != null and vo.queryEndDate != ''">
            and start_time <![CDATA[ <= ]]> #{vo.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>
    <select id="hasExecutePlan" resultType="java.lang.Integer">
        select count(1) from simas_lubricate_plan p
        left join simas_lubricate_plan_equipment e on p.id = e.plan_id
        where p.is_deleted = 0 and e.equipment_id = #{equipmentId} and p.status in (0,1)
        <if test="id != null">
            and p.id != #{id}
        </if>
    </select>


</mapper>
