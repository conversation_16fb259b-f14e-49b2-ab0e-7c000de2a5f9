/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.lubricate.dto.LubricatePlanDTO;
import com.snszyk.simas.lubricate.entity.LubricatePlan;
import com.snszyk.simas.lubricate.enums.LubPlanStatusEnum;
import com.snszyk.simas.lubricate.vo.LubricatePlanVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备点巡检标准表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class LubricatePlanWrapper extends BaseEntityWrapper<LubricatePlan, LubricatePlanVO> {

	public static LubricatePlanWrapper build() {
		return new LubricatePlanWrapper();
	}

	@Override
	public LubricatePlanVO entityVO(LubricatePlan entity) {
		LubricatePlanVO vo = Objects.requireNonNull(BeanUtil.copy(entity, LubricatePlanVO.class));
		return vo;
	}

	public LubricatePlanDTO entityDTO(LubricatePlan entity) {
		LubricatePlanDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, LubricatePlanDTO.class));
		if (Func.isNotEmpty(entity.getChargeDept())) {
			Dept dept = SysCache.getDept(entity.getChargeDept());
			if (Func.isNotEmpty(dept)) {
				dto.setChargeDeptName(dept.getDeptName());
			}
		}
		if (Func.isNotEmpty(entity.getChargeUser())) {
			User user = UserCache.getUser(entity.getChargeUser());
			if (Func.isNotEmpty(user)) {
				dto.setChargeUserName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getCreateUser())) {
			User user = UserCache.getUser(entity.getCreateUser());
			if (Func.isNotEmpty(user)) {
				dto.setCreateUserName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getStatus())) {
			dto.setStatusName(LubPlanStatusEnum.getByCode(entity.getStatus()).getName());
		}
		Optional.ofNullable(entity.getUpdateUser())
			.map(UserCache::getUser)
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));

		return dto;
	}


	public List<LubricatePlanDTO> listDTO(List<LubricatePlan> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
