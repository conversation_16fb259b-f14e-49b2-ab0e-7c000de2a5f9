<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.lubricate.mapper.LubricateOrderMapper">


    <resultMap id="pageResultMap" type="com.snszyk.simas.lubricate.entity.LubricateOrder">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="plan_id" property="planId"/>
        <result column="standards_id" property="standardsId"/>
        <result column="plan_time" property="planTime"/>
        <result column="execute_time" property="executeTime"/>
        <result column="check_user" property="checkUser"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="standards_info" property="standardsInfo"/>
        <result column="charge_user" property="chargeUser"/>
        <result column="charge_dept" property="chargeDept"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>

    <select id="page" resultMap="pageResultMap">
        select * from simas_lubricate_order where is_deleted = 0
        <if test="order.keywords != null and order.keywords != ''">
            and (`no` like concat('%', #{order.keywords}, '%') or `name` like concat('%', #{order.keywords}, '%')
            or `equipment_code` like concat('%', #{order.keywords}, '%'))
        </if>
        <if test="order.no != null and order.no != ''">
            and `no` like concat('%', #{order.no}, '%')
        </if>
        <if test="order.name != null and order.name != ''">
            and `name` like concat('%', #{order.name}, '%')
        </if>
        <if test="order.status != null">
            and status = #{order.status}
        </if>
        <if test="order.unscheduled == null">
            and unscheduled is null
        </if>
        <if test="order.unscheduled != null">
            and unscheduled = #{order.unscheduled}
        </if>
        <if test="order.queryStartDate != null and order.queryStartDate != ''">
            and plan_time <![CDATA[ >= ]]> #{order.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="order.queryEndDate != null and order.queryEndDate != ''">
            and plan_time <![CDATA[ <= ]]> #{order.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        <if test="order.chargeUser != null and order.chargeUser != ''">
            and (charge_user is null or charge_user = #{order.chargeUser})
        </if>
        <if test="order.equipmentCode != null and order.equipmentCode != ''">
            and equipment_code = #{order.equipmentCode}
        </if>
        <if test="order.startCreateTime!=null">
            and create_time <![CDATA[ >= ]]> #{order.startCreateTime}
        </if>
        <if test="order.endCreateTime!=null">
            and create_time <![CDATA[ < ]]> #{order.endCreateTime}
        </if>
        <if test="order.chargeDeptIdList != null">
            and charge_dept in
            <foreach collection="order.chargeDeptIdList" item="deptId" index="index" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="order.equipmentIds != null">
            and equipment_id in
            <foreach collection="order.equipmentIds" item="equipmentId" index="index" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>

        <if test="order.onlyQueryExecuteUser!=null">
            <choose>
                <when test="order.onlyQueryExecuteUser == 0">
                    and execute_user is null
                </when>
                <otherwise>
                    and execute_user = #{order.onlyQueryExecuteUser}
                </otherwise>
            </choose>
        </if>
        <if test="order.chargeDept != null">
            <choose>
                <when test="order.chargeDept==0">
                    and charge_dept is null
                </when>
                <otherwise>
                    and charge_dept = #{order.chargeDept}
                </otherwise>
            </choose>
        </if>
        <if test="order.equipmentId != null">
            and equipment_id = #{order.equipmentId}
        </if>
        <if test="order.neStatus != null">
            and status <![CDATA[ <> ]]> #{order.neStatus}
        </if>
        <if test="order.statusList != null and order.statusList.size() > 0">
            and status in
            <foreach collection="order.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        order by create_time desc,id desc
    </select>

    <select id="timeoutPage" resultMap="pageResultMap">
        select * from simas_lubricate_order
        where is_deleted = 0 and charge_dept = #{order.chargeDept}
          and status in (0, 1)
          and (charge_user is null  or charge_user = #{order.chargeUser})
        <![CDATA[ AND NOW() >= DATE_SUB(CONCAT(DATE_FORMAT(DATE_ADD(plan_time, INTERVAL float_time DAY), '%Y-%m-%d'), ' 23:59:59'), INTERVAL #{order.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < CONCAT(DATE_FORMAT(DATE_ADD(plan_time, INTERVAL float_time DAY), '%Y-%m-%d'), ' 23:59:59') ]]>
        order by id desc
    </select>
    <select id="expireSoonCount" resultType="java.lang.Integer">
        select count(1)
        from simas_lubricate_order o
                 LEFT JOIN device_account e ON o.equipment_id = e.id
        where e.is_deleted = 0
          and o.is_deleted = 0
          and o.charge_dept = #{order.chargeDept}
          and o.status in (0, 1)
            <if test="order.chargeUser != null">
                and (o.charge_user is null or o.charge_user = #{order.chargeUser})
            </if>
         <![CDATA[ AND NOW() >=
              DATE_SUB(CONCAT(DATE_FORMAT(DATE_ADD(o.plan_time, INTERVAL float_time DAY), '%Y-%m-%d'), ' 23:59:59'),
                       INTERVAL #{order.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < CONCAT(DATE_FORMAT(DATE_ADD(o.plan_time, INTERVAL float_time DAY), '%Y-%m-%d'), ' 23:59:59')
        ]]>
    </select>
    <select id="overdueList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        select t.no as orderNo, t2.id as equipmentId, t2.name as equipmentName
        from simas_lubricate_order t
                 left join simas_lubricate_standards t1 on t.standards_id = t1.id and t1.is_deleted = 0
                 left join device_account t2 on t1.equipment_id = t2.id and t2.is_deleted = 0
        where t.is_deleted = 0
          and t.status = 3
            <if test="tenantId != null and tenantId !=''">
                and t.tenant_id = #{tenantId}
            </if>
        order by t.create_time desc

    </select>
    <select id="lubricateStatistics" resultType="com.snszyk.simas.lubricate.dto.LubricateOrderDTO">
        select t.id,
               t.tenant_id,
               t.plan_id,
               t.standards_id,
               t.equipment_code,
               t.no,
               t.status,
               t2.id   as equipmentId,
               t3.id   as equipmentMonitorId,
               t3.name as equipmentMonitorName ,t2.name as equipmentName
        from simas_lubricate_order t
                 left join simas_lubricate_standards t1 on t.standards_id = t1.id and t1.is_deleted = 0
                 left join device_account t2 on t1.equipment_id = t2.id and t2.is_deleted = 0
                 left join simas_equipment_monitor t3 on t1.equipment_monitor_id = t3.id
        where t.is_deleted = 0
          and to_days(t.create_time) >= to_days(now()) - 30
        <if test="tenantId != null and tenantId !=''">
            and t.tenant_id = #{tenantId}
        </if>
        and t.unscheduled is null
        order by t.create_time desc, t.id desc
    </select>
    <select id="pageExcludeUnscheduled" resultMap="pageResultMap">
        select * from simas_lubricate_order where is_deleted = 0
        <if test="order.keywords != null and order.keywords != ''">
            and (`no` like concat('%', #{order.keywords}, '%') or `name` like concat('%', #{order.keywords}, '%'))
        </if>
        <if test="order.no != null and order.no != ''">
            and `no` like concat('%', #{order.no}, '%')
        </if>
        <if test="order.name != null and order.name != ''">
            and `name` like concat('%', #{order.name}, '%')
        </if>
        <if test="order.status != null">
            and status = #{order.status}
        </if>
        <if test="order.queryStartDate != null and order.queryStartDate != ''">
            and plan_time <![CDATA[ >= ]]> #{order.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="order.queryEndDate != null and order.queryEndDate != ''">
            and plan_time <![CDATA[ <= ]]> #{order.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        <if test="order.chargeUser != null and order.chargeUser != ''">
            and (charge_user is null or charge_user = #{order.chargeUser})
        </if>
        <if test="order.chargeDept != null and order.chargeDept != ''">
            and charge_dept = #{order.chargeDept}
        </if>
        <if test="order.startCreateTime!=null and order.endCreateTime!=null">
            and create_time between #{order.startCreateTime} and #{order.endCreateTime}
        </if>
        <if test="order.chargeDeptIdList != null">
            and charge_dept in
            <foreach collection="order.chargeDeptIdList" item="deptId" index="index" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="order.equipmentIds != null">
            and equipment_id in
            <foreach collection="order.equipmentIds" item="equipmentId" index="index" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>

        order by create_time desc
    </select>
    <select id="handleLubricateCount" resultType="java.lang.Integer"
            parameterType="com.snszyk.simas.lubricate.vo.LubricateOrderVO">
        select count(1) from simas_lubricate_order where is_deleted = 0
        <if test="vo.queryAuthRole != null and vo.queryAuthRole == 1">
            and (charge_user = #{vo.chargeUser} or ( charge_dept = #{vo.chargeDept} and charge_user is null))
        </if>
        <if test="vo.queryAuthRole != null and vo.queryAuthRole == 2">
            and  charge_dept = #{vo.chargeDept} and charge_user is null
        </if>
        <if test="vo.statusList != null and vo.statusList.size() > 0">
            and status in
            <foreach collection="vo.statusList" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="vo.tenantId != null and vo.tenantId != ''">
            and tenant_id = #{vo.tenantId}
        </if>
        <if test="vo.startCreateTime!=null and vo.endCreateTime!=null">
            and create_time between #{vo.startCreateTime} and #{vo.endCreateTime}
        </if>
        <if test="vo.chargeDeptIdList != null">
            and charge_dept in
            <foreach collection="vo.chargeDeptIdList" item="deptId" index="index" open="(" close=")" separator=","/>
        </if>
        and unscheduled is null
    </select>

</mapper>
