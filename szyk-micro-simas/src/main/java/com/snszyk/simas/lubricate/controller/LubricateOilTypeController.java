package com.snszyk.simas.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.lubricate.dto.LubricateOilTypeDTO;
import com.snszyk.simas.lubricate.entity.LubricateOilType;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.service.ILubricateOilTypeService;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import com.snszyk.simas.lubricate.wrapper.LubricateOilTypeWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/lubricate/oil-type")
@Api(value = "油品类型", tags = "设备油品类型接口")
public class LubricateOilTypeController extends SzykController {

	private final ILubricateOilTypeService lubricateOilTypeService;
	private final ILubricateStandardsService lubricateStandardsService;

	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "编辑", notes = "传入lubricateOilType")
	public R<Boolean> submit(@RequestBody LubricateOilType lubricateOilType) {
		if (StringUtils.isEmpty(lubricateOilType.getName())) {
			return R.fail("油品类型不能为空");
		}
		lubricateOilType.setTenantId(AuthUtil.getTenantId());
		boolean res = lubricateOilTypeService.saveOrUpdate(lubricateOilType);
		if (!res) {
			return R.fail("更新失败");
		}
		return R.data(res);
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<LubricateOilType> detail(Long id) {
		return R.data(lubricateOilTypeService.getById(id));
	}

	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入lubricateOilType")
	public R<List<LubricateOilType>> list(LubricateOilType lubricateOilType) {
		return R.data(lubricateOilTypeService.list(Condition.getQueryWrapper(lubricateOilType)));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入lubricateOilType")
	public R<IPage<LubricateOilTypeDTO>> page(LubricateOilType lubricateOilType, Query query) {
		IPage<LubricateOilType> page = lubricateOilTypeService.
			page(Condition.getPage(query), Condition.getQueryWrapper(lubricateOilType).orderByDesc("create_time"));
		IPage<LubricateOilTypeDTO> convert = page.convert(e -> LubricateOilTypeWrapper.build().entityDTO(e));
		return R.data(convert);
	}

	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> idList = Func.toLongList(ids);
		boolean quote = false;
		for (Long id : idList) {
			// 效验是否有标准引用
			int count = lubricateStandardsService.count(Wrappers.<LubricateStandards>lambdaQuery().eq(LubricateStandards::getOilTypeId, id));
			if (count > 0) {
				quote = true;
				break;
			}
		}
		if (quote) {
			return R.fail("存在数据被引用，无法删除！");
		}
		return R.data(lubricateOilTypeService.removeByIds(Func.toLongList(ids)));
	}

}
