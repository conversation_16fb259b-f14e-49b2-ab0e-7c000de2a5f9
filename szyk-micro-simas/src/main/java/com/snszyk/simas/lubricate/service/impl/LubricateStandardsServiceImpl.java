package com.snszyk.simas.lubricate.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
import com.snszyk.common.equipment.feign.FeignPage;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.inspect.service.IInspectStandardService;
import com.snszyk.simas.lubricate.entity.*;
import com.snszyk.simas.lubricate.enums.LubPlanStatusEnum;
import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
import com.snszyk.simas.lubricate.mapper.LubricatePlanMapper;
import com.snszyk.simas.lubricate.mapper.LubricateStandardsMapper;
import com.snszyk.simas.lubricate.service.ILubricateMethodsService;
import com.snszyk.simas.lubricate.service.ILubricateOilTypeService;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;
import com.snszyk.simas.lubricate.wrapper.LubricateStandardsWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class LubricateStandardsServiceImpl extends ServiceImpl<LubricateStandardsMapper, LubricateStandards> implements ILubricateStandardsService {

	private final ICommonClient commonClient;
	private final LubricatePlanMapper lubricatePlanMapper;
	private final LubricateOrderMapper lubricateOrderMapper;
	private final ILubricateMethodsService lubricateMethodsService;
	private final ILubricateOilTypeService lubricateOilTypeService;

	private final IInspectStandardService stdService;

	private final IDeviceAccountClient deviceAccountClient;


	@Override
	public IPage<DeviceAccountVO> devicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount) {
		DeviceAccountPageVO vo = BeanUtil.copy(deviceAccount, DeviceAccountPageVO.class);
		R<FeignPage<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageListScope(vo,
			Func.toInt(page.getCurrent()), Func.toInt(page.getSize()), EquipmentDataScopeEnum.LUBRICATE_STANDARD.getCode());
		if (!deviceResult.isSuccess()) {
			throw new ServiceException("查询设备台账信息失败！");
		}
		if (Func.isNotEmpty(deviceResult.getData()) && Func.isNotEmpty(deviceResult.getData().getRecords())) {
			deviceResult.getData().getRecords().forEach(data -> {
				// 润滑标准数量
				data.setLubricateStandardCount(this.count(Wrappers.<LubricateStandards>query().lambda()
					.eq(LubricateStandards::getEquipmentId, data.getId())));
			});
		}
		IPage<DeviceAccountVO> result = new Page<>();
		result.setTotal(deviceResult.getData().getTotal());
		result.setRecords(deviceResult.getData().getRecords());
		return result;


	}

	@Override
	public boolean submit(EquipmentStandardVO vo) {
		R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(vo.getEquipmentId());
// 		R<DeviceAccountVO> deviceAccountResult = commonClient.deviceInfoById(vo.getEquipmentId());
		if (!deviceAccountResult.isSuccess()) {
			throw new ServiceException("查询设备台账信息失败！");
		}
		if (Func.isEmpty(deviceAccountResult.getData())) {
			throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		}
		DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		// 部位标准
		List<LubricateStandards> tandardsList = this.list(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, vo.getEquipmentId()));
		// 如果list不为空需要比对判断新增、编辑、删除的情况
		if (Func.isNotEmpty(tandardsList)) {
			// 清空标准
			if (Func.isEmpty(vo.getLubricateStandardsList())) {
				// 删除未完成工单
				lubricateOrderMapper.delete(Wrappers.<LubricateOrder>query().lambda()
					.in(LubricateOrder::getStandardsId, tandardsList.stream().map(LubricateStandards::getId).collect(Collectors.toList()))
					.notIn(LubricateOrder::getStatus, OrderStatusEnum.completedStatus())
				);
				// 全部删除
				return this.remove(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, vo.getEquipmentId()));
			}

			// 删除标准
			Set<Long> standardsIds = vo.getLubricateStandardsList().stream().filter(lubricateStandards -> Func.isNotEmpty(lubricateStandards.getId())).map(LubricateStandards::getId).collect(Collectors.toSet());
			// 删除的标准集合,完成工单保留，未完成的工单删除；
			List<Long> deleteIds = tandardsList.stream().filter(lubricateStandards -> !standardsIds.contains(lubricateStandards.getId())).map(LubricateStandards::getId).collect(Collectors.toList());
			if (Func.isNotEmpty(deleteIds)) {
				// 删除未完成工单
				lubricateOrderMapper.delete(Wrappers.<LubricateOrder>query().lambda()
					.in(LubricateOrder::getStandardsId, deleteIds)
					.notIn(LubricateOrder::getStatus, OrderStatusEnum.completedStatus())
				);
				this.remove(Wrappers.<LubricateStandards>query().lambda().in(LubricateStandards::getId, deleteIds));
			}

			// 获取润滑计划
			LubricatePlan plan = lubricatePlanMapper.getEquipmentPlan(vo.getEquipmentId());
			// 新增：判断此设备是否存在润滑计划，如果存在设置标准的开始执行时间，否则判断计划状态，未执行状态设置为计划的开始时间（默认时间），执行中状态则第二天生成第一条工单
			if (plan != null) {
				LubPlanStatusEnum statusEnum = LubPlanStatusEnum.getByCode(plan.getStatus());
				vo.getLubricateStandardsList().forEach(s -> {
					// 新增标准
					if (Func.isEmpty(s.getId())) {
						//如果是新增的
						if (s.getEquipmentMonitorId() == null) {
//							Long monitorId = stdService.genMonitor(vo.getEquipmentId(), s.getEquipmentMonitorName(), s.getMonitorType());
//							s.setEquipmentMonitorId(monitorId);
						}
						s.setEquipmentId(vo.getEquipmentId());
						if (statusEnum == LubPlanStatusEnum.NO_START) {
							s.setStartTime(plan.getStartTime());
						}
						if (statusEnum == LubPlanStatusEnum.IN_PROGRESS) {
							s.setStartTime(DateUtils.nextDate(new Date()));
						}
						// 一个部位只能设置一条标准
						if (this.count(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentMonitorId, s.getEquipmentMonitorId())) > 0) {
							throw new ServiceException("此设备部位已存在润滑标准，请先删除再添加！");
						}
					}
				});
			}
		}
		AtomicReference<Integer> sort = new AtomicReference<>(1);
		List<LubricateStandards> list = vo.getLubricateStandardsList().stream().map(standardVO -> {
			if(standardVO.getId()==null&&standardVO.getEquipmentMonitorId()==null){
				standardVO.setEquipmentMonitorId(stdService.genMonitor(vo.getEquipmentId(), standardVO.getEquipmentMonitorName(), standardVO.getMonitorType()));
			}
			standardVO.setEquipmentId(vo.getEquipmentId());
			standardVO.setEquipmentCode(equipmentAccount.getCode());
			standardVO.setSort(sort.getAndSet(sort.get() + 1));
			return Objects.requireNonNull(BeanUtil.copy(standardVO, LubricateStandards.class));
		}).collect(Collectors.toList());
		return this.saveOrUpdateBatch(list);
	}

	@Override
	public EquipmentStandardVO detail(Long equipmentId) {
		R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		if (!deviceAccountResult.isSuccess()) {
			throw new ServiceException("查询设备台账信息失败！");
		}
		if (Func.isEmpty(deviceAccountResult.getData())) {
			throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		}
		EquipmentStandardVO detail = new EquipmentStandardVO(equipmentId);
		detail.setEquipmentAccount(deviceAccountResult.getData());
		List<LubricateStandards> standardList = this.list(Wrappers.<LubricateStandards>query().lambda()
			.eq(LubricateStandards::getEquipmentId, equipmentId));
		if (Func.isNotEmpty(standardList)) {
			List<LubricateStandardsVO> lubricateStandardsList = standardList.stream().map(l -> {
				LubricateStandardsVO vo = LubricateStandardsWrapper.build().entityVO(l);
				LubricateMethods methods = lubricateMethodsService.getById(l.getLubricateMethodsId());
				LubricateOilType oilType = lubricateOilTypeService.getById(l.getOilTypeId());
				vo.setMethodsName(methods.getName());
				vo.setOilTypeName(oilType.getName());

				return vo;
			}).collect(Collectors.toList());
			detail.setLubricateStandardsList(lubricateStandardsList);
		}
		return detail;
	}

	@Override
	public boolean clear(Long equipmentId) {
		List<LubricateStandards> list = this.list(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, equipmentId));
//		List<Long> ids = list.stream().map(LubricateStandards::getId).collect(Collectors.toList());
//		if (Func.isNotEmpty(ids)) {
//			lubricateOrderMapper.delete(Wrappers.<LubricateOrder>query().lambda()
//				.in(LubricateOrder::getStandardsId, ids)
//				.notIn(LubricateOrder::getStatus, OrderStatusEnum.completedStatus())
//			);
//		}
		return this.remove(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, equipmentId));
	}

	@Override
	public List<LubricateStandardsVO> list(String equipmentIds) {
		List<LubricateStandards> standardList = this.list(Wrappers.<LubricateStandards>query().lambda()
			.in(LubricateStandards::getEquipmentId, Func.toLongList(equipmentIds)));
		List<LubricateStandardsVO> lubricateStandardsList = new ArrayList<>();
		if (Func.isNotEmpty(standardList)) {
			Map<Long, List<LubricateStandards>> map = standardList.stream().collect(Collectors.groupingBy(LubricateStandards::getEquipmentId));
			AtomicReference<Integer> sort = new AtomicReference<>(1);
			map.forEach((equipmentId, standards) -> {
				for (LubricateStandards s : standards) {
					LubricateStandardsVO lubricateStandardsVO = Objects.requireNonNull(BeanUtil.copy(s, LubricateStandardsVO.class));
					DeviceMonitor equipmentMonitor = CommonCache.getMonitor(s.getEquipmentMonitorId());
					//
// 					R<DeviceAccountVO> deviceAccountResult = commonClient.deviceInfoById(equipmentId);
					R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
					if (deviceAccountResult.isSuccess() && Func.isNotEmpty(deviceAccountResult.getData())) {
						lubricateStandardsVO.setEquipmentName(deviceAccountResult.getData().getName());
					}
					lubricateStandardsVO.setSort(sort.getAndSet(sort.get() + 1));
					if (Func.isNotEmpty(equipmentMonitor)) {
						lubricateStandardsVO.setEquipmentMonitorName(equipmentMonitor.getName());
					}
					lubricateStandardsList.add(lubricateStandardsVO);
				}
			});
		}
		return lubricateStandardsList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveImportData(List<LubricateStandards> list) {
		updateSort(list);
		return this.saveOrUpdateBatch(list);
	}


	/**
	 * 添加排序
	 *
	 * @param list
	 */
	private void updateSort(List<LubricateStandards> list) {
		// 获取设备id分组
		Map<Long, List<LubricateStandards>> map = list.stream().collect(Collectors.groupingBy(LubricateStandards::getEquipmentId));
		map.keySet().forEach(equipmentId -> {
			LubricateStandards standard = this.getOne(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, equipmentId).orderByDesc(LubricateStandards::getSort).last("LIMIT 1"));
			AtomicReference<Integer> sort = new AtomicReference<>(1);
			if (Func.isNotEmpty(standard) && !Func.isNull(standard.getSort())) {
				sort.set(standard.getSort());
			}
			map.get(equipmentId).forEach(item -> {
				LubricateStandards entity = this.getOne(Wrappers.<LubricateStandards>query().lambda()
					.eq(LubricateStandards::getEquipmentId, item.getEquipmentId())
					.eq(LubricateStandards::getEquipmentMonitorId, item.getEquipmentMonitorId()));
				if (Func.isNotEmpty(entity)) {
					item.setStartTime(entity.getStartTime());
					item.setSort(entity.getSort());
					item.setId(entity.getId());
				} else {
					item.setSort(sort.getAndSet(sort.get() + 1));
				}
			});
		});
	}
}
