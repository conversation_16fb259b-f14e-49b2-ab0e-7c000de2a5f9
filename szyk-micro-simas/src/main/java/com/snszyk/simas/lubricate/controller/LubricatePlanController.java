 package com.snszyk.simas.lubricate.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.simas.lubricate.dto.LubricatePlanDTO;
 import com.snszyk.simas.lubricate.entity.LubricatePlan;
 import com.snszyk.simas.lubricate.enums.LubPlanStatusEnum;
 import com.snszyk.simas.common.excel.LubricatePlanExcel;
 import com.snszyk.simas.lubricate.service.ILubricatePlanService;
 import com.snszyk.simas.lubricate.vo.LubricatePlanVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;

 @RestController
 @AllArgsConstructor
 @RequestMapping("/lubricate/plan")
 @Api(value = "润滑计划", tags = "润滑计划表接口")
 public class LubricatePlanController extends SzykController {

 	private final ILubricatePlanService lubricatePlanService;

 	@PostMapping("/submit")
 	@ApiOperationSupport(order = 1)
 	@ApiOperation(value = "提交", notes = "传入lubricatePlanVO")
 	public R submit(@Valid @RequestBody LubricatePlanVO vo) {
 		return R.status(lubricatePlanService.submit(vo));
 	}

 	@GetMapping("/detail")
 	@ApiOperationSupport(order = 2)
 	@ApiOperation(value = "详情", notes = "传入no")
 	public R<LubricatePlanDTO> detail(String no) {
 		return R.data(lubricatePlanService.detail(no));
 	}

 	@GetMapping("/view")
 	@ApiOperationSupport(order = 3)
 	@ApiOperation(value = "查看", notes = "传入no")
 	public R<LubricatePlanDTO> view(String no){
 		return R.data(lubricatePlanService.view(no));
 	}

 	@GetMapping("/page")
 	@ApiImplicitParams({
 		@ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "chargeDept", value = "负责部门", paramType = "query", dataType = "long"),
 		@ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "int"),
 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
 	})
 	@ApiOperationSupport(order = 4)
 	@ApiOperation(value = "分页", notes = "传入lubricatePlanVO")
 	public R<IPage<LubricatePlanDTO>> page(@ApiIgnore LubricatePlanVO vo, Query query){
 		return R.data(lubricatePlanService.page(Condition.getPage(query), vo));
 	}

 	@PostMapping("/delete")
 	@ApiOperationSupport(order = 5)
 	@ApiOperation(value = "删除", notes = "传入planId")
 	public R delete(Long id) {
 		return R.status(lubricatePlanService.removeById(id));
 	}

 	@PostMapping("/stop")
 	@ApiOperationSupport(order = 6)
 	@ApiOperation(value = "停止执行", notes = "传入planId")
 	public R stop(Long id){
 		LubricatePlan plan = lubricatePlanService.getById(id);
 		plan.setStatus(LubPlanStatusEnum.IS_TERMINATED.getCode());
 		return R.status(lubricatePlanService.updateById(plan));
 	}

 	/**
 	 * 导出 设备润滑计划表
 	 */
 	@GetMapping("/export-plan")
 	@ApiImplicitParams({
 		@ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "chargeDept", value = "负责部门", paramType = "query", dataType = "long"),
 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
 	})
 	@ApiOperationSupport(order = 7)
 	@ApiOperation(value = "导出", notes = "传入inspectPlan")
 	public void exportPlan(@ApiIgnore LubricatePlanVO lubricatePlan, HttpServletResponse response) {
 		List<LubricatePlanExcel> list = lubricatePlanService.exportPlan(lubricatePlan);
 		ExcelUtil.export(response, "润滑计划列表" + DateUtil.time(), "润滑计划", list, LubricatePlanExcel.class);
 	}
 }
