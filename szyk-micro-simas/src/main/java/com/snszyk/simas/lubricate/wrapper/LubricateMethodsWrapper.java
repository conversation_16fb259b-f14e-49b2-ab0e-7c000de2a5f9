/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.lubricate.dto.LubricateMethodsDTO;
import com.snszyk.simas.lubricate.entity.LubricateMethods;
import com.snszyk.simas.lubricate.vo.LubricateMethodsVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 润滑包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class LubricateMethodsWrapper extends BaseEntityWrapper<LubricateMethods, LubricateMethodsVO> {

	public static LubricateMethodsWrapper build() {
		return new LubricateMethodsWrapper();
	}

	@Override
	public LubricateMethodsVO entityVO(LubricateMethods entity) {
		LubricateMethodsVO vo = Objects.requireNonNull(BeanUtil.copy(entity, LubricateMethodsVO.class));
		return vo;
	}

	public LubricateMethodsDTO entityDTO(LubricateMethods entity) {
		LubricateMethodsDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, LubricateMethodsDTO.class));
		//创建人
		if (Func.isNotEmpty(entity.getCreateUser())) {
			User user = UserCache.getUser(entity.getCreateUser());
			if (Func.isNotEmpty(user)) {
				dto.setCreateUserName(user.getRealName());
			}
		}
		//修改人
		if (Func.isNotEmpty(entity.getUpdateUser())) {
			User user = UserCache.getUser(entity.getUpdateUser());
			if (Func.isNotEmpty(user)) {
				dto.setUpdateUserName(user.getRealName());
			}
		}

		return dto;
	}


	public List<LubricateMethodsDTO> listDTO(List<LubricateMethods> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}


}
