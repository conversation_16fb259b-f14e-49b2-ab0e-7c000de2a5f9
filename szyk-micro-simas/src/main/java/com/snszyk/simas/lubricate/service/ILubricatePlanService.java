 package com.snszyk.simas.lubricate.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.simas.lubricate.dto.LubricatePlanDTO;
 import com.snszyk.simas.lubricate.entity.LubricatePlan;
 import com.snszyk.simas.common.excel.LubricatePlanExcel;
 import com.snszyk.simas.lubricate.vo.LubricatePlanVO;

 import java.util.List;

 public interface ILubricatePlanService extends BaseService<LubricatePlan> {

 	LubricatePlan getEquipmentPlan(Long equipmentId);

 	boolean submit(LubricatePlanVO vo);

 	LubricatePlanDTO detail(String no);

 	IPage<LubricatePlanDTO> page(IPage<LubricatePlanDTO> page, LubricatePlanVO vo);

 	LubricatePlanDTO view(String no);

     List<LubricatePlanExcel> exportPlan(LubricatePlanVO lubricatePlan);
 }
