 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.lubricate.wrapper;

 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.core.mp.support.BaseEntityWrapper;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.entity.EquipmentMonitor;
 import com.snszyk.simas.lubricate.entity.LubricateStandards;
 import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;
 import com.snszyk.system.cache.DictBizCache;

 import java.util.Objects;

 /**
  * 设备点巡检标准表包装类,返回视图层所需的字段
  *
  * <AUTHOR>
  * @since 2024-08-15
  */
 public class LubricateStandardsWrapper extends BaseEntityWrapper<LubricateStandards, LubricateStandardsVO> {

 	public static LubricateStandardsWrapper build() {
 		return new LubricateStandardsWrapper();
  	}

 	@Override
 	public LubricateStandardsVO entityVO(LubricateStandards lubricateStandards) {
 		LubricateStandardsVO lubricateStandardsVO = Objects.requireNonNull(BeanUtil.copy(lubricateStandards, LubricateStandardsVO.class));
		DeviceMonitor equipmentMonitor = CommonCache.getMonitor(lubricateStandards.getEquipmentMonitorId());
		if (Func.isNotEmpty(equipmentMonitor)){
 			lubricateStandardsVO.setEquipmentMonitorName(equipmentMonitor.getName());
			lubricateStandardsVO.setMonitorType(equipmentMonitor.getType());
			if (Func.isNotEmpty(equipmentMonitor.getType())) {
				String monitorTypeName = DictBizCache.getValue("monitor_type", equipmentMonitor.getType());
				lubricateStandardsVO.setMonitorTypeName(monitorTypeName);
			}
 		}

 		return lubricateStandardsVO;
 	}

 }
