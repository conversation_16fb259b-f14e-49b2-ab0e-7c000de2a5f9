 package com.snszyk.simas.lubricate.service.impl;

 import com.alibaba.fastjson.JSONObject;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.entity.EquipmentAccount;
 import com.snszyk.simas.common.entity.EquipmentMonitor;
 import com.snszyk.simas.lubricate.entity.*;
 import com.snszyk.simas.lubricate.service.*;
 import com.snszyk.simas.lubricate.dto.LubricatePlanDTO;
 import com.snszyk.simas.lubricate.dto.LubricatePlanEquipmentMonitorDTO;
 import com.snszyk.simas.lubricate.dto.LubricatePlanEquipmentOrderDTO;
 import com.snszyk.simas.common.enums.OrderStatusEnum;
 import com.snszyk.simas.lubricate.enums.LubPlanStatusEnum;
 import com.snszyk.simas.common.excel.LubricatePlanExcel;
 import com.snszyk.simas.lubricate.mapper.LubricatePlanMapper;
 import com.snszyk.simas.lubricate.vo.LubricatePlanEquipmentMonitorVO;
 import com.snszyk.simas.lubricate.vo.LubricatePlanVO;
 import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;
 import com.snszyk.simas.lubricate.wrapper.LubricatePlanWrapper;
 import com.snszyk.simas.lubricate.wrapper.LubricateStandardsWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.ArrayList;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 @AllArgsConstructor
 @Service
 public class LubricatePlanServiceImpl extends BaseServiceImpl<LubricatePlanMapper, LubricatePlan> implements ILubricatePlanService {

 	private final ILubricateStandardsService lubricateStandardsService;
 	private final ILubricateOilTypeService lubricateOilTypeService;
 	private final ILubricatePlanEquipmentService lubricatePlanEquipmentService;
 	private final ILubricateOrderService lubricateOrderService;

	 private final IDeviceAccountClient accountClient;


 	@Override
 	public LubricatePlan getEquipmentPlan(Long equipmentId) {
 		return this.baseMapper.getEquipmentPlan(equipmentId);
 	}

 	@Override
 	@Transactional(rollbackFor = Exception.class)
 	public boolean submit(LubricatePlanVO vo) {
 		LubricatePlan plan = Objects.requireNonNull(BeanUtil.copy(vo, LubricatePlan.class));
 		if (Func.isEmpty(plan.getId())){
 			plan.setNo(BizCodeUtil.generate("LP")).setStatus(LubPlanStatusEnum.NO_START.getCode());
 		}
 		boolean res = this.saveOrUpdate(plan);
 		//删除旧的关联数据
 		lubricatePlanEquipmentService.remove(Wrappers.<LubricatePlanEquipment>lambdaQuery().eq(LubricatePlanEquipment::getPlanId, plan.getId()));
 		List<LubricatePlanEquipmentMonitorVO> equipmentMonitors = vo.getEquipmentMonitors();
 		if (Func.isNotEmpty(equipmentMonitors)){
 			List<LubricatePlanEquipment> savePlanEquipmentList = new ArrayList<>();
 			List<LubricateStandards> updateStandardsList = new ArrayList<>();

 			AtomicReference<Integer> sort = new AtomicReference<>(1);
 			Map<Long, List<LubricatePlanEquipmentMonitorVO>> map = equipmentMonitors.stream().collect(Collectors.groupingBy(LubricatePlanEquipmentMonitorVO::getEquipmentId));
 			map.keySet().forEach(equipmentId -> {
 				int hasExecutePlan = this.baseMapper.hasExecutePlan(equipmentId, plan.getId());
 				if (hasExecutePlan > 0){
 					throw new ServiceException(map.get(equipmentId).get(0).getEquipmentName() + "设备存在未结束的计划！");
 				}

 				map.get(equipmentId).forEach(equipmentMonitor -> {
 					LubricateStandards standards = lubricateStandardsService.getOne(Wrappers.<LubricateStandards>lambdaQuery().eq(LubricateStandards::getEquipmentMonitorId, equipmentMonitor.getEquipmentMonitorId()));
 					if (Objects.isNull(standards)){
 						throw new ServiceException(equipmentMonitor.getEquipmentName() + ":" + equipmentMonitor.getEquipmentMonitorName() + "标准不存在，请先维护标准信息！");
 					}
 					standards.setStartTime(equipmentMonitor.getStartTime());
 					updateStandardsList.add(standards);
 				});
 				LubricatePlanEquipment planEquipment = new LubricatePlanEquipment();
 				planEquipment.setPlanId(plan.getId())
 					.setEquipmentId(equipmentId)
 					.setSort(sort.getAndSet(sort.get() + 1));
 				savePlanEquipmentList.add(planEquipment);
 			});
 			lubricateStandardsService.updateBatchById(updateStandardsList);
 			lubricatePlanEquipmentService.saveBatch(savePlanEquipmentList);
 		}
 		return res;
 	}

 	@Override
 	public LubricatePlanDTO detail(String no) {
 		LubricatePlan plan = this.getOne(Wrappers.<LubricatePlan>query().lambda().eq(LubricatePlan::getNo, no));
 		LubricatePlanDTO planDTO = LubricatePlanWrapper.build().entityDTO(plan);
 		List<LubricatePlanEquipment> list = lubricatePlanEquipmentService.list(Wrappers.<LubricatePlanEquipment>lambdaQuery()
 			.eq(LubricatePlanEquipment::getPlanId, plan.getId()).orderByAsc(LubricatePlanEquipment::getSort));
 		List<LubricatePlanEquipmentMonitorDTO> equipmentMonitorList = new ArrayList<>();
 		planDTO.setEquipmentMonitorList(equipmentMonitorList);
 		list.forEach(equipment -> {
// 			EquipmentAccount equipmentAccount = equipmentAccountService.getById(equipment.getEquipmentId());
			DeviceAccountVO equipmentAccount = accountClient.deviceInfoById(equipment.getEquipmentId()).getData();

			List<LubricateStandards> standardsList = lubricateStandardsService.list(Wrappers.<LubricateStandards>lambdaQuery().eq(LubricateStandards::getEquipmentId, equipment.getEquipmentId()).orderByAsc(LubricateStandards::getSort));
 			if (Func.isNotEmpty(standardsList)){
 				List<LubricateStandardsVO> standardsVOList = LubricateStandardsWrapper.build().listVO(standardsList);
 				equipmentMonitorList.addAll(standardsVOList.stream().map(standards -> {
 					LubricatePlanEquipmentMonitorDTO dto = new LubricatePlanEquipmentMonitorDTO();
 					dto.setEquipmentId(standards.getEquipmentId())
 						.setEquipmentMonitorId(standards.getEquipmentMonitorId())
 						.setEquipmentName(equipmentAccount!= null ? equipmentAccount.getName():null)
						.setEquipmentCode(equipmentAccount!= null ? equipmentAccount.getCode():null)
 						.setEquipmentMonitorName(standards.getEquipmentMonitorName())
 						.setStartTime(standards.getStartTime());
 					return dto;
 				}).collect(Collectors.toList()));
 			}
 		});
 		return planDTO;
 	}

 	@Override
 	public IPage<LubricatePlanDTO> page(IPage<LubricatePlanDTO> page, LubricatePlanVO vo) {
 		if (Func.isNotEmpty(vo.getQueryStartDate())) {
 			vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
 		}
 		if (Func.isNotEmpty(vo.getQueryEndDate())) {
 			vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
 		}
 		List<LubricatePlan> list = this.baseMapper.page(page, vo);
 		if (Func.isNotEmpty(list)){
 			List<LubricatePlanDTO> resultList = list.stream().map(p->{
 				LubricatePlanDTO dto = LubricatePlanWrapper.build().entityDTO(p);
 				dto.setEquipmentCount(lubricatePlanEquipmentService.count(Wrappers.<LubricatePlanEquipment>lambdaQuery().eq(LubricatePlanEquipment::getPlanId, dto.getId())));
 				return dto;
 			}).collect(Collectors.toList());
 			return page.setRecords(resultList);
 		}
 		return page.setRecords(null);
 	}

 	@Override
 	public LubricatePlanDTO view(String no) {
 		LubricatePlan plan = this.getOne(Wrappers.<LubricatePlan>query().lambda().eq(LubricatePlan::getNo, no));
 		LubricatePlanDTO planDTO = LubricatePlanWrapper.build().entityDTO(plan);
 		List<LubricateOrder> orders = lubricateOrderService.list(Wrappers.<LubricateOrder>lambdaQuery().eq(LubricateOrder::getPlanId, plan.getId()).orderByDesc(LubricateOrder::getCreateTime));
 		if (Func.isNotEmpty(orders)){
 			List<LubricatePlanEquipmentOrderDTO> equipmentOrderList = orders.stream().map(order -> {
 				OrderStatusEnum status = OrderStatusEnum.getByCode(order.getStatus());
 				LubricatePlanEquipmentOrderDTO dto = new LubricatePlanEquipmentOrderDTO();
 				dto.setOrderNo(order.getNo());
 				dto.setBizStatus(OrderStatusEnum.getByCode(order.getStatus()).getName());
 				if ( status == OrderStatusEnum.IS_CLOSED || status == OrderStatusEnum.IS_COMPLETED
 					|| status == OrderStatusEnum.OVERDUE_COMPLETED
 				){
 					JSONObject jsonObject = JSONObject.parseObject(order.getStandardsInfo());
 					dto.setCode(jsonObject.getString("equipmentCode"));
 					dto.setName(jsonObject.getString("equipmentName"));
 					dto.setEquipmentMonitorName(jsonObject.getString("equipmentMonitorName"));
 					dto.setOilTypeName(jsonObject.getString("oilTypeName"));
 				} else {
 					LubricateStandards standards = lubricateStandardsService.getById(order.getStandardsId());
					DeviceAccountVO equipmentAccount = accountClient.deviceInfoById(standards.getEquipmentId()).getData();

//					EquipmentAccount equipmentAccount = equipmentAccountService.getById(standards.getEquipmentId());
// 					EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
					DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
					LubricateOilType lubricateOilType = lubricateOilTypeService.getById(standards.getOilTypeId());
 					dto.setCode(equipmentAccount.getCode())
 						.setName(equipmentAccount.getName())
 						.setEquipmentMonitorName(equipmentMonitor.getName())
 						.setOilTypeName(lubricateOilType.getName());
 				}
 				return dto;
 			}).collect(Collectors.toList());
 			planDTO.setEquipmentOrderList(equipmentOrderList);
 		}
 		return planDTO;
 	}

 	@Override
 	public List<LubricatePlanExcel> exportPlan(LubricatePlanVO vo) {
 		if (Func.isNotEmpty(vo.getQueryStartDate())) {
 			vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
 		}
 		if (Func.isNotEmpty(vo.getQueryEndDate())) {
 			vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
 		}
 		List<LubricatePlan> list = baseMapper.selectList(Wrappers.<LubricatePlan>query().lambda()
 			.eq(Func.isNotEmpty(vo.getChargeDept()), LubricatePlan::getChargeDept, vo.getChargeDept())
 			.like(Func.isNotEmpty(vo.getName()), LubricatePlan::getName, vo.getName())
 			.ge(Func.isNotEmpty(vo.getQueryStartDate()), LubricatePlan::getStartTime, vo.getQueryStartDate())
 			.le(Func.isNotEmpty(vo.getQueryEndDate()), LubricatePlan::getStartTime, vo.getQueryEndDate())
 			.orderByDesc(LubricatePlan::getCreateTime));
 		if (Func.isNotEmpty(list)) {
 			List<LubricatePlanDTO> planList = LubricatePlanWrapper.build().listDTO(list);
 			AtomicReference<Integer> sn = new AtomicReference<>(1);
 			return planList.stream().map(plan -> {
 				LubricatePlanExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, LubricatePlanExcel.class));
 				if (Func.isNotEmpty(plan.getStartTime())) {
 					planExcel.setStartDateStr(DateUtil.formatDate(plan.getStartTime()));
 				}
 				if (Func.isNotEmpty(plan.getEndTime())) {
 					planExcel.setEndDateStr(DateUtil.formatDate(plan.getEndTime()));
 				}
 				planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
 				planExcel.setEquipmentCount(Func.toStr(lubricatePlanEquipmentService.count(Wrappers.<LubricatePlanEquipment>query().lambda()
 					.eq(LubricatePlanEquipment::getPlanId, plan.getId()))));
 				return planExcel;
 			}).collect(Collectors.toList());
 		}
 		return null;
 	}
 }
