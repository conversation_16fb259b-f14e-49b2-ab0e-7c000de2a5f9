package com.snszyk.simas.lubricate.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.excel.listener.LubricateStandardsListener;
import com.snszyk.simas.common.excel.support.EasyExcelUtil;
import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
import com.snszyk.simas.common.excel.template.LubricateStandardExcelTemplate;
import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;
import com.snszyk.simas.maintain.service.IMaintainStandardService;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@AllArgsConstructor
@RequestMapping("/lubricate/standards")
@Api(value = "润滑标准", tags = "润滑标准表接口")
public class LubricateStandardsController extends SzykController {

	private final ILubricateStandardsService lubricateStandardsService;
	private ImportDataValidLogicService importDataValidLogicService;
	private final IMaintainStandardService maintainStandardService;

	/**
	 * 自定义分页 设备点巡检标准表
	 */
	@GetMapping("/devicePage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "useDept", value = "使用部门id", required = true, paramType = "query", dataType = "long")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "设备的分页", notes = "传入useDept")
	public R<IPage<DeviceAccountVO>> devicePage(@ApiIgnore DeviceAccountVO deviceAccount, Query query) {
		return R.data(lubricateStandardsService.devicePage(Condition.getPage(query), deviceAccount));
	}

	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "提交", notes = "传入equipmentStandard")
	public R submit(@Valid @RequestBody EquipmentStandardVO equipmentStandard) {
		return R.status(lubricateStandardsService.submit(equipmentStandard));
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入equipmentId")
	public R<EquipmentStandardVO> detail(Long equipmentId) {
		return R.data(lubricateStandardsService.detail(equipmentId));
	}

	@PostMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入equipmentId")
	public R<List<LubricateStandardsVO>> list(@ApiParam(value = "设备id数组，逗号分隔", required = true) @RequestParam String equipmentIds) {
		return R.data(lubricateStandardsService.list(equipmentIds));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入lubricateStandards")
	public R<IPage<LubricateStandards>> page(LubricateStandards lubricateStandards, Query query) {
		return R.data(lubricateStandardsService.page(Condition.getPage(query), Condition.getQueryWrapper(lubricateStandards)));
	}

	/**
	 * 清空标准
	 */
	@PostMapping("/clear")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "清空标准", notes = "传入equipmentId")
	public R clear(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId) {
		return R.status(lubricateStandardsService.clear(equipmentId));
	}

	@GetMapping("/export-template")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "下载导入模版")
	public void exportTemplate(HttpServletResponse response, @RequestParam(required = false) String deptId) {
		List<BaseStandardTemplate> list = maintainStandardService.generateExcelData(deptId);
		Map<String, Long> map = list.stream().collect(Collectors.groupingBy(BaseStandardTemplate::getCode, Collectors.counting()));
		LinkedHashMap<String, Integer> mergeMap = new LinkedHashMap<>();
		List<LubricateStandardExcelTemplate> templateList = new ArrayList<>();
		for (BaseStandardTemplate account : list) {
			templateList.add(BeanUtil.copyProperties(account, LubricateStandardExcelTemplate.class));
			if (!mergeMap.containsKey(account.getCode())) {
				mergeMap.put(account.getCode(), map.get(account.getCode()).intValue());
			}
		}
		EasyExcelUtil.writerTwo(response, "润滑标准导入模板", "润滑标准", templateList, LubricateStandardExcelTemplate.class, mergeMap, new int[]{0, 1, 2}, 2);
	}

	@PostMapping("/import-data")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "导入标准", notes = "传入excel")
	public R importData(MultipartFile file) {
		LubricateStandardsListener listener = new LubricateStandardsListener(2, lubricateStandardsService, importDataValidLogicService);
		try {//监听器读取内容
			EasyExcel.read(file.getInputStream(), LubricateStandardExcelTemplate.class, listener)
				.sheet()
				.headRowNumber(2) // 忽略表头行数
				.doRead();
		} catch (IOException e) {
			e.printStackTrace();
		}
		List<String> errorMsg = listener.getFailReasonList();
		if (Func.isNotEmpty(errorMsg)) {
			return R.data(errorMsg, "导入异常！");
		}
		return R.success("导入成功");
	}
}
