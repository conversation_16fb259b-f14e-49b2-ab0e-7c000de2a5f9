package com.snszyk.simas.lubricate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.lubricate.dto.LubricateMethodsDTO;
import com.snszyk.simas.lubricate.entity.LubricateMethods;
import com.snszyk.simas.lubricate.service.ILubricateMethodsService;
import com.snszyk.simas.lubricate.vo.LubricateMethodsVO;
import com.snszyk.simas.lubricate.wrapper.LubricateMethodsWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/lubricate/methods")
@Api(value = "润滑手段", tags = "润滑手段表接口")
public class LubricateMethodsController extends SzykController {

	private final ILubricateMethodsService lubricateMethodsService;

	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "编辑", notes = "传入lubricateMethods")
	public R<Boolean> submit(@RequestBody LubricateMethods lubricateMethods) {
		if (StringUtils.isEmpty(lubricateMethods.getName())) {
			return R.fail("润滑手段不能为空");
		}
		if (Func.isEmpty(lubricateMethods.getId())) {
			lubricateMethods.setCreateTime(new Date());
		}
		lubricateMethods.setTenantId(AuthUtil.getTenantId());
		boolean res = lubricateMethodsService.saveOrUpdate(lubricateMethods);
		if (!res) {
			return R.fail("更新失败");
		}
		return R.data(res);
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<LubricateMethods> detail(Long id) {
		return R.data(lubricateMethodsService.getById(id));
	}

	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入lubricateMethods")
	public R<List<LubricateMethods>> list(LubricateMethods lubricateMethods) {
		return R.data(lubricateMethodsService.list(Condition.getQueryWrapper(lubricateMethods)));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入lubricateMethods")
	public R<IPage<LubricateMethodsDTO>> page(LubricateMethods lubricateMethods, Query query) {
		IPage<LubricateMethods> page = lubricateMethodsService.page(Condition.getPage(query), Condition.getQueryWrapper(lubricateMethods).orderByDesc("create_time"));
		return R.data(page.convert(e-> LubricateMethodsWrapper.build().entityDTO(e)));

	}
}
