package com.snszyk.simas.lubricate.schedule;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.enums.*;
import com.snszyk.simas.common.processor.OrderLogProcessor;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.service.logic.GeneralLogicService;
import com.snszyk.simas.common.util.ApprovalUtil;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.entity.LubricatePlan;
import com.snszyk.simas.lubricate.entity.LubricatePlanEquipment;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.enums.LubPlanStatusEnum;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.service.ILubricatePlanEquipmentService;
import com.snszyk.simas.lubricate.service.ILubricatePlanService;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import com.snszyk.simas.lubricate.wrapper.LubricateOrderWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保养计划任务定时器
 *
 * <AUTHOR>
 * @date 2024/08/23 15:16
 **/
@Slf4j
@AllArgsConstructor
@Service
//@EnableScheduling
//@Configuration
public class LubricateSchedule {

	private final ILubricatePlanService lubricatePlanService;
	private final ILubricatePlanEquipmentService lubricatePlanEquipmentService;
	private final ILubricateStandardsService lubricateStandardsService;
	private final ILubricateOrderService lubricateOrderService;
	private final IBizLogService bizLogService;
	private final GeneralLogicService generalLogicService;

	private final IDeviceAccountClient deviceAccountClient;

	//	@Scheduled(cron = "0 0 5 * * ?")
	@XxlJob(value = "lubricateStopPlanHandler")
	public ReturnT<String> stopPlan(String param) {
		log.info("润滑工单-，计划停止定时任务执行了");

		// 查询计划结束日期小于当前日期且状态为执行中的计划
		List<LubricatePlan> planList = lubricatePlanService.list(Wrappers.lambdaQuery(LubricatePlan.class)
			.lt(LubricatePlan::getEndTime, DateUtil.now())
			.eq(LubricatePlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode()));
		if (ObjectUtil.isEmpty(planList)) {
//			XxlJobLogger.log("################润滑工单任务生成-END-###############");
			XxlJobLogger.log("################润滑工单计划停止定时任务-END-###############");
			return ReturnT.SUCCESS;
		}
		// 将计划状态改为已终止
		planList.forEach(plan -> plan.setStatus(PlanStatusEnum.IS_COMPLETED.getCode()));
		lubricatePlanService.updateBatchById(planList);
		log.info("润滑工单-，计划停止定时任务执行了");
		XxlJobLogger.log("################润滑工单计划停止定时任务-END-###############");
		return ReturnT.SUCCESS;
	}

	/**
	 * 生成润滑工单（每日06:00进行）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/8/23 15:16
	 */
// 	@Scheduled(cron = "0 0 6 * * ?")
//	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob(value = "lubricateJobHandler")
	public ReturnT<String> lubricateSchedule(String param) {

		log.info("################润滑工单任务生成-START-################");
		Boolean needApproval;
		List<LubricatePlan> planList = lubricatePlanService.list(Wrappers.<LubricatePlan>query().lambda().in(LubricatePlan::getStatus, LubPlanStatusEnum.NO_START.getCode(), LubPlanStatusEnum.IN_PROGRESS.getCode()));
		if (Func.isNotEmpty(planList)) {

			List<LubricateOrder> list = new ArrayList<>();
			for (LubricatePlan plan : planList) {
				try {
					handleOnePlan(plan, list);
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					log.error("################润滑工单任务生成-ERROR-################ planId{}", plan.getId());
				}
			}
			if (Func.isNotEmpty(list)) {
				log.info("################润滑工单任务生成save-################:{}", list.size());
				lubricateOrderService.saveOrUpdateBatch(list);
				lubricateOrderService.sendMessage(list, MessageBizTypeEnum.SIMAS_LUBRICATE_ADD);
				list.forEach(order -> {
					OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
				});
			}
		} else {
			needApproval = false;
		}
		log.info("################润滑工单任务生成-END-################");
		XxlJobLogger.log("################润滑工单任务生成-END-###############");
		return ReturnT.SUCCESS;
	}

	private void handleOnePlan(LubricatePlan plan, List<LubricateOrder> list) {
		Boolean needApproval;
		needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.LUBRICATE_ORDER.name(), plan.getTenantId());

		String planStartTime = DateUtil.format(plan.getStartTime(), DateUtil.PATTERN_DATE) + DateUtils.DAY_START_TIME;
		String planEndTime = DateUtil.format(plan.getEndTime(), DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME;
		// 计划未开始
		if (DateUtil.now().before(DateUtil.parse(planStartTime, DateUtil.PATTERN_DATETIME))) {
			log.info("################润滑的计划开始-################");

			return;
		}
		// 计划已结束
		if (DateUtil.now().after(DateUtil.parse(planEndTime, DateUtil.PATTERN_DATETIME))) {
			plan.setStatus(LubPlanStatusEnum.IS_COMPLETED.getCode());
			lubricatePlanService.updateById(plan);
			log.info("################润滑的计划已结束-################");
			return;
		}

 				/*
 				计划已开始（状态是未开始代表该计划还未有工单生成)
 				生成工单逻辑：
 				状态未开始(第一次工单生成)
 				1. 部位开始时间 == 计划时间,则直接生成工单
 				2. 部位开始时间 > 计划时间,需要判断部位开始时间 - 浮动时间 <= 当前时间,则直接生成工单
 				状态已开始
 				1. 上次工单执行时间 + 周期 - 浮动时间 <= 当前时间,则直接生成工单
 				 */
		if (LubPlanStatusEnum.NO_START == LubPlanStatusEnum.getByCode(plan.getStatus())) {
			plan.setStatus(LubPlanStatusEnum.IN_PROGRESS.getCode());
			lubricatePlanService.updateById(plan);
			List<LubricatePlanEquipment> equipmentList = lubricatePlanEquipmentService.list(Wrappers.<LubricatePlanEquipment>query().lambda().eq(LubricatePlanEquipment::getPlanId, plan.getId()));
			if (Func.isEmpty(equipmentList)) {
				log.info("润滑计划没有关联的设备 计划id{}", plan.getId());
				return;
			}
			Boolean finalNeedApproval = needApproval;
			for (LubricatePlanEquipment equipment : equipmentList) {
				R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoById(equipment.getEquipmentId());
				DeviceAccountVO equipmentAccount = deviceAccountVOR.getData();
				if (!deviceAccountVOR.isSuccess() || equipmentAccount == null) {
					continue;
				}
// 						EquipmentAccount equipmentAccount = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda().eq(EquipmentAccount::getId, equipment.getEquipmentId()));
				List<LubricateStandards> standardsList = lubricateStandardsService.list(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, equipment.getEquipmentId()));
				if (Func.isNotEmpty(standardsList)) {
					for (LubricateStandards standards : standardsList) {
						DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());
// 								EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
						String name = equipmentAccount.getName() + "(" + equipmentMonitor.getName() + standards.getLubricateCycle() + ")";
						// 生成工单时间(计划开始时间 - 浮动时间)
						LubricateOrder order = generateOrders(plan, standards, name, standards.getStartTime());
						if (order != null) {
							order.setIsNeedApproval(finalNeedApproval);
							list.add(order);
						}

					}
				}
			}
		} else if (LubPlanStatusEnum.IN_PROGRESS == LubPlanStatusEnum.getByCode(plan.getStatus())) {
			// 进行中的计划工单生成情况
			List<LubricatePlanEquipment> equipmentList = lubricatePlanEquipmentService.list(Wrappers.<LubricatePlanEquipment>query().lambda().eq(LubricatePlanEquipment::getPlanId, plan.getId()));
			Boolean finalNeedApproval1 = needApproval;
			for (LubricatePlanEquipment equipment : equipmentList) {
				R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoById(equipment.getEquipmentId());
				DeviceAccountVO equipmentAccount = deviceAccountVOR.getData();
				if (equipmentAccount == null) {
					log.error("润滑的设备信息为空{}", equipment.getEquipmentId());
					continue;
				}
// 						EquipmentAccount equipmentAccount = equipmentAccountService.getOne(Wrappers.<EquipmentAccount>query().lambda().eq(EquipmentAccount::getId, equipment.getEquipmentId()));
				List<LubricateStandards> standardsList = lubricateStandardsService.list(Wrappers.<LubricateStandards>query().lambda().eq(LubricateStandards::getEquipmentId, equipment.getEquipmentId()));
				if (Func.isNotEmpty(standardsList)) {
					for (LubricateStandards standards : standardsList) {
						DeviceMonitor equipmentMonitor = CommonCache.getMonitor(standards.getEquipmentMonitorId());

//								EquipmentMonitor equipmentMonitor = SimasCache.getMonitor(standards.getEquipmentMonitorId());
						String name = equipmentAccount.getName() + "(" + equipmentMonitor.getName() + standards.getLubricateCycle() + ")";
						// 上次生成的工单
						LubricateOrder lastOrder = lubricateOrderService.getOne(Wrappers.<LubricateOrder>query().lambda()
							.eq(LubricateOrder::getPlanId, plan.getId())
							.isNull(LubricateOrder::getUnscheduled)
							.eq(LubricateOrder::getStandardsId, standards.getId())
							.orderByDesc(LubricateOrder::getCreateTime).last(" limit 1"));
						if (Func.isNotEmpty(lastOrder)) {
							if (OrderStatusEnum.getByCode(lastOrder.getStatus()) == OrderStatusEnum.IS_COMPLETED
								|| OrderStatusEnum.getByCode(lastOrder.getStatus()) == OrderStatusEnum.OVERDUE_COMPLETED) {
								// 计划时间
								Date nextPlanTime = DateUtils.addDays(lastOrder.getExecuteTime(), standards.getLubricateCycle());
								LubricateOrder order = generateOrders(plan, standards, name, nextPlanTime);
								if (order != null) {
									order.setLastTime(lastOrder.getExecuteTime());
									order.setIsNeedApproval(finalNeedApproval1);
									list.add(order);
								}
							}
						} else {
							// 第一次触发工单生成
							LubricateOrder order = generateOrders(plan, standards, name, standards.getStartTime());
							if (order != null) {
								order.setIsNeedApproval(finalNeedApproval1);
								list.add(order);
							}
						}
					}
				}
			}
		}
	}

	/**
	 * 生成润滑工单(存在未排期工单，更新参数，转为执行中的状态)
	 * 注：1.新工单计划执行时间 = 上次工单执行时间 + 周期
	 * 2.如果当前时间大于计划开始时间+周期，则新工单计划执行时间 = 当前时间（服务器宕机、重启、服务器升级等情况导致的特殊情况）
	 */
	private LubricateOrder generateOrders(LubricatePlan plan, LubricateStandards standards, String name, Date planTime) {
		Date now = DateUtil.now();
		//生成工单的时候的标准是实时的,在这里保存到order里,之后的逻辑都是在order里取 ,不再实时查询2025
		int exist = lubricateOrderService.count(Wrappers.<LubricateOrder>query().lambda().eq(LubricateOrder::getPlanId, plan.getId())
			.eq(LubricateOrder::getStandardsId, standards.getId())
			.isNull(LubricateOrder::getUnscheduled)
			.eq(LubricateOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
		if (exist > 0) {
			log.error("工单已存在，请勿重复执行！planId{},standards{}", plan.getId(), standards.getId());
			return null;
		}
		// 修改后：20250328改为按天比较  执行时间是昨天下午的5点,下次的计划时间是今天的下午5点,按天比较时间,今天的6点的定时任务可以生成工单
		LocalDate planTimeLocalDate = planTime.toInstant()
			.atZone(ZoneId.systemDefault())
			.toLocalDate();
		LocalDate targetLocalDate = planTimeLocalDate.minusDays(standards.getFloatTime());
//		Date targetDate = Date.from(targetLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
		LocalDate nowDay = LocalDate.now();
		if (nowDay.isAfter(targetLocalDate) || nowDay.isEqual(targetLocalDate)) {
//		if (LocalDate.now().isAfter(DateUtils.addDays(planTime, -standards.getFloatTime()))) {
			// 判断是否有未排期的工单
			LubricateOrder order = lubricateOrderService.getOne(Wrappers.<LubricateOrder>query().lambda()
				.eq(LubricateOrder::getPlanId, plan.getId())
				.eq(LubricateOrder::getStandardsId, standards.getId())
				.eq(LubricateOrder::getUnscheduled, 1)
				.orderByDesc(LubricateOrder::getCreateTime).last(" limit 1"));
			if (Func.isEmpty(order)) {
				order = new LubricateOrder();
				order.setNo(BizCodeUtil.generate("LO"));
			}
			// 润滑标准
			LubricateStandards lubricateStandards = lubricateStandardsService.getById(standards.getId());
			if (lubricateStandards == null) {
				log.error("润滑标准为空，请检查 standards.getId{}", standards.getId());
				return null;
			}
			//order的润滑的标准20250326
			order.setStandardsInfo(lubricateOrderService.parseStandardToJson(lubricateStandards));
			order.setTenantId(plan.getTenantId());
			order.setEquipmentId(standards.getEquipmentId());
			order.setUnscheduled(null);
			order.setPlanId(plan.getId());
			order.setStandardsId(standards.getId());
			order.setName(name);
			order.setEquipmentCode(standards.getEquipmentCode());
			order.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
			order.setChargeDept(plan.getChargeDept());
			order.setChargeUser(plan.getChargeUser());
			order.setFloatTime(standards.getFloatTime());
			order.setPlanTime(now);
			if (Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
				DateUtil.format(DateUtils.addDays(planTime, -standards.getFloatTime()), DateUtil.PATTERN_DATE))
				||
				Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
					DateUtil.format(planTime, DateUtil.PATTERN_DATE))) {
				order.setPlanTime(planTime);
			}
			// 当前时间大于计划开始时间+周期
			if (DateUtil.now().after(DateUtils.addDays(planTime, standards.getLubricateCycle()))) {

				order.setPlanTime(now);
			}
			return order;
		} else {
			log.info("################润滑工单任务生成- 未到标准的开始执行时间END-################");

		}
		return null;
	}

	/**
	 * 工单超期(每分钟执行一次)
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/8/23 15:16
	 */
//	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob(value = "lubricateExpireJobHandler")
	public ReturnT<String> orderOverdueSchedule(String param) {
		log.info("################润滑工单超期定时任务-START-################");
		List<LubricateOrder> list = lubricateOrderService.list(Wrappers.<LubricateOrder>query().lambda()
			.isNull(LubricateOrder::getUnscheduled)
			.eq(LubricateOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
		// 超期工单
		List<LubricateOrder> orderList = new ArrayList<>();
		if (Func.isNotEmpty(list)) {
			for (LubricateOrder order : list) {
				String standardsInfo = order.getStandardsInfo();
				if (Func.isBlank(standardsInfo)) {
					log.error("工单{}的润滑标准为空", order.getNo());
					continue;
				}
				LubricateStandards lubricateStandards = JSONUtil.toBean(standardsInfo, LubricateStandards.class);

//				LubricateStandards lubricateStandards = lubricateStandardsService.getById(order.getStandardsId());
				// 工单截止时间
				if(order.getPlanTime() == null){
					log.error("工单{}的计划性时间为空", order.getNo());
					continue;
				}
				Date endTime = DateUtils.addDays(order.getPlanTime(), lubricateStandards.getFloatTime());
				String planEndTime = DateUtil.format(endTime, DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME;
				// 计划未开始
				if (DateUtil.now().after(DateUtil.parse(planEndTime, DateUtil.PATTERN_DATETIME))) {
					if (Func.isEmpty(order.getExecuteTime())) {
						order.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
					} else {
						order.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
					}
					orderList.add(order);
				}
			}
		}
		if (Func.isNotEmpty(orderList)) {
			lubricateOrderService.updateBatchById(orderList);
			// 业务日志
			bizLogService.submitBatch(orderList.stream().map(order -> {
				BizLogVO bizLog = new BizLogVO(LubricateOrderWrapper.build().entityVO(order));
				if (order.getStatus().equals(OrderStatusEnum.OVERDUE_COMPLETED.getCode())) {
					bizLog.setContent("工单超期完成").setOperateTime(DateUtil.now());
				} else {
					bizLog.setContent("工单超时").setOperateTime(DateUtil.now());
				}
				return bizLog;
			}).collect(Collectors.toList()));
			// 发送消息提醒
			lubricateOrderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_LUBRICATE_OVERDUE);
		}
		log.info("################润滑工单超期定时任务-END-################");
		XxlJobLogger.log("################润滑工单超期定时任务-END-################");
		return ReturnT.SUCCESS;
	}

	/**
	 * 即将超时-发送消息
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2024/9/15 18:31
	 */
// 	@Scheduled(cron = "0 0/1 * * * ?")
	@XxlJob(value = "lubricateExpireSoonJobHandler")
	public ReturnT<String> expireSoonSchedule(String param) {
		log.info("################润滑工单即将超期发送消息-START-################");
		List<LubricateOrder> list = lubricateOrderService.list(Wrappers.<LubricateOrder>query().lambda()
				.isNull(LubricateOrder::getUnscheduled)
			.and(wrapper -> wrapper.eq(LubricateOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
				.or()
				.eq(LubricateOrder::getStatus, OrderStatusEnum.IS_REJECTED.getCode())));
		if (Func.isNotEmpty(list)) {
			for (LubricateOrder order : list) {
				// 消息接收人
				String standardsInfo = order.getStandardsInfo();
				if (Func.isBlank(standardsInfo)) {
					log.error("工单{}的润滑标准为空", order.getNo());
					continue;
				}
				LubricateStandards lubricateStandards = JSONUtil.toBean(standardsInfo, LubricateStandards.class);
//				LubricateStandards lubricateStandards = lubricateStandardsService.getById(order.getStandardsId());
				if (Func.isNotEmpty(lubricateStandards)) {
					if (order.getPlanTime() == null) {
						log.error("工单{}的计划时间为空", order.getNo());
						continue;
					}
					List<Long> userIds = generalLogicService.soonTimeoutUser(BizTypeEnum.LUBRICATE, order.getPlanTime(),
						lubricateStandards.getFloatTime(),
						order.getChargeUser(),
						order.getChargeDept(),
						SimasConstant.SimasRole.LUBRICATE_USER);
					if (Func.isNotEmpty(userIds)) {
						// 发送消息提醒
						generalLogicService.sendMessage(order.getNo(), JSONUtil.toJsonStr(order), userIds, MessageBizTypeEnum.SIMAS_LUBRICATE_EXPIRE);
					}
				}
			}
		}
		log.info("################保养工单即将超期发送消息-END-################");
		XxlJobLogger.log("################保养工单即将超期发送消息-END################");
		return ReturnT.SUCCESS;
	}

}
