/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.lubricate.dto.LubricateOilTypeDTO;
import com.snszyk.simas.lubricate.entity.LubricateOilType;
import com.snszyk.simas.lubricate.vo.LubricateOilTypeVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 润滑包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class LubricateOilTypeWrapper extends BaseEntityWrapper<LubricateOilType, LubricateOilTypeVO> {

	public static LubricateOilTypeWrapper build() {
		return new LubricateOilTypeWrapper();
	}

	@Override
	public LubricateOilTypeVO entityVO(LubricateOilType entity) {
		LubricateOilTypeVO vo = Objects.requireNonNull(BeanUtil.copy(entity, LubricateOilTypeVO.class));
		return vo;
	}

	public LubricateOilTypeDTO entityDTO(LubricateOilType entity) {
		LubricateOilTypeDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, LubricateOilTypeDTO.class));
		//创建人
		if (Func.isNotEmpty(entity.getCreateUser())) {
			User user = UserCache.getUser(entity.getCreateUser());
			if (Func.isNotEmpty(user)) {
				dto.setCreateUserName(user.getRealName());
			}
		}
		//修改人
		if (Func.isNotEmpty(entity.getUpdateUser())) {
			User user = UserCache.getUser(entity.getUpdateUser());
			if (Func.isNotEmpty(user)) {
				dto.setUpdateUserName(user.getRealName());
			}
		}

		return dto;
	}


	public List<LubricateOilTypeDTO> listDTO(List<LubricateOilType> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}


}
