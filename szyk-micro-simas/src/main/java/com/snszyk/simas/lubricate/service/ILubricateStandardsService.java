package com.snszyk.simas.lubricate.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;

import java.util.List;

public interface ILubricateStandardsService extends IService<LubricateStandards> {
	boolean submit(EquipmentStandardVO vo);

	EquipmentStandardVO detail(Long equipmentId);

	boolean clear(Long equipmentId);

	List<LubricateStandardsVO> list(String equipmentIds);

	/**
	 * 导入数据
	 * @param list
	 * @return
	 */
	boolean saveImportData(List<LubricateStandards> list);

	IPage<DeviceAccountVO> devicePage(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount);
}
