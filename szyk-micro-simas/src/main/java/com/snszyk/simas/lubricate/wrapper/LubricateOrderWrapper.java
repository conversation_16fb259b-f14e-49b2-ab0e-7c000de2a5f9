/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备点巡检标准表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class LubricateOrderWrapper extends BaseEntityWrapper<LubricateOrder, LubricateOrderVO> {

	public static LubricateOrderWrapper build() {
		return new LubricateOrderWrapper();
	}

	@Override
	public LubricateOrderVO entityVO(LubricateOrder entity) {
		LubricateOrderVO vo = Objects.requireNonNull(BeanUtil.copy(entity, LubricateOrderVO.class));
		return vo;
	}

	public LubricateOrderDTO entityDTO(LubricateOrder entity) {
		LubricateOrderDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, LubricateOrderDTO.class));
		if (Func.isNotEmpty(entity.getChargeDept())) {
			Dept dept = SysCache.getDept(entity.getChargeDept());
			if (Func.isNotEmpty(dept)) {
				dto.setChargeDeptName(dept.getDeptName());
			}
		}
		if (Func.isNotEmpty(entity.getChargeUser())) {
			User user = UserCache.getUser(entity.getChargeUser());
			if (Func.isNotEmpty(user)) {
				dto.setChargeUserName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getCheckUser())) {
			User user = UserCache.getUser(entity.getCheckUser());
			if (Func.isNotEmpty(user)) {
				dto.setCheckUserName(user.getRealName());
				dto.setApprovalUserName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getExecuteUser())) {
			User user = UserCache.getUser(entity.getExecuteUser());
			if (Func.isNotEmpty(user)) {
				dto.setExecuteUserName(user.getRealName());
			}
		}
		Optional.ofNullable(entity.getStatus())
			.map(OrderStatusEnum::getByCode)
			.ifPresent(statusEnum -> dto.setStatusName(statusEnum.getName()));

		Optional.ofNullable(entity.getAbnormalLevel())
			.map(OrderStatusEnum::getByCode)
			.ifPresent(statusEnum -> dto.setAbnormalLevelName(statusEnum.getName()));

		if (Func.isNotEmpty(entity.getIsHandled())) {
			dto.setIsHandledName(entity.getIsHandled() == 1 ? "是" : "否");
		}
		if (Func.isNotEmpty(entity.getIsAbnormal())) {
			dto.setAbnormalStatus(entity.getIsAbnormal() == 0 ? "正常" : "异常");
		}

		return dto;
	}


	public List<LubricateOrderDTO> listDTO(List<LubricateOrder> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
