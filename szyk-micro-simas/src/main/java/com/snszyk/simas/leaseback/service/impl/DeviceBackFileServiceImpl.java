/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.leaseback.dto.DeviceBackFileDto;
import com.snszyk.simas.leaseback.entity.DeviceBackFile;
import com.snszyk.simas.leaseback.mapper.DeviceBackFileMapper;
import com.snszyk.simas.leaseback.service.IDeviceBackFileService;
import com.snszyk.simas.leaseback.vo.DeviceBackFilePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备归还资料 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceBackFileServiceImpl extends BaseServiceImpl<DeviceBackFileMapper, DeviceBackFile> implements IDeviceBackFileService {

	/**
	 * 名称校验
	 */
	@Override
	public void checkName(Long id, String name) {
//		Integer count = lambdaQuery().eq(DeviceBackFile::getName, name).ne(id != null, DeviceBackFile::getId, id).count();
//		if (count > 0) {
//			throw new ServiceException("名称已存在");
//		}
	}

	/**
	 * 分页查询
	 */
	@Override
	public IPage<DeviceBackFileDto> pageList(DeviceBackFilePageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public DeviceBackFileDto detail(Long id) {
		return baseMapper.detail(id);
	}

	@Override
	public List<Attach> listAttachByEquipmentId(Long equipmentId) {
		return baseMapper.listAttachByEquipmentId(equipmentId);
	}
}
