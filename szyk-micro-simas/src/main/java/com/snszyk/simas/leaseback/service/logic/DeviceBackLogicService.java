/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.service.IInspectOrderService;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.leaseback.entity.DeviceBack;
import com.snszyk.simas.leaseback.entity.DeviceBackFile;
import com.snszyk.simas.leaseback.service.IDeviceBackFileService;
import com.snszyk.simas.leaseback.service.IDeviceBackService;
import com.snszyk.simas.leaseback.vo.DeviceBackAVo;
import com.snszyk.simas.leaseback.vo.DeviceBackPageVo;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.service.IMaintainOrderService;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IRepairService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备租赁归还 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceBackLogicService {

	private final IDeviceBackService deviceBackService;
	private final IDeviceBackFileService deviceBackFileService;
	private final IDeviceAccountClient client;
	private final ILubricateOrderService lubricateOrderService;
	private final IOverhaulOrderService overhaulOrderService;
	private final IInspectOrderService inspectOrderService;
	private final IRepairService repairService;
	private final IMaintainOrderService maintainOrderService;


	@Transactional(rollbackFor = Exception.class)
	public DeviceBackDto saveOrUpdate(DeviceBackAVo v) {
		//当前的设备是否已经归还
		R<DeviceAccountVO> deviceAccountVOR = client.deviceInfoById(v.getDeviceId());
		if (deviceAccountVOR.isSuccess() && deviceAccountVOR.getData() == null) {
			throw new ServiceException("当前设备已归还");
		}
		//validOrder 校验是否有未完成的工单
		validOrder(v.getDeviceId());

		DeviceBack copy = BeanUtil.copy(v, DeviceBack.class);
		boolean b = deviceBackService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		//归还的附件
		if (v.getFileIds() != null && !v.getFileIds().

			isEmpty()) {
			//保存附件
			for (Long fileId : v.getFileIds()) {
				DeviceBackFile file = new DeviceBackFile();
				if (copy != null) {
					file.setBackId(copy.getId());
				}
				file.setAttachId(fileId);
				deviceBackFileService.saveOrUpdate(file);
			}
		}

		//主数据client的同步
		R<Boolean> result = client.leaseBackDevice(copy.getDeviceId());
		if (!result.isSuccess()) {
			throw new ServiceException("主数据同步失败");
		}
		return detail(copy.getId());
	}

	/**
	 * <AUTHOR>
	 * @Description 校验是否有未完成的工单
	 * @Date 下午2:16 2025/3/29
	 * @Param [deviceId]
	 * @return void
	 **/
	private void validOrder(Long deviceId) {
		//未完成的状态
		List<Integer> statusList = Arrays.asList(OrderStatusEnum.WAIT.getCode(), OrderStatusEnum.IN_PROCESS.getCode(), OrderStatusEnum.IS_OVERDUE.getCode(),
			OrderStatusEnum.WAIT_CONFIRM.getCode(), OrderStatusEnum.IS_REJECTED.getCode());
		//润滑
		Integer count = lubricateOrderService.lambdaQuery().eq(LubricateOrder::getEquipmentId, deviceId).in(BaseEntity::getStatus, statusList).count();
		if (count > 0) {
			throw new ServiceException("当前设备有未完成的润滑工单");
		}
		//维修
		count = repairService.lambdaQuery().eq(Repair::getEquipmentId, deviceId).in(BaseEntity::getStatus, statusList).count();
		if (count > 0) {
			throw new ServiceException("当前设备有未完成的维修工单");
		}
		//点检
		count = inspectOrderService.lambdaQuery().eq(InspectOrder::getEquipmentId, deviceId).in(BaseEntity::getStatus, statusList).count();
		if (count > 0) {
			throw new ServiceException("当前设备有未完成的点巡检工单");
		}
		//检修
		count = overhaulOrderService.lambdaQuery().eq(OverhaulOrder::getEquipmentId, deviceId).in(BaseEntity::getStatus, statusList).count();
		if (count > 0) {
			throw new ServiceException("当前设备有未完成的检修工单");
		}
		//保养
		count = maintainOrderService.lambdaQuery().eq(MaintainOrder::getEquipmentId, deviceId).in(BaseEntity::getStatus, statusList).count();
		if (count > 0) {
			throw new ServiceException("当前设备有未完成的保养工单");

		}
	}

	public IPage<DeviceBackDto> pageList(DeviceBackPageVo v) {
		IPage<DeviceBackDto> page = deviceBackService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public DeviceBackDto detail(Long id) {
		// 通过ID查询数据信息
		DeviceBackDto dto = deviceBackService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			DeviceBack data = deviceBackService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
//            deleteResultDto.setName(data.getName());

			boolean b = deviceBackService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


}
