/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.leaseback.vo.DeviceBackPageVo;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.leaseback.entity.DeviceBack;
/**
 * 设备租赁归还 服务类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface IDeviceBackService extends BaseService<DeviceBack> {

    /**
    * 名称校验
    */
    void checkName(Long id, String name);

    /**
    * 分页查询
    */
    IPage<DeviceBackDto> pageList(DeviceBackPageVo v);

    /**
    * 详情
    */
    DeviceBackDto detail(Long id);

}
