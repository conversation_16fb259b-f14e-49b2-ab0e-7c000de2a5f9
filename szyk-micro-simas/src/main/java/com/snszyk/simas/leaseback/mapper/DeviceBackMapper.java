/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.mapper;

import com.snszyk.simas.leaseback.entity.DeviceBack;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import com.snszyk.simas.leaseback.vo.DeviceBackPageVo;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
/**
 * 设备租赁归还 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface DeviceBackMapper extends BaseMapper<DeviceBack> {

    IPage<DeviceBackDto> pageList(@Param("v") DeviceBackPageVo v);

    DeviceBackDto detail(Long id);

}
