///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.back.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.snszyk.core.boot.ctrl.SzykController;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.simas.back.dto.DeviceBackFileDto;
//import com.snszyk.simas.back.service.logic.DeviceBackFileLogicService;
//import com.snszyk.simas.back.vo.DeviceBackFileAVo;
//import com.snszyk.simas.back.vo.DeviceBackFilePageVo;
//import com.snszyk.simas.common.dto.CommonDeleteResultDto;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 设备归还资料 控制器
// *
// * <AUTHOR>
// * @since 2025-03-18
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("back/devicebackfile")
//@Api(value = "设备归还资料", tags = "设备归还资料接口")
//public class DeviceBackFileController extends SzykController {
//
//	private final DeviceBackFileLogicService deviceBackFileLogicService;
//
//
//	/**
//	 * 保存
//	 */
//	@PostMapping("/save")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "设备归还资料保存", notes = "DeviceBackFileVo")
//	public R<DeviceBackFileDto> save(@RequestBody DeviceBackFileAVo v) {
//		DeviceBackFileDto baseCrudDto = deviceBackFileLogicService.saveOrUpdate(v);
//		return R.data(baseCrudDto);
//	}
//
//	/**
//	 * 分页
//	 */
//	@GetMapping("/page")
//	@ApiOperationSupport(order = 2)
//	@ApiOperation(value = "设备归还资料分页", notes = "DeviceBackFilePageVo")
//	public R<IPage<DeviceBackFileDto>> page(DeviceBackFilePageVo v) {
//		IPage<DeviceBackFileDto> pageQueryResult = deviceBackFileLogicService.pageList(v);
//		return R.data(pageQueryResult);
//	}
//
//	/**
//	 * 根据ID获取数据
//	 */
//	@GetMapping("/detail")
//	@ApiOperationSupport(order = 3)
//	@ApiOperation(value = "设备归还资料详情", notes = "id")
//	public R<DeviceBackFileDto> detail(Long id) {
//		DeviceBackFileDto baseCrudDto = deviceBackFileLogicService.detail(id);
//		return R.data(baseCrudDto);
//	}
//
//	/**
//	 * 删除
//	 */
//	@PostMapping("/removeByIds")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "设备归还资料删除", notes = "id")
//	public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
//		List<CommonDeleteResultDto> result = deviceBackFileLogicService.removeByIds(ids);
//		return R.data(result);
//	}
//}
