/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.leaseback.dto.DeviceBackFileDto;
import com.snszyk.simas.leaseback.entity.DeviceBackFile;
import com.snszyk.simas.leaseback.service.IDeviceBackFileService;
import com.snszyk.simas.leaseback.vo.DeviceBackFileAVo;
import com.snszyk.simas.leaseback.vo.DeviceBackFilePageVo;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备归还资料 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceBackFileLogicService {

	private final IDeviceBackFileService deviceBackFileService;

	@Transactional
	public DeviceBackFileDto saveOrUpdate(DeviceBackFileAVo v) {
		// 名称的唯一性校验
		DeviceBackFile copy = BeanUtil.copy(v, DeviceBackFile.class);
		boolean b = deviceBackFileService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<DeviceBackFileDto> pageList(DeviceBackFilePageVo v) {
		IPage<DeviceBackFileDto> page = deviceBackFileService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public DeviceBackFileDto detail(Long id) {
		// 通过ID查询数据信息
		DeviceBackFileDto dto = deviceBackFileService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			DeviceBackFile data = deviceBackFileService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}

			boolean b = deviceBackFileService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


}
