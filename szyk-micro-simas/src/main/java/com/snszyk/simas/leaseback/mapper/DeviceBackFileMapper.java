/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tenant.annotation.TenantIgnore;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.leaseback.dto.DeviceBackFileDto;
import com.snszyk.simas.leaseback.entity.DeviceBackFile;
import com.snszyk.simas.leaseback.vo.DeviceBackFilePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备归还资料 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface DeviceBackFileMapper extends BaseMapper<DeviceBackFile> {

	IPage<DeviceBackFileDto> pageList(@Param("v") DeviceBackFilePageVo v);

	DeviceBackFileDto detail(Long id);

	@TenantIgnore
	List<Attach> listAttachByEquipmentId(@Param("equipmentId") Long equipmentId);
}
