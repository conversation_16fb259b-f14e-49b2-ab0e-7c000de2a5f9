/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.leaseback.dto.DeviceBackFileDto;
import com.snszyk.simas.leaseback.entity.DeviceBackFile;
import com.snszyk.simas.leaseback.vo.DeviceBackFilePageVo;

import java.util.List;

/**
 * 设备归还资料 服务类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface IDeviceBackFileService extends BaseService<DeviceBackFile> {

	/**
	 * 名称校验
	 */
	void checkName(Long id, String name);

	/**
	 * 分页查询
	 */
	IPage<DeviceBackFileDto> pageList(DeviceBackFilePageVo v);

	/**
	 * 详情
	 */
	DeviceBackFileDto detail(Long id);

    List<Attach> listAttachByEquipmentId(Long equipmentId);
}
