/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.leaseback.entity.DeviceBack;
import com.snszyk.simas.leaseback.vo.DeviceBackPageVo;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.leaseback.mapper.DeviceBackMapper;
import com.snszyk.simas.leaseback.service.IDeviceBackService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;
import com.snszyk.core.mp.base.BaseServiceImpl;
/**
 * 设备租赁归还 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceBackServiceImpl extends BaseServiceImpl<DeviceBackMapper, DeviceBack> implements IDeviceBackService {

    /**
     * 名称校验
     */
    @Override
    public void checkName(Long id, String name) {
//        Integer count = lambdaQuery().eq(DeviceBack::getName, name).ne(id != null, DeviceBack::getId, id).count();
//        if (count > 0) {
//            throw new ServiceException("名称已存在");
//        }
    }

    /**
     * 分页查询
     */
    @Override
    public IPage<DeviceBackDto> pageList(DeviceBackPageVo v) {
        return baseMapper.pageList(v);
    }

    /**
     * 详情
     */
    @Override
    public DeviceBackDto detail(Long id) {
        return baseMapper.detail(id);
    }

}
