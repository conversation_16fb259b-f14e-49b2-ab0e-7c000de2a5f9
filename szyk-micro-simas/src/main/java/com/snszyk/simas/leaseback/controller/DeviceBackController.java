/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.leaseback.service.logic.DeviceBackLogicService;
import com.snszyk.simas.leaseback.vo.DeviceBackAVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 设备租赁归还 控制器
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("device/deviceback")
@Api(value = "设备租赁归还", tags = "设备租赁归还接口")
public class DeviceBackController extends SzykController {

	private final DeviceBackLogicService deviceBackLogicService;


	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "设备租赁归还保存", notes = "DeviceBackVo")
	public R<DeviceBackDto> save(@RequestBody DeviceBackAVo v) {
		DeviceBackDto baseCrudDto = deviceBackLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}

}
