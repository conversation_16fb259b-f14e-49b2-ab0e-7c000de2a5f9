<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.leaseback.mapper.DeviceBackFileMapper">

        <sql id="selectData">
        select t.*,
               t1.real_name create_user_name,
               t2.real_name update_user_name
            from simas_device_back_file t
            left join szyk_user t1 on t1.id = t.create_user and t1.is_deleted = 0
            left join szyk_user t2 on t2.id = t.update_user and t2.is_deleted = 0


    </sql>
    <select id="pageList" resultType="com.snszyk.simas.leaseback.dto.DeviceBackFileDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
         order by t.create_time desc
    </select>

    <select id="detail" resultType="com.snszyk.simas.leaseback.dto.DeviceBackFileDto">
        <include refid="selectData"/>
       where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="listAttachByEquipmentId" resultType="com.snszyk.resource.entity.Attach">
        select t2.*
        from simas_device_back t
                 left join
             simas_device_back_file t1 on t1.back_id = t.id and t1.is_deleted = 0
                 left join szyk_attach t2 on t2.id = t1.attach_id and t2.is_deleted = 0
        where t2.is_deleted = 0
          and t.device_id = #{equipmentId}
    </select>

</mapper>
