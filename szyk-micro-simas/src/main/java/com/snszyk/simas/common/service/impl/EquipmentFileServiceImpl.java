/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceCategory;
import com.snszyk.common.equipment.entity.DeviceFileCategory;
import com.snszyk.common.equipment.enums.EquipmentFileGroupEnum;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.equipment.vo.DeviceCategoryVO;
import com.snszyk.common.equipment.vo.DeviceFileCategoryVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.ai.DifyClient;
import com.snszyk.simas.common.dto.EquipmentFileDTO;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.mapper.EquipmentFileMapper;
import com.snszyk.simas.common.service.IEquipmentFileService;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;
import com.snszyk.simas.common.wrapper.EquipmentFileWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备资料表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@AllArgsConstructor
@Service
@Slf4j
public class EquipmentFileServiceImpl extends BaseServiceImpl<EquipmentFileMapper, EquipmentFile>
	implements IEquipmentFileService {

	private final IAttachClient attachClient;
	private final ICommonClient iCommonClient;

	private final IDeviceAccountClient deviceAccountClient;
	private final DifyClient difyClient;

	@Override
	public IPage<EquipmentFileVO> page(IPage<EquipmentFileVO> page, EquipmentFileVO vo) {
		if (Func.isNotEmpty(vo.getCategoryId())) {
			vo.setCategoryIds(Func.toLongList(vo.getCategoryId()));
		}
		List<EquipmentFileVO> recordList = baseMapper.page(page, vo);
		if (Func.isNotEmpty(recordList)) {
			recordList.forEach(record -> {
				record.setTypeName(Optional.ofNullable(EquipmentFileGroupEnum.getByCode(record.getTypeKey()))
					.map(EquipmentFileGroupEnum::getName).orElse(""));
				record.setFileCount(0);
				// 资料数量
				if (Func.isNotEmpty(record.getAttachId())) {
					record.setFileCount(Func.toLongList(record.getAttachId()).size());
				}
				// 设备类型
				if (Func.isNotEmpty(record.getCategoryId())) {
					String categoryNames = Func.toLongList(record.getCategoryId())
						.stream()
						.map(CommonCache::getEquipmentCategory)
						.filter(Objects::nonNull)
						.map(DeviceCategory::getCategoryName)
						.filter(Objects::nonNull)
						.collect(Collectors.joining(","));
					record.setEquipmentCategory(categoryNames);
				}
				// 资料文件
				if (Func.isNotEmpty(record.getAttachId())) {
					R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(record.getAttachId()));
					if (attachListR.isSuccess()) {
						record.setAttachList(attachListR.getData());
					}
				}
			});
			return page.setRecords(recordList);
		}
		return page.setRecords(null);
	}

	@Override
	public EquipmentFileVO detail(Long id) {
		EquipmentFile file = this.getById(id);
		if (file == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		EquipmentFileVO detail = EquipmentFileWrapper.build().entityVO(file);
		// 所选设备类型
		if (Func.isNotEmpty(detail.getCategoryId())) {
			List<Long> categoryIds = Func.toLongList(detail.getCategoryId());
			detail.setCategoryIds(categoryIds);
			DeviceCategoryVO categoryVO = new DeviceCategoryVO();
			categoryVO.setIds(categoryIds);
			R<List<DeviceCategoryVO>> equipmentCategory = iCommonClient.getEquipmentCategory(categoryVO);
			detail.setCategoryList(equipmentCategory.getData());
		}
		if (ObjectUtil.isNotEmpty(detail.getType())) {
			Optional.ofNullable(iCommonClient.getEquipmentFileCategory((detail.getType())).getData())
				.ifPresent(c -> detail.setTypeName(c.getName()));
		}
		// 附件
		if (Func.isNotEmpty(detail.getAttachId())) {
			R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(detail.getAttachId()));
			if (attachListR.isSuccess()) {
				detail.setAttachList(attachListR.getData());
			}
		}
		return detail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(EquipmentFileVO vo) {
		EquipmentFile file = Objects.requireNonNull(BeanUtil.copy(vo, EquipmentFile.class));
		if (Func.isEmpty(file.getId())) {
			file.setNo(BizCodeUtil.generate("ZL"));
		}
		if (Func.isEmpty(vo.getAttachId())) {
			file.setAttachId(null);
		}
		return this.saveOrUpdate(file);
	}

	@Override
	public boolean submitPreFile(EquipmentPreFileVO preFile) {
		DeviceAccountVO data = deviceAccountClient.deviceInfoById(preFile.getEquipmentId()).getData();
		if (data == null) {
			throw new ServiceException("设备台账不存在！");
		}
		// 去除通用资料,过滤重复提交附件
		List<EquipmentFile> fileList = preFile.getFileList().stream().filter(file -> Func.isEmpty(file.getCategoryId()))
			.collect(Collectors.collectingAndThen(
				Collectors.toMap(EquipmentFile::getAttachId, file -> file, (k1, k2) -> k2),
				x -> new ArrayList<>(x.values())));
		LambdaQueryWrapper<EquipmentFile> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(EquipmentFile::getEquipmentId, preFile.getEquipmentId());
		queryWrapper.isNull(EquipmentFile::getCategoryId);
		if (Func.isNotEmpty(preFile.getModule())) {
			String code = preFile.getModule().getCode();
			DeviceFileCategoryVO categoryVO = new DeviceFileCategoryVO();
			categoryVO.setDictKey(code);
			categoryVO.setTenantId(AuthUtil.getTenantId());
			categoryVO.setParentId(0L);
			R<List<DeviceFileCategoryVO>> equipmentFileCategory = iCommonClient.getEquipmentFileCategory(categoryVO);
			List<DeviceFileCategoryVO> categoryData = equipmentFileCategory.getData();
			if (Func.isNotEmpty(categoryData)) {
				DeviceFileCategoryVO categoryVO1 = categoryData.get(0);
				queryWrapper.eq(EquipmentFile::getType, categoryVO1.getId());
			}
		}

		// 查询该设备下是否存在前期资料
		List<EquipmentFile> entityList = this.list(queryWrapper);

		if (Func.isEmpty(fileList)) {
			this.remove(queryWrapper);
		} else {
			if (Func.isNotEmpty(fileList)) {
				if (Func.isNotEmpty(entityList)) {
					// 获取id集合
					List<Long> fileIds = entityList.stream().map(EquipmentFile::getId).collect(Collectors.toList());
					// 获取fileVOList中id集合（过滤掉null的情况）
					List<Long> preFileIds = fileList.stream().map(EquipmentFile::getId)
						.filter(id -> Func.isNotEmpty(id)).collect(Collectors.toList());
					// 过滤出fileIds中需要删除的id集合
					List<Long> deleteIds = fileIds.stream().filter(id -> !preFileIds.contains(id))
						.collect(Collectors.toList());
					if (Func.isNotEmpty(deleteIds)) {
						// 数据同步到dify
						syncRemoveDify(deleteIds);
						this.removeByIds(deleteIds);

					}
				}
				// 保存新增的
				List<EquipmentFile> saveList = fileList.stream().filter(file -> Func.isEmpty(file.getId()))
					.map(file -> {
						EquipmentFile saveFile = Objects.requireNonNull(BeanUtil.copy(file, EquipmentFile.class));
						saveFile.setEquipmentId(preFile.getEquipmentId());
						saveFile.setNo(BizCodeUtil.generate("ZL"));
						saveFile.setCreateUser(AuthUtil.getUserId());
						return saveFile;
					}).collect(Collectors.toList());
				boolean res = false;
				// 保存更新的
				List<EquipmentFile> updateList = fileList.stream().filter(file -> Func.isNotEmpty(file.getId()))
					.collect(Collectors.toList());
				if (Func.isNotEmpty(updateList)) {
					res = this.updateBatchById(updateList);
				}
				if (Func.isNotEmpty(saveList)) {
					String tenantId = AuthUtil.getTenantId();
					saveList.forEach(e -> e.setTenantId(tenantId));
					res = this.saveBatch(saveList);
					try {
						// 数据同步到dify
						log.info("开始同步文件到Dify平台，文件数量: {}", saveList.size());
						syncDify(saveList);
					} catch (IOException e) {
						log.error("同步文件到Dify平台失败", e);
						throw new RuntimeException(e);
					}
				}
				return res;
			}
		}
		return true;
	}

	/**
	 * @return void
	 * <AUTHOR>
	 * @Description dify的数据的同步删除
	 * @Date 下午1:57 2025/4/8
	 * @Param [deleteIds]
	 **/
	@Override
	public void syncRemoveDify(List<Long> deleteIds) {
		log.info("开始从Dify平台删除文档，文档数量: {}", deleteIds.size());
		for (Long id : deleteIds) {
			log.info("同步删除dify中的文档:{}", id);
			EquipmentFile file = baseMapper.getByIdIgnoreDel(id);
			if (file != null) {
				String attachId = file.getAttachId();
				String result = difyClient.deleteDocument(String.valueOf(attachId));
				log.info("删除Dify文档结果: {}", result);
			}
		}
	}

	/**
	 * @return void
	 * <AUTHOR>
	 * @Description
	 * @Date 上午11:53 2025/4/8
	 * @Param [saveList]
	 **/
	@Async
	public void syncDify(List<EquipmentFile> saveList) throws IOException {
		for (EquipmentFile e : saveList) {
			Long fileType = e.getType();
			DeviceFileCategory equipmentFileCategory = CommonCache.getEquipmentFileCategory(fileType);
			// 资料的分类
			if (equipmentFileCategory == null) {
				continue;
			}
			// 只要前期资料的
			String name = equipmentFileCategory.getName();
			if (!name.contains("设备前期资料")) {
				continue;
			}
			String attachId = e.getAttachId();
			R<Attach> r = attachClient.attachInfoById(Long.valueOf(attachId));
			if (r.isSuccess()) {
				Attach attach = r.getData();
				String link = attach.getLink();
				Long id = attach.getId();
				difyClient.uploadFile(String.valueOf(id), String.valueOf(e.getEquipmentId()), e.getTenantId(),
					link, attach.getOriginalName());
			}
		}

	}

	@Override
	public List<EquipmentFileVO> preFileList(EquipmentFileVO equipmentFile) {
		if (Func.isNotEmpty(equipmentFile.getEquipmentId())) {
			List<EquipmentFile> list = this.list(Wrappers.<EquipmentFile>query().lambda()
				.eq(EquipmentFile::getEquipmentId, equipmentFile.getEquipmentId())
				.eq(EquipmentFile::getType, equipmentFile.getModule().getCode()));
			if (Func.isNotEmpty(list)) {
				return EquipmentFileWrapper.build().listVO(list);
			}
		}
		return null;
	}

	@Override
	public List<EquipmentFileDTO> listByEquipmentId(Long equipmentId) {
		List<EquipmentFileDTO> equipmentFileDTOS = baseMapper.listByEquipmentId(equipmentId);
		equipmentFileDTOS.forEach(equipmentFileDTO -> {
			equipmentFileDTO.setFileCategoryName(
				Optional.ofNullable(CommonCache.getEquipmentFileCategory(equipmentFileDTO.getFileCategoryId()))
					.map(DeviceFileCategory::getName).orElseGet(() -> ""));
			equipmentFileDTO
				.setTypeName(Optional.ofNullable(EquipmentFileGroupEnum.getByCode(equipmentFileDTO.getTypeKey()))
					.map(EquipmentFileGroupEnum::getName).orElseGet(() -> ""));
		});
		return equipmentFileDTOS;

	}

	@Override
	public boolean removeByEquipmentIds(List<Long> ids) {
		return this.remove(Wrappers.<EquipmentFile>query().lambda().in(EquipmentFile::getEquipmentId, ids));
	}

	@Override
	public List<EquipmentFileVO> list(EquipmentFileVO equipmentFile) {
		List<EquipmentFileVO> list = new ArrayList<>();
		if (Func.isNotEmpty(equipmentFile.getEquipmentId())) {
			List<EquipmentFile> preFileList = this.list(Wrappers.<EquipmentFile>query().lambda()
				.eq(EquipmentFile::getEquipmentId, equipmentFile.getEquipmentId()));
			if (Func.isNotEmpty(preFileList)) {
				list.addAll(EquipmentFileWrapper.build().listVO(preFileList));
			}
		}
		if (Func.isNotEmpty(equipmentFile.getCategoryId())) {
			List<EquipmentFile> commonFileList = this.list(Wrappers.<EquipmentFile>query().lambda()
				.like(EquipmentFile::getCategoryId, equipmentFile.getCategoryId()));
			if (Func.isNotEmpty(commonFileList)) {
				list.addAll(EquipmentFileWrapper.build().listVO(commonFileList));
			}
		}
		if (Func.isNotEmpty(list)) {
			// 根据创建时间倒序
			list.sort(Comparator.comparing(EquipmentFile::getCreateTime).reversed());
			list.forEach(attachVO -> {
				if (Func.isEmpty(attachVO.getEquipmentId())) {
					// 通用资料
					attachVO.setModule(null);
				} else {
					// 设备资料
					DeviceFileCategory category = iCommonClient.getEquipmentFileCategory(attachVO.getType()).getData();
					Optional.ofNullable(category).ifPresent(
						c -> attachVO.setModule(EquipmentFileGroupEnum.getByCode(category.getDictKey())));
				}
				R<Attach> r = attachClient.attachInfoById(Func.toLong(attachVO.getAttachId()));
				if (r.isSuccess()) {
					attachVO.setAttach(r.getData());
				}
			});
		}
		return list;
	}

	@Override
	public int countByEquipmentId(Long equipmentId, String categoryCode) {
		return baseMapper.countPreByEquipmentId(equipmentId, categoryCode);
	}

	/**
	 * @return boolean
	 * <AUTHOR>
	 * @Description 上传前期资料
	 * @Date 上午10:02 2025/4/10
	 * @Param [preFile]
	 **/
	@Override
	public boolean aiToolsAddPreFile(EquipmentPreFileVO preFile) {
		// 默认是设备前期资料
		EquipmentFileGroupEnum module = preFile.getModule();
		if (module == null) {
			module = EquipmentFileGroupEnum.PRE;
		}
		R<DeviceFileCategory> preFileCategory = iCommonClient.getPreFileCategory(AuthUtil.getTenantId());
		if (!preFileCategory.isSuccess()) {
			throw new ServiceException("请先维护资料的分类");
		}
		DeviceFileCategory data = preFileCategory.getData();
		if (Func.isEmpty(data)) {
			throw new ServiceException("请先维护资料的分类");
		}
		List<EquipmentFile> fileList = preFile.getFileList();
		// 保存新增的
		List<EquipmentFile> saveList = fileList.stream().filter(file -> Func.isEmpty(file.getId())).map(file -> {
			EquipmentFile saveFile = Objects.requireNonNull(BeanUtil.copy(file, EquipmentFile.class));
			saveFile.setEquipmentId(preFile.getEquipmentId());
			saveFile.setNo(BizCodeUtil.generate("ZL"));
			saveFile.setCreateUser(AuthUtil.getUserId());
			saveFile.setType(data.getId());
			return saveFile;
		}).collect(Collectors.toList());
		if (Func.isNotEmpty(saveList)) {
			String tenantId = AuthUtil.getTenantId();
			saveList.forEach(e -> e.setTenantId(tenantId));
			this.saveBatch(saveList);
			try {
				// 数据同步到dify
				log.info("AI工具上传前期资料，开始同步到Dify平台，文件数量: {}", saveList.size());
				syncDify(saveList);
			} catch (IOException e) {
				log.error("AI工具同步文件到Dify平台失败", e);
				throw new RuntimeException(e);
			}
		}
		return true;
	}

	/**
	 * @return boolean
	 * <AUTHOR> @Description AI运维标准删除前期资料
	 * @Date 上午10:02 2025/4/10
	 * @Param [fileId]
	 **/
	@Override
	public boolean aiToolsRemovePreFile(Long fileId) {
		if (fileId == null) {
			return false;
		}
		// 查询文件是否存在
		EquipmentFile file = this.getById(fileId);
		if (file == null) {
			return false;
		}
		// 删除文件
		boolean result = this.removeById(fileId);
		if (result) {
			// 数据同步到dify
			List<Long> deleteIds = Collections.singletonList(fileId);
			syncRemoveDify(deleteIds);
		}
		return result;
	}
}
