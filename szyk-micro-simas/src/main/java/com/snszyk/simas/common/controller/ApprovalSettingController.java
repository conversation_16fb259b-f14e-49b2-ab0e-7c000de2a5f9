/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.service.logic.ApprovalSettingLogicService;
import com.snszyk.simas.common.dto.ApprovalSettingDto;
import com.snszyk.simas.common.vo.ApprovalSettingAVo;
import com.snszyk.simas.common.vo.ApprovalSettingPageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工单审核配置 控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("approval_setting/approvalsetting")
@Api(value = "工单审核配置", tags = "工单审核配置接口")
public class ApprovalSettingController extends SzykController {

	private final ApprovalSettingLogicService approvalSettingLogicService;


	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "工单审核配置保存", notes = "ApprovalSettingVo")
	public R<ApprovalSettingDto> save(@RequestBody ApprovalSettingAVo v) {
		ApprovalSettingDto baseCrudDto = approvalSettingLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "工单审核配置list", notes = "ApprovalSettingPageVo")
	public R<List<ApprovalSettingDto>> page(ApprovalSettingPageVo v) {
		List<ApprovalSettingDto> pageQueryResult = approvalSettingLogicService.pageList(v);
		return R.data(pageQueryResult);
	}


}
