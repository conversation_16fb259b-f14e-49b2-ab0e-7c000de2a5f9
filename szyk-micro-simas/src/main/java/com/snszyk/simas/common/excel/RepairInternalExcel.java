/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * RepairInternalExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class RepairInternalExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("工单编号")
	private String no;

	@ExcelProperty("维修方式")
	private String bizTypeName;

	@ExcelProperty("工单来源")
	private String sourceName;

	@ExcelProperty("报修类型")
	private String repairTypeName;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("设备型号")
	private String equipmentModel;

	@ExcelProperty("部位名称")
	private String monitorName;

	@ExcelProperty("报修人")
	private String reportUserName;

	@ExcelProperty("维修人")
	private String receiveUserName;

	@ExcelProperty("联系电话")
	private String tel;

	@ExcelProperty("报修时间")
	private String reportTimeStr;

	@ExcelProperty("预计完成时间")
	private String completeTimeStr;

	@ExcelProperty("处理时间")
	private String handleTimeStr;

	@ExcelProperty("工单状态")
	private String statusName;


}
