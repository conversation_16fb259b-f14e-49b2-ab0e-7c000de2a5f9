<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.DeviceMoveRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deviceMoveRecordResultMap" type="com.snszyk.simas.common.entity.DeviceMoveRecord">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="source" property="source"/>
        <result column="biz_no" property="bizNo"/>
        <result column="device_id" property="deviceId"/>
        <result column="use_dept" property="useDept"/>
        <result column="user_id" property="userId"/>
        <result column="original_location" property="originalLocation"/>
        <result column="new_location" property="newLocation"/>
        <result column="move_time" property="moveTime"/>
        <result column="remark" property="remark"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_time" property="operateTime"/>
    </resultMap>

    <resultMap id="deviceMoveRecordVOResultMap" type="com.snszyk.simas.common.vo.DeviceMoveRecordVO">
        <id column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="source" property="source"/>
        <result column="biz_no" property="bizNo"/>
        <result column="device_id" property="deviceId"/>
        <result column="use_dept" property="useDept"/>
        <result column="user_id" property="userId"/>
        <result column="original_location" property="originalLocation"/>
        <result column="new_location" property="newLocation"/>
        <result column="move_time" property="moveTime"/>
        <result column="remark" property="remark"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_time" property="operateTime"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_model" property="deviceModel"/>
        <result column="original_location_name" property="originalLocationName"/>
        <result column="new_location_name" property="newLocationName"/>
    </resultMap>


    <select id="page" resultMap="deviceMoveRecordVOResultMap">
        SELECT
            r.*,
            d.`code` AS device_code,
            d.`name` AS device_name,
            d.model AS device_model,
            l1.`name` AS original_location_name,
            l2.`name` AS new_location_name
        FROM
            simas_device_move_record r
        LEFT JOIN device_account d ON d.id = r.device_id
        LEFT JOIN device_location l1 ON l1.id = r.original_location
        LEFT JOIN device_location l2 ON l2.id = r.new_location
        where d.is_deleted = 0 and l1.is_deleted = 0 and l2.is_deleted = 0 and d.tenant_id = #{deviceMoveRecord.tenantId}
        <if test="deviceMoveRecord.deviceCode != null and deviceMoveRecord.deviceCode != ''">
            and d.`code` like concat('%',#{deviceMoveRecord.deviceCode},'%')
        </if>
        <if test="deviceMoveRecord.deviceName != null and deviceMoveRecord.deviceName != ''">
            and d.`name` like concat('%',#{deviceMoveRecord.deviceName},'%')
        </if>
        <if test="deviceMoveRecord.originalLocationName != null and deviceMoveRecord.originalLocationName != ''">
            and l1.`name` like concat('%',#{deviceMoveRecord.originalLocationName},'%')
        </if>
        <if test="deviceMoveRecord.newLocationName != null and deviceMoveRecord.newLocationName != ''">
            and l2.`name` like concat('%',#{deviceMoveRecord.newLocationName},'%')
        </if>
        order by move_time desc
    </select>


</mapper>
