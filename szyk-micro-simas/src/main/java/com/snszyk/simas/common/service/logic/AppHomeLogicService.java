 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.common.service.logic;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.google.common.collect.Lists;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.crud.exception.BusinessException;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.secure.SzykUser;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.*;
 import com.snszyk.message.entity.Message;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.message.enums.MessageTypeEnum;
 import com.snszyk.message.feign.IMessageClient;
 import com.snszyk.simas.common.dto.PendingCountDto;
 import com.snszyk.simas.common.dto.StatisticsDTO;
 import com.snszyk.simas.common.dto.WorkbenchDataDTO;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.util.ApprovalUtil;
 import com.snszyk.simas.common.vo.ByDaySetVO;
 import com.snszyk.simas.fault.entity.FaultDefect;
 import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
 import com.snszyk.simas.fault.enums.FaultSourceEnum;
 import com.snszyk.simas.fault.service.IFaultDefectService;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import com.snszyk.simas.inspect.dto.InspectOrderDTO;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.inspect.entity.InspectOrder;
 import com.snszyk.simas.inspect.entity.InspectPlan;
 import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
 import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
 import com.snszyk.simas.inspect.service.IInspectOrderService;
 import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
 import com.snszyk.simas.inspect.service.IInspectPlanService;
 import com.snszyk.simas.inspect.vo.InspectOrderVO;
 import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
 import com.snszyk.simas.lubricate.entity.LubricateOrder;
 import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
 import com.snszyk.simas.lubricate.service.ILubricateOrderService;
 import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
 import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.entity.MaintainOrder;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
 import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
 import com.snszyk.simas.maintain.service.IMaintainOrderService;
 import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
 import com.snszyk.simas.maintain.service.IMaintainPlanService;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;
 import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
 import com.snszyk.simas.overhaul.dto.RepairDTO;
 import com.snszyk.simas.overhaul.entity.OverhaulOrder;
 import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
 import com.snszyk.simas.overhaul.mapper.OverhaulOrderMapper;
 import com.snszyk.simas.overhaul.mapper.RepairMapper;
 import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
 import com.snszyk.simas.overhaul.service.IRepairService;
 import com.snszyk.simas.overhaul.vo.OverhaulOrderUrgeVO;
 import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
 import com.snszyk.simas.overhaul.vo.RepairVO;
 import com.snszyk.simas.spare.schedule.SimasPlanSchedule;
 import com.snszyk.simas.inventory.service.IEquipmentInventoryOrderService;
 import com.snszyk.simas.spare.service.ISparePartsInventoryOrderService;
 import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
 import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
 import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
 import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
 import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderStatusEnum;
 import com.snszyk.simas.spare.enums.SpareInventoryOrderStatusEnum;
 import com.snszyk.simas.common.dto.InventoryStatisticsDTO;
 import com.snszyk.user.entity.UserDept;
 import com.snszyk.user.feign.IUserClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;
 import org.springframework.web.context.request.RequestContextHolder;
 import org.springframework.web.context.request.ServletRequestAttributes;

 import javax.servlet.http.HttpServletRequest;
 import java.util.*;
 import java.util.concurrent.CompletableFuture;
 import java.util.stream.Collectors;

 /**
  * 移动端数据 逻辑服务实现类
  *
  * <AUTHOR>
  * @date 2024/09/15 13:39
  **/
 @Slf4j
 @Service
 @AllArgsConstructor
 public class AppHomeLogicService {

	 private final IInspectOrderService inspectOrderService;
	 private final InspectOrderMapper inspectOrderMapper;
	 private final IMaintainOrderService maintainOrderService;
	 private final MaintainOrderMapper maintainOrderMapper;
	 private final IRepairService repairService;
	 private final RepairMapper repairMapper;
	 private final IFaultDefectService faultDefectService;
	 private final IInspectPlanService inspectPlanService;
	 private final IMaintainPlanService maintainPlanService;
	 private final IInspectPlanEquipmentService planEquipmentService;
	 private final IMaintainPlanEquipmentService maintainPlanEquipmentService;
	 private final ILubricateOrderService lubricateOrderService;
	 private final LubricateOrderMapper lubricateOrderMapper;
	 private final IOverhaulOrderService overhaulOrderService;
	 private final OverhaulOrderMapper overhaulOrderMapper;
	 private final GeneralLogicService generalLogicService;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final IUserClient userClient;
	 private final IMessageClient messageClient;
	 private final IEquipmentInventoryOrderService equipmentInventoryOrderService;
	 private final ISparePartsInventoryOrderService sparePartsInventoryOrderService;
	 private static final String[] WEEK = {"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};


	 /**
	  * APP工作台数据
	  *
	  * @return com.snszyk.simas.vo.WorkbenchDataVO
	  * <AUTHOR>
	  * @date 2024/8/20 11:39
	  */
	 public WorkbenchDataDTO workbench() {
		 WorkbenchDataDTO dto = new WorkbenchDataDTO();
		 // 点检
		 InspectOrderVO inspectOrderVO = new InspectOrderVO();
		 inspectOrderVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<InspectOrder> inspectOrderList = inspectOrderMapper.page(new Page<>(1, 9999), inspectOrderVO);
		 int inProcessOrderCount = inspectOrderList.size();
		 inspectOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 inspectOrderList = inspectOrderMapper.page(new Page<>(1, 9999), inspectOrderVO);
		 int isOverdueOrderCount = inspectOrderList.size();
		 int expireSoonOrderCount = inspectOrderService.expireSoonCount();
		 dto.setInspectData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));
		 // 保养
		 MaintainOrderVO maintainOrderVO = new MaintainOrderVO();
		 maintainOrderVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<MaintainOrder> maintainOrderList = maintainOrderMapper.page(new Page<>(1, 9999), maintainOrderVO);
		 inProcessOrderCount = maintainOrderList.size();
		 inspectOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 maintainOrderList = maintainOrderMapper.page(new Page<>(1, 9999), maintainOrderVO);
		 isOverdueOrderCount = maintainOrderList.size();
		 expireSoonOrderCount = maintainOrderService.expireSoonCount();
		 dto.setMaintainData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));
		 // 内部维修
		 RepairVO repairVO = new RepairVO();
		 repairVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<RepairDTO> internalRepairList = repairMapper.internalPage(new Page<>(1, 9999), repairVO);
		 inProcessOrderCount = internalRepairList.size();
		 repairVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 internalRepairList = repairMapper.internalPage(new Page<>(1, 9999), repairVO);
		 isOverdueOrderCount = internalRepairList.size();
		 expireSoonOrderCount = repairService.expireSoonCount();
		 dto.setRepairInternalData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));
		 // 外委维修
		 repairVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<RepairDTO> externalRepairList = repairMapper.externalPage(new Page<>(1, 9999), repairVO);
		 inProcessOrderCount = externalRepairList.size();
		 repairVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 externalRepairList = repairMapper.externalPage(new Page<>(1, 9999), repairVO);
		 isOverdueOrderCount = externalRepairList.size();
		 expireSoonOrderCount = repairService.expireSoonCount();
		 dto.setRepairExternalData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));
		 // 润滑
		 LubricateOrderVO lubricateOrderVO = new LubricateOrderVO();
		 lubricateOrderVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<LubricateOrder> lubricateOrderList = lubricateOrderMapper.page(new Page<>(1, 9999), lubricateOrderVO);
		 inProcessOrderCount = lubricateOrderList.size();
		 lubricateOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 lubricateOrderList = lubricateOrderMapper.page(new Page<>(1, 9999), lubricateOrderVO);
		 isOverdueOrderCount = lubricateOrderList.size();
		 expireSoonOrderCount = lubricateOrderService.expireSoonCount();
		 dto.setLubricateData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));
		 // 检修
		 OverhaulOrderVO overhaulOrderVO = new OverhaulOrderVO();
		 overhaulOrderVO.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		 List<OverhaulOrder> overhaulOrderList = overhaulOrderMapper.page(new Page<>(1, 9999), overhaulOrderVO);
		 inProcessOrderCount = overhaulOrderList.size();
		 overhaulOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
		 overhaulOrderList = overhaulOrderMapper.page(new Page<>(1, 9999), overhaulOrderVO);
		 isOverdueOrderCount = overhaulOrderList.size();
		 expireSoonOrderCount = overhaulOrderService.expireSoonCount();
		 dto.setOverhaulData(new StatisticsDTO(inProcessOrderCount, isOverdueOrderCount, expireSoonOrderCount));

		 // 设备盘点工单统计
		 InventoryStatisticsDTO equipmentInventoryStats = getEquipmentInventoryStatistics();
		 dto.setEquipmentInventoryData(equipmentInventoryStats);

		 // 备品备件盘点工单统计
		 InventoryStatisticsDTO sparePartsInventoryStats = getSparePartsInventoryStatistics();
		 dto.setSparePartsInventoryData(sparePartsInventoryStats);

		 return dto;
	 }

	 /**
	  * 获取检修工单数据
	  *
	  * @param szykUser
	  * @return
	  */
	 private StatisticsDTO getOverhaulStatisticsDTO(SzykUser szykUser, List<Long> equipmentIds) {
		 StatisticsDTO statisticsDTO = new StatisticsDTO(0, 0, 0);
		 // 检修单
		 List<OverhaulOrder> overhaulOrderList = overhaulOrderService.list(Wrappers.lambdaQuery(OverhaulOrder.class)
				 .ne(OverhaulOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				 .eq(OverhaulOrder::getExecuteDept, Func.toLongList(szykUser.getDeptId()).get(0))
				 .in(OverhaulOrder::getEquipmentId, equipmentIds)
			 //			.and(wrapper -> {
			 //				wrapper.isNull(OverhaulOrder::getExecuteUser)
			 //					.or()
			 //					.eq(OverhaulOrder::getExecuteUser, szykUser.getUserId());
			 //			})
		 );
		 if (ObjectUtil.isEmpty(overhaulOrderList)) {
			 return statisticsDTO;
		 }
		 // 查询执行中数量
		 Long inProcess = overhaulOrderList.stream()
			 .filter(overhaulOrder -> OrderStatusEnum.IN_PROCESS.getCode().equals(overhaulOrder.getStatus()))
			 .count();
		 // 查询逾期的
		 Long isOverdue = overhaulOrderList.stream()
			 .filter(overhaulOrder -> OrderStatusEnum.IS_OVERDUE.getCode().equals(overhaulOrder.getStatus()))
			 .count();
		 // 查询即将超期的
		 Integer expireSoonOrderCount = overhaulOrderService.expireSoonCount();
		 statisticsDTO.setInProcessOrderCount(inProcess.intValue());
		 statisticsDTO.setIsOverdueOrderCount(isOverdue.intValue());
		 statisticsDTO.setExpireSoonOrderCount(expireSoonOrderCount);
		 return statisticsDTO;
	 }

	 /**
	  * 获取润滑单统计dto
	  *
	  * @param szykUser
	  * @return
	  */
	 private StatisticsDTO getLubricateStatisticsDTO(SzykUser szykUser, List<Long> equipmentIds) {
		 StatisticsDTO statisticsDTO = new StatisticsDTO(0, 0, 0);
		 // 润滑单
		 List<LubricateOrder> lubricateOrderList = lubricateOrderService.list(Wrappers.lambdaQuery(LubricateOrder.class)
				 .ne(LubricateOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				 //			.eq(LubricateOrder::getChargeDept, Func.toLongList(szykUser.getDeptId()).get(0))
				 .isNull(LubricateOrder::getUnscheduled)
				 .in(LubricateOrder::getEquipmentId, equipmentIds)
			 //			.and(wrapper -> {
			 //				wrapper.isNull(LubricateOrder::getExecuteUser).or().eq(LubricateOrder::getExecuteUser, szykUser.getUserId());
			 //			})
		 );
		 if (ObjectUtil.isEmpty(lubricateOrderList)) {
			 return statisticsDTO;
		 }
		 // 查询执行中数量
		 Long inProcess = lubricateOrderList.stream()
			 .filter(lubricateOrder -> OrderStatusEnum.IN_PROCESS.getCode().equals(lubricateOrder.getStatus()))
			 .count();
		 // 查询逾期的
		 Long isOverdue = lubricateOrderList.stream()
			 .filter(lubricateOrder -> OrderStatusEnum.IS_OVERDUE.getCode().equals(lubricateOrder.getStatus()))
			 .count();
		 // 查询即将超期的
		 Integer expireSoonOrderCount = lubricateOrderService.expireSoonCount();
		 statisticsDTO.setInProcessOrderCount(inProcess.intValue());
		 statisticsDTO.setIsOverdueOrderCount(isOverdue.intValue());
		 statisticsDTO.setExpireSoonOrderCount(expireSoonOrderCount);
		 return statisticsDTO;
	 }

	 /**
	  * 异常上报
	  *
	  * @param vo
	  * @return boolean
	  * <AUTHOR>
	  * @date 2024/9/15 15:16
	  */
	 public boolean reportAbnormal(FaultDefectVO vo) {
		 vo.setReportUser(AuthUtil.getUserId());
		 return faultDefectService.reportAbnormal(vo);
	 }

	 /**
	  * 月检
	  *
	  * @param executeDateStr   计划执行开始日期 2024-09-19
	  * @param disableDateStr   计划执行终止日期 2024-10-24
	  * @param cycleInterval    间隔
	  * @param cycleExecuteDate 选择的每月几号 1,2
	  * @return
	  */
	 public List<Long> month(String executeDateStr, String disableDateStr, Integer cycleInterval, String cycleExecuteDate) {
		 Long executeDate = DateUtil.parse(executeDateStr, DateUtil.PATTERN_DATE).getTime();
		 Long disableDate = DateUtil.parse(disableDateStr, DateUtil.PATTERN_DATE).getTime();
		 List<Long> list = new ArrayList<>();
		 try {
			 // 超过计划执行结束时间，跳出
			 String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, disableDate);
			 Date date = DateUtil.parse(dateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			 if (DateUtil.now().after(date)) {
				 return list;
			 }
		 } catch (ServiceException e) {
			 e.printStackTrace();
		 }
		 // 计划选择的周几
		 int cycleWeekDay = 0;
		 for (int i = 0; i < WEEK.length; i++) {
			 if (WEEK[i].equals(cycleExecuteDate)) {
				 cycleWeekDay = i + 1;
				 break;
			 }
		 }
		 Calendar cd = Calendar.getInstance();
		 // 获得今天是星期几，星期日是第一天，星期一是第二天......
		 int todayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		 // 获得开始执行日期是星期几，星期日是第一天，星期一是第二天......
		 cd.setTime(new Date(executeDate));
		 int executeDayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		 String timeStr1 = DateUtil.format(new Date(executeDate), DateUtil.PATTERN_DATE);
		 String timeStr2 = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
		 if (Func.equals(timeStr1, timeStr2) && Func.equals(todayOfWeek, cycleWeekDay)) {
			 list.add(cd.getTime().getTime());
			 return list;
		 }
		 // 判断执行日期以后得日期
		 if (cycleInterval == 0) {
			 if (Func.equals(todayOfWeek, cycleWeekDay)) {
				 list.add(cd.getTime().getTime());
				 return list;
			 }
		 } else {
			 Calendar executeCalendar = Calendar.getInstance();
			 executeCalendar.setTime(new Date(executeDate));
			 // 计算距离下周一还有几天，8 表示从周日开始计数
			 int daysUntilNextMonday = 8 - executeDayOfWeek;
			 // 如果当前日期已经是周一，则下周一为7天后
			 if (executeDayOfWeek == Calendar.MONDAY) {
				 daysUntilNextMonday = 7 * cycleInterval * 2;
			 }
			 // 创建一个新的Calendar实例并加上天数
			 Calendar nextMonday = (Calendar) executeCalendar.clone();
			 nextMonday.add(Calendar.DAY_OF_MONTH, daysUntilNextMonday);
			 // 确保下周一是一周的第一天
			 while (nextMonday.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
				 nextMonday.add(Calendar.DAY_OF_MONTH, cycleInterval * 2);
			 }
			 long time1 = nextMonday.getTime().getTime();
			 long time2 = DateUtil.now().getTime();
			 if (time2 >= time1 && Func.equals(todayOfWeek, cycleWeekDay)) {
				 list.add(cd.getTime().getTime());
				 return list;
			 }
		 }
		 return list;
	 }

	 public List<InspectPlanDTO> inspectSchedule() {
		 log.info("################日检、周检、月检任务生成-START-################");
		 List<InspectPlanDTO> planList = inspectPlanService.getTheDayPlans(DateUtil.now());
		 List<InspectPlanDTO> executePlanList = new ArrayList<>();
		 if (Func.isNotEmpty(planList)) {
			 for (InspectPlanDTO plan : planList) {
				 // 生成点检任务时间判断
				 String cycleType = plan.getCycleType();
				 Integer cycleInterval = plan.getCycleInterval();
				 String cycleExecuteTimeStr = plan.getExecuteTime();
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchDay(plan.getStartDate().getTime(),
						 plan.getEndDate().getTime(), cycleInterval);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
				 if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchWeek(plan.getStartDate().getTime(),
						 plan.getEndDate().getTime(), cycleInterval, cycleExecuteTimeStr);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
				 if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchMonth(plan.getStartDate().getTime(), plan.getEndDate().getTime(),
						 cycleInterval, cycleExecuteTimeStr);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
			 }
			 if (Func.isNotEmpty(executePlanList)) {
				 this.generateInspectOrders(executePlanList);
			 }
		 }
		 log.info("################日检、周检、月检任务生成-END-################");
		 return executePlanList;
	 }

	 /**
	  * 生成点巡检工单
	  *
	  * @param planList
	  * @return void
	  * <AUTHOR>
	  * @date 2024/8/19 13:51
	  */
	 private void generateInspectOrders(List<InspectPlanDTO> planList) {
		 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.INSPECT_ORDER.name());
		 // 格式化当前时间
		 List<InspectOrder> orderList = new ArrayList<>();
		 for (InspectPlan plan : planList) {
			 List<InspectPlanEquipment> planEquipmentList
				 = planEquipmentService.list(Wrappers.<InspectPlanEquipment>query().lambda()
				 .eq(InspectPlanEquipment::getPlanId, plan.getId()).orderByAsc(InspectPlanEquipment::getSort));
			 planEquipmentList.forEach(planEquipment -> {
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
					 List<ByDaySetVO> list = JSONUtil.toList(plan.getExecuteTime(), ByDaySetVO.class);
					 for (ByDaySetVO daySet : list) {
						 InspectOrderVO inspectOrder = new InspectOrderVO(plan, planEquipment.getEquipmentId(),
							 plan.getExecuteDept(), plan.getExecuteUser());
						 R<DeviceAccountVO> deviceAccountR
							 = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
						 inspectOrder.setEquipmentCode(deviceAccountR.getData().getCode());
						 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE)
							 + " " + daySet.getStartTime() + ":00";
						 String endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE)
							 + " " + daySet.getEndTime() + ":00";
						 inspectOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
						 inspectOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
						 orderList.add(Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrder.class)));
					 }
				 } else {
					 InspectOrderVO inspectOrder = new InspectOrderVO(plan, planEquipment.getEquipmentId(),
						 plan.getExecuteDept(), plan.getExecuteUser());
					 R<DeviceAccountVO> deviceAccountR
						 = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
					 inspectOrder.setEquipmentCode(deviceAccountR.getData().getCode());
					 inspectOrder.setPlanInfo(JSONUtil.toJsonStr(plan));
					 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
					 String endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 23:59:59";
					 inspectOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
					 inspectOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
					 orderList.add(Objects.requireNonNull(BeanUtil.copy(inspectOrder, InspectOrder.class)));
				 }
			 });
			 // 更新计划状态（未开始 —> 执行中）
			 if (PlanStatusEnum.NO_START == PlanStatusEnum.getByCode(plan.getStatus())) {
				 inspectPlanService.update(Wrappers.<InspectPlan>update().lambda()
					 .set(InspectPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode())
					 .set(InspectPlan::getUpdateTime, DateUtil.now()).eq(InspectPlan::getId, plan.getId()));
			 }
			 InspectPlan p = inspectPlanService.getById(plan.getId());
			 // 更新计划状态（执行中 —> 已完成）
			 if (PlanStatusEnum.IN_PROGRESS == PlanStatusEnum.getByCode(p.getStatus())
				 && Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
				 DateUtil.format(plan.getEndDate(), DateUtil.PATTERN_DATE))) {
				 inspectPlanService.update(Wrappers.<InspectPlan>update().lambda()
					 .set(InspectPlan::getStatus, PlanStatusEnum.IS_COMPLETED.getCode())
					 .set(InspectPlan::getUpdateTime, DateUtil.now()).eq(InspectPlan::getId, plan.getId()));
			 }
		 }
		 if (Func.isNotEmpty(orderList)) {
			 orderList.forEach(e -> e.setIsNeedApproval(needApproval));
			 inspectOrderService.saveBatch(orderList);
			 orderList.forEach(order -> {
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER,
					 JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
			 });

			 // 发送消息提醒
			 inspectOrderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_INSPECT_ADD);
		 }
	 }

	 public List<MaintainPlanDTO> maintainSchedule() {
		 log.info("################日检、周检、月检任务生成-START-################");
		 List<MaintainPlanDTO> planList = maintainPlanService.getTheDayPlans(DateUtil.now());
		 List<MaintainPlanDTO> executePlanList = new ArrayList<>();
		 if (Func.isNotEmpty(planList)) {
			 for (MaintainPlanDTO plan : planList) {
				 // 生成点检任务时间判断
				 String cycleType = plan.getCycleType();
				 Integer cycleInterval = plan.getCycleInterval();
				 String cycleExecuteTimeStr = plan.getExecuteTime();
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchDay(plan.getStartDate().getTime(),
						 plan.getEndDate().getTime(), cycleInterval);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
				 if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchWeek(plan.getStartDate().getTime(),
						 plan.getEndDate().getTime(), cycleInterval, cycleExecuteTimeStr);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
				 if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(cycleType)) {
					 boolean match = SimasPlanSchedule.matchMonth(plan.getStartDate().getTime(), plan.getEndDate().getTime(),
						 cycleInterval, cycleExecuteTimeStr);
					 if (match) {
						 executePlanList.add(plan);
					 }
				 }
			 }
			 if (Func.isNotEmpty(executePlanList)) {
				 this.generateMaintainOrders(executePlanList);
			 }
		 }
		 log.info("################日检、周检、月检任务生成-END-################");
		 return executePlanList;
	 }

	 /**
	  * 生成保养工单
	  *
	  * @param planList
	  * @return void
	  * <AUTHOR>
	  * @date 2024/8/23 15:16
	  */
	 private void generateMaintainOrders(List<MaintainPlanDTO> planList) {
		 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.MAINTAIN_ORDER.name());
		 List<MaintainOrder> orderList = new ArrayList<>();
		 for (MaintainPlan plan : planList) {
			 List<MaintainPlanEquipment> planEquipmentList
				 = maintainPlanEquipmentService.list(Wrappers.<MaintainPlanEquipment>query().lambda()
				 .eq(MaintainPlanEquipment::getPlanId, plan.getId()).orderByAsc(MaintainPlanEquipment::getSort));
			 planEquipmentList.forEach(planEquipment -> {
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
					 List<ByDaySetVO> list = JSONUtil.toList(plan.getExecuteTime(), ByDaySetVO.class);
					 for (ByDaySetVO daySet : list) {
						 MaintainOrderVO maintainOrder = new MaintainOrderVO(plan, planEquipment.getEquipmentId(),
							 plan.getExecuteDept(), plan.getExecuteUser());
						 R<DeviceAccountVO> deviceAccountR
							 = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
						 maintainOrder.setEquipmentCode(deviceAccountR.getData().getCode());
						 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE)
							 + " " + daySet.getStartTime() + ":00";
						 String endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE)
							 + " " + daySet.getEndTime() + ":00";
						 maintainOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
						 maintainOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
						 orderList.add(Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrder.class)));
					 }
				 } else {
					 MaintainOrderVO maintainOrder = new MaintainOrderVO(plan, planEquipment.getEquipmentId(),
						 plan.getExecuteDept(), plan.getExecuteUser());
					 R<DeviceAccountVO> deviceAccountR
						 = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
					 maintainOrder.setEquipmentCode(deviceAccountR.getData().getCode());
					 maintainOrder.setPlanInfo(JSONUtil.toJsonStr(plan));
					 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
					 String endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 23:59:59";
					 maintainOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
					 maintainOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
					 orderList.add(Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrder.class)));
				 }
			 });
			 // 更新计划状态（未开始 —> 执行中）
			 if (PlanStatusEnum.NO_START == PlanStatusEnum.getByCode(plan.getStatus())) {
				 maintainPlanService.update(Wrappers.<MaintainPlan>update().lambda()
					 .set(MaintainPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode())
					 .set(MaintainPlan::getUpdateTime, DateUtil.now()).eq(MaintainPlan::getId, plan.getId()));
			 }
			 MaintainPlan p = maintainPlanService.getById(plan.getId());
			 // 更新计划状态（执行中 —> 已完成）
			 if (PlanStatusEnum.IN_PROGRESS == PlanStatusEnum.getByCode(p.getStatus())
				 && Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
				 DateUtil.format(plan.getEndDate(), DateUtil.PATTERN_DATE))) {
				 maintainPlanService.update(Wrappers.<MaintainPlan>update().lambda()
					 .set(MaintainPlan::getStatus, PlanStatusEnum.IS_COMPLETED.getCode())
					 .set(MaintainPlan::getUpdateTime, DateUtil.now()).eq(MaintainPlan::getId, plan.getId()));
			 }
		 }
		 if (Func.isNotEmpty(orderList)) {
			 orderList.forEach(e -> e.setIsNeedApproval(needApproval));
			 maintainOrderService.saveBatch(orderList);
			 orderList.forEach(order -> {
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER,
					 JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
			 });
			 // 发送消息提醒
			 maintainOrderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_MAINTAIN_ADD);
		 }
	 }

	 /**
	  * 检修工单催办
	  *
	  * @param v
	  * @return
	  */
	 public Boolean overhaulOrderUrge(OverhaulOrderUrgeVO v) {
		 final List<Integer> statuses
			 = Lists.newArrayList(OrderStatusEnum.IN_PROCESS.getCode(), OrderStatusEnum.IS_OVERDUE.getCode(),
			 OrderStatusEnum.WAIT_CONFIRM.getCode(), OrderStatusEnum.IS_REJECTED.getCode());
		 OverhaulOrderVO vo = new OverhaulOrderVO();
		 vo.setStatuses(statuses);
		 vo.setOnlyQuerySpecialType(true);
		 // 查询特种设备检修工单
		 IPage<OverhaulOrderDTO> page = overhaulOrderService.page(new Page<>(1L, -1L), vo);
		 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			 throw new BusinessException("催办失败！检修工单不存在");
		 }
		 // 发送催办消息
		 page.getRecords().forEach(overhaulOrder -> {
			 generalLogicService.sendMessage(overhaulOrder.getNo(), v.getContent(), listExecuteUsers(overhaulOrder),
				 MessageBizTypeEnum.SPECIAL_EQUIPMENT_OVERHAUL_URGE);
		 });
		 return true;
	 }

	 /**
	  * 查询检修人
	  *
	  * @param overhaulOrder
	  * @return
	  */
	 private List<Long> listExecuteUsers(OverhaulOrder overhaulOrder) {
		 if (ObjectUtil.isEmpty(overhaulOrder)) {
			 return Collections.emptyList();
		 }
		 // 没有具体执行人是部门下所有人
		 if (ObjectUtil.isEmpty(overhaulOrder.getExecuteUser())) {
			 final R<List<UserDept>> result
				 = userClient.userListOfDept(Collections.singletonList(overhaulOrder.getExecuteDept()));
			 if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData())) {
				 return Collections.emptyList();
			 }
			 return ListUtil.map(result.getData(), UserDept::getUserId);
		 }
		 return Collections.singletonList(overhaulOrder.getExecuteUser());
	 }

	 /**
	  * 查询特种设备检修工单
	  *
	  * @return
	  */
	 @Transactional(readOnly = true)
	 public IPage<OverhaulOrderDTO> pageSpecialTypeOverhaulOrder(Query query) {
		 final List<Integer> statuses
			 = Lists.newArrayList(OrderStatusEnum.IN_PROCESS.getCode(), OrderStatusEnum.IS_OVERDUE.getCode(),
			 OrderStatusEnum.WAIT_CONFIRM.getCode(), OrderStatusEnum.IS_REJECTED.getCode());
		 OverhaulOrderVO vo = new OverhaulOrderVO();
		 vo.setStatuses(statuses);
		 vo.setOnlyQuerySpecialType(true);
		 // 查询特种设备检修工单
		 IPage<OverhaulOrderDTO> page = overhaulOrderService.page(Condition.getPage(query), vo);
		 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			 return Condition.getPage(query);
		 }
		 // 获取检修工单单号
		 final List<String> noList = ListUtil.map(page.getRecords(), OverhaulOrderDTO::getNo);
		 // 根据检修工单单号查询催办消息Map
		 Map<String, Long> urgeCountMap = this.getNoUrgeCountMap(noList);
		 page.getRecords()
			 .forEach(overhaulOrder -> {
				 overhaulOrder.setUrgeCount(urgeCountMap.getOrDefault(overhaulOrder.getNo(), 0L));
			 });
		 return page;
	 }

	 /**
	  * 获取催办消息数量map
	  *
	  * @param noList
	  * @return
	  */
	 private Map<String, Long> getNoUrgeCountMap(List<String> noList) {
		 if (ObjectUtil.isEmpty(noList)) {
			 return MapUtil.empty();
		 }
		 // 查询催办消息
		 final R<List<Message>> result = messageClient.listByBiz(MessageTypeEnum.WORK_TODO.getCode(),
			 MessageBizTypeEnum.SPECIAL_EQUIPMENT_OVERHAUL_URGE.getCode(), noList);
		 if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(result.getData())) {
			 return MapUtil.empty();
		 }
		 return result.getData()
			 .stream()
			 .collect(Collectors.groupingBy(Message::getBizId, Collectors.counting()));
	 }

	 /**
	  * 获取各业务模块待办数量
	  * <p>
	  * 点巡检、保养、润滑、内部维修、外委维修、检修、备品备件
	  *
	  * @return
	  */
	 public List<PendingCountDto> listPendingReviewCountByModule() {
		 // 在主线程中设置 request 对象到 RequestContextHolder
		 final HttpServletRequest request = WebUtil.getRequest();
		 if (request == null) {
			 throw new IllegalStateException("Request 对象未找到");
		 }
		 // 获取点巡检工单数量
		 CompletableFuture<Long> inspectCompletableFuture = getInspectPendingReviewCompletableFuture(request);
		 // 获取保养工单待审核数量
		 CompletableFuture<Long> maintainCompletableFuture = getMaintainPendingReviewCompletableFuture(request);
		 // 获取润滑工单待审核数量
		 CompletableFuture<Long> lubricateCompletableFuture = getLubricatePendingReviewCompletableFuture(request);
		 // 获取内部维修工单待审核数量
		 CompletableFuture<Long> internalRepairCompletableFuture = getInternalRepairPendingReviewCompletableFuture(request);
		 // 获取外委维修工单待审核数量
		 CompletableFuture<Long> overhaulCompletableFuture = getExternalPendingReviewCompletableFuture(request);
		 // 获取检修工单待审数量
		 CompletableFuture<Long> overhaulPendingReviewCompletableFuture = getOverhaulPendingReviewCompletableFuture(request);
		 // 获取备品备件待审数量 TODO
//		 CompletableFuture<Long> sparePartsCompletableFuture = getSparePartsPendingReviewCompletableFuture(request);
		 CompletableFuture<Long> sparePartsCompletableFuture = null;
		 return CompletableFuture.allOf(inspectCompletableFuture, maintainCompletableFuture, lubricateCompletableFuture, internalRepairCompletableFuture, overhaulCompletableFuture, overhaulPendingReviewCompletableFuture, sparePartsCompletableFuture)
			 .thenApply(v -> Lists.newArrayList(new PendingCountDto().setModule(ApprovalModuleEnum.INSPECT).setCount(inspectCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.MAINTAIN).setCount(maintainCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.LUBRICATE).setCount(lubricateCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.EXTERNAL_REPAIR).setCount(internalRepairCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.EQUIPMENT_SCRAP).setCount(overhaulCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.OVERHAUL_ORDER).setCount(overhaulPendingReviewCompletableFuture.join()),
				 new PendingCountDto().setModule(ApprovalModuleEnum.SPARE_PART).setCount(sparePartsCompletableFuture.join()))).join();
	 }

	 /**
	  * 获取检修工单待审数量
	  *
	  * @return
	  */
	 private CompletableFuture<Long> getOverhaulPendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 OverhaulOrderVO vo = new OverhaulOrderVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 final IPage<OverhaulOrderDTO> page = overhaulOrderService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取外委维修工单审核数量
	  */
	 private CompletableFuture<Long> getExternalPendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 RepairVO vo = new RepairVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 vo.setBizType(RepairBizTypeEnum.EXTERNAL.getCode());
				 final IPage<RepairDTO> page = repairService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取内部维修工单审核数量
	  *
	  * @return
	  */
	 private CompletableFuture<Long> getInternalRepairPendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 RepairVO vo = new RepairVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 vo.setBizType(RepairBizTypeEnum.INTERNAL.getCode());
				 final IPage<RepairDTO> page = repairService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取润滑工单待审核数量
	  *
	  * @return
	  */
	 private CompletableFuture<Long> getLubricatePendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 LubricateOrderVO vo = new LubricateOrderVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 IPage<LubricateOrderDTO> page = lubricateOrderService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取保养工单待审核数量
	  *
	  * @return
	  */
	 private CompletableFuture<Long> getMaintainPendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 MaintainOrderVO vo = new MaintainOrderVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 IPage<MaintainOrderDTO> page = maintainOrderService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取点巡检待审工单数量
	  *
	  * @return
	  */
	 private CompletableFuture<Long> getInspectPendingReviewCompletableFuture(HttpServletRequest request) {
		 return CompletableFuture.supplyAsync(() -> {
			 // 在异步任务中重新设置 request 对象
			 RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
			 try {
				 InspectOrderVO vo = new InspectOrderVO();
				 vo.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
				 IPage<InspectOrderDTO> page = inspectOrderService.page(new Page<>(1L, -1L), vo);
				 if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
					 return 0L;
				 }
				 return (long) page.getRecords().size();
			 } finally {
				 RequestContextHolder.resetRequestAttributes();
			 }
		 });
	 }

	 /**
	  * 获取设备盘点工单统计
	  *
	  * @return 设备盘点统计数据
	  */
	 private InventoryStatisticsDTO getEquipmentInventoryStatistics() {
		 try {
			 // 统计盘点中的工单
			 EquipmentInventoryOrderPageVO processVO = new EquipmentInventoryOrderPageVO();
			 processVO.setCurrent(1L);
			 processVO.setSize(-1L); // 使用-1获取全部数据
			 processVO.setStatus(EquipmentInventoryOrderStatusEnum.PROCESS.getCode());
			 processVO.setNeStatus(EquipmentInventoryOrderStatusEnum.NOT_START.getCode()); // 排除未启动状态，保持数据权限逻辑
			 IPage<EquipmentInventoryOrderDTO> processPage = equipmentInventoryOrderService.pageList(processVO);
			 Integer processCount = ObjectUtil.isEmpty(processPage) ? 0 : processPage.getRecords().size();

			 // 统计已完成的工单
			 EquipmentInventoryOrderPageVO completeVO = new EquipmentInventoryOrderPageVO();
			 completeVO.setCurrent(1L);
			 completeVO.setSize(-1L); // 使用-1获取全部数据
			 completeVO.setStatus(EquipmentInventoryOrderStatusEnum.COMPLETE.getCode());
			 completeVO.setNeStatus(EquipmentInventoryOrderStatusEnum.NOT_START.getCode()); // 排除未启动状态，保持数据权限逻辑
			 IPage<EquipmentInventoryOrderDTO> completePage = equipmentInventoryOrderService.pageList(completeVO);
			 Integer completeCount = ObjectUtil.isEmpty(completePage) ? 0 : completePage.getRecords().size();

			 return new InventoryStatisticsDTO(processCount, completeCount);
		 } catch (Exception e) {
			 log.error("获取设备盘点工单统计失败", e);
			 return new InventoryStatisticsDTO(0, 0);
		 }
	 }

	 /**
	  * 获取备品备件盘点工单统计
	  *
	  * @return 备品备件盘点统计数据
	  */
	 private InventoryStatisticsDTO getSparePartsInventoryStatistics() {
		 try {
			 // 统计盘点中的工单
			 SparePartsInventoryOrderPageVO processVO = new SparePartsInventoryOrderPageVO();
			 processVO.setCurrent(1L);
			 processVO.setSize(-1L); // 使用-1获取全部数据
			 processVO.setStatus(SpareInventoryOrderStatusEnum.PROCESS.getCode());
			 processVO.setNeStatus(SpareInventoryOrderStatusEnum.NOT_START.getCode()); // 排除未启动状态，保持数据权限逻辑
			 IPage<SparePartsInventoryOrderDTO> processPage = sparePartsInventoryOrderService.pageList(processVO);
			 Integer processCount = ObjectUtil.isEmpty(processPage) ? 0 : processPage.getRecords().size();

			 // 统计已完成的工单
			 SparePartsInventoryOrderPageVO completeVO = new SparePartsInventoryOrderPageVO();
			 completeVO.setCurrent(1L);
			 completeVO.setSize(-1L); // 使用-1获取全部数据
			 completeVO.setStatus(SpareInventoryOrderStatusEnum.COMPLETE.getCode());
			 completeVO.setNeStatus(SpareInventoryOrderStatusEnum.NOT_START.getCode()); // 排除未启动状态，保持数据权限逻辑
			 IPage<SparePartsInventoryOrderDTO> completePage = sparePartsInventoryOrderService.pageList(completeVO);
			 Integer completeCount = ObjectUtil.isEmpty(completePage) ? 0 : completePage.getRecords().size();

			 return new InventoryStatisticsDTO(processCount, completeCount);
		 } catch (Exception e) {
			 log.error("获取备品备件盘点工单统计失败", e);
			 return new InventoryStatisticsDTO(0, 0);
		 }
	 }

 }
