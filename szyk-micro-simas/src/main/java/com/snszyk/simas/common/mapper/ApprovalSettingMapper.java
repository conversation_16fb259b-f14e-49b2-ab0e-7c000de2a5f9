/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.simas.common.dto.ApprovalSettingDto;
import com.snszyk.simas.common.entity.ApprovalSetting;
import com.snszyk.simas.common.vo.ApprovalSettingPageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单审核配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface ApprovalSettingMapper extends BaseMapper<ApprovalSetting> {

	List<ApprovalSettingDto> pageList(@Param("v") ApprovalSettingPageVo v);

	ApprovalSettingDto detail(Long id);

}
