/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel.template;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * MaintainStandardExcelTemplate
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(30)
@ContentRowHeight(18)
public class MaintainStandardExcelTemplate {
	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "系统编码"}, index = 0)
	private String code;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "设备名称"}, index = 1)
	private String name;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "SN编号"}, index = 2)
	private String sn;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "保养部位（必填）"}, index = 3)
	private String monitorName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "保养标准（必填）"}, index = 4)
	private String standardName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "保养方法（必填）"}, index = 5)
	private String methodsName;

}
