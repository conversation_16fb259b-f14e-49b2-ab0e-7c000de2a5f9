/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OperateTypeEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IOperateLogService;
import com.snszyk.simas.common.service.ISpecialEquipmentInspectRegisterService;
import com.snszyk.simas.common.vo.OperateLogVO;
import com.snszyk.simas.common.vo.SpecialEquipmentInspectRegisterVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * 特种设备检验登记表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/special-equipment-inspect-register")
@Api(value = "特种设备检验登记表", tags = "特种设备检验登记表接口")
public class SpecialEquipmentInspectRegisterController extends SzykController {

	private final ISpecialEquipmentInspectRegisterService inspectRegisterService;
	private final IOperateLogService operateLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<SpecialEquipmentInspectRegisterVO> detail(Long id) {
		// 操作日志
		 OperateLogVO operateLog
			 = new OperateLogVO(id, SystemModuleEnum.SPECIAL_EQUIPMENT_INSPECT_REGISTER, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		return R.data(inspectRegisterService.detail(id));
	}

	/**
	 * 自定义分页 特种设备检验登记表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备ID", paramType = "query", required = true, dataType = "long"),
		@ApiImplicitParam(name = "inspectType", value = "检验类型（字典：inspect_type）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "inspectResult", value = "检验结论（字典：inspect_result）", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入specialEquipmentInspectRegister")
	public R<IPage<SpecialEquipmentInspectRegisterVO>> page(@ApiIgnore SpecialEquipmentInspectRegisterVO specialEquipmentInspectRegister, Query query) {
		// 操作日志
		OperateLogVO operateLog =
			new OperateLogVO(null, SystemModuleEnum.SPECIAL_EQUIPMENT_INSPECT_REGISTER, OperateTypeEnum.RETRIEVE);
		operateLogService.submit(operateLog);
		return R.data(inspectRegisterService.page(Condition.getPage(query), specialEquipmentInspectRegister));
	}

	/**
	 * 提交 特种设备检验登记表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "提交", notes = "传入specialEquipmentInspectRegister")
	public R submit(@Valid @RequestBody SpecialEquipmentInspectRegisterVO specialEquipmentInspectRegister) {
		// 操作日志
		OperateLogVO operateLog;
		if (specialEquipmentInspectRegister.getId() == null) {
			operateLog
				= new OperateLogVO(null, SystemModuleEnum.SPECIAL_EQUIPMENT_INSPECT_REGISTER, OperateTypeEnum.CREATE);
		} else {
			operateLog
				= new OperateLogVO(specialEquipmentInspectRegister.getId(),
				SystemModuleEnum.SPECIAL_EQUIPMENT_INSPECT_REGISTER, OperateTypeEnum.UPDATE);
		}
		operateLogService.submit(operateLog);
		return R.status(inspectRegisterService.submit(specialEquipmentInspectRegister));
	}

	/**
	 * 删除 特种设备检验登记表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<Long> list = Func.toLongList(ids);
		list.forEach(id -> {
			OperateLogVO operateLog
				= new OperateLogVO(id, SystemModuleEnum.SPECIAL_EQUIPMENT_INSPECT_REGISTER, OperateTypeEnum.DELETE);
			operateLogService.submit(operateLog);
		});
		return R.status(inspectRegisterService.deleteLogic(list));
	}

}
