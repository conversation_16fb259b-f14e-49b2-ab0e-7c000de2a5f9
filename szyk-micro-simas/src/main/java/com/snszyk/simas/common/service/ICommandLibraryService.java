/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.CommandLibraryDto;
import com.snszyk.simas.common.entity.CommandLibrary;
import com.snszyk.simas.common.vo.CommandLibraryPageVo;

/**
 * 指令库 服务类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface ICommandLibraryService extends BaseService<CommandLibrary> {

	/**
	 * 名称校验
	 */
	void checkName(Long id, String name);

	/**
	 * 分页查询
	 */
	IPage<CommandLibraryDto> pageList(CommandLibraryPageVo v);

	/**
	 * 详情
	 */
	CommandLibraryDto detail(Long id);

	CommandLibraryDto getPageRoute(String commandRoute);
}
