/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.EquipmentFileDTO;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;

import java.util.List;

/**
 * 设备资料表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface IEquipmentFileService extends BaseService<EquipmentFile> {

    /**
     * 自定义分页
     *
     * @param page
     * @param equipmentFile
     * @return
     */
    IPage<EquipmentFileVO> page(IPage<EquipmentFileVO> page, EquipmentFileVO equipmentFile);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    EquipmentFileVO detail(Long id);

    /**
     * 提交
     *
     * @param equipmentFile
     * @return
     */
    boolean submit(EquipmentFileVO equipmentFile);

    /**
     * 提交前期资料
     * 
     * @param preFile
     * @return
     */
    boolean submitPreFile(EquipmentPreFileVO preFile);

    /**
     * 删除设备资料数据
     * 
     * @param ids
     * @return
     */
    boolean removeByEquipmentIds(List<Long> ids);

    void syncRemoveDify(List<Long> deleteIds);

    List<EquipmentFileVO> preFileList(EquipmentFileVO equipmentFile);

    List<EquipmentFileDTO> listByEquipmentId(Long equipmentId);

    /**
     * 列表
     * 
     * @param equipmentFile
     * @return
     */
    List<EquipmentFileVO> list(EquipmentFileVO equipmentFile);

    int countByEquipmentId(Long equipmentId, String categoryCode);

    boolean aiToolsAddPreFile(EquipmentPreFileVO preFile);

    /**
     * AI运维标准删除前期资料
     * 
     * @param fileId 资料ID
     * @return 删除结果
     */
    boolean aiToolsRemovePreFile(Long fileId);
}
