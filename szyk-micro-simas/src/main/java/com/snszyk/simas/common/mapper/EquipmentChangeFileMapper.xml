<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.EquipmentChangeFileMapper">


    <select id="pageList" resultType="com.snszyk.simas.change.dto.EquipmentChangeFileDto">
        select * from simas_equipment_change_file where is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.simas.change.dto.EquipmentChangeFileDto">
        select * from simas_equipment_change_file where is_deleted = 0 and id=#{id}
    </select>
    <select id="listByChangeId" resultType="com.snszyk.simas.change.dto.EquipmentChangeFileDto"
            parameterType="java.lang.Long">
        select t.id,
               t.business_type,
               t.attach_id,
               t.risk_type,
               t.change_id,
               t.create_user,
               t.create_time,
               t.create_dept,
               t.update_user,
               t.update_time,
               t.tenant_id,
               t1.link,
               t1.domain,
               t1.name,
               t1.original_name,
               t1.extension,
               t1.attach_size,
               t2.dict_value as riskTypeName
        from simas_equipment_change_file t
                 left join szyk_attach t1 on t1.id = t.attach_id and t1.is_deleted = 0
                 left join szyk_dict_biz t2 on t2.code = 'equipment_change_risk_file' and t2.dict_key = t.risk_type and t2.is_deleted = 0
        where t.is_deleted = 0
          and t.change_id = #{changeId}


    </select>

</mapper>
