/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.common.service.IEquipmentFileUpdateService;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.dto.EquipmentFileUpdateDto;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.vo.EquipmentFileUpdateAVo;
import com.snszyk.simas.common.vo.EquipmentFileUpdatePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备文件更新 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@AllArgsConstructor
@Service
public class EquipmentFileUpdateLogicService  {

    private final IEquipmentFileUpdateService equipmentFileUpdateService;

    @Transactional
    public EquipmentFileUpdateDto saveOrUpdate(EquipmentFileUpdateAVo v) {
		EquipmentFileUpdate copy = BeanUtil.copy(v, EquipmentFileUpdate.class);
		boolean b = equipmentFileUpdateService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
    }

    public IPage<EquipmentFileUpdateDto> pageList(EquipmentFileUpdatePageVo v) {
        IPage<EquipmentFileUpdateDto> page = equipmentFileUpdateService.pageList(v);
        return page;
    }

    /**
     * 根据ID查询分类详情
     *
     * @param id 分类的唯一标识符。
     * @return 包含分类详细信息的DTO（数据传输对象）。
     * @throws ServiceException 如果分类不存在，则抛出服务异常。
     */
    public EquipmentFileUpdateDto detail(Long id) {
        // 通过ID查询数据信息
        EquipmentFileUpdateDto dto = equipmentFileUpdateService.detail(id);
        // 检查查询结果，如果数据不存在，则抛出异常
        if (dto == null) {
            throw new ServiceException("数据不存在");
        }
        return dto;
    }

    /**
     * 删除 如已被引用则不允许删除
     *
     * @param ids 要删除的ID列表
     * @return 删除结果
     */
    @Transactional
    public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
        List<CommonDeleteResultDto> result = new ArrayList<>();
        for (Long id : ids) {
            CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
            deleteResultDto.setId(id);
            result.add(deleteResultDto);

            EquipmentFileUpdate data = equipmentFileUpdateService.getById(id);
            if (data == null) {
                throw new ServiceException("数据不存在");
            }

            boolean b = equipmentFileUpdateService.removeById(id);
            if (!b) {
                throw new ServiceException("系统异常,删除失败");
            }
            deleteResultDto.setResult(true);
        }
        return result;
    }


}
