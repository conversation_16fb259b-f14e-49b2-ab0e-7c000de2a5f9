/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.BizLogDTO;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.vo.BizLogPageVO;
import com.snszyk.simas.common.vo.BizSearchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface BizLogMapper extends BaseMapper<BizLog> {

	/**
	 * 设备关联工单分页
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<BizLogDTO> equipmentOrderPage(IPage page, @Param("search") BizSearchVO search);


	IPage<BizLogDTO> pageList(@Param("v") BizLogPageVO v);
}
