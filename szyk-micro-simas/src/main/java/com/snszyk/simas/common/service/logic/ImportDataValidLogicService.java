 package com.snszyk.simas.common.service.logic;

 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.equipment.entity.DeviceAccount;
 import com.snszyk.common.equipment.entity.DeviceCategory;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.common.equipment.feign.ICommonClient;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.equipment.vo.DeviceMonitorVO;
 import com.snszyk.common.location.entity.Location;
 import com.snszyk.common.utils.CommonUtil;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.StringPool;
 import com.snszyk.simas.lubricate.entity.LubricateMethods;
 import com.snszyk.simas.lubricate.entity.LubricateOilType;
 import com.snszyk.simas.lubricate.service.ILubricateMethodsService;
 import com.snszyk.simas.lubricate.service.ILubricateOilTypeService;
 import com.snszyk.simas.overhaul.entity.OverhaulMethods;
 import com.snszyk.simas.overhaul.service.IOverhaulMethodsService;
 import com.snszyk.system.entity.DictBiz;
 import com.snszyk.system.feign.IDictBizClient;
 import com.snszyk.system.feign.ISysClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;

 import java.util.List;
 import java.util.Objects;

 /**
  * 导入标准校验
  *
  * <AUTHOR>
  * @date 2025/03/18 13:56
  **/
 @Slf4j
 @Service
 @AllArgsConstructor
 public class ImportDataValidLogicService {

 	private final ILubricateMethodsService lubricateMethodsService;
 	private final ILubricateOilTypeService lubricateOilTypeService;
 	private final IOverhaulMethodsService overhaulMethodsService;
	private final IDeviceAccountClient deviceAccountClient;
 	private final ICommonClient commonClient;
 	private final ISysClient sysClient;
 	private final IDictBizClient dictBizClient;

 	/**
 	 * 设备类型
	 *
 	 * @param pathName
 	 * @return
 	 */
 	public DeviceCategory getDeviceCategory(String pathName) {
 		List<String> pathNameList = CommonUtil.handleSeparate("/", pathName);
		R<DeviceCategory> deviceCategoryResult = commonClient.deviceCategoryByPathName(Func.join(pathNameList, StringPool.COMMA));
		if(!deviceCategoryResult.isSuccess()){
			throw new IllegalArgumentException("查询设备分类错误！");
		}
		if(Func.isEmpty(deviceCategoryResult.getData())){
			throw new IllegalArgumentException("设备类型不匹配，请仔细核对！");
		}
 		return deviceCategoryResult.getData();
 	}

 	/**
 	 * 存放地点
	 *
 	 * @param locationName
 	 * @return
 	 */
 	public Long getLocationId(String locationName) {
 		List<String> locationNameList = CommonUtil.handleSeparate("/", locationName);
		R<Location> locationResult = commonClient.locationByPath(Func.join(locationNameList, StringPool.COMMA));
		if(!locationResult.isSuccess()){
			throw new IllegalArgumentException("查询设备存放地点错误！");
		}
 		if (Func.isEmpty(locationResult.getData())){
 			throw new IllegalArgumentException("设备存放地点不存在，请先创建！");
 		}
 		return locationResult.getData().getId();
 	}

 	/**
 	 * 使用部门
 	 * @param name
 	 * @return
 	 */
 	public Long getDept(String name) {
 		R<Long> r = sysClient.getDeptIdByNamePath(AuthUtil.getTenantId(), name);
 		if (r.isSuccess()){
 			return r.getData();
 		} else {
 			throw new RuntimeException(r.getMsg());
 		}
 	}

 	/**
 	 * 字典值
 	 * @param code
 	 * @param dictValue
 	 * @return
 	 */
 	public DictBiz getByValue(String code, String dictValue){
 		R<DictBiz> r = dictBizClient.getByValue(code, dictValue);
 		if (r.isSuccess()){
 			return r.getData();
 		}
 		return null;
 	}

 	public DeviceAccount getDeviceAccount(String code) {
		DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		deviceAccountVO.setCode(code);
		R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoByParams(deviceAccountVO);
 		if (!deviceAccountResult.isSuccess()){
 			throw new IllegalArgumentException("查询设备台账错误！");
 		}
 		if (Func.isEmpty(deviceAccountResult.getData())){
 			throw new IllegalArgumentException("设备编号不存在，请仔细核对！");
 		}
 		return Objects.requireNonNull(BeanUtil.copy(deviceAccountResult.getData(), DeviceAccount.class));
 	}

 	/**
 	 * 设备部位
	 *
 	 * @param deviceId
 	 * @param monitorName
 	 * @return
 	 */
 	public DeviceMonitor getDeviceMonitor(Long deviceId, String monitorName) {
		DeviceMonitorVO deviceMonitorVO = new DeviceMonitorVO();
		deviceMonitorVO.setDeviceId(deviceId);
		deviceMonitorVO.setName(monitorName);
		R<List<DeviceMonitor>> deviceMonitorListResult = commonClient.deviceMonitorByParams(deviceMonitorVO);
		if (!deviceMonitorListResult.isSuccess()){
			throw new IllegalArgumentException("查询设备部位错误！");
		}
		if(Func.isEmpty(deviceMonitorListResult.getData())){
			throw new IllegalArgumentException("设备部位不存在，请仔细核对！");
		}
 		return deviceMonitorListResult.getData().get(0);
 	}

 	public LubricateMethods getLubricateMethods(String name) {
 		LubricateMethods lubricateMethods = lubricateMethodsService.getOne(Wrappers.<LubricateMethods>lambdaQuery().eq(LubricateMethods::getName, name));
 		if (lubricateMethods == null){
 			throw new IllegalArgumentException("润滑手段不存在，请仔细核对！");
 		}
 		return lubricateMethods;
 	}

 	public LubricateOilType getLubricateOilType(String name) {
 		LubricateOilType lubricateOilType = lubricateOilTypeService.getOne(Wrappers.<LubricateOilType>lambdaQuery().eq(LubricateOilType::getName, name));
 		if (lubricateOilType == null){
 			throw new IllegalArgumentException("油品类型不存在，请仔细核对！");
 		}
 		return lubricateOilType;
 	}

 	public OverhaulMethods getOverhaulMethods(String name) {
 		OverhaulMethods overhaulMethods = overhaulMethodsService.getOne(Wrappers.<OverhaulMethods>lambdaQuery().eq(OverhaulMethods::getName, name));
 		if (overhaulMethods == null){
 			throw new IllegalArgumentException("检修方式不存在，请仔细核对！");
 		}
 		return overhaulMethods;
 	}


 }
