package com.snszyk.simas.common.excel.support;

import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.excel.annotation.ExcelSelected;
import com.snszyk.simas.common.service.logic.ExcelSelectLogicService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 根据 ExcelSelected 注解解析下拉列表数据源。
 */
@Data
@Slf4j
public class ExcelSelectedResolve {

	/**
	 * 下拉选项数组。
	 */
	private String[] source;

	/**
	 * 下拉列表的起始行。
	 */
	private int firstRow;

	/**
	 * 下拉列表的结束行。
	 */
	private int lastRow;

	/**
	 * 解析下拉列表数据来源
	 *
	 * @param excelSelected 下拉框注解对象
	 * @return 下拉框选项数组
	 */
	public String[] resolveSelectedSource(ExcelSelected excelSelected) {
		if (excelSelected == null) {
			return null;
		}

		// 方式一：获取固定下拉框的内容
		String[] source = excelSelected.source();
		if (source.length > 0) {
			return source;
		}

		// 方式二：获取动态下拉框的内容
		Class<? extends ExcelDynamicSelect>[] classes = excelSelected.sourceClass();
		if (classes.length > 0) {
			try {
				ExcelDynamicSelect excelDynamicSelect = classes[0].newInstance();
				String[] dynamicSelectSource = excelDynamicSelect.getSource();
				if (dynamicSelectSource != null && dynamicSelectSource.length > 0) {
					return dynamicSelectSource;
				}
			} catch (InstantiationException | IllegalAccessException e) {
				log.error("解析动态下拉框数据异常", e);
			}
		}

		// 方式三：获取码值下拉数据（动态下拉）
		String codeField = excelSelected.codeField();
		if (Func.isNotEmpty(codeField)) {
			try {
				// 这里就是通过码值查询码值表，写死了，每次传码值查询即可
				String[] codeFieldSource = SpringUtil.getBean(ExcelSelectLogicService.class)
					.selectByCode(codeField);
				if (Func.isNotEmpty(codeFieldSource)) {
					return codeFieldSource;
				}
			} catch (Exception e) {
				log.error("解析动态下拉框(码值)数据异常", e);
			}
		}

		return null;
	}
}
