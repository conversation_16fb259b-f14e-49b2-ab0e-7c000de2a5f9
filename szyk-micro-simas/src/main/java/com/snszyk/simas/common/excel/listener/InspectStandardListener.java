 package com.snszyk.simas.common.excel.listener;

 import com.alibaba.excel.context.AnalysisContext;
 import com.alibaba.excel.event.AnalysisEventListener;
 import com.snszyk.common.equipment.entity.DeviceAccount;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.excel.template.InspectStandardTemplate;
 import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
 import com.snszyk.simas.inspect.entity.InspectStandard;
 import com.snszyk.simas.inspect.service.IInspectStandardService;
 import lombok.Data;
 import lombok.extern.slf4j.Slf4j;

 import java.util.ArrayList;
 import java.util.List;

 @Data
 @Slf4j
 public class InspectStandardListener extends AnalysisEventListener<InspectStandardTemplate> {

 	private IInspectStandardService inspectStandardService;
 	private ImportDataValidLogicService importDataValidLogicService;

 	private List<InspectStandard> saveList = new ArrayList<>();
 	private List<String> failReasonList = new ArrayList<>();
 	private int headRowNumber;

 	public InspectStandardListener(int headRowNumber, IInspectStandardService inspectStandardService, ImportDataValidLogicService importDataValidLogicService) {
 		this.headRowNumber = headRowNumber;
 		this.inspectStandardService = inspectStandardService;
 		this.importDataValidLogicService = importDataValidLogicService;
 	}

 	@Override
 	public void invoke(InspectStandardTemplate template, AnalysisContext analysisContext) {
 		InspectStandard entity= new InspectStandard();
 		int rowIndex = analysisContext.getCurrentRowNum();
 		log.info("====================================读取第{}行==================================================", rowIndex + 1);
 		String rowFailReason = validParam(template, entity);
 		if (Func.isNotBlank(rowFailReason)){
 			rowFailReason = "excel第" + (rowIndex + 1) + "行：" + rowFailReason;
 			failReasonList.add(rowFailReason);
 		}
 		saveList.add(entity);
 	}

 	@Override
 	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
 		if (analysisContext.getCurrentRowNum() <= headRowNumber - 1) {
 			failReasonList.add("Excel文件数据为空");
 		}
 		if (Func.isEmpty(failReasonList) && Func.isNotEmpty(saveList)){
 			inspectStandardService.saveImportData(saveList);
 		}
 	}

 	private String validParam(InspectStandardTemplate template, InspectStandard entity){
 		List<String> rowFailReason = new ArrayList<>();
 		// 校验必填项
 		if (Func.isBlank(template.getName())) {
 			rowFailReason.add("设备名称不能为空");
 		}
 		if (Func.isBlank(template.getSn())) {
 			rowFailReason.add("SN编号不能为空");
 		}
 		if (Func.isBlank(template.getMonitorName())) {
 			rowFailReason.add("检查部位不能为空");
 		}
 		if (Func.isBlank(template.getStandardName())) {
 			rowFailReason.add("检查标准不能为空");
 		}
 		if (Func.isBlank(template.getMethodsName())) {
 			rowFailReason.add("检查方法不能为空");
 		}
 		if (Func.isNotBlank(template.getName()) && Func.isNotBlank(template.getSn())){
 			try {
 				DeviceAccount account = importDataValidLogicService.getDeviceAccount(template.getCode());
 				entity.setEquipmentId(account.getId());
 				DeviceMonitor monitor = importDataValidLogicService.getDeviceMonitor(account.getId(), template.getMonitorName());
 				entity.setMonitorId(monitor.getId());
 			} catch (Exception e) {
 				e.printStackTrace();
 				rowFailReason.add(e.getMessage());
 			}
 		}
 		entity.setStandard(template.getStandardName());
 		entity.setMethod(template.getMethodsName());
 		return Func.join(rowFailReason, ";");
 	}


 }
