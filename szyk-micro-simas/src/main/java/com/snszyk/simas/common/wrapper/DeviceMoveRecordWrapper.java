/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.enums.MoveSourceEnum;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备移动记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public class DeviceMoveRecordWrapper extends BaseEntityWrapper<DeviceMoveRecord, DeviceMoveRecordVO> {

	public static DeviceMoveRecordWrapper build() {
		return new DeviceMoveRecordWrapper();
 	}

	@Override
	public DeviceMoveRecordVO entityVO(DeviceMoveRecord deviceMoveRecord) {
		DeviceMoveRecordVO deviceMoveRecordVO
			= Objects.requireNonNull(BeanUtil.copy(deviceMoveRecord, DeviceMoveRecordVO.class));
		if(Func.isNotEmpty(deviceMoveRecord.getOperateUser())){
			User operateUser = UserCache.getUser(deviceMoveRecord.getOperateUser());
			if(Func.isNotEmpty(operateUser)){
				deviceMoveRecordVO.setOperateUserName(operateUser.getRealName());
			}
		}
		deviceMoveRecordVO
			.setSourceName(Objects.requireNonNull(MoveSourceEnum.getByCode(deviceMoveRecord.getSource())).getName());
		deviceMoveRecordVO
			.setOriginalLocationName(CommonCache.getLocation(deviceMoveRecord.getOriginalLocation()).getName());
		deviceMoveRecordVO
			.setNewLocationName(CommonCache.getLocation(deviceMoveRecord.getNewLocation()).getName());
		return deviceMoveRecordVO;
	}


}
