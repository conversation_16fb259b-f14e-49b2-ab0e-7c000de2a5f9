 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.common.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.secure.annotation.PreAuth;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.simas.common.dto.PendingCountDto;
 import com.snszyk.simas.common.dto.WorkbenchDataDTO;
 import com.snszyk.simas.common.service.logic.AppHomeLogicService;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import com.snszyk.simas.inspect.dto.InspectPlanDTO;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
 import com.snszyk.simas.overhaul.vo.OverhaulOrderUrgeVO;
 import io.swagger.annotations.Api;
 import io.swagger.annotations.ApiOperation;
 import io.swagger.annotations.ApiParam;
 import lombok.AllArgsConstructor;
 import org.springframework.validation.annotation.Validated;
 import org.springframework.web.bind.annotation.*;

 import javax.validation.Valid;
 import java.util.List;

 /**
  * 移动端数据 控制器
  *
  * <AUTHOR>
  * @since 2024-09-15
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/app-home")
 @Api(value = "移动端数据接口", tags = "移动端数据接口")
 public class AppHomeController extends SzykController {

 	private final AppHomeLogicService logicService;

 	/**
 	 * APP工作台数据
 	 */
 	@GetMapping("/workbench")
 	@ApiOperationSupport(order = 1)
 	@ApiOperation(value = "APP工作台数据", notes = "传入")
 	public R<WorkbenchDataDTO> workbench() {
 		return R.data(logicService.workbench());
 	}

 	/**
 	 * 异常上报
 	 */
 	@PostMapping("/report-abnormal")
 	@ApiOperationSupport(order = 2)
 	@ApiOperation(value = "异常上报", notes = "传入faultDefect")
 	public R reportAbnormal(@Valid @RequestBody FaultDefectVO faultDefect) {
 		return R.status(logicService.reportAbnormal(faultDefect));
 	}

 	/**
 	 * APP工作台数据
 	 */
 	@GetMapping("/test")
 	@ApiOperationSupport(order = 3)
 	@ApiOperation(value = "APP工作台数据", notes = "传入")
 	public R<List<Long>> test(@ApiParam(value = "executeDateStr") @RequestParam String executeDateStr,
 							  @ApiParam(value = "disableDateStr") @RequestParam String disableDateStr,
 							  @ApiParam(value = "cycleInterval") @RequestParam Integer cycleInterval,
 							  @ApiParam(value = "cycleExecuteDate") @RequestParam String cycleExecuteDate) {
 		return R.data(logicService.month(executeDateStr, disableDateStr, cycleInterval, cycleExecuteDate));
 	}

 	/**
 	 * 手动生成点巡检工单
 	 */
 	@PostMapping("/inspect-schedule")
 	@ApiOperationSupport(order = 4)
 	@ApiOperation(value = "手动生成点巡检工单", notes = "传入")
 	public R<List<InspectPlanDTO>> inspectSchedule() {
 		return R.data(logicService.inspectSchedule());
 	}

 	/**
 	 * 手动生成保养工单
 	 */
 	@PostMapping("/maintain-schedule")
 	@ApiOperationSupport(order = 5)
 	@ApiOperation(value = "手动生成保养工单", notes = "传入")
 	public R<List<MaintainPlanDTO>> maintainSchedule() {
 		return R.data(logicService.maintainSchedule());
 	}

 	/**
 	 * 催办
 	 */
 	@PostMapping("/urge")
 	@ApiOperationSupport(order = 6)
 	@ApiOperation(value = "催办", notes = "传入")
 	@PreAuth("hasRole('service_owner')")
 	public R<Boolean> urge(@Validated @RequestBody OverhaulOrderUrgeVO v) {
 		return R.status(logicService.overhaulOrderUrge(v));
 	}

 	/**
 	 * 特种设备检修工单列表
 	 */
 	@GetMapping("/special-type-overhaul-order")
 	@ApiOperationSupport(order = 7)
 	@ApiOperation(value = "特种设备设备检修工单列表", notes = "传入")
 	public R<IPage<OverhaulOrderDTO>> overhaulOrderList(Query query) {
 		return R.data(logicService.pageSpecialTypeOverhaulOrder(query));
 	}

 	/**
 	 * 获取各业务模块待办数量
 	 */
 	@GetMapping("/pendingReviewCountByModule")
 	@ApiOperationSupport(order = 8)
 	@ApiOperation(value = "获取各业务模块待办数量", notes = "传入")
 	public R<List<PendingCountDto>> listPendingReviewCountByModule() {
 		return R.data(logicService.listPendingReviewCountByModule());
 	}


 }
