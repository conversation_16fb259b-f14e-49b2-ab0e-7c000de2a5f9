/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;

import java.util.Objects;

/**
 * 备件耗材表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
public class ComponentMaterialWrapper extends BaseEntityWrapper<ComponentMaterial, ComponentMaterialVO> {

	public static ComponentMaterialWrapper build() {
		return new ComponentMaterialWrapper();
 	}

	@Override
	public ComponentMaterialVO entityVO(ComponentMaterial componentMaterial) {
		ComponentMaterialVO componentMaterialVO = Objects.requireNonNull(BeanUtil.copy(componentMaterial, ComponentMaterialVO.class));
		componentMaterialVO.setBizModuleName(SystemModuleEnum.getByCode(componentMaterial.getBizModule()).getName());
		return componentMaterialVO;
	}

}
