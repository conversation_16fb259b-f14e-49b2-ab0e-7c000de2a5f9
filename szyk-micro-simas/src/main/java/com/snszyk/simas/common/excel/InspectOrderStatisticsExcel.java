/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * InspectOrderStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class InspectOrderStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("工单号")
	private String no;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("设备类型")
	private String equipmentCategoryName;

	@ExcelProperty("点巡检结果")
	private String inspectResult;

	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@ExcelProperty("开始时间")
	private Date startTime;

	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@ExcelProperty("截止时间")
	private Date endTime;

	@ExcelProperty("执行人")
	private String executeUserName;

	@ExcelProperty("执行部门")
	private String executeDeptName;

	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@ExcelProperty("完成时间")
	private Date submitTime;

	@ExcelProperty("工单状态")
	private String statusName;


}
