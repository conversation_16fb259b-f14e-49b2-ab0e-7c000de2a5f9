package com.snszyk.simas.common.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.equipment.vo.DeviceMonitorVO;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.excel.template.OverhaulStandardExcelTemplate;
import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import com.snszyk.simas.overhaul.entity.OverhaulStandard;
import com.snszyk.simas.overhaul.service.IOverhaulStandardService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class OverhaulStandardListener extends AnalysisEventListener<OverhaulStandardExcelTemplate> {

	private IOverhaulStandardService overhaulStandardService;
	private ImportDataValidLogicService importDataValidLogicService;

	private List<OverhaulStandard> saveList = new ArrayList<>();
	private List<String> failReasonList = new ArrayList<>();
	private int headRowNumber;

	public OverhaulStandardListener(int headRowNumber, IOverhaulStandardService overhaulStandardService, ImportDataValidLogicService importDataValidLogicService) {
		this.headRowNumber = headRowNumber;
		this.overhaulStandardService = overhaulStandardService;
		this.importDataValidLogicService = importDataValidLogicService;
	}

	@Override
	public void invoke(OverhaulStandardExcelTemplate template, AnalysisContext analysisContext) {
		OverhaulStandard entity = new OverhaulStandard();
		int rowIndex = analysisContext.getCurrentRowNum();
		log.info("====================================读取第{}行==================================================", rowIndex + 1);
		String rowFailReason = validParam(template, entity);
		if (Func.isNotBlank(rowFailReason)) {
			rowFailReason = "excel第" + (rowIndex + 1) + "行：" + rowFailReason;
			failReasonList.add(rowFailReason);
		}
		saveList.add(entity);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
		if (analysisContext.getCurrentRowNum() <= headRowNumber - 1) {
			failReasonList.add("Excel文件数据为空");
		}
		if (Func.isEmpty(failReasonList) && Func.isNotEmpty(saveList)) {
			overhaulStandardService.saveImportData(saveList);
		}
	}

	private String validParam(OverhaulStandardExcelTemplate template, OverhaulStandard entity) {
		List<String> rowFailReason = new ArrayList<>();
		// 校验必填项
		if (Func.isBlank(template.getName())) {
			rowFailReason.add("设备名称不能为空");
		}
		if (Func.isBlank(template.getSn())) {
			rowFailReason.add("SN编号不能为空");
		}
		if (Func.isBlank(template.getMonitorName())) {
			rowFailReason.add("检修部位不能为空");
		}
		if (Func.isBlank(template.getStandardName())) {
			rowFailReason.add("检修标准不能为空");
		}
		if (Func.isBlank(template.getMethodsName())) {
			rowFailReason.add("检修方式不能为空");
		} else {
			try {
				OverhaulMethods overhaulMethods = importDataValidLogicService.getOverhaulMethods(template.getMethodsName());
				if (Func.isNotEmpty(overhaulMethods)) {
					entity.setMethodsId(overhaulMethods.getId());
				}
			} catch (Exception e) {
				e.printStackTrace();
				rowFailReason.add(e.getMessage());
			}
		}
		if (Func.isNotBlank(template.getName()) && Func.isNotBlank(template.getSn())) {
			try {

				IDeviceAccountClient accountClient = SpringUtil.getBean(IDeviceAccountClient.class);
				DeviceAccountVO vo = new DeviceAccountVO();
				vo.setCode(template.getCode());
				DeviceAccountVO account = accountClient.deviceInfoByParams(vo).getData();
// 				EquipmentAccount account = importDataValidLogicService.getEquipmentAccount(template.getCode());
				entity.setEquipmentId(account.getId());
				ICommonClient commonClient = SpringUtil.getBean(ICommonClient.class);
				DeviceMonitorVO monitorVO = new DeviceMonitorVO();
				monitorVO.setDeviceId(account.getId());
				monitorVO.setName(template.getMonitorName());
				R<List<DeviceMonitor>> listR = commonClient.deviceMonitorByParams(monitorVO);
				List<DeviceMonitor> data = listR.getData();
				if (Func.isNotEmpty(data)) {
					DeviceMonitor monitor = data.get(0);
					entity.setMonitorId(monitor.getId());
				}
//				EquipmentMonitor monitor = importDataValidLogicService.getEquipmentMonitor(account.getId(), template.getMonitorName());
			} catch (Exception e) {
				e.printStackTrace();
				rowFailReason.add(e.getMessage());
			}
		}
		entity.setStandard(template.getStandardName());
		return Func.join(rowFailReason, ";");
	}
}
