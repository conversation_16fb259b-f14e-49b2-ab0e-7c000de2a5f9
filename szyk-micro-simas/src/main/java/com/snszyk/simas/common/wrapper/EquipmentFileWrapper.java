/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.common.equipment.entity.DeviceFileCategory;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;
import java.util.Optional;

/**
 * 设备资料表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public class EquipmentFileWrapper extends BaseEntityWrapper<EquipmentFile, EquipmentFileVO> {
	private static ICommonClient commonClient;

	static {
		commonClient = SpringUtil.getBean(ICommonClient.class);
	}

	public static EquipmentFileWrapper build() {
		return new EquipmentFileWrapper();
	}

	@Override
	public EquipmentFileVO entityVO(EquipmentFile equipmentFile) {
		EquipmentFileVO equipmentFileVO = Objects.requireNonNull(BeanUtil.copy(equipmentFile, EquipmentFileVO.class));

		Optional.ofNullable(equipmentFile.getFileCategoryId())
			.map(commonClient::getEquipmentFileCategory)
			.map(R::getData)
			.map(DeviceFileCategory::getName)
			.ifPresent(equipmentFileVO::setFileCategoryName);

		User createUser = UserCache.getUser(equipmentFile.getCreateUser());
		if (Func.isNotEmpty(createUser)) {
			equipmentFileVO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(equipmentFile.getUpdateUser());
		if (Func.isNotEmpty(updateUser)) {
			equipmentFileVO.setUpdateUserName(updateUser.getRealName());
		}
		return equipmentFileVO;
	}

}
