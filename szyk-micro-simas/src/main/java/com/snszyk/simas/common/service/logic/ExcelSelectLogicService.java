package com.snszyk.simas.common.service.logic;

import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.DictBiz;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Service
public class ExcelSelectLogicService {

	public String[] selectByCode(String code) {
		List<DictBiz> list = DictBizCache.getList(code);
		if (list != null){
			return list.stream().map(DictBiz::getDictValue).toArray(String[]::new);
		}
		return new String[0];
	}

}
