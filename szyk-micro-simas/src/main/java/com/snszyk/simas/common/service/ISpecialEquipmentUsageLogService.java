package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.entity.SpecialEquipmentUsageLog;
import com.snszyk.simas.common.vo.SpecialEquipmentUsagePageVo;

/**
 * ClassName: ISpecialEquipmentUsageLogService
 * Package: com.snszyk.simas.service
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:28
 */
public interface ISpecialEquipmentUsageLogService extends BaseService<SpecialEquipmentUsageLog> {

	IPage<SpecialEquipmentUsageLog> pageList(SpecialEquipmentUsagePageVo v);
}
