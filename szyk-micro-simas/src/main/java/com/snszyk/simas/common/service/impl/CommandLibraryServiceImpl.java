/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.ribbon.utils.BeanUtil;
import com.snszyk.simas.common.dto.CommandLibraryDto;
import com.snszyk.simas.common.entity.CommandLibrary;
import com.snszyk.simas.common.mapper.CommandLibraryMapper;
import com.snszyk.simas.common.service.ICommandLibraryService;
import com.snszyk.simas.common.vo.CommandLibraryPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 指令库 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@AllArgsConstructor
@Service
public class CommandLibraryServiceImpl extends BaseServiceImpl<CommandLibraryMapper, CommandLibrary> implements ICommandLibraryService {

	private final BeanUtil beanUtil;

	/**
	 * 名称校验
	 */
	@Override
	public void checkName(Long id, String name) {
		Integer count = lambdaQuery().eq(CommandLibrary::getAction, name).ne(id != null, CommandLibrary::getId, id).count();
		if (count > 0) {
			throw new ServiceException("门类名称已存在");
		}
	}

	/**
	 * 分页查询
	 */
	@Override
	public IPage<CommandLibraryDto> pageList(CommandLibraryPageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public CommandLibraryDto detail(Long id) {
		return baseMapper.detail(id);
	}

	@Override
	public CommandLibraryDto getPageRoute(String commandRoute) {
		return baseMapper.getPageRoute(commandRoute);

	}

}
