package com.snszyk.simas.common.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.equipment.entity.DeviceAccount;
import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
import com.snszyk.common.equipment.feign.FeignPage;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.tenant.annotation.TenantIgnore;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.entity.EquipmentStatusRecord;
import com.snszyk.simas.common.service.IEquipmentStatusRecordService;
import com.snszyk.simas.common.service.logic.EquipmentStatusRecordLogicService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.undertow.security.idm.Account;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName: EquipmentStatusRecordSchedule
 * Package: com.snszyk.simas.schedule
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/2/11 10:26
 */
@Slf4j
@AllArgsConstructor
@Service
public class EquipmentStatusRecordSchedule {

	private final IEquipmentStatusRecordService equipmentStatusRecordService;
	private final EquipmentStatusRecordLogicService equipmentStatusRecordLogicService;

	private final IDeviceAccountClient accountClient;

	/**
	 * 每天23:30
	 * 保存设备状态记录
	 */
//	@Scheduled(cron = "0 30 23 * * ?")
	@TenantIgnore
	@XxlJob(value = "equipmentStatusRecordJobHandler")
	public ReturnT<String> saveRecord(String param) {
		log.info("开始保存设备状态记录");
		final LocalDate localDate = LocalDate.now();

		final int pageSize = 500;
		int current = 1;

		while (true) {
			// 分页查询设备列表
//			Page<DeviceAccount> equipmentAccountPage = equipmentAccountMapper.selectPage(new Page<>(current, pageSize),
//				Wrappers.lambdaQuery(DeviceAccount.class));
			DeviceAccountPageVO vo = new DeviceAccountPageVO();
			R<FeignPage<DeviceAccountVO>> feignPageR = accountClient.devicePageListScope(vo, current, pageSize, EquipmentDataScopeEnum.ALL_SCOPE.getCode());
			FeignPage<DeviceAccountVO> equipmentPageData = feignPageR.getData();
			if (ObjectUtil.isEmpty(equipmentPageData) || ObjectUtil.isEmpty(equipmentPageData.getRecords())) {
				break;
			}

			List<DeviceAccountVO> equipmentAccountList = equipmentPageData.getRecords();

			if (ObjectUtil.isEmpty(equipmentAccountList)) {
				break;
			}
			// 转换并保存当前页数据
			List<EquipmentStatusRecord> recordList = equipmentAccountList.stream()
				.map(equipmentAccount -> equipmentStatusRecordLogicService.convertToEquipmentStatusRecord(equipmentAccount,localDate))
				.collect(Collectors.toList());

			if (ObjectUtil.isNotEmpty(recordList)) {
				equipmentStatusRecordService.saveBatch(recordList);
			}
			// 判断是否还有下一页
//			if (equipmentAccountPage.hasNext()) {
			if (Func.isNotEmpty(!equipmentAccountList.isEmpty())) {
				current++;
			} else {
				break;
			}
		}
		log.info("保存设备状态记录完成");
		XxlJobLogger.log("################保存设备状态记录完成-END-################");
		return ReturnT.SUCCESS;
	}
}
