/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ComponentMaterialStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ComponentMaterialStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("备品备件名称")
	private String name;

	@ExcelProperty("备品备件类型")
	private String model;

	@ExcelProperty("消耗方式")
	private String bizModuleName;

	@ExcelProperty("消耗数量")
	private Integer count;


}
