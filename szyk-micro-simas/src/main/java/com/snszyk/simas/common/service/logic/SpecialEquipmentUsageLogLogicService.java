package com.snszyk.simas.common.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.service.ISpecialEquipmentUsageLogService;
import com.snszyk.simas.common.dto.SpecialEquipmentUsageLogDto;
import com.snszyk.simas.common.entity.SpecialEquipmentUsageLog;
import com.snszyk.simas.common.vo.SpecialEquipmentUsageLogVo;
import com.snszyk.simas.common.vo.SpecialEquipmentUsagePageVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;

/**
 * ClassName: SpecialEquipmentUsageLogLogicService
 * Package: com.snszyk.simas.service.logic
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:27
 */
@Service
@AllArgsConstructor
@Slf4j
public class SpecialEquipmentUsageLogLogicService {
	private final ISpecialEquipmentUsageLogService specialEquipmentUsageLogService;

	/**
	 * 保存
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean save(SpecialEquipmentUsageLogVo v) {
		return specialEquipmentUsageLogService.save(BeanUtil.copy(v, SpecialEquipmentUsageLog.class));
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SpecialEquipmentUsageLogDto> page(SpecialEquipmentUsagePageVo v) {
		// 参数解析处理
		this.preparePage(v);
		// 分页查询
		IPage<SpecialEquipmentUsageLog> page = specialEquipmentUsageLogService.pageList(v);
		if (ObjectUtil.isEmpty(v) || ObjectUtil.isEmpty(page.getRecords())) {
			return v;
		}
		return page.convert(e -> BeanUtil.copy(e, SpecialEquipmentUsageLogDto.class));
	}

	/**
	 * 查询前的预处理
	 *
	 * @param v
	 */
	private void preparePage(SpecialEquipmentUsagePageVo v) {
		// 计算查询开始时间 1-近30天，2-近180天，3-近一年"
		if (v.getTimeType() != null) {
			final LocalDate localDate = LocalDate.now();
			switch (v.getTimeType()) {
				case THIRTY_DAYS:
					v.setStartDate(localDate.minusDays(30));
					break;
				case ONE_HUNDRED_EIGHTY_DAYS:
					v.setStartDate(localDate.minusDays(180));
					break;
				case ONE_YEAR:
					v.setStartDate(localDate.minusYears(1));
			}
			v.setEndDate(localDate);
		}

	}

	/**
	 * 导出
	 *
	 * @param v
	 * @param response
	 */
	public void export(SpecialEquipmentUsagePageVo v, HttpServletResponse response) {
		v.setSize(-1L);
		final IPage<SpecialEquipmentUsageLogDto> page = this.page(v);
		ExcelUtil.export(response, "特种设备使用记录" + DateUtil.time(), "使用记录", page.getRecords(), SpecialEquipmentUsageLogDto.class);
	}
}
