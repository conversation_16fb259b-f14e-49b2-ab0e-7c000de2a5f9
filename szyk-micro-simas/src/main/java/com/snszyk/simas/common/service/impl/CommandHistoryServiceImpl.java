/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.common.dto.CommandHistoryDto;
import com.snszyk.simas.common.entity.CommandHistory;
import com.snszyk.simas.common.mapper.CommandHistoryMapper;
import com.snszyk.simas.common.service.ICommandHistoryService;
import com.snszyk.simas.common.vo.CommandHistoryPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指令历史 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@AllArgsConstructor
@Service
public class CommandHistoryServiceImpl extends BaseServiceImpl<CommandHistoryMapper, CommandHistory> implements ICommandHistoryService {


	/**
	 * 分页查询
	 */
	@Override
	public IPage<CommandHistoryDto> pageList(CommandHistoryPageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public CommandHistoryDto detail(Long id) {
		return baseMapper.detail(id);
	}

    @Override
    public List<CommandHistoryDto> commandHistoryList(Long userId) {
		List<CommandHistory> list = this.lambdaQuery().eq(CommandHistory::getUserId, userId).orderByDesc(CommandHistory::getEffectiveTime).last("limit 5").list();
		List<CommandHistoryDto> copy = BeanUtil.copy(list, CommandHistoryDto.class);
		for (CommandHistoryDto commandHistoryDto : copy) {
			String commandRoute = commandHistoryDto.getCommandRoute();
			String[] split = commandRoute.split("\\|");
			String secCommand = split[1];
			//三级指令
			String thriCommand = split[2];
			commandHistoryDto.setHistoryCommand(secCommand+"-"+thriCommand+":"+commandHistoryDto.getInputContent());
		}
		return copy;
	}

}
