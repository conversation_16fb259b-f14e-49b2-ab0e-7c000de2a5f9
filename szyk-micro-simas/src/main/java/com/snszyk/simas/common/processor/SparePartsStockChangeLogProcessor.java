package com.snszyk.simas.common.processor;

import com.alibaba.fastjson.JSON;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.enums.SparePartsOrderModuleEnum;
import com.snszyk.simas.spare.enums.SparePartsStockChangeActionEnum;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import com.snszyk.simas.spare.vo.SparePartsInterimInventoryVO;
import com.snszyk.simas.spare.vo.SparePartsOutboundItemVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 备品备件库存变更日志生成器
 * ClassName: SparePartsStockChangeLogGenerator
 * Package: com.snszyk.simas.handle
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 17:13
 */
public class SparePartsStockChangeLogProcessor {
	/**
	 * 出入库日志格式
	 * 示例：
	 * 2024/12/12 8：00：00张三完成了入库，入库N，当前库存M。
	 * 2024/12/12 8：00：00张三完成了出库，出库N，当前库存M。
	 */
	private static final String STORAGE_IN_OR_OUT_LOG_FORMAT = "%s%s完成了%s，%s%s，当前库存%s";
	/**
	 * 盘点日志格式
	 * <p>
	 * 2024/12/12 8：00：00张三完成了盘点，盘点后库存N，盘点前库存M；
	 */
	private static final String INVENTORY_INVENTORY_LOG_FORMAT = "%s%s完成了盘点，盘点后库存%s，盘点前库存%s";
	/**
	 * 临时盘点日志
	 * 2024/12/12 8：00：00张三完成了临时盘点，盘点后库存N，盘点前库存M；
	 */
	private static final String TEMPORARY_INVENTORY_LOG_FORMAT = "%s%s完成了临时盘点，盘点后库存%s，盘点前库存%s；盘点原因“%s”";

	private static ISparePartsStockService stockService;
	// private static ISparePartsAccountService sparePartsAccountService;
	private static IBizLogService bizLogService;

	static {
		stockService = SpringUtil.getBean(ISparePartsStockService.class);
		bizLogService = SpringUtil.getBean(IBizLogService.class);
	}

	/**
	 * 保存盘点日志
	 */
	// public static Boolean saveInventoryLog(List<SparePartsInventory> inventoryList) {
	// 	if (ObjectUtil.isEmpty(inventoryList)) {
	// 		throw new BusinessException("获取日志失败！entity不可为空");
	// 	}
	// 	// 获取当前时间
	// 	final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
	// 	// 获取操作人名，默认为"系统"
	// 	String realName = Optional.ofNullable(AuthUtil.getUserId())
	// 		.map(UserCache::getUser)
	// 		.map(User::getRealName)
	// 		.orElse("系统");
	// 	// 获取盘点数据map，key-备品备件台账id，value-盘点后数据
	// 	final Map<Long, Integer> accountIdAmoutMap = ListUtil.toMap(inventoryList, SparePartsInventory::getAccountId, SparePartsInventory::getAmount);
	// 	// 查询备品备件台账信息
	// 	final List<SparePartsAccount> sparePartsAccountList = sparePartsAccountService.listByIds(accountIdAmoutMap.keySet());
	// 	if (ObjectUtil.isEmpty(sparePartsAccountList)) {
	// 		throw new BusinessException("获取备品备件台账信息失败！");
	// 	}
	// 	final List<BizLog> bizLogList = sparePartsAccountList.stream()
	// 		.map(sparePartsAccount -> {
	// 			final String content = String.format(INVENTORY_INVENTORY_LOG_FORMAT,
	// 				localDateTimeStr, realName, accountIdAmoutMap.get(sparePartsAccount.getId()), sparePartsAccount.getCurrentAmount());
	//
	// 			BizLog bizLog = new BizLog();
	// 			bizLog.setBizId(sparePartsAccount.getId())
	// 				.setModule(SystemModuleEnum.SPARE_STOCK_CHANGE.getCode())
	// 				.setContent(content)
	// 				.setOperateUser(AuthUtil.getUserId())
	// 				.setOperateTime(DateUtil.now());
	//
	// 			return bizLog;
	// 		}).collect(Collectors.toList());
	// 	return bizLogService.saveBatch(bizLogList);
	// }

	/**
	 * 保存临时盘点日志
	 */
	public static Boolean saveTemporaryInventoryLog(SparePartsInterimInventoryVO vo) {
		if (ObjectUtil.isEmpty(vo)) {
			throw new BusinessException("获取日志失败！vo不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 获取备品备件库存信息
		final SparePartsStockDTO stockDTO = stockService.fetchById(vo.getStockId());
		if (ObjectUtil.isEmpty(stockDTO)) {
			throw new BusinessException("获取备品备件库存信息失败！");
		}
		final String content = String.format(TEMPORARY_INVENTORY_LOG_FORMAT,
			localDateTimeStr, realName, vo.getCurrentAmount(), stockDTO.getCurrentQuantity(), vo.getRemark());

		BizLog bizLog = new BizLog();
		bizLog.setBizId(stockDTO.getId())
			.setModule(SystemModuleEnum.SPARE_STOCK_CHANGE.getCode())
			.setBizInfo(JSON.toJSONString(vo))
			.setContent(content)
			.setOperateUser(AuthUtil.getUserId())
			.setOperateTime(DateUtil.now());
		return bizLogService.save(bizLog);
	}

	/**
	 * 保存入库日志
	 */
	public static Boolean saveInBoundLog(List<SparePartsInboundItemVO> itemList, Long warehouseId) {
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("获取日志失败！itemList不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 获取备品备件字典id
		final List<Long> dictIds = ListUtil.map(itemList, SparePartsInboundItemVO::getDictId);
		// 查询库存信息
		final List<SparePartsStockDTO> stockDTOList = stockService.listBy(warehouseId, dictIds);
		// 当前库存map；key-字典id，value-当前库存
		final Map<Long, BigDecimal> currentQuantityMap = stockDTOList.stream()
			.collect(Collectors.toMap(SparePartsStockDTO::getDictId, SparePartsStockDTO::getCurrentQuantity, (v1, v2) -> v1));

		final List<BizLog> bizLogList = itemList.stream()
			.map(itemVO -> {
				BizLog bizLog = new BizLog();
				bizLog.setBizId(null)
					.setModule(SystemModuleEnum.SPARE_STOCK_CHANGE.getCode())
					.setContent(getStorageInOrOutLog(SparePartsOrderModuleEnum.IN_BOUND, localDateTimeStr, realName, itemVO.getInboundQuantity(), currentQuantityMap.get(itemVO.getDictId())))
					.setOperateUser(AuthUtil.getUserId())
					.setOperateTime(DateUtil.now());
				return bizLog;
			}).collect(Collectors.toList());
		return bizLogService.saveBatch(bizLogList);
	}

	/**
	 * 保存出库日志
	 */
	public static Boolean saveOutBoundLog(List<SparePartsOutboundItemVO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("获取日志失败！itemList不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 获取备品备件库存id
		final List<Long> stockIds = ListUtil.map(itemList, SparePartsOutboundItemVO::getStockId);
		if (ObjectUtil.isEmpty(stockIds)) {
			throw new BusinessException("获取备品备件库存信息失败！");
		}
		// 查询库存信息
		final List<SparePartsStockDTO> stockDTOList = stockService.listByIds(stockIds);
		// 当前库存map；key-备品备件id，value-当前库存
		final Map<Long, BigDecimal> currentQuantityMap = stockDTOList.stream()
			.collect(Collectors.toMap(SparePartsStockDTO::getId, SparePartsStockDTO::getCurrentQuantity, (v1, v2) -> v1));

		final List<BizLog> bizLogList = itemList.stream()
			.map(itemVO -> {
				BizLog bizLog = new BizLog();
				bizLog.setBizId(itemVO.getStockId())
					.setModule(SystemModuleEnum.SPARE_STOCK_CHANGE.getCode())
					.setContent(getStorageInOrOutLog(SparePartsOrderModuleEnum.OUT_BOUND, localDateTimeStr, realName, itemVO.getOutboundQuantity(), currentQuantityMap.get(itemVO.getStockId())))
					.setOperateUser(AuthUtil.getUserId())
					.setOperateTime(DateUtil.now());
				return bizLog;
			}).collect(Collectors.toList());
		return bizLogService.saveBatch(bizLogList);

	}

	/**
	 * 获取出入库盘点日志
	 */
	public static String getStorageInOrOutLog(SparePartsOrderModuleEnum module, String localDateTimeStr, String realName, BigDecimal inOrOutQuantity, BigDecimal currentQuantity) {
		if (ObjectUtil.isEmpty(module) || ObjectUtil.isEmpty(localDateTimeStr) || ObjectUtil.isEmpty(realName) ||
			ObjectUtil.isEmpty(inOrOutQuantity) || ObjectUtil.isEmpty(currentQuantity)) {
			throw new BusinessException("获取日志失败！localDateTimeStr或realName或inOrOutQuantity或currentQuantity不可为空");
		}
		SparePartsStockChangeActionEnum actionEnum;
		if (SparePartsOrderModuleEnum.IN_BOUND == module) {
			actionEnum = SparePartsStockChangeActionEnum.IN;
		} else if (SparePartsOrderModuleEnum.OUT_BOUND == module) {
			actionEnum = SparePartsStockChangeActionEnum.OUT;
		} else {
			throw new BusinessException("获取日志失败！module不可为空");
		}
		return String.format(STORAGE_IN_OR_OUT_LOG_FORMAT, localDateTimeStr, realName, actionEnum.getDesc(), actionEnum.getDesc(), inOrOutQuantity, currentQuantity);
	}

}
