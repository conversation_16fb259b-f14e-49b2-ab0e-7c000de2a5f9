/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceCategory;
import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
import com.snszyk.common.equipment.feign.FeignPage;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.*;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.simas.common.IOrderField;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.common.dto.OrderStatisticsDTO;
import com.snszyk.simas.common.enums.*;
import com.snszyk.simas.common.excel.*;
import com.snszyk.simas.common.service.IComponentMaterialService;
import com.snszyk.simas.common.util.TimeTypeUtil;
import com.snszyk.simas.common.vo.OrderStatisticsGroupByStatisticsDimensionVO;
import com.snszyk.simas.common.vo.OrderStatisticsVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.fault.service.IFaultDefectService;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
import com.snszyk.simas.inspect.service.IInspectOrderService;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
import com.snszyk.simas.maintain.service.IMaintainOrderService;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.dto.RepairDurationDTO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.mapper.RepairMapper;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IRepairRecordService;
import com.snszyk.simas.overhaul.service.IRepairService;
import com.snszyk.simas.overhaul.vo.RepairDurationPageVO;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.feign.ISysClient;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 统计报表相关 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/09 09:16
 **/
@Service
@AllArgsConstructor
@Slf4j
public class StatisticalReportLogicService {
	private final IInspectOrderService inspectOrderService;
	private final IMaintainOrderService maintainOrderService;
	private final IFaultDefectService faultDefectService;
	private final IRepairService repairService;
	private final InspectOrderMapper inspectOrderMapper;
	private final MaintainOrderMapper maintainOrderMapper;
	private final RepairMapper repairMapper;
	private final IComponentMaterialService componentMaterialService;
	private final ILubricateOrderService lubricateOrderService;
	private final IRepairRecordService repairRecordService;
	private final IOverhaulOrderService overhaulOrderService;
	private final ISysClient sysClient;
	private final IUserClient userClient;

	private final LubricateOrderMapper lubricateOrderMapper;

	private final IDeviceAccountClient deviceAccountClient;

	/**
	 * 点巡检统计
	 *
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.vo.InspectOrderVO>
	 * <AUTHOR>
	 * @date 2024/9/9 9:38
	 */
	public IPage<InspectOrderDTO> inspectStatistics(IPage<InspectOrderDTO> page, StatisticSearchVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		List<Long> deptIdList = new ArrayList<>();
		if (Func.isNotEmpty(vo.getDeptId())) {
			List<Long> deptIds = Func.toLongList(vo.getDeptId());
			deptIdList.addAll(deptIds);
			Func.toLongList(vo.getDeptId()).forEach(deptId -> {
				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
				if (Func.isNotEmpty(deptChildIds)) {
					deptIdList.addAll(deptChildIds);
				}
			});
			vo.setDeptIdList(deptIdList.stream().distinct().collect(Collectors.toList()));
		}
		return inspectOrderService.statisticalReport(page, vo);
	}

	/**
	 * 导出点检工单
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.InspectOrderStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:07
	 */
	public List<InspectOrderStatisticsExcel> exportInspectOrder(StatisticSearchVO vo) {
		IPage<InspectOrderDTO> page = this.inspectStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}

		return BeanUtil.copy(page.getRecords(), InspectOrderStatisticsExcel.class);
	}

	/**
	 * 导出设备点巡检统计
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.EquipmentInspectStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:35
	 */
	public List<EquipmentInspectStatisticsExcel> exportEquipmentInspect(StatisticSearchVO vo) {
		IPage<EquipmentStatisticsDTO> page = this.equipmentStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return page.getRecords()
			.stream()
			.map(e -> {
				EquipmentInspectStatisticsExcel excel = BeanUtil.copy(e, EquipmentInspectStatisticsExcel.class);
				excel.setCompleteRate(e.getCompleteRate() + StringPool.PERCENT);
				excel.setAbnormalRate(e.getAbnormalRate() + StringPool.PERCENT);
				return excel;
			}).collect(Collectors.toList());
	}

	/**
	 * 按设备统计
	 *
	 * @param page
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.dto.EquipmentStatisticsDTO>
	 * <AUTHOR>
	 * @date 2024/9/9 13:51
	 */
	public IPage<EquipmentStatisticsDTO> equipmentStatistics(IPage<EquipmentStatisticsDTO> page, StatisticSearchVO vo) {
		return statisticalReport(page, vo);
	}

	/**
	 * 保养统计
	 *
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.vo.MaintainOrderVO>
	 * <AUTHOR>
	 * @date 2024/9/9 9:38
	 */
	public IPage<MaintainOrderDTO> maintainStatistics(IPage<MaintainOrderDTO> page, StatisticSearchVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		List<Long> deptIdList = new ArrayList<>();
		if (Func.isNotEmpty(vo.getDeptId())) {
			List<Long> deptIds = Func.toLongList(vo.getDeptId());
			deptIdList.addAll(deptIds);
			Func.toLongList(vo.getDeptId()).forEach(deptId -> {
				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
				if (Func.isNotEmpty(deptChildIds)) {
					deptIdList.addAll(deptChildIds);
				}
			});
			vo.setDeptIdList(deptIdList.stream().distinct().collect(Collectors.toList()));
		}
		return maintainOrderService.statisticalReport(page, vo);
	}

	/**
	 * 导出保养工单
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.InspectOrderStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:25
	 */
	public List<MaintainOrderStatisticsExcel> exportMaintainOrder(StatisticSearchVO vo) {
		IPage<MaintainOrderDTO> page = maintainStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(page.getRecords(), MaintainOrderStatisticsExcel.class);
		// return maintainOrderService.exportStatisticalReport(vo);
	}

	/**
	 * 导出设备保养统计
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.EquipmentMaintainStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:35
	 */
	public List<EquipmentMaintainStatisticsExcel> exportEquipmentMaintain(StatisticSearchVO vo) {
		IPage<EquipmentStatisticsDTO> page = this.equipmentStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return page.getRecords().stream()
			.map(e -> {
				EquipmentMaintainStatisticsExcel excel = BeanUtil.copy(e, EquipmentMaintainStatisticsExcel.class);
				excel.setCompleteRate(e.getCompleteRate() + StringPool.PERCENT);
				excel.setAbnormalRate(e.getAbnormalRate() + StringPool.PERCENT);
				return excel;
			}).collect(Collectors.toList());
	}

	/**
	 * 维修单统计
	 *
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.dto.RepairDTO>
	 * <AUTHOR>
	 * @date 2024/9/9 9:39
	 */
	public IPage<RepairDTO> repairStatistics(IPage<RepairDTO> page, StatisticSearchVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		return repairService.statisticalReport(page, vo);
	}

	/**
	 * 导出维修工单
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.RepairStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:07
	 */
	public List<RepairStatisticsExcel> exportRepair(StatisticSearchVO vo) {
		IPage<RepairDTO> page = repairStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}

		return page.getRecords()
			.stream()
			.map(e -> {
				RepairStatisticsExcel excel = BeanUtil.copy(e, RepairStatisticsExcel.class);
				if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(e.getBizType())) {
					excel.setUserName(e.getReceiveUserName());
				} else {
					excel.setUserName(e.getFollowUserName());
				}
				return excel;
			}).collect(Collectors.toList());
	}

	/**
	 * 导出设备维修统计
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.EquipmentRepairStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:35
	 */
	public List<EquipmentRepairStatisticsExcel> exportEquipmentRepair(StatisticSearchVO vo) {
		IPage<EquipmentStatisticsDTO> page = this.equipmentStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return page.getRecords()
			.stream()
			.map(e -> {
				EquipmentRepairStatisticsExcel excel = BeanUtil.copy(e, EquipmentRepairStatisticsExcel.class);
				excel.setCompleteRate(e.getCompleteRate() + StringPool.PERCENT);
				return excel;
			}).collect(Collectors.toList());
	}

	/**
	 * 故障缺陷统计
	 *
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.vo.FaultDefectVO>
	 * <AUTHOR>
	 * @date 2024/9/9 9:39
	 */
	public IPage<FaultDefectDTO> faultDefectStatistics(IPage<FaultDefectDTO> page, StatisticSearchVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		return repairService.calculateStatisticsByFaultDefect(page, vo);
	}

	/**
	 * 导出故障缺陷统计
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.FaultDefectStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/9 15:07
	 */
	public List<FaultDefectStatisticsExcel> exportFaultDefect(StatisticSearchVO vo) {
		IPage<FaultDefectDTO> page = this.faultDefectStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return page.getRecords()
			.stream()
			.map(e -> {
				FaultDefectStatisticsExcel excel = BeanUtil.copy(e, FaultDefectStatisticsExcel.class);
				excel.setAverageTimeTake(e.getAverageTimeTake().toString());
				excel.setCompleteRate(e.getCompleteRate() + StringPool.PERCENT);
				return excel;
			}).collect(Collectors.toList());
	}

	/**
	 * 备品备件消耗统计
	 *
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.vo.ComponentMaterialVO>
	 * <AUTHOR>
	 * @date 2024/9/11 9:39
	 */
	public IPage<ComponentMaterialVO> componentMaterialStatistics(IPage<ComponentMaterialVO> page, StatisticSearchVO vo) {
		return componentMaterialService.statisticalReport(page, vo);
	}

	/**
	 * 导出备品备件耗材
	 *
	 * @param vo
	 * @return java.util.List<com.snszyk.simas.excel.FaultDefectStatisticsExcel>
	 * <AUTHOR>
	 * @date 2024/9/11 18:07
	 */
	public void exportComponentMaterial(StatisticSearchVO vo, HttpServletResponse response) {
		final IPage<ComponentMaterialVO> page = this.componentMaterialStatistics(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return;
		}
		final List<ComponentMaterialStatisticsExcel> list = BeanUtil.copy(page.getRecords(), ComponentMaterialStatisticsExcel.class);
		ExcelUtil.export(response, "备件损耗统计列表" + com.snszyk.core.tool.utils.DateUtil.time(), "备件损耗", list, ComponentMaterialStatisticsExcel.class);
	}

	/**
	 * 润滑工单统计
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	public IPage<LubricateOrderDTO> lubricateOrderStatistics(IPage<LubricateOrderDTO> page, StatisticSearchVO vo) {
		List<Long> deptIdList = new ArrayList<>();
		if (Func.isNotEmpty(vo.getDeptId())) {
			List<Long> deptIds = Func.toLongList(vo.getDeptId());
			deptIdList.addAll(deptIds);
			Func.toLongList(vo.getDeptId()).forEach(deptId -> {
				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
				if (Func.isNotEmpty(deptChildIds)) {
					deptIdList.addAll(deptChildIds);
				}
			});
			vo.setDeptIdList(deptIdList.stream().distinct().collect(Collectors.toList()));
		}
		LubricateOrderVO orderVO = new LubricateOrderVO();
		orderVO.setQueryStartDate(vo.getStartDate());
		orderVO.setQueryEndDate(vo.getEndDate());
		orderVO.setChargeDeptIdList(vo.getDeptIdList());
		orderVO.setStatus(vo.getStatus());
		if (vo.getQueryDate() != null) {
			// 近一年
			if (vo.getQueryDate() == 0) {
				orderVO.setStartCreateTime(DateUtil.getOneYearBeforeLocalDateTime());
			}
			// 近30天
			if (vo.getQueryDate() == 1) {
				orderVO.setStartCreateTime(DateUtil.getThirtyDayBeforeLocalDateTime());
			}
			// 近7天
			if (vo.getQueryDate() == 2) {
				orderVO.setStartCreateTime(DateUtil.getSevenDayBeforeLocalDateTime());
			}
			// 今天
			if (vo.getQueryDate() == 3) {
				orderVO.setStartCreateTime(LocalDate.now().atStartOfDay());
			}
			orderVO.setEndCreateTime(LocalDateTime.now());
		}
		return lubricateOrderService.page(page, orderVO);
	}

	/**
	 * 导出润滑工单统计
	 *
	 * @param statisticSearch
	 * @param response
	 */
	public void exportLubricateOrderStatistics(StatisticSearchVO statisticSearch, HttpServletResponse response) {
		IPage<LubricateOrderDTO> page = lubricateOrderStatistics(new Page<>(1, -1L), statisticSearch);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return;
		}
		List<LubricateOrderStatisticsExcel> list = page.getRecords().stream()
			.map(e -> {
				LubricateOrderStatisticsExcel excel = BeanUtil.copy(e, LubricateOrderStatisticsExcel.class);
				return excel;
			}).collect(Collectors.toList());
		ExcelUtil.export(response, "设备润滑工单统计" + com.snszyk.core.tool.utils.DateUtil.time(), "工单统计", list, LubricateOrderStatisticsExcel.class);
	}

	/**
	 * 导出设备润滑统计
	 *
	 * @param statisticSearch
	 * @param response
	 */
	public void exportEquipmentLubricateStatistics(StatisticSearchVO statisticSearch, HttpServletResponse response) {
		IPage<EquipmentStatisticsDTO> page = new Page<>(1, -1L);
		page = statisticalReport(page, statisticSearch);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return;
		}
		List<LubricateStatisticsExcel> list = page.getRecords()
			.stream()
			.map(d -> {
				LubricateStatisticsExcel excel = BeanUtil.copy(d, LubricateStatisticsExcel.class);
				excel.setCompleteRate(d.getCompleteRate() + "%");
				return excel;
			}).collect(Collectors.toList());
		ExcelUtil.export(response, "设备润滑工单统计", "工单统计", list, LubricateStatisticsExcel.class);
	}

	public IPage<EquipmentStatisticsDTO> statisticalReport(IPage<EquipmentStatisticsDTO> page, StatisticSearchVO search) {
		DeviceAccountPageVO deviceAccountPageVO = new DeviceAccountPageVO();
		List<Long> deptIdList = new ArrayList<>();
		if (Func.isNotEmpty(search.getDeptId())) {
			List<Long> deptIds = Func.toLongList(search.getDeptId());
			deptIdList.addAll(deptIds);
			Func.toLongList(search.getDeptId()).forEach(deptId -> {
				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
				if (Func.isNotEmpty(deptChildIds)) {
					deptIdList.addAll(deptChildIds);
				}
			});
			search.setDeptIdList(deptIdList.stream().distinct().collect(Collectors.toList()));
			deviceAccountPageVO.setUseDeptList(search.getDeptIdList());
		}
		if (Func.isNotEmpty(search.getStartDate())) {
			search.setStartDate(search.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(search.getEndDate())) {
			search.setEndDate(search.getEndDate() + DateUtils.DAY_END_TIME);
		}
		R<FeignPage<DeviceAccountVO>> equipmentPageData = deviceAccountClient.devicePageListScope(deviceAccountPageVO,
			Func.toInt(page.getCurrent()), Func.toInt(page.getSize()), EquipmentDataScopeEnum.ALL_SCOPE.getCode());
		FeignPage<DeviceAccountVO> equipmentPage = equipmentPageData.getData();
		if (ObjectUtil.isEmpty(equipmentPage) || ObjectUtil.isEmpty(equipmentPage.getRecords())) {
			return page;
		}
		List<EquipmentStatisticsDTO> list = null;
		List<DeviceAccountVO> equipmentList = equipmentPage.getRecords();
		log.info("返回的设备数据个数：============{}", equipmentList.size());
		Map<Long, DeviceAccountVO> idEquipmentMap = ListUtil.toMap(equipmentList, DeviceAccountVO::getId, Function.identity());
		log.info("设备数据Map：==========={}", idEquipmentMap);
		List<Long> ids = equipmentList.stream().map(DeviceAccountVO::getId).collect(Collectors.toList());
		search.setEquipmentIds(ids);
		if (SystemModuleEnum.INSPECT_ORDER == SystemModuleEnum.getByCode(search.getBizModule())) {
			list = this.orderStatistics(search, idEquipmentMap, SystemModuleEnum.INSPECT_ORDER);
			//list = inspectOrderService.inspectStatistics(search, idEquipmentMap);
		}
		if (SystemModuleEnum.MAINTAIN_ORDER == SystemModuleEnum.getByCode(search.getBizModule())) {
			list = this.orderStatistics(search, idEquipmentMap, SystemModuleEnum.MAINTAIN_ORDER );
			//list = maintainOrderService.maintainStatistics(search, idEquipmentMap);
		}
		if (SystemModuleEnum.LUBRICATE_ORDER == SystemModuleEnum.getByCode(search.getBizModule())) {
			list = this.lubricateStatistics(search, idEquipmentMap);
		}
		if (SystemModuleEnum.REPAIR == SystemModuleEnum.getByCode(search.getBizModule())) {
			list = this.orderStatistics(search, idEquipmentMap, SystemModuleEnum.REPAIR);
		}
		final Page<EquipmentStatisticsDTO> result = BeanUtil.copy(equipmentPage, Page.class);
		result.setRecords(list);
		return result;
	}

	/**
	 * @param search
	 * @param equipmentMap
	 * @param systemModuleEnum
	 * @return java.util.List<com.snszyk.simas.common.dto.EquipmentStatisticsDTO>
	 * <AUTHOR>
	 * @date 2025/3/28 17:03
	 */
	private List<EquipmentStatisticsDTO> orderStatistics(StatisticSearchVO search, Map<Long, DeviceAccountVO> equipmentMap, SystemModuleEnum systemModuleEnum) {
		if (ObjectUtil.isEmpty(systemModuleEnum)) {
			throw new BusinessException("系统模块不可为空！");
		}
		if (ObjectUtil.isEmpty(equipmentMap)) {
			return Collections.emptyList();
		}
		List<EquipmentStatisticsDTO> list = Collections.emptyList();
		List<EquipmentStatisticsDTO> result = new ArrayList<>();
		equipmentMap.forEach((equipmentId, equipmentAccount) -> {
			// 初始化
			EquipmentStatisticsDTO dto = new EquipmentStatisticsDTO()
				.setId(equipmentAccount.getId())
				.setCount(0)
				.setCompleteCount(0)
				.setUnfinishedCount(0)
				.setAbnormalCount(0)
				.setCompleteRate(BigDecimal.ZERO)
				.setAbnormalRate(BigDecimal.ZERO)
				.setName(equipmentAccount.getName())
				.setSn(equipmentAccount.getSn())
				.setUseDept(equipmentAccount.getUseDept());
			// 分类名称
			Optional.ofNullable(equipmentAccount.getCategoryId())
				.map(CommonCache::getEquipmentCategory)
				.map(DeviceCategory::getCategoryName)
				.ifPresent(dto::setCategoryName);
			// 使用部门
			if (Func.isNotEmpty(dto.getUseDept())) {
				Optional.ofNullable(dto.getUseDept())
					.map(SysCache::getDept)
					.map(Dept::getDeptName)
					.ifPresent(deptName -> dto.setUseDeptName(deptName));
			}
			result.add(dto);
		});
		// 查询设备点检工单
		switch (SystemModuleEnum.getByCode(search.getBizModule())) {
			case INSPECT_ORDER:
				list = inspectOrderMapper.statisticsByEquipment(search);
				break;
			case MAINTAIN_ORDER:
				list = maintainOrderMapper.statisticsByEquipment(search);
				break;
			case REPAIR:
				list = repairMapper.statisticsByEquipment(search);
				break;
			default:
		}
		if (Func.isNotEmpty(list)) {
			// 根据设备id分组
			final Map<Long, List<EquipmentStatisticsDTO>> equipmentListMap = ListUtil.groupingBy(list, EquipmentStatisticsDTO::getId);
			result.forEach(dto -> {
				// 获取设备工单
				List<EquipmentStatisticsDTO> orderList = equipmentListMap.getOrDefault(dto.getId(), Lists.newArrayList());
				// 总数
				dto.setCount(orderList.size());
				// 完成数量
				Long completeCount = orderList.stream()
					.map(EquipmentStatisticsDTO::getStatus)
					.filter(ObjectUtil::isNotEmpty)
					.filter(OrderStatusEnum.completedStatus()::contains)
					.collect(Collectors.counting());
				dto.setCompleteCount(Math.toIntExact(completeCount));
				// 未完成数量
				dto.setUnfinishedCount(dto.getCount() - dto.getCompleteCount());
				// 异常数量
				Long abnormalCount = orderList.stream()
					.filter(order -> ObjectUtil.isNotEmpty(order.getIsAbnormal()) && order.getIsAbnormal() == 1)
					.collect(Collectors.counting());
				dto.setAbnormalCount(Math.toIntExact(abnormalCount));
				// 计算完成率
				dto.calculateCompleteRate();
				// 计算异常率
				dto.calculateAbnormalRate();
			});
		}
		return result;
	}

	private List<EquipmentStatisticsDTO> lubricateStatistics(StatisticSearchVO vo, Map<Long, DeviceAccountVO> idEquipmentMap) {
		List<EquipmentStatisticsDTO> result = new ArrayList<>();
		List<Long> deptIdList = new ArrayList<>();
		if (Func.isNotEmpty(vo.getDeptId())) {
			List<Long> deptIds = Func.toLongList(vo.getDeptId());
			deptIdList.addAll(deptIds);
			Func.toLongList(vo.getDeptId()).forEach(deptId -> {
				List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
				if (Func.isNotEmpty(deptChildIds)) {
					deptIdList.addAll(deptChildIds);
				}
			});
			vo.setDeptIdList(deptIdList.stream().distinct().collect(Collectors.toList()));
		}
		LubricateOrderVO orderVO = new LubricateOrderVO();
		orderVO.setQueryStartDate(vo.getStartDate());
		orderVO.setQueryEndDate(vo.getEndDate());
		orderVO.setChargeDeptIdList(vo.getDeptIdList());
		orderVO.setStatus(vo.getStatus());
		orderVO.setEquipmentIds(vo.getEquipmentIds());
		if (vo.getQueryDate() != null) {
			// 近一年
			if (vo.getQueryDate() == 0) {
				orderVO.setStartCreateTime(com.snszyk.common.utils.DateUtil.getOneYearBeforeLocalDateTime());
			}
			// 近30天
			if (vo.getQueryDate() == 1) {
				orderVO.setStartCreateTime(com.snszyk.common.utils.DateUtil.getThirtyDayBeforeLocalDateTime());
			}
			// 近7天
			if (vo.getQueryDate() == 2) {
				orderVO.setStartCreateTime(com.snszyk.common.utils.DateUtil.getSevenDayBeforeLocalDateTime());
			}
			// 近一天
			if (vo.getQueryDate() == 3) {
				orderVO.setStartCreateTime(LocalDate.now().atStartOfDay());
			}
			orderVO.setEndCreateTime(LocalDateTime.now());
		}
//
//		// 查询润滑工单
//		Long simasAdmin = null;
//		Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
//		if (Func.isNotEmpty(role)) {
//			simasAdmin = role.getId();
//		}
//		Long lubricateUser = null;
//		role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.LUBRICATE_USER);
//		if (Func.isNotEmpty(role)) {
//			lubricateUser = role.getId();
//		}
//		// 数据权限
//		String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
//		if (simasAdmin != null && !roleId.contains(Func.toStr(simasAdmin))) {
//			// 只能查看本部门的
//			orderVO.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//			if (lubricateUser != null && roleId.contains(Func.toStr(lubricateUser))) {
//				// 润滑人员只能查看派发给本人的点检工单
//				orderVO.setChargeUser(AuthUtil.getUserId());
//			}
//		}
		if (Func.isNotEmpty(orderVO.getQueryStartDate())) {
			orderVO.setQueryStartDate(orderVO.getQueryStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(orderVO.getQueryEndDate())) {
			orderVO.setQueryEndDate(orderVO.getQueryEndDate() + DateUtils.DAY_END_TIME);
		}
		IPage<LubricateOrderDTO> page = new Page<>(1, -1L);
		List<LubricateOrder> list = lubricateOrderMapper.page(page, orderVO);

//		if (ObjectUtil.isEmpty(list)) {
//			return Collections.emptyList();
//		}
		Map<Long, List<LubricateOrder>> equipmentIdMap = list.stream()
			.collect(Collectors.groupingBy(LubricateOrder::getEquipmentId));
//		if (ObjectUtil.isEmpty(equipmentIdMap)) {
//			return Collections.emptyList();
//		}

		for (Map.Entry<Long, DeviceAccountVO> deviceEntry : idEquipmentMap.entrySet()) {
			Long equipmentId = deviceEntry.getKey();
			DeviceAccountVO equipmentAccount = deviceEntry.getValue();
			List<LubricateOrder> lubricateOrders = equipmentIdMap.get(equipmentId);
			if (Func.isEmpty(lubricateOrders)) {
				lubricateOrders = Lists.newArrayList();
			}
			// 设备信息
//			DeviceAccountVO equipmentAccount = idEquipmentMap.get(entry.getKey());
			EquipmentStatisticsDTO dto = BeanUtil.copy(equipmentAccount, EquipmentStatisticsDTO.class);
			dto.setCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
			Optional.ofNullable(equipmentAccount.getUseDept())
				.map(SysCache::getDept)
				.ifPresent(dept -> dto.setUseDeptName(dept.getDeptName()));
			dto.setCount(lubricateOrders.size());
			// 完成次数
			Long completeCount = lubricateOrders.stream()
				.filter(o -> OrderStatusEnum.IS_COMPLETED.getCode().equals(o.getStatus())
					|| OrderStatusEnum.OVERDUE_COMPLETED.getCode().equals(o.getStatus()))
				.collect(Collectors.counting());
			dto.setCompleteCount(Math.toIntExact(completeCount));
			// 未完成次数
			dto.setUnfinishedCount(dto.getCount() - dto.getCompleteCount());
			dto.setCompleteRate(dto.getCompleteCount() > 0 ? new BigDecimal(dto.getCompleteCount()).divide(new BigDecimal(dto.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO);
			result.add(dto);
		}
		return result;
	}
//		return equipmentIdMap.entrySet()
//			.stream()
//			.filter(entry -> idEquipmentMap.containsKey(entry.getKey()))
//			.map(entry -> {
//				// 设备信息
//				DeviceAccountVO equipmentAccount = idEquipmentMap.get(entry.getKey());
//				EquipmentStatisticsDTO dto = BeanUtil.copy(equipmentAccount, EquipmentStatisticsDTO.class);
//				dto.setCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
//				Optional.ofNullable(equipmentAccount.getUseDept())
//					.map(SysCache::getDept)
//					.ifPresent(dept -> dto.setUseDeptName(dept.getDeptName()));
//				dto.setCount(entry.getValue().size());
//				// 完成次数
//				Long completeCount = entry.getValue().stream()
//					.filter(o -> OrderStatusEnum.IS_COMPLETED.getCode().equals(o.getStatus())
//						|| OrderStatusEnum.OVERDUE_COMPLETED.getCode().equals(o.getStatus()))
//					.collect(Collectors.counting());
//				dto.setCompleteCount(Math.toIntExact(completeCount));
//				// 未完成次数
//				dto.setUnfinishedCount(dto.getCount() - dto.getCompleteCount());
//				dto.setCompleteRate(dto.getCompleteCount() > 0 ? new BigDecimal(dto.getCompleteCount()).divide(new BigDecimal(dto.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO);
//				return dto;
//			}).collect(Collectors.toList());
//	}

	/**
	 * 设备完好率
	 *
	 * @param deptId
	 * @return
	 */
	public String healthPercentage(Long deptId) {
		List<Long> deptIdList = new ArrayList<>();
		if (ObjectUtil.isNotEmpty(deptId)) {
			deptIdList.add(deptId);
			// 获取子部门
			List<Long> deptChildIds = SysCache.getDeptChildIds(deptId);
			if (ObjectUtil.isNotEmpty(deptChildIds)) {
				deptIdList.addAll(deptChildIds);
			}
		}
		// 查询维修状态和再用状态的设备
//		LambdaQueryWrapper<EquipmentAccount> wrapper = Wrappers.lambdaQuery(EquipmentAccount.class)
//			.in(EquipmentAccount::getStatus, Arrays.asList(EquipmentStatusEnum.IN_USE.getCode(), EquipmentStatusEnum.IN_REPAIR.getCode(), EquipmentStatusEnum.IDLE.getCode()))
//			.in(ObjectUtil.isNotEmpty(deptIdList), EquipmentAccount::getUseDept, deptIdList);
		DeviceAccountPageVO vo = new DeviceAccountPageVO();
		vo.setUseDeptList(deptIdList);
		vo.setStatusList(Arrays.asList(EquipmentStatusEnum.IN_USE.getCode(), EquipmentStatusEnum.IN_REPAIR.getCode(), EquipmentStatusEnum.IDLE.getCode()));
		R<FeignPage<DeviceAccountVO>> feignPageR = deviceAccountClient.devicePageListScope(vo, -1, -1, EquipmentDataScopeEnum.ALL_SCOPE.getCode());
		List<DeviceAccountVO> list = feignPageR.getData().getRecords();
//		List<EquipmentAccount> list = equipmentAccountService.list(wrapper);
		if (ObjectUtil.isEmpty(list)) {
			return StringPool.ZERO_PERCENT;
		}
		// 统计在用状态设备数量
		BigDecimal inUserCountBD = new BigDecimal(list.stream()
			.filter(e -> e.getStatus().equals(EquipmentStatusEnum.IN_USE.getCode()) || e.getStatus().equals(EquipmentStatusEnum.IDLE.getCode()))
			.count());
		// 总计数量
		BigDecimal listSizeBD = new BigDecimal(list.size());
		// 计算结果
		BigDecimal result = inUserCountBD.divide(listSizeBD, 4, RoundingMode.HALF_UP)
			.multiply(new BigDecimal(StringPool.HUNDRED))
			.setScale(2, RoundingMode.HALF_UP);
		return result.toPlainString() + StringPool.PERCENT;

	}

	/**
	 * 设备维修率
	 *
	 * @param v
	 * @return
	 */
	public String repairPercentage(RepairDurationPageVO v) {
		v.setSize(-1L);
		final IPage<RepairDurationDTO> page = this.repairDurationPage(v);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords()) || page.getRecords().size() == 0) {
			return StringPool.ZERO_PERCENT;
		}
		log.info("设备维修率：============={}", page.getRecords());
		return page.getRecords()
			.stream()
			.map(e -> new BigDecimal(e.getRepairRate().replaceAll("%", "")))
			.reduce(BigDecimal.ZERO, BigDecimal::add)
			.divide(BigDecimal.valueOf(page.getRecords().size()), 2, RoundingMode.HALF_UP)
			.toPlainString() + StringPool.PERCENT;
	}

	/**
	 * 维修时间分页统计
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<RepairDurationDTO> repairDurationPage(RepairDurationPageVO vo) {

		// 解析查询时间参数
		if (ObjectUtil.isNotEmpty(vo.getTimeType())) {
			vo.setStartDate(TimeTypeUtil.getStartDate(vo.getTimeType()));
			vo.setEndDate(LocalDate.now());
		}
		vo.preHandleStartAndEndDateTime();
		if (ObjectUtil.isNotEmpty(vo.getDeptId())) {
			vo.setUseDeptList(SysCache.getDeptChildIds(vo.getDeptId()));
		}
		if (ObjectUtil.isEmpty(vo.getSortOrder())) {
			vo.setSortOrder("desc");
		}
		vo.setRepairStatusList(Lists.newArrayList(OrderStatusEnum.IS_COMPLETED.getCode(), OrderStatusEnum.OVERDUE_COMPLETED.getCode()));
		vo.setTenantId(AuthUtil.getTenantId());
		vo.setIsLeaseBack(SzykConstant.DB_NOT_DELETED);
		// 查询维修时长
		IPage<RepairDurationDTO> page = repairRecordService.pageRepairDuration(vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return vo;
		}
		// 间隔时长
		final BigDecimal timeInterval = new BigDecimal(vo.getTimeInterval());
		if(Func.isNotEmpty(page.getRecords())){
			List<Long> equipmentIdList = page.getRecords().stream()
				.map(RepairDurationDTO::getId).collect(Collectors.toList());
			// 查询设备Map
			final Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(equipmentIdList);
			page.getRecords().forEach(durationDTO -> {
				DeviceAccountVO equipmentAccount = equipmentVOMap.get(durationDTO.getId());
				if(Func.isNotEmpty(equipmentAccount)){
					BeanUtil.copy(equipmentAccount, durationDTO);
				}
				// 总时长
				durationDTO.setTotalDuration(timeInterval);
				// 计算维修率
				durationDTO.setRepairRate(StringPool.ZERO_PERCENT);
				if (durationDTO.getRepairDuration().compareTo(BigDecimal.ZERO) != 0
					&& durationDTO.getTotalDuration().compareTo(BigDecimal.ZERO) != 0) {
					BigDecimal result = durationDTO.getRepairDuration().divide(durationDTO.getTotalDuration(), 4, RoundingMode.HALF_UP)
						.multiply(new BigDecimal(StringPool.HUNDRED))
						.setScale(2, RoundingMode.HALF_UP);
					durationDTO.setRepairRate(result.toPlainString() + StringPool.PERCENT);
				}
			});
		}
		return page;
	}

	/**
	 * 获取设备Map
	 */
	private Map<Long, DeviceAccountVO> getEquipmentVOMap(List<Long> equipmentIdList) {
		if (ObjectUtil.isEmpty(equipmentIdList)) {
			return MapUtil.empty();
		}
		// 根据设备ids查询设备信息
		DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		deviceAccountVO.setDeviceIds(equipmentIdList);
		R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		if(!equipmentListResult.isSuccess()){
			throw new ServiceException("查询设备台账信息失败！");
		}
		if(Func.isEmpty(equipmentListResult.getData())){
			return MapUtil.empty();
		}
		return ListUtil.toMap(equipmentListResult.getData(), DeviceAccountVO::getId, e -> Objects.requireNonNull(BeanUtil.copy(e, DeviceAccountVO.class)));
	}

	/**
	 * 工单统计
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<OrderStatisticsDTO> orderStatistics(OrderStatisticsVO vo) {
		List<OrderStatisticsDTO> result = Lists.newArrayList();
		// 查询全部工单
		if (ObjectUtil.isEmpty(vo.getOrderType())) {
			// 点巡检
			List<InspectOrder> inspectOrderList = inspectOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
			result.add(convertToOrderStatisticsDTO(inspectOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.INSPECT_ORDER), null));
			// 内部维修
			List<Repair> innerRepairOrderList = repairService.listBy(null, null, null, null, null, RepairBizTypeEnum.INTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
			result.add(convertToOrderStatisticsDTO(innerRepairOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.INTERNAL_REPAIR), null));
			// 外部维修单
			List<Repair> externalRepairOrderList = repairService.listBy(null, null, null, null, null, RepairBizTypeEnum.EXTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
			result.add(convertToOrderStatisticsDTO(externalRepairOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.EXTERNAL_REPAIR), null));
			// 保养工单
			List<MaintainOrder> maintainOrderList = maintainOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
			result.add(convertToOrderStatisticsDTO(maintainOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.MAINTAIN_ORDER), null));
			// 计划性检修工单
			List<OverhaulOrder> overhaulOrderList = overhaulOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
			result.add(convertToOrderStatisticsDTO(overhaulOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.OVERHAUL_ORDER), null));
			// 润滑工单
			List<LubricateOrder> lubricateOrderList = lubricateOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode(), Boolean.TRUE);
			result.add(convertToOrderStatisticsDTO(lubricateOrderList, new OrderStatisticsDTO().init(OrderTypeEnum.LUBRICATE_ORDER), null));

			return result.stream()
				.sorted(Comparator.comparing(OrderStatisticsDTO::getCompleteRate).reversed())
				.collect(Collectors.toList());
		}

		// 初始化
		OrderStatisticsDTO dto = new OrderStatisticsDTO().init(vo.getOrderType());
		switch (vo.getOrderType()) {
			// 点巡检
			case INSPECT_ORDER:
				List<InspectOrder> inspectOrderList = inspectOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				result.add(convertToOrderStatisticsDTO(inspectOrderList, dto, null));
				break;
			// 内部维修单
			case INTERNAL_REPAIR:
				List<Repair> innerRepairOrderList = repairService.listBy(null, null, null, null, null, RepairBizTypeEnum.INTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				result.add(convertToOrderStatisticsDTO(innerRepairOrderList, dto, null));
				break;
			// 外部维修单
			case EXTERNAL_REPAIR:
				List<Repair> externalRepairOrderList = repairService.listBy(null, null, null, null, null, RepairBizTypeEnum.EXTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				result.add(convertToOrderStatisticsDTO(externalRepairOrderList, dto, null));
				break;
			// 保养工单
			case MAINTAIN_ORDER:
				List<MaintainOrder> maintainOrderList = maintainOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				result.add(convertToOrderStatisticsDTO(maintainOrderList, dto, null));
				break;
			// 计划性检修工单
			case OVERHAUL_ORDER:
				List<OverhaulOrder> overhaulOrderList = overhaulOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				result.add(convertToOrderStatisticsDTO(overhaulOrderList, dto, null));
				break;
			case LUBRICATE_ORDER:
				List<LubricateOrder> lubricateOrderList = lubricateOrderService.listBy(null, null, null, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode(), Boolean.TRUE);
				result.add(convertToOrderStatisticsDTO(lubricateOrderList, dto, null));
				break;
			default:
				throw new BusinessException("未知的工单类型");
		}
		return result;
	}

	/**
	 * 根据统计维度进行工单分组统计
	 *
	 * @param vo
	 * @param orderList
	 * @return
	 */
	private List<OrderStatisticsDTO> orderGroupByDimensionStatistics(OrderStatisticsGroupByStatisticsDimensionVO vo, List<? extends IOrderField> orderList, List<Long> userIds) {
		if (ObjectUtil.isEmpty(orderList)) {
			return Collections.emptyList();
		}
		Comparator<OrderStatisticsDTO> comparing = Comparator.comparing(OrderStatisticsDTO::getCompleteRate).reversed();
		if (SortOrderEnum.ASC == vo.getSortOrder()) {
			comparing = Comparator.comparing(OrderStatisticsDTO::getCompleteRate);
		}
		// 按照统计维度分组
		final Map<Long, ? extends List<? extends IOrderField>> longMap = groupByDimensionEnum(orderList, vo.getStatisticsDimension(), userIds);
		// 按照统计维度统计
		final List<OrderStatisticsDTO> result = longMap.entrySet()
			.stream()
			.map(entry -> convertToOrderStatisticsDTO(entry.getValue(), new OrderStatisticsDTO().init(vo.getOrderType()), entry.getKey()))
			.sorted(comparing)
			.collect(Collectors.toList());
		if (ObjectUtil.isEmpty(result)) {
			return Collections.emptyList();
		}
		// 设置名称
		setNamesByIds(result, vo.getStatisticsDimension());
		return result;
	}

	/**
	 * 根据id设置对应名称
	 * id可能是部门id，用户id，设备id
	 * name对应为部门名称，用户名称，设备名称
	 *
	 * @param result
	 * @param statisticsDimension
	 */
	private void setNamesByIds(List<OrderStatisticsDTO> result, StatisticsDimensionEnum statisticsDimension) {
		if (ObjectUtil.isEmpty(result) || ObjectUtil.isEmpty(statisticsDimension)) {
			return;
		}
		switch (statisticsDimension) {
			case DEPT:
				result.forEach(dto -> {
					Optional.ofNullable(dto.getId())
						.map(SysCache::getDept)
						.ifPresent(dept -> dto.setName(dept.getDeptName()));
				});
				break;
			case USER:
				result.forEach(dto -> {
					Optional.ofNullable(dto.getId())
						.map(UserCache::getUser)
						.ifPresent(user -> dto.setName(user.getRealName()));
				});
				break;
			case EQUIPMENT:
				// 获取ids，id可能是部门id，用户id，设备id
				final List<Long> ids = ListUtil.map(result, OrderStatisticsDTO::getId);
				// 根据ids
				final DeviceAccountVO vo = new DeviceAccountVO();
				vo.setDeviceIds(ids);
				final R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(vo);
				if (ObjectUtil.isEmpty(listR) || ObjectUtil.isEmpty(listR.getData())) {
					return;
				}
				List<DeviceAccountVO> accountList = listR.getData();
				// key-设备id，value-设备名称
				final Map<Long, DeviceAccountVO> idToAccountMap = ListUtil.toMap(accountList, DeviceAccountVO::getId, Function.identity());

				result.forEach(dto -> {
					Optional.ofNullable(dto.getId())
						.map(idToAccountMap::get)
						.ifPresent(entity -> {
							dto.setName(entity.getName());
							dto.setEquipmentSn(entity.getSn());
						});

				});

		}
	}

	/**
	 * 根据统计维度分组
	 */
	private Map<Long, ? extends List<? extends IOrderField>> groupByDimensionEnum(List<? extends IOrderField> orderList, StatisticsDimensionEnum dimensionEnum, List<Long> userIds) {
		if (ObjectUtil.isEmpty(orderList) || ObjectUtil.isEmpty(dimensionEnum)) {
			return Collections.emptyMap();
		}
		switch (dimensionEnum) {
			case DEPT:
				final Map<Long, ? extends List<? extends IOrderField>> collect = orderList.stream()
					.collect(Collectors.groupingBy(order -> {
						Long deptId = order.getDeptId();
						return deptId == null ? 0L : deptId;
					}));
				return collect;
			case USER:
				return orderList.stream()
					.collect(Collectors.groupingBy(order -> {
						Long executeUser = order.getExecuteUser();
						return executeUser == null ? 0L : executeUser;
					}));
			case EQUIPMENT:

				return orderList.stream()
					.collect(Collectors.groupingBy(order -> {
						Long equipmentId = order.getEquipmentId();
						return equipmentId == null ? 0L : equipmentId;
					}));
			default:
				return Collections.emptyMap();
		}

	}


	/**
	 * 转为DTO
	 *
	 * @param orderList
	 * @return
	 */
	private static OrderStatisticsDTO convertToOrderStatisticsDTO(List<? extends IOrderField> orderList, OrderStatisticsDTO dto, Long nameId) {
		if (ObjectUtil.isEmpty(orderList)) {
			return dto;
		}
		// 工单总数
		dto.setOrderNum((long) orderList.size());
		// 已经完成的工单数量
		long completeNum = orderList.stream()
			.filter(order -> OrderStatusEnum.completedStatus().contains(order.getStatus()))
			.count();
		dto.setCompleteNum(completeNum);
		// 计划未完成数量和完成率
		dto.computeUnCompleteNum()
			.computeCompleteRate();

		dto.setId(nameId);

		return dto;
	}

	/**
	 * 导出设备完好率
	 *
	 * @param deptId
	 * @param response
	 */
	public void exportHealthPercentage(Long deptId, HttpServletResponse response) throws UnsupportedEncodingException {
		CustomExcelUtil.setResponse("设备完好率" + com.snszyk.core.tool.utils.DateUtil.time(), response);
		// 查询设备完好率
		final String healthPercentageStr = this.healthPercentage(deptId);
		DeviceAccountPageVO pageVO = new DeviceAccountPageVO();
		pageVO.setUseDept(deptId);
		pageVO.setStatus(3);
		R<FeignPage<DeviceAccountVO>> feignPageR = deviceAccountClient.devicePageListScope(pageVO, -1, -1, EquipmentDataScopeEnum.ALL_SCOPE.getCode());
		FeignPage<DeviceAccountVO> page = feignPageR.getData();
//		IPage<EquipmentAccountVO> page = equipmentAccountService.page(new Page<>(1L, -1L), vo);

		ClassPathResource pathResource = new ClassPathResource("/template/healthPercentage.xlsx");
		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(pathResource.getInputStream()).build()) {
			WriteSheet writeSheet = EasyExcel.writerSheet().build();

			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("healthPercentageStr", healthPercentageStr);
			excelWriter.fill(map, writeSheet);
			excelWriter.fill(page.getRecords(), fillConfig, writeSheet);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 导出设备维修率
	 *
	 * @param vo
	 * @param response
	 */
	public void exportRepairPercentage(RepairDurationPageVO vo, HttpServletResponse response) throws UnsupportedEncodingException {
		CustomExcelUtil.setResponse("维修工单完成率" + com.snszyk.core.tool.utils.DateUtil.time(), response);
		// 查询设备维修率
		String repairPercentageStr = repairPercentage(vo);
		// 查询设备分页
		vo.setSize(-1L);
		IPage<RepairDurationDTO> page = this.repairDurationPage(vo);
		ClassPathResource pathResource = new ClassPathResource("/template/repairPercentage.xlsx");
		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(pathResource.getInputStream()).build()) {
			WriteSheet writeSheet = EasyExcel.writerSheet().build();

			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("repairPercentageStr", repairPercentageStr);
			excelWriter.fill(map, writeSheet);
			excelWriter.fill(page.getRecords(), fillConfig, writeSheet);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 工单统计分组
	 *
	 * @param vo
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<OrderStatisticsDTO> orderStatisticsGroupByStatisticsDimension(OrderStatisticsGroupByStatisticsDimensionVO vo) {
		// 根据姓名查询用户id
		List<Long> userIds = Lists.newArrayList();
		if (ObjectUtil.isNotEmpty(vo.getUserName())) {
			userIds = listUserIdsByName(vo.getUserName());
			if (ObjectUtil.isEmpty(userIds)) {
				return new Page<>(vo.getCurrent(), vo.getSize());
			}
		}
		// 设备id
		List<Long> equipmentIds = Lists.newArrayList();
		if (StringUtil.isNotBlank(vo.getEquipmentName())) {
			equipmentIds = listEquipmentIdsByName(vo.getEquipmentName());
			if (ObjectUtil.isEmpty(equipmentIds)) {
				return new Page<>(vo.getCurrent(), vo.getSize());
			}
		}
		switch (vo.getOrderType()) {
			case INSPECT_ORDER:
				List<InspectOrder> inspectOrderList = inspectOrderService.listBy(vo.getDeptId(), userIds, equipmentIds, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, inspectOrderList, userIds));
			case INTERNAL_REPAIR:
				List<Repair> innderRepairList = repairService.listBy(vo.getDeptId(), null, userIds, null, equipmentIds, RepairBizTypeEnum.INTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, innderRepairList, userIds));
			case EXTERNAL_REPAIR:
				List<Repair> externalRepairList = repairService.listBy(null, vo.getDeptId(), null, userIds, equipmentIds, RepairBizTypeEnum.EXTERNAL.getCode(), vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, externalRepairList, userIds));
			case MAINTAIN_ORDER:
				List<MaintainOrder> maintainOrderList = maintainOrderService.listBy(vo.getDeptId(), userIds, equipmentIds, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, maintainOrderList, userIds));
			case OVERHAUL_ORDER:
				List<OverhaulOrder> overhaulOrderList = overhaulOrderService.listBy(vo.getDeptId(), userIds, equipmentIds, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode());
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, overhaulOrderList, userIds));
			case LUBRICATE_ORDER:
				List<LubricateOrder> lubricateOrderList = lubricateOrderService.listBy(vo.getDeptId(), userIds, equipmentIds, vo.getStartDateTime(), vo.getEndDateTime(), OrderStatusEnum.IS_CLOSED.getCode(), Boolean.TRUE);
				return ManualPageUtil.manualPage(new Page<>(vo.getCurrent(), vo.getSize()), orderGroupByDimensionStatistics(vo, lubricateOrderList, userIds));
		}
		throw new BusinessException("暂不支持该工单类型");
	}

	/**
	 * 查询时设备ids
	 *
	 * @param equipmentName
	 * @return
	 */
	private List<Long> listEquipmentIdsByName(String equipmentName) {
		final DeviceAccountVO accountVO = new DeviceAccountVO();
		accountVO.setLikeDeviceName(equipmentName);
		final R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(accountVO);
		if (ObjectUtil.isEmpty(listR.getData())) {
			return Collections.emptyList();
		}
		return ListUtil.map(listR.getData(), DeviceAccountVO::getId);
	}

	/**
	 * 查询用户ids
	 *
	 * @param userName
	 * @return
	 */
	private List<Long> listUserIdsByName(String userName) {
		R<List<User>> listR = userClient.userListByName(userName);
		if (ObjectUtil.isEmpty(listR) || ObjectUtil.isEmpty(listR.getData())) {
			return Collections.emptyList();
		}
		return ListUtil.map(listR.getData(), User::getId);
	}

}
