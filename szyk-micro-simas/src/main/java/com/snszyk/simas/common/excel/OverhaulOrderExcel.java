package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OverhaulOrderExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("工单编号")
	private String no;

	@ExcelProperty("工单名称")
	private String orderName;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("负责部门")
	private String executeDeptName;

	@ExcelProperty("开始时间")
	private String startTimeStr;

	@ExcelProperty("结束时间")
	private String endTimeStr;

	@ExcelProperty("检修周期")
	private String cycleTypeName;

	@ExcelProperty("检修人员")
	private String executeUserName;

	@ExcelProperty("检修结果")
	private String overhaulResult;

	@ExcelProperty("审核人员")
	private String checkUserName;

	@ExcelProperty("工单状态")
	private String statusName;

}
