/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.TimeoutRemindSet;
import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
import com.snszyk.simas.common.service.ITimeoutRemindSetService;
import com.snszyk.simas.common.vo.TimeoutRemindSetVO;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 设备工单超时提醒设置表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class TimeoutRemindSetServiceImpl extends ServiceImpl<TimeoutRemindSetMapper, TimeoutRemindSet> implements ITimeoutRemindSetService {


	@Override
	public boolean submit(TimeoutRemindSetVO vo) {
		TimeoutRemindSet timeoutRemindSet = this.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
			.eq(TimeoutRemindSet::getBizType, vo.getBizType()).eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId()));
		if(Func.isNotEmpty(timeoutRemindSet)){
			timeoutRemindSet.setTimeInterval(vo.getTimeInterval()).setUpdateTime(DateUtil.now());
			return this.updateById(timeoutRemindSet);
		}
		vo.setUserId(AuthUtil.getUserId()).setCreateTime(DateUtil.now()).setUpdateTime(DateUtil.now());
		return this.save(Objects.requireNonNull(BeanUtil.copy(vo, TimeoutRemindSet.class)));
	}


}
