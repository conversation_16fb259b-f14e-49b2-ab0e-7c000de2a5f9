/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;

import java.util.List;

/**
 * 备件耗材表 服务类
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
public interface IComponentMaterialService extends IService<ComponentMaterial> {

	/**
	 * 批量保存
	 *
	 * @param bizNo
	 * @param bizModule
	 * @param list
	 * @return
	 */
	boolean submitBatch(String bizNo, String bizModule, List<ComponentMaterialVO> list);


	/**
	 * 根据业务编号查询
	 * @param bizNo
	 * @return
	 */
	List<ComponentMaterialVO> selectByBizNo(String bizNo, String bizModule);

	/**
	 * 统计报表-备件损耗统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	IPage<ComponentMaterialVO> statisticalReport(IPage<ComponentMaterialVO> page, StatisticSearchVO search);


}
