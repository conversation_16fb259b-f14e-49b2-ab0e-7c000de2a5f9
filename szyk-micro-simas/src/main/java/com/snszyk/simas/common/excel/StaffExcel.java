/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * StaffExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class StaffExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String no;

	@ExcelProperty("人员姓名")
	private String name;

	@ExcelProperty("联系电话")
	private String tel;

	@ExcelProperty("部门")
	private String deptName;

	@ExcelProperty("人员性别")
	private String sexName;

	@ExcelProperty("岗位")
	private String postName;

	@ExcelProperty("学历")
	private String qualificationName;

	@ExcelProperty("年龄")
	private String age;

	@ExcelProperty("职称")
	private String title;

	@ExcelProperty("工作年限")
	private String workYears;

}
