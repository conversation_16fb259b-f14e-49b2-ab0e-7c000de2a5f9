/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.common.excel.OperateLogExcel;
import com.snszyk.simas.common.entity.OperateLog;
import com.snszyk.simas.common.vo.OperateLogVO;

import java.util.List;

/**
 * 操作日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
public interface IOperateLogService extends IService<OperateLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param operateLog
	 * @return
	 */
	IPage<OperateLogVO> page(IPage<OperateLogVO> page, OperateLogVO operateLog);

	/**
	 * 保存
	 *
	 * @param operateLog
	 * @return
	 */
	boolean submit(OperateLogVO operateLog);

	/**
	 * 导出
	 *
	 * @param operateLog
	 * @return
	 */
	List<OperateLogExcel> exportLog(OperateLogVO operateLog);

}
