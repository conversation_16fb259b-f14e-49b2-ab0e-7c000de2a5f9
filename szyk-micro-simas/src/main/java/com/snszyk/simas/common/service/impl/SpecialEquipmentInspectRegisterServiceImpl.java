/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.utils.PageUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.common.entity.SpecialEquipmentInspectRegister;
import com.snszyk.simas.common.mapper.SpecialEquipmentInspectRegisterMapper;
import com.snszyk.simas.common.service.ISpecialEquipmentInspectRegisterService;
import com.snszyk.simas.common.vo.SpecialEquipmentInspectRegisterVO;
import com.snszyk.simas.common.wrapper.SpecialEquipmentInspectRegisterWrapper;
import com.snszyk.system.cache.DictBizCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.snszyk.core.tool.utils.BeanUtil.copy;

/**
 * 特种设备检验登记表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
@AllArgsConstructor
public class SpecialEquipmentInspectRegisterServiceImpl extends BaseServiceImpl<SpecialEquipmentInspectRegisterMapper, SpecialEquipmentInspectRegister> implements ISpecialEquipmentInspectRegisterService {

	private final IDeviceAccountClient deviceAccountClient;
	private final IAttachClient attachClient;


	@Override
	public IPage<SpecialEquipmentInspectRegisterVO> page(IPage<SpecialEquipmentInspectRegisterVO> page,
														 SpecialEquipmentInspectRegisterVO vo) {
		LambdaQueryWrapper<SpecialEquipmentInspectRegister> queryWrapper = Wrappers.<SpecialEquipmentInspectRegister>lambdaQuery()
			.eq(Func.isNotEmpty(vo.getEquipmentId()), SpecialEquipmentInspectRegister::getEquipmentId, vo.getEquipmentId())
			.eq(Func.isNotEmpty(vo.getInspectType()), SpecialEquipmentInspectRegister::getInspectType, vo.getInspectType())
			.eq(Func.isNotEmpty(vo.getInspectResult()), SpecialEquipmentInspectRegister::getInspectResult, vo.getInspectResult())
			.orderByDesc(SpecialEquipmentInspectRegister::getInspectDate);

		IPage<SpecialEquipmentInspectRegister> entityPage = this.page(page.convert(input -> null), queryWrapper);
		return PageUtil.toPage(entityPage, entityPage.getRecords()
			.stream()
			.map(entity -> SpecialEquipmentInspectRegisterWrapper.build().entityVO(entity))
			.collect(Collectors.toList()));
	}

	@Override
	public SpecialEquipmentInspectRegisterVO detail(Long id) {
		SpecialEquipmentInspectRegister entity = this.getById(id);
		if (entity == null) {
			throw new ServiceException("当前检验登记不存在！");
		}
		R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(entity.getEquipmentId());
		if (!deviceAccountResult.isSuccess()) {
			throw new ServiceException("查询设备台账信息失败！");
		}
		if (Func.isEmpty(deviceAccountResult.getData())) {
			throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		}
		DeviceAccountVO deviceAccount = deviceAccountResult.getData();
		SpecialEquipmentInspectRegisterVO detail = SpecialEquipmentInspectRegisterWrapper.build().entityVO(entity);
		detail.setEquipmentName(deviceAccount.getName()).setEquipmentCode(deviceAccount.getCode());
		// 附件列表
		if (Func.isNotEmpty(detail.getAttachId())) {
			R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(detail.getAttachId()));
			if (attachListR.isSuccess() && Func.isNotEmpty(attachListR.getData())) {
				detail.setAttachList(attachListR.getData());
			}
		}
		return detail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(SpecialEquipmentInspectRegisterVO vo) {
		// 校验下次检验时间不能早于检验时间
		if (vo.getNextInspectDate() != null && vo.getInspectDate() != null) {
			if (vo.getNextInspectDate().before(vo.getInspectDate())) {
				throw new ServiceException("下次检验时间不能早于检验时间！");
			}
		}
		// 唯一性校验：检验日期+检验类型
		if (Func.isNotEmpty(vo.getInspectDate()) && Func.isNotEmpty(vo.getInspectType())) {
			int count = this.count(Wrappers.<SpecialEquipmentInspectRegister>query().lambda()
				.eq(SpecialEquipmentInspectRegister::getEquipmentId, vo.getEquipmentId())
				.eq(SpecialEquipmentInspectRegister::getInspectType, vo.getInspectType())
				.eq(SpecialEquipmentInspectRegister::getInspectDate, vo.getInspectDate())
				.ne(Func.isNotEmpty(vo.getId()), SpecialEquipmentInspectRegister::getId, vo.getId()));
			if (count > 0) {
				throw new ServiceException(String.format("当前设备已存在检验日期是%s，检验类型为%s的检验登记！",
					DateUtil.format(vo.getInspectDate(), DateUtil.PATTERN_DATE),
					DictBizCache.getValue("se_inspect_type", vo.getInspectType())));
			}
		}
		SpecialEquipmentInspectRegister inspectRegister
			= Objects.requireNonNull(copy(vo, SpecialEquipmentInspectRegister.class));
		// 新增时生成单号
		if (Func.isEmpty(inspectRegister.getId())) {
			inspectRegister.setNo(BizCodeUtil.generate("JR"));
		}
		if(Func.isEmpty(vo.getAttachId())){
			inspectRegister.setAttachId(null);
		}
		return this.saveOrUpdate(inspectRegister);
	}


}
