package com.snszyk.simas.common.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.enums.OrderActionEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 工单日志处理器
 * ClassName: OrderLogProcessor
 * Package: com.snszyk.simas.processor
 * Description:
 */
public class OrderLogProcessor {


	private static IBizLogService bizLogService;

	static {
		bizLogService = SpringUtil.getBean(IBizLogService.class);
	}

	/**
	 * 2024/12/12 8：00：00工单生成。
	 * 2024/12/12 8：00：00张三提交了工单。
	 * 2024/12/12 8：00：00张三再次提交了工单。
	 * 2024/12/12 8：00：00李四审核通过了了工单。
	 * 2024/12/12 8：00：00李四驳回了工单，驳回原因“具体原因”。
	 * 2024/12/12 8：00：00工单关闭，关闭原因“设备完成报废处置”。
	 */
	private static final String LOG_INFO_FORMAT = "%s%s%s变更单";
	/**
	 * 驳回原因后缀format
	 */
	private static final String REJECT_REASON_FORMAT = "，驳回原因“%s”。";

	/**
	 * 根据动作获取操作日志信息
	 *
	 * @param actionEnum   动作枚举
	 * @param rejectReason 驳回原因
	 * @return
	 */
	public static String getLogContent(OrderActionEnum actionEnum, String rejectReason) {
		if (ObjectUtil.isEmpty(actionEnum)) {
			throw new BusinessException("获取日志失败！动作不可为空");
		}
		// 驳回时不许为空
		if (actionEnum == OrderActionEnum.AUDIT_FAIL && ObjectUtil.isEmpty(rejectReason)) {
			throw new BusinessException("获取日志失败！驳回原因不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 构建日志信息
		// 工单生成
		if (actionEnum == OrderActionEnum.GEN || actionEnum == OrderActionEnum.CANCEL) {
			return String.format(actionEnum.getDesc(), localDateTimeStr);
		} else if (actionEnum == OrderActionEnum.AUDIT_FAIL) {
			// 驳回
			return String.format(actionEnum.getDesc(), localDateTimeStr, realName, rejectReason);
		} else {
			return String.format(actionEnum.getDesc(), localDateTimeStr, realName);
		}

	}


	/**
	 * 保存业务日志
	 *
	 * @param entity
	 * @param actionEnum
	 * @return
	 */
	public static Boolean saveBizLog(SystemModuleEnum module, JSONObject entity, OrderActionEnum actionEnum) {
		return saveBizLog(module, entity, actionEnum, null);
	}

	/**
	 * 保存业务日志
	 *
	 * @param entity
	 * @param actionEnum
	 * @return
	 */
	public static Boolean saveBizLog(SystemModuleEnum module, JSONObject entity, OrderActionEnum actionEnum, String rejectReason) {
		if (ObjectUtil.isEmpty(entity) || ObjectUtil.isEmpty(actionEnum)) {
			throw new ServiceException("保存业务日志失败！entity或actionEnum不可为空");
		}
		BizLogVO logVO = new BizLogVO();
		logVO.setBizId(entity.getLong("id"))
			.setBizNo(entity.getString("no"))
			.setModule(module.getCode())
			.setBizStatus(actionEnum.getCode())
			.setContent(OrderLogProcessor.getLogContent(actionEnum, rejectReason))
			.setBizInfo(JSON.toJSONString(entity))
			.setOperateTime(DateUtil.now());
		if(!Func.equals(-1L, AuthUtil.getUserId())){
			logVO.setOperateUser(AuthUtil.getUserId());
		}
		if(OrderActionEnum.ABNORMAL_GEN == actionEnum){
			logVO.setOperateUser(entity.getLong("reportUser"));
		}
		if(OrderActionEnum.ABNORMAL_HANDLE == actionEnum
			|| OrderActionEnum.ABNORMAL_REPAIR == actionEnum || OrderActionEnum.ABNORMAL_CLOSE == actionEnum){
			logVO.setOperateUser(entity.getLong("operateUser"));
		}
		return bizLogService.submit(logVO);
	}
}
