package com.snszyk.simas.common.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.StringUtils;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.equipment.vo.DeviceMonitorVO;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.excel.template.LubricateStandardExcelTemplate;
import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
import com.snszyk.simas.lubricate.entity.LubricateMethods;
import com.snszyk.simas.lubricate.entity.LubricateOilType;
import com.snszyk.simas.lubricate.entity.LubricateStandards;
import com.snszyk.simas.lubricate.service.ILubricateStandardsService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class LubricateStandardsListener extends AnalysisEventListener<LubricateStandardExcelTemplate> {

	private ILubricateStandardsService lubricateStandardsService;
	private ImportDataValidLogicService importDataValidLogicService;

	private List<LubricateStandards> saveList = new ArrayList<>();
	private List<String> failReasonList = new ArrayList<>();
	private int headRowNumber;

	public LubricateStandardsListener(int headRowNumber, ILubricateStandardsService lubricateStandardsService, ImportDataValidLogicService importDataValidLogicService) {
		this.headRowNumber = headRowNumber;
		this.lubricateStandardsService = lubricateStandardsService;
		this.importDataValidLogicService = importDataValidLogicService;
	}

	@Override
	public void invoke(LubricateStandardExcelTemplate template, AnalysisContext analysisContext) {
		LubricateStandards entity = new LubricateStandards();
		int rowIndex = analysisContext.getCurrentRowNum();
		log.info("====================================读取第{}行==================================================", rowIndex + 1);
		String rowFailReason = validParam(template, entity);
		if (Func.isNotBlank(rowFailReason)) {
			rowFailReason = "excel第" + (rowIndex + 1) + "行：" + rowFailReason;
			failReasonList.add(rowFailReason);
		}
		saveList.add(entity);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext analysisContext) {
		if (analysisContext.getCurrentRowNum() <= headRowNumber - 1) {
			failReasonList.add("Excel文件数据为空");
		}
		if (Func.isEmpty(failReasonList) && Func.isNotEmpty(saveList)) {
			lubricateStandardsService.saveImportData(saveList);
		}
	}

	private String validParam(LubricateStandardExcelTemplate template, LubricateStandards entity) {
		List<String> rowFailReason = new ArrayList<>();
		// 校验必填项
		if (Func.isBlank(template.getName())) {
			rowFailReason.add("设备名称不能为空");
		}
		if (Func.isBlank(template.getSn())) {
			rowFailReason.add("SN编号不能为空");
		}
		if (Func.isBlank(template.getMonitorName())) {
			rowFailReason.add("润滑部位不能为空");
		}
		if (Func.isBlank(template.getMethodsName())) {
			rowFailReason.add("润滑手段不能为空");
		} else {
			try {
				LubricateMethods lubricateMethods = importDataValidLogicService.getLubricateMethods(template.getMethodsName());
				entity.setLubricateMethodsId(lubricateMethods.getId());
			} catch (Exception e) {
				e.printStackTrace();
				rowFailReason.add(e.getMessage());
			}
		}
		if (Func.isBlank(template.getOilTypeName())) {
			rowFailReason.add("油品类型不能为空");
		} else {
			try {
				LubricateOilType lubricateOilType = importDataValidLogicService.getLubricateOilType(template.getOilTypeName());
				entity.setOilTypeId(lubricateOilType.getId());
			} catch (Exception e) {
				e.printStackTrace();
				rowFailReason.add(e.getMessage());
			}
		}
		if (Func.isBlank(template.getCycleTypeName())) {
			rowFailReason.add("润滑周期不能为空");
		} else if (!StringUtils.isNumeric(template.getCycleTypeName())) {
			rowFailReason.add("润滑周期必须为数字");
		} else {
			entity.setLubricateCycle(Integer.parseInt(template.getCycleTypeName()));
			entity.setFloatTime((int) Math.ceil(entity.getLubricateCycle() * 0.1));
		}
		if (Func.isNotBlank(template.getName()) && Func.isNotBlank(template.getSn())) {
			try {
				IDeviceAccountClient accountClient = SpringUtil.getBean(IDeviceAccountClient.class);
				DeviceAccountVO vo = new DeviceAccountVO();
				vo.setCode(template.getCode());
				DeviceAccountVO account = accountClient.deviceInfoByParams(vo).getData();
// 				EquipmentAccount account = importDataValidLogicService.getEquipmentAccount(template.getCode());
				if (Func.isEmpty(account)) {
					rowFailReason.add("设备不存在");
				} else {
					entity.setEquipmentId(account.getId());
					entity.setEquipmentCode(account.getCode());
					ICommonClient commonClient = SpringUtil.getBean(ICommonClient.class);
					DeviceMonitorVO monitorVO = new DeviceMonitorVO();
					monitorVO.setDeviceId(account.getId());
					monitorVO.setName(template.getMonitorName());
					R<List<DeviceMonitor>> listR = commonClient.deviceMonitorByParams(monitorVO);
					List<DeviceMonitor> data = listR.getData();
					if (Func.isNotEmpty(data)) {
						DeviceMonitor deviceMonitor = data.get(0);
						entity.setEquipmentMonitorId(deviceMonitor.getId());
					}
//				EquipmentMonitor monitor = importDataValidLogicService.getEquipmentMonitor(account.getId(), template.getMonitorName());
				}
			} catch (Exception e) {
				e.printStackTrace();
				rowFailReason.add(e.getMessage());
			}
		}
		return Func.join(rowFailReason, ";");
	}
}
