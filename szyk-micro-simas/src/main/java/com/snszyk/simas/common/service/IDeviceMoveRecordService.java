/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;

/**
 * 设备移动记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface IDeviceMoveRecordService extends IService<DeviceMoveRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceMoveRecord
	 * @return
	 */
	IPage<DeviceMoveRecordVO> page(IPage<DeviceMoveRecordVO> page, DeviceMoveRecordVO deviceMoveRecord);

	/**
	 * 提交
	 *
	 * @param deviceMoveRecord
	 * @return
	 */
	boolean submit(DeviceMoveRecordVO deviceMoveRecord);

	/**
	 * 批量保存
	 *
	 * @param deviceMoveRecord
	 * @return
	 */
	boolean saveRecords(DeviceMoveRecordVO deviceMoveRecord);


}
