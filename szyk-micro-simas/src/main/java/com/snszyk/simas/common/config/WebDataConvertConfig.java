//package com.snszyk.simas.common.config;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.fasterxml.jackson.databind.DeserializationFeature;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.module.SimpleModule;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
//
//import java.util.Arrays;
//import java.util.List;
//
//@Configuration
//public class WebDataConvertConfig extends WebMvcConfigurationSupport {
//
//
//	@Override
//	public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
//		converters.add(mappingJackson2HttpMessageConverter());
//		super.configureMessageConverters(converters);
//	}
//
//	@Bean
//	public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
//		MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
//		ObjectMapper mapper = new ObjectMapper();
//		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//
//		SimpleModule module = new SimpleModule();
//		module.addDeserializer(IPage.class, new IPageDeserializer(IPage.class));
//		mapper.registerModule(module);
//
//		converter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.APPLICATION_JSON_UTF8,MediaType.APPLICATION_OCTET_STREAM));
//		converter.setObjectMapper(mapper);
//
//		return converter;
//	}
//
//
//}
