/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.common.entity.TimeoutRemindSet;
import com.snszyk.simas.common.vo.TimeoutRemindSetVO;

/**
 * 设备工单超时提醒设置表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface ITimeoutRemindSetService extends IService<TimeoutRemindSet> {


	/**
	 * 提交
	 *
	 * @param timeoutRemindSet
	 * @return
	 */
	boolean submit(TimeoutRemindSetVO timeoutRemindSet);


}
