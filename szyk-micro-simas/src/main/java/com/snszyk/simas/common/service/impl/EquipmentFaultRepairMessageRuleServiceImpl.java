/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.simas.common.entity.EquipmentFaultRepairMessageRule;
import com.snszyk.simas.common.mapper.EquipmentFaultRepairMessageRuleMapper;
import com.snszyk.simas.common.service.IEquipmentFaultRepairMessageRuleService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 设备等级信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@AllArgsConstructor
@Service
public class EquipmentFaultRepairMessageRuleServiceImpl extends BaseServiceImpl<EquipmentFaultRepairMessageRuleMapper, EquipmentFaultRepairMessageRule> implements IEquipmentFaultRepairMessageRuleService {

	@Override
	public EquipmentFaultRepairMessageRule getBy(String tenantId) {
		return this.lambdaQuery()
			.eq(EquipmentFaultRepairMessageRule::getTenantId, tenantId)
			.one();
	}
}
