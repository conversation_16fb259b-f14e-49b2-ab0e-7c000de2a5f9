/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * EquipmentRepairStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EquipmentRepairStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("设备名称")
	private String name;

	@ExcelProperty("设备编号")
	private String sn;

	@ExcelProperty("设备类型")
	private String categoryName;

	@ExcelProperty("归属部门")
	private String useDeptName;
	/**
	 * 次数
	 */
	@ExcelProperty("维修次数")
	private Integer count;

	/**
	 *
	 */
	@ExcelProperty(value = "完成次数")
	private Integer completeCount;

	/**
	 *
	 */
	@ExcelProperty(value = "完成率")
	private String completeRate;

}
