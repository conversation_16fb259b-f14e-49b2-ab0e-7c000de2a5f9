// /*
//  *      Copyright (c) 2018-2028
//  */
// package com.snszyk.simas.common.controller;
//
// import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
// import com.github.xiaoymin.knife4j.annotations.ApiSupport;
// import com.snszyk.core.tool.api.R;
// import com.snszyk.simas.common.dto.EquipmentFaultRepairMessageRuleDto;
// import com.snszyk.simas.common.service.logic.EquipmentFaultRepairMessageRuleLogicService;
// import com.snszyk.simas.common.vo.EquipmentFaultRepairMessageRuleSaveVo;
// import io.swagger.annotations.Api;
// import io.swagger.annotations.ApiOperation;
// import lombok.AllArgsConstructor;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.web.bind.annotation.*;
//
// /**
//  * 设备等级信息 控制器
//  *
//  * <AUTHOR>
//  * @since 2024-11-12
//  */
// @RestController
// @AllArgsConstructor
// @RequestMapping("equipment-fault-repair-message-rule")
// @Api(value = "设备故障维修消息规则", tags = "设备故障维修消息规则接口")
// @Validated
// @ApiSupport(author = "zzp", order = 50)
// public class EquipmentFaultRepairMessageRuleController {
//
// 	private final EquipmentFaultRepairMessageRuleLogicService equipmentLevelLogicService;
//
// 	/**
// 	 * 新增设备等级
// 	 */
// 	@PostMapping
// 	@ApiOperationSupport(order = 1)
// 	@ApiOperation(value = "新增或修改", notes = "EquipmentLevel")
// 	public R<Boolean> saveOrUpdate(@RequestBody @Validated EquipmentFaultRepairMessageRuleSaveVo v) {
// 		return R.status(equipmentLevelLogicService.saveOrUpdate(v));
// 	}
//
// 	/**
// 	 * 详情
// 	 */
// 	@GetMapping("/detail")
// 	@ApiOperationSupport(order = 2)
// 	@ApiOperation(value = "详情", notes = "EquipmentLevel")
// 	public R<EquipmentFaultRepairMessageRuleDto> getDetail() {
// 		return R.data(equipmentLevelLogicService.getDetail());
// 	}
//
// }
