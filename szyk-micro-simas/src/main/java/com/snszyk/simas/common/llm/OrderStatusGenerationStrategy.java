package com.snszyk.simas.common.llm;

import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.exception.LlmIdGenException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrderStatusGenerationStrategy implements IdGenerationStrategy {


	@Override
	public String getElement() {
		return "工单状态";
	}

	@Override
	public String getId(String statusName) {
		OrderStatusEnum byName = OrderStatusEnum.getByName(statusName);
		if (byName == null) {
			throw new LlmIdGenException("抱歉，没有识别到您输入的工单状态，您可以描述的更具体一些");
		}
		return byName.getName() + "|" + byName.getCode();
	}

}
