package com.snszyk.simas.common.excel.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.snszyk.core.tool.jackson.JsonUtil;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.Collections;
import java.util.List;

public class AddCellRangeWriteHandler implements SheetWriteHandler {

	private final List<CellRangeAddress> rangeCellList;

	public AddCellRangeWriteHandler(List<CellRangeAddress> rangeCellList) {
		this.rangeCellList = (rangeCellList == null) ? Collections.emptyList() : rangeCellList;
	}

	public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
		Sheet sheet = writeSheetHolder.getSheet();
		System.out.println("==============rangeCellList===========" + JsonUtil.toJson(rangeCellList));
		for (CellRangeAddress cellRangeAddress : this.rangeCellList) {
			sheet.addMergedRegionUnsafe(cellRangeAddress);
		}
	}
}
