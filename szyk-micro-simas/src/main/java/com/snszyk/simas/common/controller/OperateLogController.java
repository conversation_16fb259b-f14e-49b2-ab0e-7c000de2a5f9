/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.excel.OperateLogExcel;
import com.snszyk.simas.common.service.IOperateLogService;
import com.snszyk.simas.common.vo.OperateLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作日志表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/operate-log")
@Api(value = "操作日志表", tags = "操作日志表接口")
public class OperateLogController extends SzykController {

	private final IOperateLogService operateLogService;

	/**
	 * 自定义分页 操作日志表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "module", value = "系统模块", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "type", value = "操作类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入operateLog")
	public R<IPage<OperateLogVO>> page(@ApiIgnore OperateLogVO operateLog, Query query) {
		IPage<OperateLogVO> pages = operateLogService.page(Condition.getPage(query), operateLog);
		return R.data(pages);
	}

	/**
	 * 导出操作日志
	 */
	@GetMapping("export-log")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "module", value = "系统模块", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "type", value = "操作类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "导出操作日志", notes = "传入operateLog")
	public void exportLog(@ApiIgnore OperateLogVO operateLog, HttpServletResponse response) {
		List<OperateLogExcel> list = operateLogService.exportLog(operateLog);
		ExcelUtil.export(response, "操作日志数据" + DateUtil.time(), "操作日志数据表", list, OperateLogExcel.class);
	}


}
