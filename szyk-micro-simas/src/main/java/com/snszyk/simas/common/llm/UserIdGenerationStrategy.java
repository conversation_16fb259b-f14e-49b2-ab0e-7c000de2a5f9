package com.snszyk.simas.common.llm;

import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.exception.LlmIdGenException;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @since 2025/2/12 11:34
 *
 **/

@Component
public class UserIdGenerationStrategy implements IdGenerationStrategy {

	@Autowired
	private IUserClient userClient;

	@Override
	public String getElement() {
		return "user";
	}

	@Override
	public String getId(String userName) {
		R<User> userR = userClient.userInfoByUserRealName(userName);
		if (userR.isSuccess()) {
			User user = userR.getData();
			if (user == null) {
				throw new LlmIdGenException("抱歉，没有识别到您输入的用户，您可以描述的更具体一些");
			}
			return userName + "|" + user.getId();
		}
		return null;
	}
}
