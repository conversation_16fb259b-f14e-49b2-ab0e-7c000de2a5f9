package com.snszyk.simas.common.excel.annotation;

import com.snszyk.simas.common.excel.support.ExcelDynamicSelect;

import java.lang.annotation.*;

/**
 * 定义Excel列下拉列表属性的注解。
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelSelected {
	/**
	 * 方式一：固定的下拉选项
	 */
	String[] source() default {};

	/**
	 * 方式二：提供动态下拉选项的类
	 */
	Class<? extends ExcelDynamicSelect>[] sourceClass() default {};

	/**
	 * 方式三：基于码值从数据库查询数据
	 */
	String codeField() default "";

	/**
	 * 下拉列表的起始行（默认从第二行开始）。
	 */
	int firstRow() default 1;

	/**
	 * 下拉列表的结束行（默认到第65536行）。
	 */
	int lastRow() default 65536;
}
