/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.EquipmentFileUpdateDto;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.vo.EquipmentFileUpdatePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备文件更新 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface EquipmentFileUpdateMapper extends BaseMapper<EquipmentFileUpdate> {

    IPage<EquipmentFileUpdateDto> pageList(@Param("v") EquipmentFileUpdatePageVo v);

    EquipmentFileUpdateDto detail(Long id);

	List<EquipmentFileUpdateDto> listByChangeId(@Param("changeId") Long changeId);
}
