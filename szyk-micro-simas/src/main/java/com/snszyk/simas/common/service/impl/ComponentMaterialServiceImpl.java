/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.common.utils.ManualPageUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.simas.common.mapper.ComponentMaterialMapper;
import com.snszyk.simas.common.service.IComponentMaterialService;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.common.wrapper.ComponentMaterialWrapper;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.spare.enums.SparePartConsumptionTypeEnum;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 备件耗材表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Service
public class ComponentMaterialServiceImpl extends ServiceImpl<ComponentMaterialMapper, ComponentMaterial> implements IComponentMaterialService {


	@Override
	public boolean submitBatch(String bizNo, String bizModule, List<ComponentMaterialVO> list) {
		if (Func.isEmpty(bizNo)) {
			this.remove(Wrappers.<ComponentMaterial>query().lambda().eq(ComponentMaterial::getBizNo, bizNo));
		}
		AtomicReference<Integer> sort = new AtomicReference<>(1);
		List<ComponentMaterial> materialList = list.stream().map(vo -> {
			ComponentMaterial componentMaterial = Objects.requireNonNull(BeanUtil.copy(vo, ComponentMaterial.class));
			componentMaterial.setBizNo(bizNo).setBizModule(bizModule)
				.setSort(sort.getAndSet(sort.get() + 1)).setCreateTime(DateUtil.now());
			componentMaterial.setId(null);
			return componentMaterial;
		}).collect(Collectors.toList());
		return this.saveBatch(materialList);
	}

	@Override
	public List<ComponentMaterialVO> selectByBizNo(String bizNo, String bizModule) {
		List<ComponentMaterial> list = this.list(Wrappers.<ComponentMaterial>query().lambda()
			.eq(ComponentMaterial::getBizNo, bizNo)
			.eq(ComponentMaterial::getBizModule, bizModule)
			.orderByAsc(ComponentMaterial::getSort));
		return ComponentMaterialWrapper.build().listVO(list);
	}

	@Override
	public IPage<ComponentMaterialVO> statisticalReport(IPage<ComponentMaterialVO> page, StatisticSearchVO vo) {

		if (ObjectUtil.isNotEmpty(vo.getSparePartConsumptionType())) {
			vo.setBizModuleList(vo.getSparePartConsumptionType().getBizModuleList());
		}
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		List<ComponentMaterialVO> dataList = baseMapper.statisticalReport(new Page(1, -1L), vo);
		if (ObjectUtil.isEmpty(dataList)) {
			return page;
		}
		// 根据编码分组统计
		Map<String, List<ComponentMaterialVO>> noComponentMaterialMap = dataList.stream()
			.filter(d -> StringUtil.isNotBlank(d.getNo()))
			.collect(Collectors.groupingBy(ComponentMaterial::getNo));

		if (ObjectUtil.isEmpty(noComponentMaterialMap)) {
			return page;
		}
		// 默认升序
		Comparator<ComponentMaterialVO> comparing = Comparator.comparing(ComponentMaterialVO::getCount);
		if (ObjectUtil.isNotEmpty(vo.getAsc()) && !vo.getAsc()) {
			comparing = comparing.reversed();
		}
		List<ComponentMaterialVO> list = noComponentMaterialMap.entrySet()
			.stream()
			.map(entry -> {
				ComponentMaterialVO materialVO = BeanUtil.copy(entry.getValue().get(0), ComponentMaterialVO.class);
				// 消耗总数 bigdecimal的累加
				BigDecimal count = entry.getValue().stream()
					.map(ComponentMaterial::getCount)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
				materialVO.setCount(count);
//			 entry.getValue().stream()
//					.map(ComponentMaterial::getCount)
//					.sum();

				materialVO.setCount(count);
				// 消耗方式
				List<String> bizModuleList = entry.getValue().stream()
					.map(ComponentMaterial::getBizModule)
					.distinct()
					.collect(Collectors.toList());

				if (ObjectUtil.isNotEmpty(bizModuleList)) {
					String bizModuleNames = bizModuleList.stream()
						.map(bizModule -> SparePartConsumptionTypeEnum.getByBizModule(bizModule).getDesc())
						.filter(StringUtil::isNotBlank)
						.distinct()
						.collect(Collectors.joining(StringPool.SLASH));
					materialVO.setBizModuleName(bizModuleNames);
				}

				return materialVO;
			}).sorted(comparing).collect(Collectors.toList());

		return ManualPageUtil.manualPage(new Page<>(page.getCurrent(), page.getSize()), list);
	}
}
