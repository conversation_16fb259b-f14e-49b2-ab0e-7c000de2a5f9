/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.change.dto.EquipmentChangeFileDto;
import com.snszyk.simas.change.entity.EquipmentChangeFile;
import com.snszyk.simas.change.vo.EquipmentChangeFilePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备变更附件 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface EquipmentChangeFileMapper extends BaseMapper<EquipmentChangeFile> {

    IPage<EquipmentChangeFileDto> pageList(@Param("v") EquipmentChangeFilePageVo v);

    EquipmentChangeFileDto detail(Long id);

    List<EquipmentChangeFileDto> listByChangeId(@Param("changeId") Long changeId);
}
