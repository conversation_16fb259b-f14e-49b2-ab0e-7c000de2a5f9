/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * ScrapExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class ScrapExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("报废单号")
	private String no;

	@ExcelProperty("报废单名称")
	private String name;

	@ExcelProperty("操作人员")
	private String operateUserName;

	@ExcelProperty("报废时间")
	private String scrapDateStr;

	@ExcelProperty("设备数量")
	private String equipmentCount;

	@ExcelProperty("备注")
	private String remark;

	@ExcelProperty("状态")
	private String statusName;


}
