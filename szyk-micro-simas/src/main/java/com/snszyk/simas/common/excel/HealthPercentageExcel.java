package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName: HealthPercentageExcel
 * Package: com.snszyk.simas.excel
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 17:55
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class HealthPercentageExcel {

	private String name;

	private String model;

	private String locationName;

	private String statusName;
}
