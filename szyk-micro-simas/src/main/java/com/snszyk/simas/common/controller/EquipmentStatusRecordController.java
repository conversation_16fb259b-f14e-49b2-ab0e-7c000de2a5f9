/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.KeyValueDTO;
import com.snszyk.simas.common.service.logic.EquipmentStatusRecordLogicService;
import com.snszyk.simas.common.vo.EquipmentStatusRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 设备状态记录 控制器
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipmentstatusrecord")
@Api(value = "设备状态记录", tags = "设备状态记录接口")
public class EquipmentStatusRecordController {


	private final EquipmentStatusRecordLogicService equipmentStatusRecordLogicService;

	/**
	 * 列表
	 */
	@GetMapping("health-percentage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "设备完好率", notes = "EquipmentStatusRecordVo")
	public R<List<KeyValueDTO>> listHealthPercentage(@Validated EquipmentStatusRecordVo v) {
		return R.data(equipmentStatusRecordLogicService.listHealthPercentage(v));
	}


}
