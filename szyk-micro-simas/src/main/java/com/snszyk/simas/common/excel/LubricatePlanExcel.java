/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * InspectPlanExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class LubricatePlanExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("计划编号")
	private String no;

	@ExcelProperty("计划名称")
	private String name;

	@ExcelProperty("负责部门")
	private String chargeDeptName;

	@ExcelProperty("负责人员")
	private String chargeUserName;

	@ExcelProperty("设备数量")
	private String equipmentCount;

	@ExcelProperty("制定计划人员")
	private String createUserName;

	@ExcelProperty("开始执行日期")
	private String startDateStr;

	@ExcelProperty("结束执行日期")
	private String endDateStr;

	@ExcelProperty("状态")
	private String statusName;

}
