/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 业务日志表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public class BizLogWrapper extends BaseEntityWrapper<BizLog, BizLogVO> {

	public static BizLogWrapper build() {
		return new BizLogWrapper();
 	}

	@Override
	public BizLogVO entityVO(BizLog bizLog) {
		BizLogVO bizLogVO = Objects.requireNonNull(BeanUtil.copy(bizLog, BizLogVO.class));
		if(Func.isNotEmpty(bizLog.getOperateUser())){
			User operateUser = UserCache.getUser(bizLog.getOperateUser());
			if(Func.isNotEmpty(operateUser)){
				bizLogVO.setOperateUserName(operateUser.getRealName());
			}
		}
		if(SystemModuleEnum.MAINTAIN_ORDER == SystemModuleEnum.getByCode(bizLog.getModule())){
			if(OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(bizLog.getBizStatus())){
				bizLogVO.setContent(bizLogVO.getContent() + StringPool.LEFT_BRACKET +
					"驳回" + StringPool.RIGHT_BRACKET);
			}
			if(OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(bizLog.getBizStatus())
				|| OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(bizLog.getBizStatus())){
				bizLogVO.setContent(bizLogVO.getContent() + StringPool.LEFT_BRACKET +
					"通过" + StringPool.RIGHT_BRACKET);
			}
		}
		if(SystemModuleEnum.INTERNAL_REPAIR == SystemModuleEnum.getByCode(bizLog.getModule())
			|| SystemModuleEnum.EXTERNAL_REPAIR == SystemModuleEnum.getByCode(bizLog.getModule())){
			if(OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(bizLog.getBizStatus())){
				bizLogVO.setContent(bizLogVO.getContent() + StringPool.LEFT_BRACKET +
					"驳回" + StringPool.RIGHT_BRACKET);
			}
			if(OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(bizLog.getBizStatus())
				|| OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(bizLog.getBizStatus())){
				bizLogVO.setContent(bizLogVO.getContent() + StringPool.LEFT_BRACKET +
					"通过" + StringPool.RIGHT_BRACKET);
			}
		}
		return bizLogVO;
	}

}
