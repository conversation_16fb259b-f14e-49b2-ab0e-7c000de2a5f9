/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.equipment.entity.DeviceFileCategory;
import com.snszyk.common.equipment.enums.EquipmentFileGroupEnum;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.EquipmentFileUpdateDto;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.enums.EquipmentUpdateStatusEnum;
import com.snszyk.simas.common.mapper.EquipmentFileUpdateMapper;
import com.snszyk.simas.common.service.IEquipmentFileUpdateService;
import com.snszyk.simas.common.vo.EquipmentFileUpdatePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 设备文件更新 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@AllArgsConstructor
@Service
public class EquipmentFileUpdateServiceImpl extends BaseServiceImpl<EquipmentFileUpdateMapper, EquipmentFileUpdate> implements IEquipmentFileUpdateService {

	private final ICommonClient commonClient;


	/**
	 * 分页查询
	 */
	@Override
	public IPage<EquipmentFileUpdateDto> pageList(EquipmentFileUpdatePageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public EquipmentFileUpdateDto detail(Long id) {
		return baseMapper.detail(id);
	}

	@Override
	public List<EquipmentFileUpdateDto> listByChangeId(Long changeId) {
		List<EquipmentFileUpdateDto> equipmentFileUpdateDtos = baseMapper.listByChangeId(changeId);
		// typeName
		for (EquipmentFileUpdateDto equipmentFileUpdateDto : equipmentFileUpdateDtos) {

			// updateStatusName
			if (equipmentFileUpdateDto.getUpdateStatus() != null) {
				equipmentFileUpdateDto.setUpdateStatusName(Optional.ofNullable(EquipmentUpdateStatusEnum.getByCode(equipmentFileUpdateDto.getUpdateStatus())).map(EquipmentUpdateStatusEnum::getName).orElse(""));
			}
			Optional.ofNullable(equipmentFileUpdateDto.getFileCategoryId())
				.map(commonClient::getEquipmentFileCategory)
				.map(R::getData)
				.map(DeviceFileCategory::getName)
				.ifPresent(equipmentFileUpdateDto::setFileCategoryName);

			equipmentFileUpdateDto.setTypeName(Optional.ofNullable(EquipmentFileGroupEnum.getByCode(equipmentFileUpdateDto.getTypeKey())).map(EquipmentFileGroupEnum::getName).orElse(""));
		}
		return equipmentFileUpdateDtos;
	}

}
