/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.BizLogDTO;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogPageVO;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.common.vo.BizSearchVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 大屏数据 控制器
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/bizLog")
@Api(value = "业务日志", tags = "业务日志")
public class BizLogController extends SzykController {

	private final IBizLogService bizLogService;

	/**
	 * 查询业务日志列表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "", notes = "如果无需分页，size传-1")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "bizId", value = "业务id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "bizNo", value = "业务单号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "module", value = "业务模块", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bizStatus", value = "业务状态", paramType = "query", dataType = "int"),
	})
	public R<IPage<BizLog>> page(BizLogVO vo, Query query) {
		return R.data(bizLogService.page(vo, query));
	}

	@GetMapping("/logPage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "日志的分页", notes = "如果无需分页")
	public R<IPage<BizLogDTO>> page(BizLogPageVO v) {
		return R.data(bizLogService.pageList(v));
	}



	/**
	 * 查看-关联工单 设备台账表
	 */
	@GetMapping("/equipmentOrderPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "bizNo", value = "业务单号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "module", value = "业务模块", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "查看-关联工单", notes = "传入search")
	public R<IPage<BizLogDTO>> equipmentOrderPage(@ApiIgnore BizSearchVO search, Query query) {
		return R.data(bizLogService.equipmentOrderPage(Condition.getPage(query), search));
	}
}
