/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * RepairStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class RepairStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("工单号")
	private String no;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("设备类型")
	private String equipmentCategoryName;

	@ExcelProperty("维修部位")
	private String monitorName;

	@ExcelProperty("故障缺陷名称")
	private String faultName;

	@ExcelProperty("维修方式")
	private String bizTypeName;

	@ExcelProperty("维修人/跟进人")
	private String userName;

	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@ExcelProperty("工单生成时间")
	private Date createTime;

	@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
	@ExcelProperty("完成时间")
	private Date actualCompleteTime;

	@ExcelProperty("工单状态")
	private String statusName;


}
