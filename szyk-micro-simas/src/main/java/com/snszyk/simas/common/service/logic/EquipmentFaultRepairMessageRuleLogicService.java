 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.common.service.logic;


 import cn.hutool.core.util.ObjectUtil;
 import com.alibaba.fastjson.JSON;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.location.entity.Location;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.StringUtil;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.dto.EquipmentFaultRepairMessageRuleDto;
 import com.snszyk.simas.common.entity.EquipmentFaultRepairMessageRule;
 import com.snszyk.simas.common.enums.MessageContentTypeEnum;
 import com.snszyk.simas.common.enums.TicketStatusEnum;
 import com.snszyk.simas.common.service.IEquipmentFaultRepairMessageRuleService;
 import com.snszyk.simas.common.vo.EquipmentFaultRepairMessageRuleSaveVo;
 import com.snszyk.simas.common.vo.MessageRuleVo;
 import com.snszyk.system.cache.DictBizCache;
 import com.snszyk.system.enums.DictBizEnum;
 import com.snszyk.user.entity.UserRole;
 import com.snszyk.user.feign.IUserClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import javax.validation.constraints.NotNull;
 import java.util.List;
 import java.util.Objects;


 /**
  * 设备等级信息 逻辑服务实现类
  *
  * <AUTHOR>
  * @since 2024-11-12
  */
 @AllArgsConstructor
 @Service
 @Slf4j
 public class EquipmentFaultRepairMessageRuleLogicService {

	 private final IEquipmentFaultRepairMessageRuleService equipmentLevelService;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final GeneralLogicService generalLogicService;
	 private final IUserClient userClient;


	 /**
	  * 保存
	  *
	  * @param v
	  * @return
	  */
	 @Transactional(rollbackFor = Exception.class)
	 public Boolean saveOrUpdate(EquipmentFaultRepairMessageRuleSaveVo v) {
		 EquipmentFaultRepairMessageRule equipmentLevel = new EquipmentFaultRepairMessageRule();
		 equipmentLevel.setId(v.getId());
		 equipmentLevel.setImportantLevel(StringUtil.join(v.getImportantLevelList()));
		 equipmentLevel.setPushType(StringUtil.join(v.getPushTypeList()));
		 equipmentLevel.setPushRoleId(StringUtil.join(v.getPushRoleIdList()));
		 return equipmentLevelService.saveOrUpdate(equipmentLevel);
	 }

	 /**
	  * 详情
	  *
	  * @param
	  * @return
	  */
	 @Transactional(readOnly = true)
	 public EquipmentFaultRepairMessageRuleDto getDetail() {
		 // 查询设备等级信息
		 EquipmentFaultRepairMessageRule equipmentLevel = equipmentLevelService.getBy(AuthUtil.getTenantId());
		 if (ObjectUtil.isEmpty(equipmentLevel)) {
			 return null;
		 }
		 EquipmentFaultRepairMessageRuleDto dto = new EquipmentFaultRepairMessageRuleDto();
		 dto.setId(equipmentLevel.getId());
		 dto.setLevelList(Func.toIntList(equipmentLevel.getImportantLevel()));
		 dto.setPushTypeList(Func.toIntList(equipmentLevel.getPushType()));
		 dto.setPushRoleIdList(Func.toLongList(equipmentLevel.getPushRoleId()));
		 return dto;
	 }

	 /**
	  * 推送故障缺陷信息或者维修工单信息
	  */
	 public void pushFaultDefectOrRepairMessage(@NotNull Long equipmentId, String no, @NotNull MessageContentTypeEnum messageContentTypeEnum, @NotNull TicketStatusEnum ticketStatusEnum) {
		 log.info("推送故障缺陷信息,入参：{}", equipmentId);
		 // 获取详情
		 EquipmentFaultRepairMessageRuleDto ruleDto = getDetail();
		 if (ObjectUtil.isEmpty(ruleDto)) {
			 log.info("设备等级信息为空");
			 return;
		 }
		 // 查询设备信息
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		 if (!deviceAccountResult.isSuccess()) {
			 throw new RuntimeException("推送故障缺陷信息-查询设备信息异常！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 log.error("设备不存在，设备id：{}", equipmentId);
			 throw new RuntimeException("推送故障缺陷信息失败！设备不存在");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 // 不包含这种类型
		 if (!ruleDto.getLevelList().contains(equipmentAccount.getImportantLevel())) {
			 log.info("设备等级不包含该类型，设备id：{}", equipmentId);
			 return;
		 }
		 // 查询设备位置
		 Location location = CommonCache.getLocation(equipmentAccount.getLocationId());
		 if (ObjectUtil.isEmpty(location)) {
			 throw new RuntimeException("设备位置不存在！");
		 }
		 // 查询角色用户
		 R<List<UserRole>> listR = userClient.userListOfRole(ruleDto.getPushRoleIdList());
		 if (ObjectUtil.isEmpty(listR) || ObjectUtil.isEmpty(listR.getData())) {
			 throw new RuntimeException("获取用户列表失败！");
		 }
		 final List<Long> userIdList = ListUtil.map(listR.getData(), UserRole::getUserId);

		 MessageRuleVo messageRuleVo = new MessageRuleVo()
			 .setEquipmentName(equipmentAccount.getName())
			 .setEquipmentLocationName(location.getName())
			 .setEquipmentLocationPath(location.getPath())
			 .setImportantLevel(equipmentAccount.getImportantLevel())
			 .setImportantLevelName(DictBizCache.getValue(DictBizEnum.IMPORTANT_LEVEL, equipmentAccount.getImportantLevel()))
			 .setPushContent(getPushContent(messageContentTypeEnum, ticketStatusEnum, no));

		 // 封装消息参数
		 // Map<String, String> params = EquipmentFaultMaintenanceMessageUtil.buildParams(equipmentAccount.getName(), no, DictBizCache.getValue(DictBizEnum.IMPORTANT_LEVEL, equipmentAccount.getImportantLevel()), locationName, messageContentTypeEnum, ticketStatusEnum);
		 // 解析消息内容
		 // String messageContent = EquipmentFaultMaintenanceMessageUtil.parse(params);
		 // 发送消息
		 generalLogicService.sendMessage(no, JSON.toJSONString(messageRuleVo), userIdList, MessageBizTypeEnum.MESSAGE_RULE);
	 }

	 /**
	  * 获取消息推送内容
	  *
	  * @param messageContentTypeEnum
	  * @param ticketStatusEnum
	  * @param no
	  * @return
	  */
	 private String getPushContent(MessageContentTypeEnum messageContentTypeEnum, TicketStatusEnum ticketStatusEnum, String no) {
		 if (Objects.isNull(messageContentTypeEnum) || Objects.isNull(ticketStatusEnum)) {
			 throw new RuntimeException("获取规则消息推送内容失败！消息类型或工单状态不可为空");
		 }

		 String pushContent = ticketStatusEnum.getName() + messageContentTypeEnum.getDesc();
		 return StringUtil.isBlank(no) ? pushContent : pushContent + "(" + no + ")";
	 }


 }
