<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.EquipmentChangeMapper">


    <select id="pageList" resultType="com.snszyk.simas.change.dto.EquipmentChangeDto">
        <include refid="selectData"/>
        <include refid="pageWhere"/>
        order by t.create_time desc
    </select>
    <select id="detail" resultType="com.snszyk.simas.change.dto.EquipmentChangeDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
        and t.id = #{id}
        limit 1
    </select>
    <select id="pageAuditList" resultType="com.snszyk.simas.change.dto.EquipmentChangeDto">
        <include refid="selectData"/>
        <include refid="pageWhere"/>
        order by t.create_time desc
    </select>
    <sql id="selectData">
        select t.id,
               t.equipment_id,
               t.change_number,
               t.change_name,
               t.change_reason,
               t.change_purpose,
               t.change_category,
               t.expected_implementation_date,
               t.change_content,
               t.implementation_plan,
               t.expected_effect,
               t.remarks,
               t.approval_flow_id,
               t.change_status,
               t.renovation_evaluation,
               t.rejection_person,
               t.rejection_time,
               t.rejection_reason,
               t.audit_person,
               t.audit_time,
               t.create_user,
               t.create_time,
               t.create_dept,
               t.update_user,
               t.update_time,
               t.is_deleted,
               t.tenant_id,
               t.status,
               e.name           as equipment_name,
               e.model,
               e.category_id    as equipment_category_id,
               ec.category_name as equipment_category_name,
               u.real_name      as apply_user_name,
               u2.real_name     as audit_person_name,
               d.dict_value     as change_status_name,
               e.use_dept,
               f.dept_name      as use_dept_name,
               d1.dict_value    as change_category_name,
               u.real_name      as create_user_name,
               u3.real_name     as update_user_name
        from simas_equipment_change t
                 left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
                 left join szyk_user u on t.create_user = u.id and u.is_deleted = 0
                 left join szyk_user u2 on t.audit_person = u2.id and u2.is_deleted = 0
                 left join device_category ec on e.category_id = ec.id
                 left join szyk_dict_biz d on t.change_status = d.dict_key and d.is_deleted = 0 and d.code =
                                                                                                    'equipment_change_status'
                 left join szyk_dept f on f.id = e.use_dept and f.is_deleted = 0
                 left join szyk_dict_biz d1 on t.change_category = d1.dict_key and d1.is_deleted = 0 and d1.code =
                                                                                                         'equipment_change_category'
                 left join szyk_user u3 on t.update_user = u3.id and u3.is_deleted = 0
    </sql>
    <sql id="pageWhere">
        <where>
            t.is_deleted = 0
            <if test="v.changeNumber != null and v.changeNumber.trim() != ''">
                AND t.change_number like concat('%',#{v.changeNumber}, '%')
            </if>
            <if test="v.changeName != null and v.changeName.trim() != ''">
                AND t.change_name like concat('%',#{v.changeName}, '%')
            </if>

            <if test="v.startApplyTime != null">
                AND t.create_time &gt;= #{v.startApplyTime}
            </if>
            <if test="v.endApplyTime != null">
                AND t.create_time &lt;= #{v.endApplyTime}
            </if>
            <if test="v.changeStatusListParam != null and v.changeStatusListParam.size() > 0">
                and t.change_status in
                <foreach collection="v.changeStatusListParam" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="v.applyUserName != null and v.applyUserName.trim() != ''">
                AND u.real_name like concat('%',#{v.applyUserName}, '%')
            </if>
        </where>
    </sql>
</mapper>
