/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.entity.EquipmentStatusRecord;
import com.snszyk.simas.common.mapper.EquipmentStatusRecordMapper;
import com.snszyk.simas.common.service.IEquipmentStatusRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 设备状态记录 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@AllArgsConstructor
@Service
public class EquipmentStatusRecordServiceImpl extends BaseServiceImpl<EquipmentStatusRecordMapper, EquipmentStatusRecord> implements IEquipmentStatusRecordService {


	@Override
	public List<EquipmentStatusRecord> list(List<Long> equipmentIds, LocalDate startDate, LocalDate endDate, List<Integer> statusList) {
		return this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(equipmentIds), EquipmentStatusRecord::getEquipmentId, equipmentIds)
			.between(ObjectUtil.isNotEmpty(startDate) && ObjectUtil.isNotEmpty(endDate), EquipmentStatusRecord::getRecordDate, startDate, endDate)
			.in(ObjectUtil.isNotEmpty(statusList), EquipmentStatusRecord::getStatus, statusList)
			.list();
	}
}
