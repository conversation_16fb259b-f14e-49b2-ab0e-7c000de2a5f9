<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.EquipmentFileUpdateMapper">


    <select id="pageList" resultType="com.snszyk.simas.common.dto.EquipmentFileUpdateDto">
        select * from simas_equipment_file_update where is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.simas.common.dto.EquipmentFileUpdateDto">
        select * from simas_equipment_file_update where is_deleted = 0 and id=#{id}
    </select>
    <select id="listByChangeId" resultType="com.snszyk.simas.common.dto.EquipmentFileUpdateDto"
            parameterType="java.lang.Long">
        select t.id,
               t.equipment_file_id,
               t.change_id,
               t.update_status,
               t.create_user,
               t.create_time,
               t.create_dept,
               t.update_user,
               t.update_time,
               t.is_deleted,
               t.tenant_id,
               t1.equipment_id,
               t1.no,
               t.name,
               t.type,
               t.file_category_id,
               t.attach_id,
               t.extension,
               t2.dict_key as typeKey
        from simas_equipment_file_update t
                 left join simas_equipment_file t1 on t1.id = t.equipment_file_id and t1.is_deleted = 0
                 left join device_file_category t2 on t2.id = t.file_category_id and t2.tenant_id = t.tenant_id
        where t.is_deleted = 0
          and t.change_id = #{changeId}
    </select>

</mapper>
