package com.snszyk.simas.common.exception;

import com.snszyk.core.tool.api.IResultCode;
import com.snszyk.core.tool.api.ResultCode;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @since 2025/2/12 11:41
 *
 **/
public class LlmIdGenException extends RuntimeException {

	private static final long serialVersionUID = 2359767895161832954L;
	private final IResultCode resultCode;

	public LlmIdGenException(String message) {
		super(message);
		this.resultCode = ResultCode.FAILURE;
	}

	public LlmIdGenException(IResultCode resultCode) {
		super(resultCode.getMessage());
		this.resultCode = resultCode;
	}

	public LlmIdGenException(IResultCode resultCode, Throwable cause) {
		super(cause);
		this.resultCode = resultCode;
	}

	public Throwable fillInStackTrace() {
		return this;
	}

	public Throwable doFillInStackTrace() {
		return super.fillInStackTrace();
	}

	public IResultCode getResultCode() {
		return this.resultCode;
	}
}
