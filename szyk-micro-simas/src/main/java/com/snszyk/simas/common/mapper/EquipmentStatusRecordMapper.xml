<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.EquipmentStatusRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentStatusRecordResultMap" type="com.snszyk.simas.common.entity.EquipmentStatusRecord">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="record_date" property="recordDate"/>
    </resultMap>


    <select id="selectEquipmentStatusRecordPage" resultMap="equipmentStatusRecordResultMap">
        select * from simas_equipment_status_record where is_deleted = 0
    </select>

</mapper>
