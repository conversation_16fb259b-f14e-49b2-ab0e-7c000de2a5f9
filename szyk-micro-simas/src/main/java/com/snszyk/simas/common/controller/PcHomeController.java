/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.CommonChartDTO;
import com.snszyk.simas.common.dto.PcHomeEquipmentStatusDTO;
import com.snszyk.simas.common.dto.PcHomeNoFinishStatDTO;
import com.snszyk.simas.common.dto.YearWorkSummaryDTO;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.service.logic.PcHomeLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * pc端的首页
 */
@RestController
@AllArgsConstructor
@RequestMapping("/pc-home")
@Api(value = "pc端的首页", tags = "pc端的首页")
public class PcHomeController extends SzykController {

	private final PcHomeLogicService logicService;

	/**
	 * 未完成工单的数据统计
	 */
	@GetMapping("/noFinishOrderStat")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "未完成工单的数据统计", notes = "未完成工单的数据统计")
	public R<PcHomeNoFinishStatDTO> noFinishOrderStat() {
		// 未完成工单说明：执行中、已超期、待确认、已驳回（验证未通过）的都属于未完成工单。
		List<Integer> statusList = Arrays.asList(OrderStatusEnum.IN_PROCESS.getCode(), OrderStatusEnum.IS_OVERDUE.getCode(),
			OrderStatusEnum.WAIT_CONFIRM.getCode(), OrderStatusEnum.IS_REJECTED.getCode());
		String tenantId = AuthUtil.getTenantId();
		return R.data(logicService.pcHomeOrderStat(statusList, true, tenantId, null, null));
	}

	/**
	 * 工单占比功能说明
	 */
	@GetMapping("/allOrderStat")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "工单占比功能说明", notes = "工单占比功能说明")
	public R<List<CommonChartDTO>> allOrderStat() {
		String tenantId = AuthUtil.getTenantId();
		// 开始时间为今天
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime start = now.withHour(0).withSecond(0).withMinute(0);
		// 今天的结束时间
		LocalDateTime end = now.withHour(23).withSecond(59).withMinute(59);
		return R.data(logicService.pcHomeOrderStatList(new ArrayList<>(), false, tenantId, start, end));
	}

	/**
	 * 工单占比功能说明
	 */
	@GetMapping("/curYearEquipmentStatus")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "本年度设备运行情况", notes = "本年度设备运行情况")
	public R<List<PcHomeEquipmentStatusDTO>> curYearEquipmentStatus() {
		Integer year = Year.now().getValue();
		return R.data(logicService.curYearEquipmentStatus(year));
	}

	/**
	 * 年度工作汇总
	 */
	@GetMapping("/curYearWorkSummary")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "年度工作汇总", notes = "年度工作汇总")
	public R<YearWorkSummaryDTO> curYearWorkSummary() {
		String tenantId = AuthUtil.getTenantId();
		return R.data(logicService.curYearWorkSummary(tenantId));
	}


}
