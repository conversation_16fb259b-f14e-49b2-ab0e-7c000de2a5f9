package com.snszyk.simas.common.processor;

import com.alibaba.fastjson.JSON;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.change.entity.EquipmentChange;
import com.snszyk.simas.common.enums.EquipmentChangeActionEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 设备变更日志处理器
 * ClassName: EquipmentChangeLogProcessor
 * Package: com.snszyk.simas.processor
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/16 14:35
 */
public class EquipmentChangeLogProcessor {


	private static IBizLogService bizLogService;

	static {
		bizLogService = SpringUtil.getBean(IBizLogService.class);
	}

	/**
	 * 格式：时间 操作人 动作
	 * 示例：2024/12/12 8：00：00张三提交了变更单。
	 * 2024/12/12 8：00：00张三再次提交了变更单。
	 * 2024/12/12 8：00：00张三撤销了变更单。
	 * 2024/12/12 8：00：00李四审核通过了变更单。
	 * 2024/12/12 8：00：00李四驳回了变更单，驳回原因“具体原因”。
	 * 2024/12/12 8：00：00李四验收了变更单。
	 */
	private static final String LOG_INFO_FORMAT = "%s%s%s变更单";
	/**
	 * 驳回原因后缀format
	 */
	private static final String REJECT_REASON_FORMAT = "，驳回原因“%s”。";

	/**
	 * 根据动作获取操作日志信息
	 *
	 * @param actionEnum   动作枚举
	 * @param rejectReason 驳回原因
	 * @return
	 */
	public static String getLogContent(EquipmentChangeActionEnum actionEnum, String rejectReason) {
		if (ObjectUtil.isEmpty(actionEnum)) {
			throw new BusinessException("获取日志失败！动作不可为空");
		}
		// 驳回时不许为空
		if (actionEnum == EquipmentChangeActionEnum.AUDIT_FAIL && ObjectUtil.isEmpty(rejectReason)) {
			throw new BusinessException("获取日志失败！驳回原因不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 构建日志信息
		final String logInfo = String.format(LOG_INFO_FORMAT, localDateTimeStr, realName, actionEnum.getDesc());
		// 非驳回时，直接返回
		if (actionEnum != EquipmentChangeActionEnum.AUDIT_FAIL) {
			return logInfo;
		}
		// 驳回时，添加驳回原因
		return logInfo + String.format(REJECT_REASON_FORMAT, rejectReason);
	}


	/**
	 * 保存业务日志
	 *
	 * @param entity
	 * @param actionEnum
	 * @return
	 */
	public static Boolean saveBizLog(EquipmentChange entity, EquipmentChangeActionEnum actionEnum) {
		return saveBizLog(entity, actionEnum, null);
	}

	/**
	 * 保存业务日志
	 *
	 * @param entity
	 * @param actionEnum
	 * @return
	 */
	public static Boolean saveBizLog(EquipmentChange entity, EquipmentChangeActionEnum actionEnum, String rejectReason) {
		if (ObjectUtil.isEmpty(entity) || ObjectUtil.isEmpty(actionEnum)) {
			throw new ServiceException("保存业务日志失败！entity或actionEnum不可为空");
		}
		BizLogVO logVO = new BizLogVO();
		logVO.setBizId(entity.getId())
			.setBizNo(entity.getChangeNumber())
			.setModule(SystemModuleEnum.EQUIPMENT_CHANGE.getCode())
			.setBizStatus(Integer.valueOf(actionEnum.getCode()))
			.setContent(EquipmentChangeLogProcessor.getLogContent(actionEnum, rejectReason))
			.setBizInfo(JSON.toJSONString(entity))
			.setOperateUser(AuthUtil.getUserId())
			.setOperateTime(DateUtil.now());
		return bizLogService.submit(logVO);
	}
}
