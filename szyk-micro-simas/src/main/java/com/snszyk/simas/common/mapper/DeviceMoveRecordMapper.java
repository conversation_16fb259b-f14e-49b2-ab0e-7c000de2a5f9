/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 	设备移动记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface DeviceMoveRecordMapper extends BaseMapper<DeviceMoveRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceMoveRecord
	 * @return
	 */
	List<DeviceMoveRecordVO> page(IPage page, @Param("deviceMoveRecord") DeviceMoveRecordVO deviceMoveRecord);


}
