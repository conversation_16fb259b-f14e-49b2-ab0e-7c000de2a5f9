package com.snszyk.simas.common.processor;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.enums.RepairActionEnum;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * ClassName: RepairLogProcessor
 * Package: com.snszyk.simas.processor
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/17 15:10
 */
@Slf4j
public class RepairLogProcessor {
	private static IBizLogService bizLogService;

	static {
		bizLogService = SpringUtil.getBean(IBizLogService.class);
	}

	/**
	 * 获取缺陷处理上报content
	 *
	 * @return
	 */
	public static String getHandlingDefectsReportLogContent() {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.HANDLING_DEFECTS_REPORT.getDesc(), localDateTimeStr, realName);
	}

	/**
	 * 获取内部派单content
	 */
	public static String getInternalDispatchLogContent(String receiveUserName) {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.INTERNAL_DISPATCH.getDesc(), localDateTimeStr, realName, receiveUserName);
	}

	/**
	 * 获取外委派单content
	 */
	public static String getExternalDispatchLogContent(String No) {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.EXTERNAL_DISPATCH.getDesc(), localDateTimeStr, realName, No);
	}

	/**
	 * 获取正常创建content
	 */
	public static String getCreateLogContent(RepairBizTypeEnum repairBizTypeEnum) {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.CREATE.getDesc(), localDateTimeStr, realName, repairBizTypeEnum.getName() + "单");
	}

	/**
	 * 获取维修提交工单content
	 */
	public static String getRepairedSubmitLogContent() {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.REPAIRED_SUBMIT.getDesc(), localDateTimeStr, realName);
	}

	/**
	 * 获取审核通过content
	 */
	public static String getAuditPassLogContent() {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.AUDIT_PASS.getDesc(), localDateTimeStr, realName);
	}

	/**
	 * 获取审核驳回content
	 */
	public static String getAuditRejectLogContent(String rejectReason) {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.AUDIT_FAIL.getDesc(), localDateTimeStr, realName, rejectReason);
	}

	/**
	 * 获取正常关闭content
	 */
	public static String getCloseLogContent() {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.CLOSE.getDesc(), localDateTimeStr, realName);
	}

	/**
	 * 获取设备报废关闭content
	 */
	public static String getScrapCloseLogContent() {
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		return String.format(RepairActionEnum.SCRAP_CLOSE.getDesc(), localDateTimeStr, realName);
	}

	/**
	 * 转为systemEnum
	 */
	private static String convertModule(RepairBizTypeEnum repairBizTypeEnum) {
		if (ObjectUtil.isEmpty(repairBizTypeEnum)) {
			throw new BusinessException("获取日志失败！repairBizTypeEnum不可为空");
		}
		if (RepairBizTypeEnum.INTERNAL == repairBizTypeEnum) {
			return SystemModuleEnum.INTERNAL_REPAIR.getCode();
		}
		if (RepairBizTypeEnum.EXTERNAL == repairBizTypeEnum) {
			return SystemModuleEnum.EXTERNAL_REPAIR.getCode();
		}
		throw new BusinessException("未知的类型");
	}

	/**
	 * 保存故障处理转内部维修单日志
	 */
	public static Boolean saveBizLog(RepairActionEnum actionEnum, RepairBizTypeEnum repairBizTypeEnum, Long bizId, String bizInfoStr, String content) {
		if (ObjectUtil.isEmpty(actionEnum) || ObjectUtil.isEmpty(repairBizTypeEnum)) {
			throw new BusinessException("保存业务日志失败！actionEnum不可为空");
		}
		Repair repair = JSONUtil.toBean(bizInfoStr, Repair.class);
		BizLogVO logVO = new BizLogVO();
		logVO.setBizId(bizId)
			.setBizNo(repair.getNo())
			.setModule(convertModule(repairBizTypeEnum))
			.setBizStatus(Integer.valueOf(actionEnum.getCode()))
			.setContent(content)
			.setBizInfo(bizInfoStr)
			.setOperateUser(AuthUtil.getUserId())
			.setOperateTime(DateUtil.now());
		return bizLogService.submit(logVO);
	}

}
