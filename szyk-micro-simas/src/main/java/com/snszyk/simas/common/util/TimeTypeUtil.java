package com.snszyk.simas.common.util;

import com.snszyk.simas.common.enums.TimeTypeEnum;

import java.time.*;

/**
 * ClassName: TimeTypeUtil
 * Package: com.snszyk.simas.util
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 10:17
 */
public class TimeTypeUtil {
	/**
	 * 获取TimeTypeEnum对应的开始日期
	 *
	 * @param timeType
	 * @return
	 */
	public static LocalDate getStartDate(TimeTypeEnum timeType) {
		final LocalDate localDate = LocalDate.now();
		switch (timeType) {
			// 今天
			case TODAY:
				return LocalDate.now();
			// 7天
			case SEVEN_DAYS:
				return localDate.minusDays(6);
			// 30天
			case THIRTY_DAYS:
				return localDate.minusDays(29);
			// 180天
			case ONE_HUNDRED_EIGHTY_DAYS:
				return localDate.minusDays(179);
			// 1年
			case ONE_YEAR:
				return localDate.minusYears(1).plusDays(1);
		}
		throw new IllegalArgumentException("时间类型错误");
	}
	/**
	 * 获取TypeTimeEnum对应的开始日期时间
	 */
	public static LocalDateTime getStartDateTime(TimeTypeEnum timeType) {
		return getStartDate(timeType).atTime(LocalTime.MIN);
	}
}
