/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel.template;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.simas.common.excel.annotation.ExcelSelected;
import lombok.Data;

import java.util.Date;

/**
 * EquipmentAccountExcelTemplate
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(65)
@ContentRowHeight(18)
public class EquipmentAccountExcelTemplate {

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "设备名称（必填）"}, index = 0)
	private String name;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "SN编号（必填）"}, index = 1)
	private String sn;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "规格型号（必填）"}, index = 2)
	private String model;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "计量单位（必填）"}, index = 3)
	@ExcelSelected(codeField = "measure_unit")
	private String measureUnitName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "设备等级（必填）"}, index = 4)
	@ExcelSelected(codeField = "important_level")
	private String importantLevelName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "设备类型（必填）"}, index = 5)
	private String categoryName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "特种设备类型（非特种设备不用填写）"}, index = 6)
	@ExcelSelected(codeField = "equipment_special_type")
	private String specialTypeName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "特种设备检查周期（天）"}, index = 7)
	private String specialInspectPeriod;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "设备位置（必填）"}, index = 8)
	private String locationName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "购买日期（1991-01-01）"}, index = 9)
	@ContentStyle(dataFormat = 14)
	@DateTimeFormat("yyyy-MM-dd")
	private Date purchaseDateStr;


	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "投产日期（1991-01-01）"}, index = 10)
	@ContentStyle(dataFormat = 14)
	@DateTimeFormat("yyyy-MM-dd")
	private Date productDateStr;


	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "使用部门（必填）"}, index = 11)
	private String useDeptName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "生产厂家"}, index = 12)
	private String supplier;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有要求按照“系统设置填写”的内容，务必保持和系统设置的内容一致，比如设备类型系统设置为“皮带机”，填写表格时，不能填写“皮带”、“传输皮带”等内容；\n" +
		"2、所有必填项必须填写；\n" +
		"3、设备类型、使用部门、设备位置信息，需要填写完整的路径信息，如“(一级位置/二级位置/三级位置)”，如果该设备没有使用部门，则填写“无”；", "联系人员"}, index = 13)
	private String contact;

}
