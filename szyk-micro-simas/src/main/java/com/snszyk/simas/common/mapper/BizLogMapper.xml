<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.BizLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bizLogResultMap" type="com.snszyk.simas.common.entity.BizLog">
        <id column="id" property="id"/>
        <result column="biz_id" property="bizId"/>
        <result column="biz_no" property="bizNo"/>
        <result column="biz_status" property="bizStatus"/>
        <result column="module" property="module"/>
        <result column="content" property="content"/>
        <result column="biz_info" property="bizInfo"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_time" property="operateTime"/>
    </resultMap>

    <resultMap id="bizLogDTOResultMap" type="com.snszyk.simas.common.dto.BizLogDTO">
        <result column="biz_no" property="bizNo"/>
        <result column="module" property="module"/>
        <result column="biz_info" property="bizInfo"/>
    </resultMap>


    <select id="equipmentOrderPage" resultMap="bizLogDTOResultMap">
        SELECT
        t1.*
        FROM simas_biz_log t1
        inner join
        (select biz_id, max(operate_time) as operate_time from simas_biz_log
        where biz_no is not null and biz_info like concat('%', #{search.equipmentId}, '%')
        <if test="search.moduleList != null and search.moduleList.size() > 0">
            and `module` in
            <foreach collection="search.moduleList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="search.bizNo != null and search.bizNo != ''">
            and biz_no like concat('%', #{search.bizNo}, '%')
        </if>
        <if test="search.module != null and search.module != ''">
            and `module` = #{search.module}
        </if>
        group by biz_id ) t
        on t1.biz_id = t.biz_id and t1.operate_time = t.operate_time
        where 1=1
        <if test="search.moduleList != null and search.moduleList.size() > 0">
            and t1.`module` in
            <foreach collection="search.moduleList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY t1.operate_time desc
    </select>
    <select id="pageList" resultType="com.snszyk.simas.common.dto.BizLogDTO">


select * From (SELECT t1.module,
                      t1.content,
                      t1.operate_time,
                      t2.real_name as operate_user_name,
                      t3.dept_name as dept_name,
                      t2.dept_id as dept_id,
                      '' as module_name,
                      '1' as log_type
               FROM simas_biz_log t1
                        left join szyk_user t2 on t1.operate_user = t2.id
                        left join szyk_dept t3 on t2.dept_id = t3.id


               UNION all
               select 'loginLog'                   as module,
                      concat(t10.time, '登录成功') as content,
                      t10.time                     as operate_time,
                      t11.real_name                as operate_user_name,
                      t12.dept_name                as dept_name,
                      t11.dept_id                      as dept_id,
                      '登录登出'                      as module_name,
                      '2'                            as log_type
               From szyk_log_login t10
                        left join szyk_user t11 on t10.user_id = t11.id
                        left join szyk_dept t12 on t11.dept_id = t12.id) tt
        <where>
            <if test="v.startDate != null">
                and date(tt.operate_time) &gt;= #{v.startDate}
            </if>
            <if test="v.endDate != null">
                and date(tt.operate_time) &lt;= #{v.endDate}
            </if>
        </where>

        order by tt.operate_time desc

    </select>


</mapper>
