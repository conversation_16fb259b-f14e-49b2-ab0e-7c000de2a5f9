/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.enums.MoveSourceEnum;
import com.snszyk.simas.common.mapper.DeviceMoveRecordMapper;
import com.snszyk.simas.common.service.IDeviceMoveRecordService;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 设备移动记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Service
@AllArgsConstructor
public class DeviceMoveRecordServiceImpl extends ServiceImpl<DeviceMoveRecordMapper, DeviceMoveRecord> implements IDeviceMoveRecordService {

	private final IDeviceAccountClient deviceAccountClient;


	@Override
	public IPage<DeviceMoveRecordVO> page(IPage<DeviceMoveRecordVO> page, DeviceMoveRecordVO vo) {
		List<DeviceMoveRecordVO> list = baseMapper.page(page, vo);
		if(Func.isNotEmpty(list)){
			list.forEach(record -> {
				if(Func.isNotEmpty(record.getSource())){
					record.setSourceName(Objects.requireNonNull(MoveSourceEnum.getByCode(record.getSource())).getName());
				}
				if(Func.isNotEmpty(record.getUseDept())){
					Dept dept = SysCache.getDept(record.getUseDept());
					if(Func.isNotEmpty(dept)){
						record.setUseDeptName(dept.getDeptName());
					}
				}
				if(Func.isNotEmpty(record.getUserId())){
					User user = UserCache.getUser(record.getUserId());
					if(Func.isNotEmpty(user)){
						record.setUserName(user.getRealName());
					}
				}
				if(Func.isNotEmpty(record.getOperateUser())){
					User operateUser = UserCache.getUser(record.getOperateUser());
					if(Func.isNotEmpty(operateUser)){
						record.setOperateUserName(operateUser.getRealName());
					}
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(DeviceMoveRecordVO vo) {
		// 校验设备移动前位置
		R<DeviceAccountVO> deviceR = deviceAccountClient.deviceInfoById(vo.getDeviceId());
		if (!deviceR.isSuccess()) {
			throw new ServiceException("查询设备台账失败，请重试！");
		}
		if (Func.isEmpty(deviceR.getData())) {
			throw new ServiceException("设备台账不存在，请刷新后重试！");
		}
		DeviceAccountVO deviceAccount = deviceR.getData();
		if(!Func.equals(deviceAccount.getLocationId(), vo.getOriginalLocation())){
			throw new ServiceException("设备位置已改变，请刷新后重试！");
		}
		if(!Func.equals(deviceAccount.getUseDept(), vo.getUseDept())){
			throw new ServiceException("设备使用部门已改变，请刷新后重试！");
		}
		if(!Func.equals(deviceAccount.getUserId(), vo.getUserId())){
			throw new ServiceException("设备使用人已改变，请刷新后重试！");
		}
		if(Func.equals(vo.getOriginalLocation(), vo.getNewLocation())){
			throw new ServiceException("移动后位置不能与移动前位置一样，请修改！");
		}
		DeviceMoveRecord deviceMoveRecord = Objects.requireNonNull(BeanUtil.copy(vo, DeviceMoveRecord.class));
		deviceMoveRecord.setNo(BizCodeUtil.generate("M"))
			.setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
		// 更新设备台账位置信息
		DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		deviceAccountVO.setId(vo.getDeviceId());
		deviceAccountVO.setLocationId(vo.getNewLocation());
		R r = deviceAccountClient.updateLocation(deviceAccountVO);
		if(!r.isSuccess() || !Func.equals(Boolean.TRUE, r.getData())){
			log.error("更新设备台账位置失败，设备id：{}", vo.getDeviceId());
			throw new ServiceException("更新设备台账位置失败，请重试！");
		}
		return this.save(deviceMoveRecord);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveRecords(DeviceMoveRecordVO vo) {
		if(Func.isEmpty(vo.getDeviceIds())){
			return false;
		}
		vo.getDeviceIds().forEach(deviceId -> {
			R<DeviceAccountVO> deviceR = deviceAccountClient.deviceInfoById(deviceId);
			if (!deviceR.isSuccess()) {
				throw new ServiceException("查询设备台账失败，请重试！");
			}
			if (Func.isEmpty(deviceR.getData())) {
				throw new ServiceException("设备台账不存在，请刷新后重试！");
			}
			DeviceAccountVO deviceAccount = deviceR.getData();
			if(Func.equals(deviceAccount.getLocationId(), vo.getNewLocation())){
				throw new ServiceException("移动后位置不能与移动前位置一样，请修改！");
			}
			DeviceMoveRecord deviceMoveRecord = Objects.requireNonNull(BeanUtil.copy(vo, DeviceMoveRecord.class));
			deviceMoveRecord.setDeviceId(deviceId).setOriginalLocation(deviceAccount.getLocationId())
				.setNo(BizCodeUtil.generate("M"));
			this.save(deviceMoveRecord);
			// 更新设备台账位置信息
			DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
			deviceAccountVO.setId(deviceId);
			deviceAccountVO.setLocationId(vo.getNewLocation());
			R<Boolean> r = deviceAccountClient.updateLocation(deviceAccountVO);
			if(!r.isSuccess() || !Func.equals(Boolean.TRUE, r.getData())){
				log.error("更新设备台账位置失败，设备id：{}", deviceId);
				throw new ServiceException("更新设备台账位置失败，请重试！");
			}
		});
		return false;
	}


}
