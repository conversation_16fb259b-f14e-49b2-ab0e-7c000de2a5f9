// /*
//  *      Copyright (c) 2018-2028
//  */
// package com.snszyk.simas.common.controller;
//
// import com.baomidou.mybatisplus.core.metadata.IPage;
// import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
// import com.snszyk.core.boot.ctrl.SzykController;
// import com.snszyk.core.excel.util.ExcelUtil;
// import com.snszyk.core.mp.support.Condition;
// import com.snszyk.core.mp.support.Query;
// import com.snszyk.core.tool.api.R;
// import com.snszyk.core.tool.utils.DateUtil;
// import com.snszyk.simas.common.excel.InspectPlanStatisticsExcel;
// import com.snszyk.simas.common.excel.MaintainPlanStatisticsExcel;
// import com.snszyk.simas.common.service.logic.StatisticsLogicService;
// import com.snszyk.simas.inspect.dto.InspectPlanDTO;
// import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
// import com.snszyk.simas.common.dto.StatisticsDTO;
// import com.snszyk.simas.inspect.vo.InspectPlanVO;
// import com.snszyk.simas.maintain.vo.MaintainPlanVO;
// import io.swagger.annotations.*;
// import lombok.AllArgsConstructor;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.RestController;
// import springfox.documentation.annotations.ApiIgnore;
//
// import javax.servlet.http.HttpServletResponse;
// import java.util.List;
//
// /**
//  * 统计相关 控制器
//  *
//  * <AUTHOR>
//  * @since 2024-08-20
//  */
// @RestController
// @AllArgsConstructor
// @RequestMapping("/statistics")
// @Api(value = "统计相关接口", tags = "统计相关接口")
// public class StatisticsController extends SzykController {
//
// 	private final StatisticsLogicService logicService;
//
// 	/**
// 	 * 点巡检工单完成情况
// 	 */
// 	@GetMapping("/inspect-order")
// 	@ApiOperationSupport(order = 1)
// 	@ApiOperation(value = "点巡检工单完成情况", notes = "传入queryDate")
// 	public R<StatisticsDTO> inspectOrder(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.inspectOrderStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 点巡检异常统计
// 	 */
// 	@GetMapping("/inspect-abnormal")
// 	@ApiOperationSupport(order = 2)
// 	@ApiOperation(value = "点巡检异常统计", notes = "传入queryDate")
// 	public R<StatisticsDTO> inspectAbnormal(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.inspectAbnormalStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 点巡检任务统计
// 	 */
// 	@GetMapping("/inspect-task")
// 	@ApiOperationSupport(order = 3)
// 	@ApiOperation(value = "点巡检任务统计", notes = "传入queryDate")
// 	public R<StatisticsDTO> inspectTask(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.inspectTaskStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 点巡检计划统计分页
// 	 */
// 	@GetMapping("/inspect-plan")
// 	@ApiImplicitParams({
// 		@ApiImplicitParam(name = "keywords", value = "查询-计划名称或编号", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
// 	})
// 	@ApiOperationSupport(order = 4)
// 	@ApiOperation(value = "点巡检计划统计分页", notes = "传入inspectPlan")
// 	public R<IPage<InspectPlanDTO>> inspectPlan(@ApiIgnore InspectPlanVO inspectPlan, Query query) {
// 		return R.data(logicService.inspectPlanPage(Condition.getPage(query), inspectPlan));
// 	}
//
// 	/**
// 	 * 导出点检计划列表
// 	 */
// 	@GetMapping("/export-plan")
// 	@ApiImplicitParams({
// 		@ApiImplicitParam(name = "keywords", value = "查询-计划名称或编号", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
// 	})
// 	@ApiOperationSupport(order = 5)
// 	@ApiOperation(value = "导出点检计划列表", notes = "传入inspectPlan")
// 	public void exportPlan(@ApiIgnore InspectPlanVO inspectPlan, HttpServletResponse response) {
// 		List<InspectPlanStatisticsExcel> list = logicService.exportPlan(inspectPlan);
// 		ExcelUtil.export(response, "点巡检计划统计列表" + DateUtil.time(), "点巡检计划统计", list, InspectPlanStatisticsExcel.class);
// 	}
//
// 	/**
// 	 * 保养工单完成情况
// 	 */
// 	@GetMapping("/maintain-order")
// 	@ApiOperationSupport(order = 6)
// 	@ApiOperation(value = "保养工单完成情况", notes = "传入queryDate")
// 	public R<StatisticsDTO> maintainOrder(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.maintainOrderStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 保养异常统计
// 	 */
// 	@GetMapping("/maintain-abnormal")
// 	@ApiOperationSupport(order = 7)
// 	@ApiOperation(value = "保养异常统计", notes = "传入queryDate")
// 	public R<StatisticsDTO> maintainAbnormal(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.maintainAbnormalStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 保养任务统计
// 	 */
// 	@GetMapping("/maintain-task")
// 	@ApiOperationSupport(order = 8)
// 	@ApiOperation(value = "保养任务统计", notes = "传入queryDate")
// 	public R<StatisticsDTO> maintainTask(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
// 		required = true) @RequestParam Integer queryDate) {
// 		return R.data(logicService.maintainTaskStatistics(queryDate));
// 	}
//
// 	/**
// 	 * 保养计划统计分页
// 	 */
// 	@GetMapping("/maintain-plan")
// 	@ApiImplicitParams({
// 		@ApiImplicitParam(name = "keywords", value = "查询-计划名称或编号", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
// 	})
// 	@ApiOperationSupport(order = 9)
// 	@ApiOperation(value = "保养计划统计分页", notes = "传入maintainPlan")
// 	public R<IPage<MaintainPlanDTO>> maintainPlan(@ApiIgnore MaintainPlanVO maintainPlan, Query query) {
// 		return R.data(logicService.maintainPlanPage(Condition.getPage(query), maintainPlan));
// 	}
//
// 	/**
// 	 * 导出保养计划列表
// 	 */
// 	@GetMapping("/export-maintain-plan")
// 	@ApiImplicitParams({
// 		@ApiImplicitParam(name = "keywords", value = "查询-计划名称或编号", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
// 		@ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
// 	})
// 	@ApiOperationSupport(order = 10)
// 	@ApiOperation(value = "导出保养计划列表", notes = "传入maintainPlan")
// 	public void exportMaintainPlan(@ApiIgnore MaintainPlanVO maintainPlan, HttpServletResponse response) {
// 		List<MaintainPlanStatisticsExcel> list = logicService.exportMaintainPlan(maintainPlan);
// 		ExcelUtil.export(response, "保养计划统计列表" + DateUtil.time(), "保养计划统计", list, MaintainPlanStatisticsExcel.class);
// 	}
//
//
// }
