package com.snszyk.simas.common.llm;

import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.exception.LlmIdGenException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EquipmentIdGenerationStrategy implements IdGenerationStrategy {

	@Autowired
	private IDeviceAccountClient equipmentAccountService;

	@Override
	public String getElement() {
		return "equipment";
	}

	@Override
	public String getId(String equipmentName) {
		DeviceAccountVO vo = new DeviceAccountVO();
		vo.setName(equipmentName);
		R<DeviceAccountVO> deviceAccountVOR = equipmentAccountService.deviceInfoByParams(vo);
		if (!deviceAccountVOR.isSuccess() && deviceAccountVOR.getData() == null) {
			throw new LlmIdGenException("抱歉，没有识别到您输入的设备，您可以描述的更具体一些");
		}
		return equipmentName + "|" + deviceAccountVOR.getData().getId();
	}

}
