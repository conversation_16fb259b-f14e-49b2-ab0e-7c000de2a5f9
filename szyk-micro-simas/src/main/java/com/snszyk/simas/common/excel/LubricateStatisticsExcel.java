/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * LubricateStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class LubricateStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 设备名称
	 */
	@ExcelProperty(value = "设备名称")
	@ApiModelProperty(value = "设备名称")
	private String name;
	/**
	 * 设备编号
	 */
	@ExcelProperty(value = "设备编号")
	@ApiModelProperty(value = "设备编号")
	private String sn;
	/**
	 * 设备类型
	 */
	@ExcelProperty(value = "设备类型")
	@ApiModelProperty(value = "设备类型")
	private String categoryName;
	/**
	 * 归属部门
	 */
	@ExcelProperty(value = "归属部门")
	@ApiModelProperty(value = "归属部门")
	private String useDeptName;
	/**
	 * 次数
	 */
	@ExcelProperty(value = "润滑次数")
	@ApiModelProperty(value = "润滑次数")
	private Integer count;
	/**
	 *
	 */
	@ExcelProperty(value = "完成次数")
	@ApiModelProperty(value = "完成次数")
	private Integer completeCount;
	/**
	 *
	 */
	@ExcelProperty(value = "未完成次数")
	@ApiModelProperty(value = "未完成次数")
	private Integer unfinishedCount;
	/**
	 *
	 */
	@ExcelProperty(value = "完成率")
	@ApiModelProperty(value = "完成率")
	private String completeRate;


}
