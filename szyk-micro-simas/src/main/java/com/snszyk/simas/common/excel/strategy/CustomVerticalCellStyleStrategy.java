package com.snszyk.simas.common.excel.strategy;

import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.AbstractVerticalCellStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * 自定义表头和内容的样式
 */
public class CustomVerticalCellStyleStrategy extends AbstractVerticalCellStyleStrategy {


	public CustomVerticalCellStyleStrategy() {}


	@Override
	protected WriteCellStyle headCellStyle(CellWriteHandlerContext context) {

		Cell cell = context.getCell();
		WriteCellStyle headCellStyle = new WriteCellStyle();

		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setBold(false);
		headWriteFont.setFontName("等线");
		headWriteFont.setFontHeightInPoints((short)11);

		headCellStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		headCellStyle.setWriteFont(headWriteFont);
		headCellStyle.setWrapped(true);// 自动换行
		headCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

		// 设置表头的样式
		if (0 == cell.getRowIndex()) {
			headCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
			headWriteFont.setColor(IndexedColors.RED.getIndex());
		} else if (1 == cell.getRowIndex()){
			headWriteFont.setColor(IndexedColors.BLACK.getIndex());
			headCellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
			headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		}
		return headCellStyle;
	}

	@Override
	protected WriteCellStyle contentCellStyle(CellWriteHandlerContext context) {
		Cell cell = context.getCell();
		// 第三列设置为文本格式
		int contentColumnIndex = cell.getColumnIndex();

		WriteCellStyle contentCellStyle = new WriteCellStyle();
		WriteFont headWriteFont = new WriteFont();
		headWriteFont.setBold(false);
		headWriteFont.setFontName("等线");
		contentCellStyle.setWriteFont(headWriteFont);

		contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		contentCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

		if (1 == contentColumnIndex) {
			DataFormatData dataFormatData = new DataFormatData();
			dataFormatData.setIndex((short)49);
			contentCellStyle.setDataFormatData(dataFormatData);
		}
		return contentCellStyle;
	}
}
