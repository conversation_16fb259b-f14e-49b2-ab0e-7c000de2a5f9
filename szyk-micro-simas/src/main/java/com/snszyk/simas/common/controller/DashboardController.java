/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.DashboardDTO;
import com.snszyk.simas.common.service.logic.DashboardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大屏数据 控制器
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard")
@Api(value = "大屏数据接口", tags = "大屏数据接口")
public class DashboardController extends SzykController {

	private final DashboardLogicService logicService;
	// get 请求 工单的消息通知
	// @GetMapping("/notice")
	// @ApiOperationSupport(order = 0)
	// @ApiOperation(value = "0.工单的消息通知", notes = "传入")
	// public R<List<BigScreenMessageDTO>> notice() {
	// 	return R.data(logicService.notice());
	// }
	// /**
	//  * (1)维修情况
	//  *  0：近1年；1：近30天；2：近7天
	//  */
	// @GetMapping("/repair-statistics")
	// @ApiImplicitParams({
	// 	@ApiImplicitParam(name = "bizType", value = "维修类型", paramType = "query", dataType = "string")
	// })
	// @ApiOperationSupport(order = 1)
	// @ApiOperation(value = "1.维修情况", notes = "传入bizType")
	// public R<DashboardDTO> repairStatistics(@ApiIgnore RepairVO repair) {
	// 	return R.data(logicService.repairStatistics(repair,1));
	// }
	//
	// /**
	//  *
	//  *  (2)点巡检情况（当日）
	//  */
	// @GetMapping("/inspect-statistics")
	// @ApiOperationSupport(order = 2)
	// @ApiOperation(value = "2.点巡检情况（当日）", notes = "传入")
	// public R<DashboardDTO> inspectStatistics() {
	// 	return R.data(logicService.inspectStatistics());
	// }
	//
	// /**
	//  * (3)保养情况（近30天）
	//  */
	// @GetMapping("/maintain-statistics")
	// @ApiOperationSupport(order = 3)
	// @ApiOperation(value = "3.保养情况（近30天）", notes = "传入queryDate")
	// public R<DashboardDTO> maintainStatistics(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
	// 	required = true) @RequestParam Integer queryDate) {
	// 	return R.data(logicService.maintainStatistics(queryDate));
	// }
	//
	// /**
	//  * (4)润滑情况
	//  */
	// @GetMapping("/lubricate-statistics")
	// @ApiOperationSupport(order = 4)
	// @ApiOperation(value = "4.润滑情况 近30天", notes = "传入")
	// public R<DashboardDTO> lubricateStatistics() {
	// 	return R.data(logicService.lubricateStatistics());
	// }

	/**
	 * (5)设备概览
	 */
	@GetMapping("/equipment-summary")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "5.设备概览", notes = "传入")
	public R<DashboardDTO> equipmentSummary() {
		return R.data(logicService.equipmentSummary());
	}
	// /**
	//  * *(6)二级部门的设备使用情况
	//  */
	// @GetMapping("/sec-equipment-statistics")
	// @ApiOperationSupport(order = 6)
	// @ApiOperation(value = "6.二级部门的设备使用情况", notes = "传入useDept")
	// public R<DashboardDTO> secEquipmentStatistics() {
	// 	return R.data(logicService.secEquipmentList());
	// }
	//
	//
	// /**
	//  * (7)故障缺陷情况（近30天）
	//  */
	// @GetMapping("/fault-defect-statistics")
	// @ApiOperationSupport(order = 7)
	// @ApiOperation(value = "7.故障缺陷情况（近30天）", notes = "传入queryDate")
	// public R<DashboardDTO> faultDefectStatistics(@ApiParam(value = "按时间查询（0：近1年；1：近30天；2：近7天）",
	// 	required = true) @RequestParam Integer queryDate) {
	// 	return R.data(logicService.faultDefectStatistics(queryDate));
	// }
	//
	// /**
	//  * (8)维修耗时统计（近一年）
	//  */
	// @GetMapping("/repair-time-take")
	// @ApiImplicitParams({
	// 	@ApiImplicitParam(name = "bizType", value = "维修类型", paramType = "query", dataType = "string")
	// })
	// @ApiOperationSupport(order = 8)
	// @ApiOperation(value = "8.维修耗时统计(本年）", notes = "传入bizType")
	// public R<DashboardDTO> repairTimeTake(@ApiIgnore RepairVO repair) {
	// 	return R.data(logicService.repairTimeTake(repair.getBizType(), null));
	// }
	//
	// /**
	//  * (9)点巡检覆盖率（当日）
	//  */
	// @GetMapping("/inspect-cover-rate")
	// @ApiOperationSupport(order = 9)
	// @ApiOperation(value = "9点巡检覆盖率（当日）", notes = "传入")
	// public R<DashboardDTO> inspectCoverRate() {
	// 	return R.data(logicService.inspectCoverRate());
	// }
	//
	// /**
	//  * (10)保养覆盖率（近30天）
	//  */
	// @GetMapping("/maintain-cover-rate")
	// @ApiOperationSupport(order = 10)
	// @ApiOperation(value = "10.保养覆盖率（近30天）", notes = "传入queryDate")
	// public R<DashboardDTO> maintainCoverRate() {
	// 	return R.data(logicService.maintainCoverRate());
	// }
	//
	//
	// /**
	//  * (11)配件与耗材排名
	//  */
	// @GetMapping("/component-material-rank")
	// @ApiImplicitParams({
	// 	@ApiImplicitParam(name = "bizModule", value = "业务模块 保养:MAINTAIN_ORDER  维修:REPAIR ", paramType = "query", dataType = "string")
	// })
	// @ApiOperationSupport(order = 11)
	// @ApiOperation(value = "11.配件与耗材排名", notes = "传入bizModule")
	// public R<DashboardDTO> componentMaterialRank(@ApiIgnore ComponentMaterialVO componentMaterial) {
	// 	return R.data(logicService.componentMaterialRank(componentMaterial.getBizModule()));
	// }
	//
	// /**
	//  * 告警工单列表
	//  */
	// @GetMapping("/warning-order-list")
	// @ApiOperationSupport(order = 10)
	// @ApiOperation(value = "告警工单列表", notes = "传入")
	// public R<DashboardDTO> warningOrderList() {
	// 	return R.data(logicService.warningOrderList());
	// }
	//
	// /**
	//  * 设备情况统计
	//  */
	// @GetMapping("/equipment-statistics")
	// @ApiImplicitParams({
	// 	@ApiImplicitParam(name = "useDept", value = "所属部门", paramType = "query", dataType = "long")
	// })
	// @ApiOperationSupport(order = 12)
	// @ApiOperation(value = "设备情况统计", notes = "传入equipmentAccount")
	// public R<DashboardDTO> equipmentStatistics(@ApiIgnore EquipmentAccountVO equipmentAccount) {
	// 	return R.data(logicService.equipmentStatistics(equipmentAccount.getUseDept()));
	// }


}
