package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.converters.date.DateDateConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ClassName: SealPointExcel
 * Package: com.snszyk.simas.excel
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 19:47
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SealPointExcel {

	/**
	 * 密封点名称
	 */
	@ExcelProperty(value = "密封点名称")
	private String name;
	/**
	 * 密封点位置
	 */
	@ExcelProperty(value = "密封点位置")
	private String location;
	/**
	 * 密封点类型名称
	 */
	@ExcelProperty(value = "密封点类型")
	private String typeName;
	/**
	 * 密封点状态名称
	 */
	@ExcelProperty(value = "密封点状态")
	private String statusName;
	/**
	 * 更新人姓名
	 */
	@ExcelProperty(value = "更新人姓名")
	private String updateUserName;
	/**
	 * 更新时间
	 */
	@ExcelProperty(value = "更新时间", converter = DateDateConverter.class)
	private Date updateTime;

	@ExcelIgnore
	private Integer type;
	@ExcelIgnore
	private Integer status;
	@ExcelIgnore
	private Long updateUser;
}
