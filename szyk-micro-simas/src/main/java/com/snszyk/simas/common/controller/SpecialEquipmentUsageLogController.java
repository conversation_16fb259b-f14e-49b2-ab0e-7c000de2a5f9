package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.SpecialEquipmentUsageLogDto;
import com.snszyk.simas.common.service.logic.SpecialEquipmentUsageLogLogicService;
import com.snszyk.simas.common.vo.SpecialEquipmentUsageLogVo;
import com.snszyk.simas.common.vo.SpecialEquipmentUsagePageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * ClassName: SpecialEquipmentUsageLogController
 * Package: com.snszyk.simas.controller
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:23
 */
@RestController
@AllArgsConstructor
@RequestMapping("special-equipment-usage-log")
@Api(value = "特种设备使用记录", tags = "特种设备使用记录接口")
@Validated
@ApiSupport(author = "zzp", order = 99)
public class SpecialEquipmentUsageLogController {
	private final SpecialEquipmentUsageLogLogicService specialEquipmentUsageLogLogicService;

	/**
	 * 新增设备等级
	 */
	@PostMapping
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增", notes = "")
	public R<Boolean> save(@RequestBody @Validated SpecialEquipmentUsageLogVo v) {
		return R.status(specialEquipmentUsageLogLogicService.save(v));
	}

	/**
	 * 分页查询
	 */
	@GetMapping("page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页查询", notes = "")
	public R<IPage<SpecialEquipmentUsageLogDto>> page(SpecialEquipmentUsagePageVo v) {
		return R.data(specialEquipmentUsageLogLogicService.page(v));
	}

	/**
	 * 导出
	 */
	@GetMapping("export")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "导出", notes = "")
	public void export(SpecialEquipmentUsagePageVo v, HttpServletResponse response) {
		specialEquipmentUsageLogLogicService.export(v, response);
	}

}
