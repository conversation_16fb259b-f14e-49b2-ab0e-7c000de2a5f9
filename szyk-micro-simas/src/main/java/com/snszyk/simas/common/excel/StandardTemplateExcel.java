/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * EquipmentLabelExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class StandardTemplateExcel implements Serializable {
	private static final long serialVersionUID = 1L;


	@ExcelProperty("设备编号")
	private String equipmentCode;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("部位名称")
	private String monitorName;

	@ExcelProperty("检查标准")
	private String checkStandard;

	@ExcelProperty("检查方法")
	private String checkMethod;

	public StandardTemplateExcel(){
		super();
	}

	public StandardTemplateExcel(String equipmentCode, String equipmentName){
		super();
		this.equipmentCode = equipmentCode;
		this.equipmentName = equipmentName;
	}

}
