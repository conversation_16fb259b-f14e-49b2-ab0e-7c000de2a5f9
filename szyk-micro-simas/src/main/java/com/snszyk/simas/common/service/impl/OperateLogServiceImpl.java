/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.simas.common.entity.OperateLog;
import com.snszyk.simas.common.enums.OperateTypeEnum;
import com.snszyk.simas.common.excel.OperateLogExcel;
import com.snszyk.simas.common.mapper.OperateLogMapper;
import com.snszyk.simas.common.service.IOperateLogService;
import com.snszyk.simas.common.vo.OperateLogVO;
import com.snszyk.simas.common.wrapper.OperateLogWrapper;
import com.snszyk.user.utils.IpUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 操作日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLog> implements IOperateLogService {

	@Override
	public IPage<OperateLogVO> page(IPage<OperateLogVO> page, OperateLogVO operateLog) {
		if (Func.isNotEmpty(operateLog.getStartDate())) {
			operateLog.setStartDate(operateLog.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(operateLog.getEndDate())) {
			operateLog.setEndDate(operateLog.getEndDate() + DateUtils.DAY_END_TIME);
		}
		operateLog.setTenantId(AuthUtil.getTenantId());
		List<OperateLog> operateLogList = baseMapper.page(page, operateLog);
		if (Func.isNotEmpty(operateLogList)) {
			List<OperateLogVO> list = OperateLogWrapper.build().listVO(operateLogList);
			return page.setRecords(list);
		}
		return page.setRecords(null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(OperateLogVO vo) {
		OperateLog operateLog = Objects.requireNonNull(BeanUtil.copy(vo, OperateLog.class));
		HttpServletRequest request = WebUtil.getRequest();
		operateLog.setHost(IpUtil.getRealIP(request));
		if (OperateTypeEnum.RETRIEVE == OperateTypeEnum.getByCode(operateLog.getType())) {
			operateLog.setRequest("GET");
		} else {
			operateLog.setRequest("POST");
		}
		operateLog.setOperateUser(AuthUtil.getUserId());
		operateLog.setOperateTime(DateUtil.now());
		operateLog.setTenantId(AuthUtil.getTenantId());
		return this.save(operateLog);
	}

	@Override
	public List<OperateLogExcel> exportLog(OperateLogVO vo) {
		final IPage<OperateLogVO> page = this.page(new Page<>(1, -1L), vo);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return Collections.emptyList();
		}
		return BeanUtil.copy(page.getRecords(), OperateLogExcel.class);
	}

}
