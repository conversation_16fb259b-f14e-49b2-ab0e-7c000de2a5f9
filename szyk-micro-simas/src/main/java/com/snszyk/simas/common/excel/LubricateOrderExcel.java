/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.core.tool.utils.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * InspectOrderExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(30)
@ContentRowHeight(18)
public class LubricateOrderExcel implements Serializable {
	private static final long serialVersionUID = 1L;


	@ExcelProperty("工单编号")
	private String no;

	@ExcelProperty("工单名称")
	private String name;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("负责部门")
	private String chargeDeptName;

	@ExcelProperty("计划时间")
	@DateTimeFormat(value = DateUtil.PATTERN_DATE)
	private Date planTime;

	@ExcelProperty("执行时间")
	@DateTimeFormat(value = DateUtil.PATTERN_DATE)
	private Date executeTime;

	@ExcelProperty("浮动时间（天）")
	private Integer floatTime;

	@ExcelProperty("执行人员")
	private String executeUserName;

	@ExcelProperty("检查人员")
	private String checkUserName;

	@ExcelProperty("工单状态")
	private String statusName;

}
