package com.snszyk.simas.common.excel.support;

import com.alibaba.excel.EasyExcel;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.simas.common.excel.handler.AddCellRangeWriteHandler;
import com.snszyk.simas.common.excel.handler.SelectedSheetWriteHandler;
import com.snszyk.simas.common.excel.strategy.CustomVerticalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.poi.ss.util.CellRangeAddress;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class EasyExcelUtil {


	/**
	 * 下载模版
	 *
	 * @param response 响应
	 * @param fileName 文件名
	 * @param sheetName sheet名
	 * @param dataList 初始模版数据
	 * @param clazz 模板类
	 */
	public static <T extends Object> void writer(HttpServletResponse response, String fileName, String sheetName, List<T> dataList, Class<T> clazz){
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(Charsets.UTF_8.name());
			fileName = URLEncoder.encode(fileName, Charsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
			EasyExcel.write(response.getOutputStream(), clazz)
				.registerWriteHandler(new SelectedSheetWriteHandler(clazz))
				.registerWriteHandler(new CustomVerticalCellStyleStrategy())
				.sheet(sheetName).doWrite(dataList);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 内容数据合并单元格模版下载
	 *
	 * @param response 响应
	 * @param fileName 文件名
	 * @param sheetName sheet名
	 * @param dataList 初始模版数据
	 * @param clazz 模板类
	 * @param mergeMap 合并行集合
	 * @param mergeColumnIndex 合并列索引
	 * @param lineCount 开始行数
	 */
	public static <T extends Object> void writerTwo(HttpServletResponse response, String fileName, String sheetName, List<T> dataList, Class<T> clazz, LinkedHashMap<String, Integer> mergeMap, int[] mergeColumnIndex, int lineCount){
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding(Charsets.UTF_8.name());
			fileName = URLEncoder.encode(fileName, Charsets.UTF_8.name());
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
			EasyExcel.write(response.getOutputStream(), clazz)
				.registerWriteHandler(new SelectedSheetWriteHandler(clazz))
				.registerWriteHandler(new AddCellRangeWriteHandler(createCellRange(mergeMap, mergeColumnIndex, lineCount)))
				.registerWriteHandler(new CustomVerticalCellStyleStrategy())
				.sheet(sheetName).doWrite(dataList);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 生成合并区
	 *
	 * @param mergeMap 合并集合
	 * @param mergeColumnIndex sheet 中需要合并的列的索引
	 * @param lineCount 行计数（包括列头行）
	 * @return 合并区
	 */
	public static List<CellRangeAddress> createCellRange(LinkedHashMap<String, Integer> mergeMap, int[] mergeColumnIndex, int lineCount) {
		if (mergeMap.isEmpty()) {
			return Collections.emptyList();
		}
		log.info("============mergeMap======================" + JsonUtil.toJson(mergeMap));
		List<CellRangeAddress> rangeCellList = new ArrayList<>();
		Iterator<Integer> i = mergeMap.values().iterator();
		while (i.hasNext()) {
			int count = i.next();
			int startRowIndex = lineCount;
			// 如合并第2到4行，共3行，行索引从1到3
			int endRowIndex = lineCount + count - 1;
			lineCount += count;
			if (count < 2){
				continue;
			}
			for (int columnIndex : mergeColumnIndex) {
				log.info("columnIndex:{},startRowIndex:{},endRowIndex:{}", columnIndex, startRowIndex, endRowIndex);
				rangeCellList.add(new CellRangeAddress(startRowIndex, endRowIndex, columnIndex, columnIndex));
			}
		}
		return rangeCellList;
	}
}
