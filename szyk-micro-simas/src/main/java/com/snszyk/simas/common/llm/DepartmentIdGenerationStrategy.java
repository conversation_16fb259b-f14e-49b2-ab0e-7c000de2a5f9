package com.snszyk.simas.common.llm;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.exception.LlmIdGenException;
import com.snszyk.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @since 2025/2/12 11:34
 *
 **/

@Component
public class DepartmentIdGenerationStrategy implements IdGenerationStrategy {

	@Autowired
	private ISysClient sysClient;

	@Override
	public String getElement() {
		return "dept";
	}

	@Override
	public String getId(String department) {
		R<String> deptIds = sysClient.getDeptIds(AuthUtil.getTenantId(), department);
		if (deptIds.isSuccess()) {
			String data = deptIds.getData();
			if (Func.isEmpty(data)) {
				throw new LlmIdGenException("抱歉，没有识别到您输入的部门，您可以描述的更具体一些");
			}
			if (data.contains(",")) {
				data = data.split(",")[0];
			}
			return department + "|" + data;
		}
		return null;
	}
}
