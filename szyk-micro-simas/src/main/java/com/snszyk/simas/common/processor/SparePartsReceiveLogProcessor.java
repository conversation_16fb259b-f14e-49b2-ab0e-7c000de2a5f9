package com.snszyk.simas.common.processor;

import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.spare.enums.SparePartsIssuanceOrderActionEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * ClassName: SparePartsReciveLogProcessor
 * Package: com.snszyk.simas.processor
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/16 15:23
 */
public class SparePartsReceiveLogProcessor {

	private static IBizLogService bizLogService;

	static {
		bizLogService = SpringUtil.getBean(IBizLogService.class);
	}


	/**
	 * 根据动作获取操作日志信息
	 *
	 * @param actionEnum   动作枚举
	 * @param rejectReason 驳回原因
	 * @return
	 */
	public static String getLogContent(SparePartsIssuanceOrderActionEnum actionEnum, String rejectReason) {
		if (ObjectUtil.isEmpty(actionEnum)) {
			throw new BusinessException("获取日志失败！动作不可为空");
		}
		// 驳回时不许为空
		if (actionEnum == SparePartsIssuanceOrderActionEnum.AUDIT_FAIL && ObjectUtil.isEmpty(rejectReason)) {
			throw new BusinessException("获取日志失败！驳回原因不可为空");
		}
		// 获取当前时间
		final String localDateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATETIME));
		// 获取操作人名，默认为"系统"
		String realName = Optional.ofNullable(AuthUtil.getUserId())
			.map(UserCache::getUser)
			.map(User::getRealName)
			.orElse("系统");
		// 驳回
		if (SparePartsIssuanceOrderActionEnum.AUDIT_FAIL == actionEnum) {
			return String.format(actionEnum.getDesc(), localDateTimeStr, realName, rejectReason);
		} else {
			return String.format(actionEnum.getDesc(), localDateTimeStr, realName);
		}
	}

	/**
	 * 保存日志
	 *
	 * @param actionEnum
	 * @param bizId
	 * @param bizInfoStr
	 * @param rejectReason
	 * @return
	 */
	public static Boolean saveBizLog(SparePartsIssuanceOrderActionEnum actionEnum, Long bizId, String bizInfoStr, String rejectReason) {
		if (ObjectUtil.isEmpty(bizId) || ObjectUtil.isEmpty(actionEnum)) {
			throw new ServiceException("保存业务日志失败！entity或actionEnum不可为空");
		}
		BizLogVO logVO = new BizLogVO();
		logVO.setBizId(bizId)
			.setBizNo(null)
			.setModule(SystemModuleEnum.SPARE_PART_RECEIVE.getCode())
			.setBizStatus(Integer.valueOf(actionEnum.getCode()))
			.setContent(getLogContent(actionEnum, rejectReason))
			.setBizInfo(bizInfoStr)
			.setOperateUser(AuthUtil.getUserId())
			.setOperateTime(DateUtil.now());
		return bizLogService.submit(logVO);
	}
}
