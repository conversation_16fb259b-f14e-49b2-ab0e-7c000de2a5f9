/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备件耗材表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
public interface ComponentMaterialMapper extends BaseMapper<ComponentMaterial> {


	/**
	 * 统计报表-备件损耗统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<ComponentMaterialVO> statisticalReport(IPage page, @Param("search") StatisticSearchVO search);

	/**
	 * 统计报表-导出备件损耗统计
	 *
	 * @param search
	 * @return
	 */
	List<ComponentMaterialVO> exportList(@Param("search") StatisticSearchVO search);

}
