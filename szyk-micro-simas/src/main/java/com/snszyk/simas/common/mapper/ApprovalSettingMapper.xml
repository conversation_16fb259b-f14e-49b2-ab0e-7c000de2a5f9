<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.ApprovalSettingMapper">

    <sql id="selectData">
        select t.*, u.real_name as update_user_name
        from simas_approval_setting t
        left join szyk_user u on t.update_user = u.id and u.is_deleted = 0
    </sql>
    <select id="pageList" resultType="com.snszyk.simas.common.dto.ApprovalSettingDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
        <if test="v.tenantId != null and v.tenantId != ''">
            and t.tenant_id = #{v.tenantId}
        </if>
    </select>

    <select id="detail" resultType="com.snszyk.simas.common.dto.ApprovalSettingDto">
        <include refid="selectData"/>
        where t.is_deleted = 0 and t.id=#{id}
    </select>

</mapper>
