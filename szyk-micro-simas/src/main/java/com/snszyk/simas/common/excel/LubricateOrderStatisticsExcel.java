package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.Date;

/**
 * ClassName: LubricateOrderStatisticsExcel
 * Package: com.snszyk.simas.excel
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/14 19:34
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class LubricateOrderStatisticsExcel {

	@ExcelProperty(value = "工单号")
	private String no;

	@ExcelProperty(value = "设备名称")
	private String equipmentName;

	@ExcelProperty(value = "设备类型")
	private String equipmentCategoryName;

	@ExcelProperty(value = "计划时间")
	private Date planTime;

	@ExcelProperty(value = "执行人")
	private String executeUserName;

	@ExcelProperty(value = "完成时间")
	private Date completeTime;

	@ExcelProperty(value = "润滑部位")
	private String equipmentMonitorName;

	@ExcelProperty(value = "工单状态")
	private String statusName;

}
