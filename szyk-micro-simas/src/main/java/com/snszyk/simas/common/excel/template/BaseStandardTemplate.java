/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * InspectBaseStandardTemplate
 *
 * <AUTHOR>
 */
@Data
public class BaseStandardTemplate implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备编号")
	private String code;
	@ApiModelProperty(value = "设备名称")
	private String name;
	@ApiModelProperty(value = "SN编号")
	private String sn;
	@ApiModelProperty(value = "设备部位")
	private String monitorName;

}
