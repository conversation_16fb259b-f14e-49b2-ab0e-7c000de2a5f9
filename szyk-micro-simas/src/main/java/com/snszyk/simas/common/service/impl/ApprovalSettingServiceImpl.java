/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.simas.common.dto.ApprovalSettingDto;
import com.snszyk.simas.common.entity.ApprovalSetting;
import com.snszyk.simas.common.mapper.ApprovalSettingMapper;
import com.snszyk.simas.common.service.IApprovalSettingService;
import com.snszyk.simas.common.vo.ApprovalSettingPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工单审核配置 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class ApprovalSettingServiceImpl extends BaseServiceImpl<ApprovalSettingMapper, ApprovalSetting> implements IApprovalSettingService {

	/**
	 * 名称校验
	 */
	@Override
	public void checkName(Long id, String name) {
		Integer count = lambdaQuery().eq(ApprovalSetting::getOrderType, name).ne(id != null, ApprovalSetting::getId, id).count();
		if (count > 0) {
			throw new ServiceException("门类名称已存在");
		}
	}

	/**
	 * 分页查询
	 */
	@Override
	public List<ApprovalSettingDto> pageList(ApprovalSettingPageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public ApprovalSettingDto detail(Long id) {
		return baseMapper.detail(id);
	}

	/**
	 * 是否需要审核
	 */
	@Override
	public boolean isNeedApproval(String type, String tenantId) {
		ApprovalSetting approvalSetting = lambdaQuery().eq(ApprovalSetting::getOrderType, type).
			eq(ApprovalSetting::getTenantId, tenantId)
			.last("limit 1").
			one();
		if (approvalSetting == null) {
			return false;
		}
		return approvalSetting.getIsApproval() == 1;

	}

}
