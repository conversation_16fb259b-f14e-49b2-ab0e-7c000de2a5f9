/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.core.tool.utils.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * EquipmentAccountExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EquipmentAccountExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("SN编号")
	private String sn;

	@ExcelProperty("设备名称")
	private String name;

	@ExcelProperty("规格型号")
	private String model;

	@ExcelProperty("计量单位")
	private String measureUnitName;

	@ExcelProperty("工艺类别")
	private String processCategoryName;

	@ExcelProperty("设备等级")
	private String importantLevelName;
	@ExcelProperty("特种设备类型")
	private String specialTypeName;
	@ExcelProperty("特种设备检查周期(天)")
	private Integer specialInspectPeriod;


	@ExcelProperty("设备类型")
	private String categoryName;

	@ExcelProperty("NFC")
	private String nfc;

	@ExcelProperty("存放地点")
	private String locationName;

	@DateTimeFormat(value = DateUtil.PATTERN_DATE)
	@ExcelProperty("购买日期")
	private Date purchaseDate;

	@ExcelProperty("使用部门")
	private String useDeptName;

	@ExcelProperty("使用人员")
	private String userName;

	@DateTimeFormat(value = DateUtil.PATTERN_DATE)
	@ExcelProperty("投产时间")
	private Date productDate;

	@ExcelProperty("供应商")
	private String supplier;

	@ExcelProperty("联系人")
	private String contact;

	@ExcelProperty("联系方式")
	private String tel;

	@ExcelProperty("设备状态")
	private String statusName;

}
