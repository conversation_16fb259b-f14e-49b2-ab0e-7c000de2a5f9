///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.common.service;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.snszyk.simas.common.entity.EquipmentFileCategory;
//import com.snszyk.simas.common.vo.EquipmentFileCategoryVO;
//import com.snszyk.system.vo.DelResultVO;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 设备分类表 服务类
// *
// * <AUTHOR>
// * @since 2024-08-24
// */
//public interface IEquipmentFileCategoryService extends IService<EquipmentFileCategory> {
//
//	/**
//	 * 搜索分页
//	 *
//	 * @param page
//	 * @param equipmentFileCategory
//	 * @return
//	 */
//	IPage<EquipmentFileCategory> page(IPage<EquipmentFileCategory> page, EquipmentFileCategoryVO equipmentFileCategory);
//
//	/**
//	 * 懒加载基础树列表
//	 *
//	 * @param tenantId
//	 * @param parentId
//	 * @param param
//	 * @return
//	 */
//	List<EquipmentFileCategoryVO> lazyList(String tenantId, Long parentId, Map<String, Object> param);
//
//	/**
//	 * 树形结构
//	 *
//	 * @param tenantId
//	 * @return
//	 */
//	List<EquipmentFileCategoryVO> tree(String tenantId);
//
//	/**
//	 * 懒加载树形结构
//	 *
//	 * @param tenantId
//	 * @param parentId
//	 * @return
//	 */
//	List<EquipmentFileCategoryVO> lazyTree(String tenantId, Long parentId);
//
//	/**
//	 * 获取基础树ID
//	 *
//	 * @param tenantId
//	 * @param nodeNames
//	 * @return
//	 */
//	String getNodeIds(String tenantId, String nodeNames);
//
//	/**
//	 * 获取基础树ID
//	 *
//	 * @param tenantId
//	 * @param nodeNames
//	 * @return
//	 */
//	String getNodeIdsByFuzzy(String tenantId, String nodeNames);
//
//	/**
//	 * 获取基础树节点名
//	 *
//	 * @param nodeIds
//	 * @return
//	 */
//	List<String> getNodeNames(String nodeIds);
//
//	/**
//	 * 获取子节点
//	 *
//	 * @param nodeId
//	 * @return
//	 */
//	List<EquipmentFileCategory> getNodeChild(Long nodeId);
//
//	/**
//	 * 获取所有的父节点
//	 *
//	 * @param nodeId
//	 * @return
//	 */
//	List<EquipmentFileCategory> getNodeParent(Long nodeId);
//
//	/**
//	 * 获取所有的父节点（包含自己本身）
//	 *
//	 * @param tenantId
//	 * @param param
//	 * @return
//	 */
//	List<EquipmentFileCategory> getNodeParent(String tenantId, Map<String, Object> param);
//
//	/**
//	 * 提交
//	 *
//	 * @param equipmentCategory
//	 * @return
//	 */
//	boolean submit(EquipmentFileCategoryVO equipmentFileCategory);
//
//	/**
//	 * 删除分类
//	 *
//	 * @param id
//	 * @return
//	 */
//	boolean removeCategory(Long id);
//
//	/**
//	 * 校验并删除分类
//	 *
//	 * @param ids
//	 * @return
//	 */
//	DelResultVO checkAndRemoveCategory(List<Long> ids);
//
//	/**
//	 * 部门信息查询
//	 *
//	 * @param name
//	 * @param parentId
//	 * @return
//	 */
//	List<EquipmentFileCategoryVO> searchCategory(String name, Long parentId);
//
//	/**
//	 * 获取树形结构
//	 * @param tenantId
//	 * @param parentId
//	 * @return
//	 */
//	List<EquipmentFileCategoryVO> nodeTree(String tenantId, Long parentId);
//
//	EquipmentFileCategory getByCodeAndTenantId(String cateGoryCode, String tenantId);
//}
