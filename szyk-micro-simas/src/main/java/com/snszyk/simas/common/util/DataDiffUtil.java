package com.snszyk.simas.common.util;

import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.base.BaseService;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DataDiffUtil {

	/**
	 * 区分增删改的数据
	 *
	 * @param <T> 泛型类型，必须实现 Identifiable 接口
	 * @param dbDataList 数据库中的数据列表
	 * @param newDataList 新的数据列表
	 * @return 一个包含新增、更新和删除数据的 Map
	 */
	public static <T extends BaseEntity> Map<String, List<T>> diffData(List<T> dbDataList, List<T> newDataList, BaseService<T> service) {
		// 创建一个 Map 来存储数据库中的数据，以便快速查找
		Map<Long, T> dbDataMap = dbDataList.stream()
			.collect(Collectors.toMap(BaseEntity::getId, data -> data));

		// 新增的数据
		List<T> addedDataList = newDataList.stream()
			.filter(data -> data.getId() == null || !dbDataMap.containsKey(data.getId()))
			.collect(Collectors.toList());

		// 删除的数据
		List<T> removedDataList = dbDataList.stream()
			.filter(data -> newDataList.stream().noneMatch(newData -> newData.getId() != null && newData.getId().equals(data.getId())))
			.collect(Collectors.toList());

		// 更新的数据
		List<T> updatedDataList = newDataList.stream()
			.filter(data -> data.getId() != null && dbDataMap.containsKey(data.getId()) && !data.equals(dbDataMap.get(data.getId())))
			.collect(Collectors.toList());

		// 返回结果
		Map<String, List<T>> result = new HashMap<>();
		result.put("added", addedDataList);
		result.put("removed", removedDataList);
		result.put("updated", updatedDataList);
		//执行数据库的操作
		//如果有新增的数据
		log.info("新增的数据有{}条", addedDataList.size());
		if (!addedDataList.isEmpty()) {
			boolean b = service.saveBatch(addedDataList);
			if (!b) {
				throw new RuntimeException("新增失败");
			}
		}
		//如果有更新的数据
		log.info("更新的数据有{}条", updatedDataList.size());
		if (!updatedDataList.isEmpty()) {
			boolean b = service.updateBatchById(updatedDataList);
			if (!b) {
				throw new RuntimeException("更新失败");
			}
		}
		//如果有删除的数据
		log.info("删除的数据有{}条", removedDataList.size());
		if (!removedDataList.isEmpty()) {
			boolean b = service.removeByIds(removedDataList.stream().map(BaseEntity::getId).collect(Collectors.toList()));
			if (!b) {
				throw new RuntimeException("删除失败");
			}
		}
		return result;
	}
}
