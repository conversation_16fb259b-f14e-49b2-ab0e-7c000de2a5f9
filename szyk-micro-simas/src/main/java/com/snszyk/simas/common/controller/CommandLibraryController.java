/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.CommandAnswerDto;
import com.snszyk.simas.common.dto.CommandHistoryDto;
import com.snszyk.simas.common.service.ICommandHistoryService;
import com.snszyk.simas.common.service.logic.CommandLibraryLogicService;
import com.snszyk.simas.common.vo.CommandQuestionVo;
import com.snszyk.simas.common.vo.CommandQuestionVoV2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 指令库 控制器
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("aiAssistant")
@Api(value = "指令库", tags = "指令库接口")
public class CommandLibraryController extends SzykController {

	private final CommandLibraryLogicService commandLibraryLogicService;

	private final ICommandHistoryService commandHistoryLogicService;


	/**
	 * 问答的接口post
	 */
	@PostMapping("/answer")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "智能问答", notes = "")
	public R<CommandAnswerDto> answer(@RequestBody @Valid CommandQuestionVo v) {
		CommandAnswerDto baseCrudDto = commandLibraryLogicService.answer(v);
		return R.data(baseCrudDto);
	}
	/**
	 * 问答的接口post
	 */
	@PostMapping("/answerV2")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "智能问答V2", notes = "")
	public R<CommandAnswerDto> answerV2(@RequestBody @Valid CommandQuestionVoV2 v) {
		CommandAnswerDto baseCrudDto = commandLibraryLogicService.answerV2(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/commandHistoryList")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "指令历史", notes = "")
	public R<List<CommandHistoryDto>> commandHistoryList() {
		Long userId = AuthUtil.getUserId();
		List<CommandHistoryDto> pageQueryResult = commandHistoryLogicService.commandHistoryList(userId);
		return R.data(pageQueryResult);
	}
}
