/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;

import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.dto.DashboardDTO;
import com.snszyk.simas.common.enums.EquipmentStatusEnum;
import com.snszyk.simas.common.service.IComponentMaterialService;
import com.snszyk.simas.inspect.service.IInspectOrderService;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.maintain.service.IMaintainOrderService;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IRepairService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 大屏数据 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2024/09/06 15:16
 **/
@Slf4j
@Service
@AllArgsConstructor
public class DashboardLogicService {


	private final IInspectOrderService inspectOrderService;
	private final IMaintainOrderService maintainOrderService;
	private final IRepairService repairService;
	private final IComponentMaterialService componentMaterialService;
	private final ILubricateOrderService lubricateOrderService;
	private final IOverhaulOrderService overhaulOrderService;
	private final IDeviceAccountClient deviceAccountClient;

	/**
	 * 设备概览
	 *
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 9:16
	 */
	@Transactional(readOnly = true)
	public DashboardDTO equipmentSummary() {
		String tenantId = AuthUtil.getTenantId();
		DashboardDTO dto = new DashboardDTO();
		dto.setInUseCount(0);
		dto.setIdleCount(0);
		dto.setInRepairCount(0);
		dto.setScrappedCount(0);
		dto.setEquipmentTotal(0);
		// 1. 获取设备总数
		final DeviceAccountVO accountVO = new DeviceAccountVO();
		accountVO.setTenantId(tenantId);
		final R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(accountVO);
		final List<DeviceAccountVO> accountVOList = listR.getData();
		if (ObjectUtil.isNotEmpty(accountVOList)) {
			// 设备总数
			dto.setEquipmentTotal(accountVOList.size());
			// 根据状态分组
			final Map<Integer, Long> statusCountMap = ListUtil.groupingByCount(accountVOList, DeviceAccountVO::getStatus);
			// 在用数量
			dto.setInUseCount(Math.toIntExact(statusCountMap.getOrDefault(EquipmentStatusEnum.IN_USE.getCode(), 0L)));
			// 闲置数量
			dto.setIdleCount(Math.toIntExact(statusCountMap.getOrDefault(EquipmentStatusEnum.IDLE.getCode(), 0L)));
			// 维修数量
			dto.setInRepairCount(Math.toIntExact(statusCountMap.getOrDefault(EquipmentStatusEnum.IN_REPAIR.getCode(), 0L)));
			// 报废数量
			dto.setScrappedCount(Math.toIntExact(statusCountMap.getOrDefault(EquipmentStatusEnum.SCRAPPED.getCode(), 0L)));
		}
		return dto;
	}

	/**
	 * 维修耗时统计（本年）
	 *
	 * @param bizType   INTERNAL：内部维修 EXTERNAL：外委维修
	 * @param queryDate 0：近1年；1：近30天；2：近7天
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 9:35
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO repairTimeTake(String bizType, Integer queryDate) {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	LocalDateTime startTime = LocalDate.now().withMonth(1).withDayOfMonth(1).atStartOfDay();
	// 	List<RepairDTO> list = repairService.finishList(bizType, startTime, null);
	// 	// 平均耗时  h
	// 	dto.setRepairAverageTimeTake(BigDecimal.ZERO);
	// 	if (Func.isNotEmpty(list)) {
	// 		int total = list.size();
	// 		double sum = list.stream().mapToDouble(r -> r.getTimeTake().doubleValue()).sum();
	// 		dto.setRepairAverageTimeTake(BigDecimal.valueOf(sum).divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP));
	// 	}
	// 	List<RepairDTO> repairList = repairService.timeTakeStatisticsList(bizType, queryDate, startTime, null);
	// 	if (Func.isNotEmpty(repairList)) {
	// 		dto.setRepairTimeTakeRankList(repairList);
	// 	}
	// 	return dto;
	// }

	/**
	 * 点巡检覆盖率（当日）
	 *
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 10:16
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO inspectCoverRate() {
	// 	String tenantId = AuthUtil.getTenantId();
	// 	DashboardDTO dto = new DashboardDTO();
	// 	List<OrderCoverDataDTO> dataList = new ArrayList<>();
	// 	List<InspectOrderDTO> list = inspectOrderService.coverListToday(tenantId);
	// 	// 根据分析的部门id分组
	// 	Map<Long, List<InspectOrderDTO>> deptList = list.stream().collect(groupingBy(e -> e.getAnalysisDeptId()));
	// 	for (Map.Entry<Long, List<InspectOrderDTO>> entry : deptList.entrySet()) {
	// 		Long deptId = entry.getKey();
	// 		List<InspectOrderDTO> value = entry.getValue();
	// 		String deptName = SysCache.getDeptName(deptId);
	// 		OrderCoverDataDTO dataDTO = new OrderCoverDataDTO();
	// 		dataList.add(dataDTO);
	// 		// 处理一个部门的数据
	// 		dataDTO.setDeptId(deptId);
	// 		dataDTO.setDeptName(deptName);
	// 		// 设备的数量
	// 		long equipmentCount = value.stream().map(InspectOrder::getEquipmentId).distinct().filter(Objects::nonNull).count();
	// 		AtomicLong finishCount = new AtomicLong();
	// 		// 完成的设备的数量,工单都完成的这个设备才算完成
	// 		value.stream().collect(Collectors.groupingBy(InspectOrder::getEquipmentId)).forEach((k, v) -> {
	// 			boolean b = v.stream().allMatch(e -> e.getStatus().equals(OrderStatusEnum.IS_COMPLETED.getCode()) || e.getStatus()
	// 				.equals(OrderStatusEnum.OVERDUE_COMPLETED.getCode()));
	// 			if (b) {
	// 				finishCount.getAndIncrement();
	// 			}
	// 		});
	// 		dataDTO.setTotalCount(Math.toIntExact(equipmentCount));
	// 		dataDTO.setCompleteCount(Math.toIntExact(finishCount.get()));
	// 		// 覆盖率:完成量/设备总数
	// 		dataDTO.setCoverRate(BigDecimal.valueOf(finishCount.get()).divide(BigDecimal.valueOf(equipmentCount),
	// 			RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP)));
	// 	}
	// 	// dataList按覆盖率排序
	// 	dto.setInspectOrderCoverDataList(dataList.stream().sorted(Comparator.comparing(OrderCoverDataDTO::getCoverRate).reversed()).collect(Collectors.toList()));
	// 	return dto;
	// }

	/**
	 * 保养覆盖率（近30天）
	 *
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 10:51
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO maintainCoverRate() {
	// 	String tenantId = AuthUtil.getTenantId();
	// 	DashboardDTO dto = new DashboardDTO();
	// 	List<OrderCoverDataDTO> dataList = new ArrayList<>();
	// 	List<MaintainOrderDTO> list = maintainOrderService.coverList30day(tenantId);
	// 	// 根据分析的部门id分组
	// 	Map<Long, List<MaintainOrderDTO>> deptList = list.stream().collect(Collectors.groupingBy(e -> e.getAnalysisDeptId()));
	// 	for (Map.Entry<Long, List<MaintainOrderDTO>> entry : deptList.entrySet()) {
	// 		Long deptId = entry.getKey();
	// 		List<MaintainOrderDTO> value = entry.getValue();
	// 		String deptName = SysCache.getDeptName(deptId);
	// 		OrderCoverDataDTO dataDTO = new OrderCoverDataDTO();
	// 		dataList.add(dataDTO);
	// 		// 处理一个部门的数据
	// 		dataDTO.setDeptId(deptId);
	// 		dataDTO.setDeptName(deptName);
	// 		// 设备的数量
	// 		long equipmentCount = value.stream().map(MaintainOrderDTO::getEquipmentId).distinct().filter(Objects::nonNull).count();
	// 		AtomicLong finishCount = new AtomicLong();
	// 		// 完成的设备的数量,工单都完成的这个设备才算完成
	// 		value.stream().collect(Collectors.groupingBy(MaintainOrderDTO::getEquipmentId)).forEach((k, v) -> {
	// 			boolean b = v.stream().allMatch(e -> e.getStatus().equals(OrderStatusEnum.IS_COMPLETED.getCode()) || e.getStatus()
	// 				.equals(OrderStatusEnum.OVERDUE_COMPLETED.getCode()));
	// 			if (b) {
	// 				finishCount.getAndIncrement();
	// 			}
	// 		});
	// 		dataDTO.setTotalCount(Math.toIntExact(equipmentCount));
	// 		dataDTO.setCompleteCount(Math.toIntExact(finishCount.get()));
	// 		// 覆盖率:完成量/设备总数
	// 		dataDTO.setCoverRate(BigDecimal.valueOf(finishCount.get()).divide(BigDecimal.valueOf(equipmentCount),
	// 			RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP)));
	// 	}
	// 	// dataList按覆盖率排序
	// 	dto.setMaintainOrderCoverDataList(dataList.stream().sorted(Comparator.comparing(OrderCoverDataDTO::getCoverRate).reversed()).collect(Collectors.toList()));
	// 	return dto;
	// }

	/**
	 * 维修情况
	 *
	 * @param vo        bizType=INTERNAL：内部维修 EXTERNAL：外委维修
	 * @param queryDate
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 13:35
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO repairStatistics(RepairVO vo, int queryDate) {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	RepairVO repairVO = new RepairVO();
	// 	if (Func.isNotEmpty(vo.getBizType())) {
	// 		repairVO.setBizType(vo.getBizType());
	// 	}
	// 	String tenantId = AuthUtil.getTenantId();
	// 	vo.setTenantId(tenantId);
	// 	List<RepairDTO> list = repairService.repairStatistics(repairVO, queryDate);
	// 	if (Func.isNotEmpty(list)) {
	// 		list.forEach(repair -> {
	// 			repair.setBizTypeName(RepairBizTypeEnum.getByCode(repair.getBizType()).getName())
	// 				.setStatusName(OrderStatusEnum.getByCode(repair.getStatus()).getName());
	// 		});
	// 		dto.setRepairList(list);
	// 	}
	// 	return dto;
	// }

	/**
	 * 点巡检情况(当天)
	 *
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 13:51
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO inspectStatistics() {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	dto.setEquipmentTotal(0);
	// 	String tenantId = AuthUtil.getTenantId();
	// 	List<InspectOrder> list = inspectOrderService.list(Wrappers.<InspectOrder>query().lambda()
	// 		.ge(InspectOrder::getCreateTime,
	// 			DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_START_TIME)
	// 		.le(InspectOrder::getCreateTime,
	// 			DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + DateUtils.DAY_END_TIME)
	// 		.eq(InspectOrder::getTenantId, tenantId).orderByDesc(InspectOrder::getCreateTime));
	// 	if (Func.isNotEmpty(list)) {
	// 		dto.setEquipmentTotal(list.stream().map(InspectOrder::getEquipmentId)
	// 			.collect(Collectors.toSet()).size());
	// 		Map<Integer, List<InspectOrder>> dataMap = list.stream().collect(groupingBy(InspectOrder::getStatus));
	// 		List<String> statusList = Arrays.asList(OrderStatusEnum.IN_PROCESS.getName(), OrderStatusEnum.IS_COMPLETED.getName(),
	// 			OrderStatusEnum.IS_OVERDUE.getName(), OrderStatusEnum.OVERDUE_COMPLETED.getName(), OrderStatusEnum.IS_CLOSED.getName());
	// 		List<Integer> statusCountList = Arrays.asList(0, 0, 0, 0, 0);
	// 		dataMap.forEach((k, v) -> {
	// 			if (statusList.contains(OrderStatusEnum.getByCode(k).getName())) {
	// 				statusCountList.set(statusList.indexOf(OrderStatusEnum.getByCode(k).getName()), v.size());
	// 			}
	// 		});
	// 		dto.setOrderStatusList(statusList);
	// 		dto.setOrderStatusCountList(statusCountList);
	// 	}
	// 	return dto;
	// }

	/**
	 * 保养情况
	 *
	 * @param queryDate 0：近1年；1：近30天；2：近7天
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 14:01
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO maintainStatistics(Integer queryDate) {
	// 	String tenantId = AuthUtil.getTenantId();
	// 	DashboardDTO dto = new DashboardDTO();
	// 	dto.setEquipmentTotal(0);
	// 	QueryWrapper<MaintainOrder> queryWrapper = Wrappers.query();
	// 	queryWrapper.lambda().eq(MaintainOrder::getTenantId, tenantId);
	// 	if (queryDate == 0) {
	// 		queryWrapper.lambda().ge(MaintainOrder::getCreateTime, LocalDate.now().minusYears(1));
	// 	}
	// 	if (queryDate == 1) {
	// 		queryWrapper.lambda().ge(MaintainOrder::getCreateTime, LocalDate.now().minusDays(30));
	// 	}
	// 	if (queryDate == 2) {
	// 		queryWrapper.lambda().ge(MaintainOrder::getCreateTime, LocalDate.now().minusDays(7));
	// 	}
	// 	queryWrapper.lambda().orderByDesc(MaintainOrder::getCreateTime);
	// 	List<MaintainOrder> list = maintainOrderService.list(queryWrapper);
	// 	if (Func.isNotEmpty(list)) {
	// 		dto.setEquipmentTotal(list.stream().map(MaintainOrder::getEquipmentId)
	// 			.collect(Collectors.toSet()).size());
	// 		Map<Integer, List<MaintainOrder>> dataMap = list.stream().collect(groupingBy(MaintainOrder::getStatus));
	// 		List<String> statusList = Arrays.asList(OrderStatusEnum.IN_PROCESS.getName(), OrderStatusEnum.WAIT_CONFIRM.getName(),
	// 			OrderStatusEnum.IS_COMPLETED.getName(), OrderStatusEnum.IS_REJECTED.getName(),
	// 			OrderStatusEnum.IS_OVERDUE.getName(), OrderStatusEnum.OVERDUE_COMPLETED.getName());
	// 		List<Integer> statusCountList = Arrays.asList(0, 0, 0, 0, 0, 0);
	// 		dataMap.forEach((k, v) -> {
	// 			if (statusList.contains(OrderStatusEnum.getByCode(k).getName())) {
	// 				statusCountList.set(statusList.indexOf(OrderStatusEnum.getByCode(k).getName()), v.size());
	// 			}
	// 		});
	// 		dto.setOrderStatusList(statusList);
	// 		dto.setOrderStatusCountList(statusCountList);
	// 	}
	// 	return dto;
	// }

	/**
	 * 故障缺陷情况（近30天）
	 *
	 * @param queryDate 0：近1年；1：近30天；2：近7天
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 14:11
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO faultDefectStatistics(Integer queryDate) {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	Integer total = 0;
	// 	Integer lastTotal = 0;
	// 	// 当前租户下的设备id
	// 	List<Long> equipmentIds = equipmentAccountService.listIdsByTenantId(AuthUtil.getTenantId());
	// 	if (Func.isEmpty(equipmentIds)) {
	// 		return dto;
	// 	}
	// 	QueryWrapper<FaultDefect> queryWrapper = Wrappers.query();
	// 	QueryWrapper<FaultDefect> lastQueryWrapper = Wrappers.query();
	// 	if (queryDate == 0) {
	// 		queryWrapper.lambda().ge(FaultDefect::getReportTime, LocalDate.now().minusYears(1)).in(FaultDefect::getEquipmentId, equipmentIds);
	// 	}
	// 	LocalDateTime now = LocalDateTime.now();
	// 	if (queryDate == 1) {
	// 		// 如果当前的日期>15号的则统计的是当月从1号到现在的数据,否则统计的是上月的数据
	// 		if (now.getDayOfMonth() > 15) {
	// 			queryWrapper.lambda().ge(FaultDefect::getReportTime, LocalDate.now().withDayOfMonth(1).atStartOfDay())
	// 				.in(FaultDefect::getEquipmentId, equipmentIds);
	// 			LocalDateTime lastNow = now.minusMonths(1);
	// 			int dayOfMonth = now.getDayOfMonth();
	// 			// 获取上月的第一天
	// 			LocalDateTime firstDayOfLastMonth = lastNow.with(TemporalAdjusters.firstDayOfMonth());
	// 			firstDayOfLastMonth = firstDayOfLastMonth.withMinute(0).withHour(0).withSecond(0);
	// 			int lastMonthDayCount = lastNow.toLocalDate().with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth();
	// 			lastQueryWrapper.lambda().ge(FaultDefect::getReportTime, firstDayOfLastMonth)
	// 				.lt(FaultDefect::getReportTime, lastNow.withDayOfMonth(Math.min(dayOfMonth, lastMonthDayCount)))
	// 				.in(FaultDefect::getEquipmentId, equipmentIds)
	// 				.orderByDesc(FaultDefect::getReportTime);
	// 		} else {
	// 			now = LocalDateTime.now();
	// 			// 获取上月的第一天
	// 			now = now.minusMonths(1);
	// 			LocalDateTime firstDayOfMonth = now.with(TemporalAdjusters.firstDayOfMonth());
	// 			LocalDateTime firstDayOfMonthAtStart = firstDayOfMonth.withMinute(0).withHour(0).withSecond(0);
	// 			// 获取上月的最后一天
	// 			LocalDateTime lastDayOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());
	// 			LocalDateTime lastDayOfMonthAtEnd = lastDayOfMonth.withMinute(59).withHour(23).withSecond(59);
	// 			queryWrapper.lambda().ge(FaultDefect::getReportTime, firstDayOfMonthAtStart)
	// 				.lt(FaultDefect::getReportTime, lastDayOfMonthAtEnd)
	// 				.in(FaultDefect::getEquipmentId, equipmentIds);
	// 			// 获取上上月的第一天
	// 			LocalDateTime lastNow = now.minusMonths(1);
	// 			lastNow = lastNow.withDayOfMonth(1).withHour(0).withSecond(0).withMinute(0);
	// 			// 上上月的最后一天
	// 			LocalDateTime lastDayOfLastMonth = lastNow.withDayOfMonth(lastNow.toLocalDate().lengthOfMonth()).withHour(23).withSecond(59).withMinute(59);
	// 			lastQueryWrapper.lambda().ge(FaultDefect::getReportTime, lastNow)
	// 				.lt(FaultDefect::getReportTime, lastDayOfLastMonth)
	// 				.in(FaultDefect::getEquipmentId, equipmentIds);
	// 		}
	//
	// 	}
	// 	if (queryDate == 2) {
	// 		queryWrapper.lambda().ge(FaultDefect::getReportTime, LocalDate.now().minusDays(7))
	// 			.in(FaultDefect::getEquipmentId, equipmentIds);
	// 	}
	// 	queryWrapper.lambda().orderByDesc(FaultDefect::getReportTime);
	// 	List<FaultDefect> list = faultDefectService.list(queryWrapper);
	// 	if (Func.isNotEmpty(list)) {
	// 		total = list.size();
	// 		List<FaultDefectDTO> resultList = FaultDefectWrapper.build().listDTO(list);
	// 		resultList.forEach(faultDefect -> {
	// 			EquipmentAccount equipmentAccount = equipmentAccountService.getById(faultDefect.getEquipmentId());
	// 			EquipmentMonitor equipmentMonitor = equipmentMonitorService.getById(faultDefect.getMonitorId());
	// 			// FaultBizStatusEnum 状态名
	// 			faultDefect.setStatusName(Optional.ofNullable(FaultBizStatusEnum.getByCode(faultDefect.getStatus())).map(e -> e.getName()).orElse(""));
	// 			// 来源
	// 			faultDefect.setSourceName(DictBizCache.getValue(DictBizEnum.FAULT_SOURCE, faultDefect.getSource()));
	// 			faultDefect.setEquipmentName(Optional.ofNullable(equipmentAccount).map(EquipmentAccount::getName).orElse(""))
	// 				.setMonitorName(Optional.ofNullable(equipmentMonitor).map(EquipmentMonitor::getName).orElse(""));
	// 		});
	// 		dto.setFaultDefectList(resultList);
	// 	}
	// 	List<FaultDefect> lastList = faultDefectService.list(lastQueryWrapper);
	// 	if (Func.isNotEmpty(lastList)) {
	// 		lastTotal = lastList.size();
	// 	}
	// 	if (lastTotal != 0) {
	// 		dto.setFaultDefectMom((BigDecimal.valueOf(total).subtract(BigDecimal.valueOf(lastTotal)))
	// 			.multiply(new BigDecimal(100L)).divide(BigDecimal.valueOf(lastTotal), 2, RoundingMode.HALF_UP));
	// 	} else {
	// 		dto.setFaultDefectMom(null);
	// 	}
	// 	return dto;
	// }

	/**
	 * 配件与耗材排名
	 *
	 * @param bizModule MAINTAIN：保养，REPAIR：维修
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 14:56
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO componentMaterialRank(String bizModule) {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	String tenantId = AuthUtil.getTenantId();
	// 	List<ComponentMaterial> list = Collections.emptyList();
	// 	if (Func.isNotEmpty(bizModule)) {
	// 		if (SystemModuleEnum.MAINTAIN_ORDER.getCode().contains(bizModule)) {
	// 			list = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda()
	// 				.eq(ComponentMaterial::getBizModule, SystemModuleEnum.MAINTAIN_ORDER.getCode()).eq(TenantEntity::getTenantId, tenantId).orderByDesc());
	// 		} else if ("REPAIR".equals(bizModule)) {
	// 			// 维修的包括内部维修,外部维修还有检修
	// 			list = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda()
	// 				.in(ComponentMaterial::getBizModule, SystemModuleEnum.INTERNAL_REPAIR.getCode(),
	// 					SystemModuleEnum.EXTERNAL_REPAIR.getCode(), SystemModuleEnum.OVERHAUL_ORDER.getCode()).eq(TenantEntity::getTenantId, tenantId));
	// 		}
	// 	} else {
	// 		list = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda().eq(TenantEntity::getTenantId, tenantId));
	// 	}
	// 	if (Func.isNotEmpty(list)) {
	// 		List<ComponentMaterialVO> resultList = new ArrayList<>();
	// 		Map<String, List<ComponentMaterial>> dataMap = list.stream()
	// 			.collect(groupingBy(c -> c.getName() + "::" + c.getModel()));
	// 		dataMap.forEach((k, v) -> {
	// 			ComponentMaterialVO vo = new ComponentMaterialVO();
	// 			vo.setName(k.split("::")[0]).setModel(k.split("::")[1])
	// 				.setCount(v.stream().mapToInt(ComponentMaterial::getCount).sum());
	// 			if (SystemModuleEnum.MAINTAIN_ORDER.getCode().contains(bizModule)) {
	// 				vo.setBizModuleName("保养");
	// 			} else {
	// 				vo.setBizModuleName("维修");
	// 			}
	// 			resultList.add(vo);
	// 		});
	// 		resultList.sort(Comparator.comparing(ComponentMaterialVO::getCount).reversed());
	// 		List<ComponentMaterialVO> collect = resultList.stream().limit(20).collect(Collectors.toList());
	// 		dto.setComponentMaterialRankList(collect);
	// 	}
	// 	return dto;
	// }

	/**
	 * 告警工单列表
	 *
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/7 16:28
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO warningOrderList() {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	List<WarningOrderVO> warningOrderList = new ArrayList<>();
	// 	// 点巡检
	// 	InspectOrderVO inspectOrderVO = new InspectOrderVO();
	// 	inspectOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	List<InspectOrderDTO> inspectOrderList = inspectOrderService.queryList(inspectOrderVO);
	// 	if (Func.isNotEmpty(inspectOrderList)) {
	// 		warningOrderList.addAll(inspectOrderList.stream().map(inspectOrder ->
	// 			new WarningOrderVO().toOrderVO(inspectOrder)
	// 		).collect(Collectors.toList()));
	// 	}
	// 	// 保养
	// 	MaintainOrderVO maintainOrderVO = new MaintainOrderVO();
	// 	maintainOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	List<MaintainOrderDTO> maintainOrderList = maintainOrderService.queryList(maintainOrderVO);
	// 	if (Func.isNotEmpty(maintainOrderList)) {
	// 		warningOrderList.addAll(maintainOrderList.stream().map(maintainOrder ->
	// 			new WarningOrderVO().toOrderVO(maintainOrder)).collect(Collectors.toList()));
	// 	}
	// 	// 维修
	// 	RepairVO repairVO = new RepairVO();
	// 	repairVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	List<RepairDTO> repairOrderList = repairService.queryList(repairVO);
	// 	if (Func.isNotEmpty(repairOrderList)) {
	// 		warningOrderList.addAll(repairOrderList.stream().map(repairOrder ->
	// 			new WarningOrderVO().toOrderVO(repairOrder)).collect(Collectors.toList()));
	// 	}
	// 	if (Func.isNotEmpty(warningOrderList)) {
	// 		dto.setWarningOrderList(warningOrderList);
	// 	}
	// 	return dto;
	// }

	/**
	 * 设备情况统计
	 *
	 * @param useDept
	 * @return com.snszyk.simas.dto.DashboardDTO
	 * <AUTHOR>
	 * @date 2024/9/11 11:31
	 */
	// @Transactional(readOnly = true)
	// public DashboardDTO equipmentStatistics(Long useDept) {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	List<EquipmentAccountDTO> list = new ArrayList<>();
	// 	List<EquipmentAccount> equipmentList = equipmentAccountService.list(Wrappers.<EquipmentAccount>query().lambda()
	// 		.isNotNull(EquipmentAccount::getUseDept).eq(Func.isNotEmpty(useDept), EquipmentAccount::getUseDept, useDept));
	// 	if (Func.isNotEmpty(equipmentList)) {
	// 		Map<Long, List<EquipmentAccount>> dataMap = equipmentList.stream().collect(groupingBy(EquipmentAccount::getUseDept));
	// 		dataMap.forEach((k, v) -> {
	// 			EquipmentAccountDTO equipmentAccount = new EquipmentAccountDTO();
	// 			equipmentAccount.setUseDeptName(SysCache.getDept(k).getDeptName()).setEquipmentTotal(v.size());
	// 			Integer inUseCount = 0;
	// 			Integer idleCount = 0;
	// 			Integer inRepairCount = 0;
	// 			Integer scrappedCount = 0;
	// 			for (EquipmentAccount equipment : v) {
	// 				if (EquipmentStatusEnum.IDLE == EquipmentStatusEnum.getByCode(equipment.getStatus())) {
	// 					idleCount++;
	// 				}
	// 				if (EquipmentStatusEnum.IN_USE == EquipmentStatusEnum.getByCode(equipment.getStatus())) {
	// 					inUseCount++;
	// 				}
	// 				if (EquipmentStatusEnum.IN_REPAIR == EquipmentStatusEnum.getByCode(equipment.getStatus())) {
	// 					inRepairCount++;
	// 				}
	// 				if (EquipmentStatusEnum.SCRAPPED == EquipmentStatusEnum.getByCode(equipment.getStatus())) {
	// 					scrappedCount++;
	// 				}
	// 			}
	// 			equipmentAccount.setIdleCount(idleCount).setInUseCount(inUseCount)
	// 				.setInRepairCount(inRepairCount).setScrappedCount(scrappedCount);
	// 			list.add(equipmentAccount);
	// 		});
	// 	}
	// 	if (Func.isNotEmpty(list)) {
	// 		dto.setEquipmentList(list);
	// 	}
	// 	return dto;
	// }


	// @Transactional(readOnly = true)
	// public List<BigScreenMessageDTO> notice() {
	// 	List<BigScreenMessageDTO> list = new ArrayList<>();
	// 	String tenantId = AuthUtil.getTenantId();
	// 	List<BigScreenMessageDTO> bigScreenMessageDTOS = overhaulOrderService.specialNoFinishList(tenantId);
	// 	// 特种设备的超时工单
	// 	bigScreenMessageDTOS.forEach(e -> {
	// 		e.setIsSpecialEquipment(true);
	// 		list.add(e);
	// 	});
	// 	List<BigScreenMessageDTO> otherOverhaulList = overhaulOrderService.noSpecialOverdueList(tenantId);
	//
	// 	// 其他的超期的工单 检修的
	// 	otherOverhaulList.stream().peek(e -> {
	// 		e.setIsSpecialEquipment(false);
	// 		e.setOrderType(OrderTypeEnum.OVERHAUL_ORDER.getDesc());
	// 	}).forEach(list::add);
	// 	// 润滑
	// 	LubricateOrderVO lubricateOrderVO = new LubricateOrderVO();
	// 	lubricateOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	lubricateOrderService.overdueList(tenantId).stream().peek(e -> {
	// 		e.setIsSpecialEquipment(false);
	// 		e.setOrderType(OrderTypeEnum.LUBRICATE_ORDER.getDesc());
	// 	}).forEach(list::add);
	// 	// 保养
	// 	MaintainOrderVO maintainOrderVO = new MaintainOrderVO();
	// 	maintainOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	maintainOrderService.overdueList(tenantId).stream().peek(e -> {
	// 		e.setIsSpecialEquipment(false);
	// 		e.setOrderType(OrderTypeEnum.MAINTAIN_ORDER.getDesc());
	// 	}).forEach(list::add);
	// 	// 点巡检
	// 	InspectOrderVO inspectOrderVO = new InspectOrderVO();
	// 	inspectOrderVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	inspectOrderService.overdueList(tenantId).stream().peek(e -> {
	// 		e.setIsSpecialEquipment(false);
	// 		e.setOrderType(OrderTypeEnum.INSPECT_ORDER.getDesc());
	// 	}).forEach(list::add);
	// 	// 外部维修+内部维修
	// 	RepairVO repairVO = new RepairVO();
	// 	repairVO.setStatus(OrderStatusEnum.IS_OVERDUE.getCode());
	// 	repairVO.setBizType(RepairBizTypeEnum.EXTERNAL.getCode());
	// 	repairService.overdueList(tenantId).stream().peek(e -> {
	// 		e.setIsSpecialEquipment(false);
	// 		String orderType = Optional.ofNullable(RepairBizTypeEnum.getByCode(e.getBizType())).map(RepairBizTypeEnum::getName).orElse("");
	//
	// 		e.setOrderType(orderType);
	// 	}).forEach(list::add);
	// 	for (BigScreenMessageDTO bigScreenMessageDTO : list) {
	// 		String orderType = bigScreenMessageDTO.getOrderType();
	// 		if (orderType != null && orderType.endsWith("工单")) {
	// 			orderType = orderType.replace("工单", "");
	// 			bigScreenMessageDTO.setOrderType(orderType);
	// 		}
	// 	}
	// 	return list;
	// }

	/**
	 * 润滑统计近30天的
	 *
	 * @return
	 */
	// public DashboardDTO lubricateStatistics() {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	String tenantId = AuthUtil.getTenantId();
	// 	List<LubricateOrderDTO> lubricateOrderDTOS = lubricateOrderService.lubricateStatistics(tenantId);
	// 	// 工单的状态
	// 	lubricateOrderDTOS.forEach(e -> e.setStatusName(Optional.ofNullable(OrderStatusEnum.getByCode(e.getStatus())).map(OrderStatusEnum::getName).orElse("")));
	// 	if (Func.isNotEmpty(lubricateOrderDTOS)) {
	// 		dto.setLubricateList(lubricateOrderDTOS);
	// 	}
	// 	// 设备总数 lubricateOrderDTOS设备的id去重
	// 	Set<Long> equipmentIds = lubricateOrderDTOS.stream().map(LubricateOrderDTO::getEquipmentId).collect(Collectors.toSet());
	// 	dto.setEquipmentTotal(equipmentIds.size());
	// 	// 部位总数 lubricateOrderDTOS 部位的id去重
	// 	Set<Long> monitorIds = lubricateOrderDTOS.stream().map(LubricateOrderDTO::getEquipmentMonitorId).collect(Collectors.toSet());
	// 	dto.setMonitorTotal(monitorIds.size());
	// 	dto.setOrderTotal(lubricateOrderDTOS.size());
	// 	return dto;
	//
	// }

	/**
	 * 二级部门的二设备使用情况
	 *
	 * @return
	 */
	// public DashboardDTO secEquipmentList() {
	// 	DashboardDTO dto = new DashboardDTO();
	// 	String tenantId = AuthUtil.getTenantId();
	// 	List<EquipmentAccountDTO> allList = equipmentAccountService.secDeptList(tenantId);
	// 	List<DashboardSecEquipmentDTO> secEquipmentList = new ArrayList<>();
	// 	dto.setSecEquipmentList(secEquipmentList);
	// 	allList.stream().forEach(e -> {
	// 		String deptAncestors = e.getDeptAncestors();
	// 		if (Func.isNotBlank(deptAncestors)) {
	// 			// 分割后取第三位的
	// 			String[] split = deptAncestors.split(",");
	// 			if (split.length > 2) {
	// 				String deptId = split[2];
	// 				e.setSecDeptId(Long.valueOf(deptId));
	// 			} else if (split.length == 2) {
	// 				e.setSecDeptId(e.getUseDept());
	// 			}
	// 		}
	// 	});
	// 	// secDeptId分组
	// 	Map<Long, List<EquipmentAccountDTO>> dataMap = allList.stream().filter(e -> e.getSecDeptId() != null).collect(groupingBy(EquipmentAccountDTO::getSecDeptId));
	// 	dataMap.forEach((k, v) -> {
	// 		DashboardSecEquipmentDTO dashboardSecEquipmentDTO = new DashboardSecEquipmentDTO();
	// 		String deptName = SysCache.getDeptName(k);
	// 		dashboardSecEquipmentDTO.setSecDeptId(k);
	// 		dashboardSecEquipmentDTO.setSecDeptName(deptName);
	// 		// v根据statue分组 数量求和count
	// 		Map<String, Long> statusMap = v.stream().collect(groupingBy(EquipmentAccountDTO::getStatus, Collectors.counting()));
	// 		Long idle = statusMap.get(EquipmentStatusEnum.IDLE.getCode().toString());
	// 		// 备用的
	// 		dashboardSecEquipmentDTO.setIdleCount(idle == null ? 0 : idle);
	// 		// 在用的
	// 		Long inUse = statusMap.get(EquipmentStatusEnum.IN_USE.getCode().toString());
	// 		// 报废的
	// 		Long scrapped = statusMap.get(EquipmentStatusEnum.SCRAPPED.getCode().toString());
	// 		// 维修的
	// 		Long inRepair = statusMap.get(EquipmentStatusEnum.IN_REPAIR.getCode().toString());
	// 		dashboardSecEquipmentDTO.setInUseCount(inUse == null ? 0 : inUse);
	// 		dashboardSecEquipmentDTO.setScrappedCount(scrapped == null ? 0 : scrapped);
	// 		dashboardSecEquipmentDTO.setInRepairCount(inRepair == null ? 0 : inRepair);
	// 		dashboardSecEquipmentDTO.setEquipmentTotal(v.size());
	// 		secEquipmentList.add(dashboardSecEquipmentDTO);
	// 	});
	// 	return dto;
	// }
}
