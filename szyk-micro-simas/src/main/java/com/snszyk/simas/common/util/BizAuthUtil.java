package com.snszyk.simas.common.util;

import com.snszyk.common.constant.SimasConstant;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.StringUtil;

/**
 * ClassName: AuthUtil
 * Package: com.snszyk.auth.utils
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/26 15:19
 */
public class BizAuthUtil extends com.snszyk.core.secure.utils.AuthUtil {
	/**
	 * 是否设备管理员
	 */
	public static boolean isSimasAdmin() {
		return StringUtil.containsAny(AuthUtil.getUserRole(), new CharSequence[]{SimasConstant.SimasRole.SIMAS_ADMIN});
	}

}
