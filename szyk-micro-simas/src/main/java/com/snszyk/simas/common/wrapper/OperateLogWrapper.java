/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.common.entity.OperateLog;
import com.snszyk.simas.common.enums.OperateTypeEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.vo.OperateLogVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;
import java.util.Optional;

/**
 * 人员表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
public class OperateLogWrapper extends BaseEntityWrapper<OperateLog, OperateLogVO> {

	public static OperateLogWrapper build() {
		return new OperateLogWrapper();
	}

	@Override
	public OperateLogVO entityVO(OperateLog operateLog) {
		OperateLogVO operateLogVO = Objects.requireNonNull(BeanUtil.copy(operateLog, OperateLogVO.class));
		// 系统模块
		Optional.ofNullable(SystemModuleEnum.getByCode(operateLog.getModule()))
			.ifPresent(module -> operateLogVO.setModuleName(module.getName()));
		// 操作类型
		// String type = DictBizCache.getValue(DictBizEnum.OPERATE_TYPE, operateLog.getType());
		Optional.ofNullable(OperateTypeEnum.getByCode(operateLog.getType()))
			.ifPresent(operateTypeEnum -> operateLogVO.setTypeName(operateTypeEnum.getName()));

		// operateLogVO.setTypeName(type);
		operateLogVO.setNetName(Func.equals(Func.toInt(StringPool.ONE), operateLog.getNet()) ? "内网" : "外网");
		operateLogVO.setStatusName(Func.equals(Func.toInt(StringPool.ONE), operateLog.getNet()) ? "成功" : "失败");
		User operateUser = UserCache.getUser(operateLog.getOperateUser());
		if (Func.isNotEmpty(operateUser)) {
			operateLogVO.setOperateUserName(operateUser.getRealName());
		}
		return operateLogVO;
	}

}
