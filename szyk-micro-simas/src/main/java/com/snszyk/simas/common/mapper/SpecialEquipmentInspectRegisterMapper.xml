<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.SpecialEquipmentInspectRegisterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="specialEquipmentInspectRegisterResultMap" type="com.snszyk.simas.common.entity.SpecialEquipmentInspectRegister">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="inspect_type" property="inspectType"/>
        <result column="inspect_org" property="inspectOrg"/>
        <result column="inspect_date" property="inspectDate"/>
        <result column="next_inspect_date" property="nextInspectDate"/>
        <result column="security_admin" property="securityAdmin"/>
        <result column="inspect_result" property="inspectResult"/>
        <result column="important_issues" property="importantIssues"/>
        <result column="measures" property="measures"/>
        <result column="attach_id" property="attachId"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


</mapper>
