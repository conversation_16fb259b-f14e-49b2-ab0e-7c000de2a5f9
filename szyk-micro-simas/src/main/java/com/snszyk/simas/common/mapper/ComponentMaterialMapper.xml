<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.ComponentMaterialMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="componentMaterialResultMap" type="com.snszyk.simas.spare.entity.ComponentMaterial">
        <id column="id" property="id"/>
        <result column="biz_no" property="bizNo"/>
        <result column="biz_module" property="bizModule"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="measure_unit_id" property="measureUnitId"/>
        <result column="measure_unit_name" property="measureUnitName"/>
        <result column="count" property="count"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="componentMaterialVOResultMap" type="com.snszyk.simas.spare.vo.ComponentMaterialVO">
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="count" property="count"/>
    </resultMap>


    <select id="statisticalReport" resultMap="componentMaterialVOResultMap">
        SELECT
            m.id,
            m.biz_no,
            m.biz_module,
            m.NO,
            m.NAME,
            m.model,
            m.count,
            m.remark,
            m.sort,
            u.accuracy as "measureUnitPrecision",
            m.tenant_id
        FROM
        simas_component_material m
        left join device_measure_unit u on u.id = m.measure_unit_id and u.is_deleted = 0
        <where>
            <if test="search.bizModuleList != null">
                and m.biz_module in
                <foreach collection="search.bizModuleList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="search.queryDate == 1">
                AND m.create_time >= CURDATE() - INTERVAL 30 DAY
            </if>
            <if test="search.queryDate == 2">
                AND m.create_time >= CURDATE() - INTERVAL 7 DAY
            </if>
            <if test="search.queryDate == 3">
                AND TO_DAYS(m.create_time) = TO_DAYS(NOW())
            </if>
            <if test="search.startDate != null and search.startDate != ''">
                and m.create_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="search.endDate != null and search.endDate != ''">
                and m.create_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

    <select id="exportList" resultMap="componentMaterialVOResultMap">
        SELECT name, model, count(*) as count FROM simas_component_material
        where 1=1
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and create_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and create_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        group by name,model
        order by count desc
    </select>



</mapper>
