package com.snszyk.simas.common.llm;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class IdGenerationStrategyFactory {

    private static final Map<String, IdGenerationStrategy> strategyMap = new HashMap<>();

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        Map<String, IdGenerationStrategy> beans = applicationContext.getBeansOfType(IdGenerationStrategy.class);
        for (IdGenerationStrategy strategy : beans.values()) {
            strategyMap.put(strategy.getElement(), strategy);
        }
    }

    public static IdGenerationStrategy getStrategy(String tab) {
        return strategyMap.get(tab);
    }
}
