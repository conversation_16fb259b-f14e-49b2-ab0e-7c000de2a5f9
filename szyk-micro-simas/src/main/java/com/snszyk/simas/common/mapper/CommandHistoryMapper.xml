<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.CommandHistoryMapper">

        <sql id="selectData">
        select t.* from simas_command_history t
    </sql>
    <select id="pageList" resultType="com.snszyk.simas.common.dto.CommandHistoryDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.simas.common.dto.CommandHistoryDto">
        <include refid="selectData"/>
       where t.is_deleted = 0 and t.id=#{id}
    </select>

</mapper>
