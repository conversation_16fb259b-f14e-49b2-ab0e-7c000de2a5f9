///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.common.wrapper;
//
//import com.snszyk.common.equipment.entity.DeviceFileCategory;
//import com.snszyk.common.equipment.feign.ICommonClient;
//import com.snszyk.core.mp.support.BaseEntityWrapper;
//import com.snszyk.core.tool.constant.SzykConstant;
//import com.snszyk.core.tool.node.ForestNodeMerger;
//import com.snszyk.core.tool.utils.BeanUtil;
//import com.snszyk.core.tool.utils.Func;
//import com.snszyk.core.tool.utils.SpringUtil;
//import com.snszyk.core.tool.utils.StringPool;
//import com.snszyk.simas.common.entity.EquipmentFileCategory;
//import com.snszyk.simas.common.vo.EquipmentFileCategoryVO;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * 设备资料类型表包装类,返回视图层所需的字段
// *
// * <AUTHOR>
// * @since 2024-08-24
// */
//public class EquipmentFileCategoryWrapper extends BaseEntityWrapper<EquipmentFileCategory, EquipmentFileCategoryVO> {
//
//	private static ICommonClient commonClient;
//
//	static {
//		commonClient = SpringUtil.getBean(ICommonClient.class);
//	}
//
//	public static EquipmentFileCategoryWrapper build() {
//		return new EquipmentFileCategoryWrapper();
//	}
//
//	@Override
//	public EquipmentFileCategoryVO entityVO(EquipmentFileCategory equipmentCategory) {
//		EquipmentFileCategoryVO equipmentCategoryVO = Objects.requireNonNull(BeanUtil.copy(equipmentCategory, EquipmentFileCategoryVO.class));
//		if (Func.equals(equipmentCategory.getParentId(), SzykConstant.TOP_PARENT_ID)) {
//			equipmentCategoryVO.setParentName(SzykConstant.TOP_PARENT_NAME);
//		} else {
//			DeviceFileCategory parent = commonClient.getEquipmentFileCategory(equipmentCategory.getParentId()).getData();
//			equipmentCategoryVO.setParentName(parent.getName());
//		}
//		equipmentCategoryVO.setPathName(equipmentCategoryVO.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
//		return equipmentCategoryVO;
//	}
//
//	public List<EquipmentFileCategoryVO> listNodeVO(List<EquipmentFileCategory> list) {
//		List<EquipmentFileCategoryVO> collect = list.stream().map(equipmentCategory ->
//				Objects.requireNonNull(BeanUtil.copy(equipmentCategory, EquipmentFileCategoryVO.class)))
//			.collect(Collectors.toList());
//		return ForestNodeMerger.merge(collect);
//	}
//
//	public List<EquipmentFileCategoryVO> listNodeLazyVO(List<EquipmentFileCategoryVO> list) {
//		List<EquipmentFileCategoryVO> collect = list.stream().peek(equipmentCategory ->
//				Objects.requireNonNull(BeanUtil.copy(equipmentCategory, EquipmentFileCategoryVO.class)))
//			.collect(Collectors.toList());
//		return ForestNodeMerger.merge(collect);
//	}
//
//}
