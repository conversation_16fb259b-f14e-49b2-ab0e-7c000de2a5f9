<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.CommandLibraryMapper">

    <sql id="selectData">
        select t.*
        from simas_command_library t
    </sql>
    <select id="pageList" resultType="com.snszyk.simas.common.dto.CommandLibraryDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
    </select>

    <select id="detail" resultType="com.snszyk.simas.common.dto.CommandLibraryDto">
        <include refid="selectData"/>
        where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="getPageRoute" resultType="com.snszyk.simas.common.dto.CommandLibraryDto"
            parameterType="java.lang.String">

        select t.*
        from simas_command_library t
        where t.is_deleted = 0
          and t.command_route = #{commandRoute}
        limit 1

    </select>

</mapper>
