package com.snszyk.simas.common.excel.strategy;

import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import org.apache.poi.ss.usermodel.Row;

/**
 * 单元格高度策略
 */
public class CellRowHeightStyleStrategy extends AbstractRowHeightStyleStrategy {
	@Override
	protected void setHeadColumnHeight(Row row, int relativeRowIndex) {
		// 设置第一个表头的高度为900
		if(relativeRowIndex == 0){
			//如果excel需要显示行高为15，那这里就要设置为15*20=300
			// 一行文字, 高度为300应该够了
			row.setHeight((short) (900));
		}
	}

	@Override
	protected void setContentColumnHeight(Row row, int i) {

	}
}
