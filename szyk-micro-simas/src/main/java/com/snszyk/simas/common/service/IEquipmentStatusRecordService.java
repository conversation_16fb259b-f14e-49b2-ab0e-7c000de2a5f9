/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.entity.EquipmentStatusRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * 设备状态记录 服务类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public interface IEquipmentStatusRecordService extends BaseService<EquipmentStatusRecord> {

	List<EquipmentStatusRecord> list(List<Long> equipmentIds, LocalDate startDate, LocalDate endDate, List<Integer> statusList);
}
