package com.snszyk.simas.common.bizparser;

import com.snszyk.common.utils.DateUtil;
import com.snszyk.simas.common.enums.TimeTypeEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * ClassName: TimeRangeParser
 * Package: com.snszyk.simas.parser
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/14 18:59
 */
public class TimeRangeParser {

	public static LocalDate parseLocalDate(TimeTypeEnum timeType) {
		// 计算查询开始时间 1-近30天，2-近180天，3-近一年"
		switch (timeType) {
			// 今天
			case TODAY:
				return LocalDate.now();
			// 7天前
			case SEVEN_DAYS:
				return DateUtil.getSevenDayBeforeLocalDate();
			// 三十天前
			case THIRTY_DAYS:
				return DateUtil.getThirtyDayBeforeLocalDate();
			// 180天前
			case ONE_HUNDRED_EIGHTY_DAYS:
				return DateUtil.getOneEightyDayBeforeLocalDate();
			// 1年前
			case ONE_YEAR:
				return DateUtil.getOneYearBeforeLocalDate();
		}
		throw new IllegalArgumentException("时间类型错误");

	}

	public static LocalDateTime parseLocalDateTime(TimeTypeEnum timeType) {
		switch (timeType) {
			// 今天
			case TODAY:
				return LocalDate.now().atTime(LocalTime.MIN);
			// 7天前
			case SEVEN_DAYS:
				return DateUtil.getSevenDayBeforeLocalDateTime();
			// 三十天前
			case THIRTY_DAYS:
				return DateUtil.getThirtyDayBeforeLocalDateTime();
			// 180天前
			case ONE_HUNDRED_EIGHTY_DAYS:
				return DateUtil.getOneEightyDayBeforeLocalDateTime();
			// 1年前
			case ONE_YEAR:
				return DateUtil.getOneYearBeforeLocalDateTime();
		}
		throw new IllegalArgumentException("时间类型错误");
	}
}
