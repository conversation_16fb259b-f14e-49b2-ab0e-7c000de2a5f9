/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * MaintainOrderExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class MaintainOrderExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("工单编号")
	private String no;

	@ExcelProperty("工单名称")
	private String orderName;

	@ExcelProperty("使用部门")
	private String executeDeptName;

	@ExcelProperty("开始时间")
	private String startTimeStr;

	@ExcelProperty("结束时间")
	private String endTimeStr;

	@ExcelProperty("计划周期")
	private String cycleTypeName;

	@ExcelProperty("使用人员")
	private String executeUserName;

	@ExcelProperty("保养结果")
	private String maintainResult;

	@ExcelProperty("工单状态")
	private String statusName;

}
