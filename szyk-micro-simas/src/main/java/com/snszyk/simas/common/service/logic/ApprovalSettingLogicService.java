/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;


import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.simas.common.dto.ApprovalSettingDto;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.entity.ApprovalSetting;
import com.snszyk.simas.common.enums.OrderTypeEnum;
import com.snszyk.simas.common.service.IApprovalSettingService;
import com.snszyk.simas.common.vo.ApprovalSettingAVo;
import com.snszyk.simas.common.vo.ApprovalSettingPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单审核配置 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@AllArgsConstructor
@Service
public class ApprovalSettingLogicService {

	private final IApprovalSettingService approvalSettingService;

	@Transactional
	public ApprovalSettingDto saveOrUpdate(ApprovalSettingAVo v) {
		// 名称的唯一性校验
		ApprovalSetting copy = BeanUtil.copy(v, ApprovalSetting.class);
		//如果是新增的,之前是否已经存在orderType
		if (copy.getId() == null) {
			Integer count = approvalSettingService.lambdaQuery().eq(ApprovalSetting::getOrderType, copy.getOrderType()).count();
			if (count > 0) {
				throw new ServiceException("工单类型已存在");
			}
		}
		boolean b = approvalSettingService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public List<ApprovalSettingDto> pageList(ApprovalSettingPageVo v) {
		List<ApprovalSettingDto> result = new ArrayList<>();
		v.setTenantId(AuthUtil.getTenantId());
		List<ApprovalSettingDto> list = approvalSettingService.pageList(v);
		Map<String, Integer> map = list.stream().collect(Collectors.toMap(ApprovalSettingDto::getOrderType, e -> e.getIsApproval()));
		result.addAll(list);
		//补充orderTypeEnum中的数据,默认为不审核
		for (OrderTypeEnum value : OrderTypeEnum.values()) {
			String name = value.name();
			if (!map.containsKey(name)) {
				ApprovalSettingDto dto = new ApprovalSettingDto();
				dto.setOrderType(name);
				dto.setIsApproval(0);
				result.add(dto);
			}
		}
         //添加工单类型名称
		for (ApprovalSettingDto dto : result) {
			dto.setOrderTypeName(OrderTypeEnum.valueOf(dto.getOrderType()).getDesc());
		}
		return result;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public ApprovalSettingDto detail(Long id) {
		// 通过ID查询数据信息
		ApprovalSettingDto dto = approvalSettingService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			ApprovalSetting data = approvalSettingService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}

			boolean b = approvalSettingService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


}
