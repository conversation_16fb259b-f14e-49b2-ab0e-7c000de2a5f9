/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.entity.SpecialEquipmentInspectRegister;
import com.snszyk.simas.common.vo.SpecialEquipmentInspectRegisterVO;

/**
 * 特种设备检验登记表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
public interface ISpecialEquipmentInspectRegisterService extends BaseService<SpecialEquipmentInspectRegister> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectRegister
	 * @return
	 */
	IPage<SpecialEquipmentInspectRegisterVO> page(IPage<SpecialEquipmentInspectRegisterVO> page,
												  SpecialEquipmentInspectRegisterVO inspectRegister);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	SpecialEquipmentInspectRegisterVO detail(Long id);

	/**
	 * 提交
	 *
	 * @param inspectRegister
	 * @return
	 */
	boolean submit(SpecialEquipmentInspectRegisterVO inspectRegister);


}
