/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * LocationExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class LocationExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("分类编码")
	private String code;

	@ExcelProperty("父分类编码")
	private String parentCode;

	@ExcelProperty("分类名称")
	private String name;

	@ExcelProperty("层级")
	private Integer level;

}
