/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;

import com.google.common.collect.Lists;
import com.snszyk.common.equipment.entity.DeviceAccount;
import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
import com.snszyk.common.equipment.enums.EquipmentStatusEnum;
import com.snszyk.common.equipment.feign.FeignPage;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.common.utils.StringPool;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.dto.KeyValueDTO;
import com.snszyk.simas.common.entity.EquipmentStatusRecord;
import com.snszyk.simas.common.enums.TimeTypeEnumV2;
import com.snszyk.simas.common.service.IEquipmentStatusRecordService;
import com.snszyk.simas.common.vo.EquipmentStatusRecordVo;
import com.snszyk.system.cache.SysCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 设备状态记录 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@AllArgsConstructor
@Service
public class EquipmentStatusRecordLogicService {

	private final IEquipmentStatusRecordService equipmentStatusRecordService;
	private final IDeviceAccountClient deviceAccountClient;

	/**
	 * 查询设备完好率
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<KeyValueDTO> listHealthPercentage(EquipmentStatusRecordVo v) {
		// 初始化
		List<KeyValueDTO> keyValueDTOList = KeyValueDTO.initKeyValueDTOList(v.getTimeType());
		// 备用 + 在用 + 维修状态
		List<Integer> statusList = Lists.newArrayList(EquipmentStatusEnum.IDLE.getCode(), EquipmentStatusEnum.IN_USE.getCode(), EquipmentStatusEnum.IN_REPAIR.getCode());
		// 查询设备台账列表
		DeviceAccountPageVO vo = new DeviceAccountPageVO();
		vo.setUseDeptList(SysCache.getDeptChildIds(v.getUseDeptId()));
		vo.setCategoryId(v.getCategoryId());
		vo.setImportantLevel(v.getImportantLevel());
		vo.setStatusList(statusList);
		R<FeignPage<DeviceAccountVO>> feignPageR = deviceAccountClient.devicePageListScope(vo, -1, -1, EquipmentDataScopeEnum.ALL_SCOPE.getCode());
//		final List<DeviceAccount> accountList = DeviceAccountService.list(SysCache.getDeptChildIds(v.getUseDeptId()), v.getCategoryId(), v.getImportantLevel(), statusList);
		List<DeviceAccountVO> accountList = feignPageR.getData().getRecords();
		if (ObjectUtil.isEmpty(accountList)) {
			return keyValueDTOList;
		}
		// 初始化设备状态记录
		List<EquipmentStatusRecord> equipmentStatusRecordList = Lists.newArrayList();
		// 设备当前状态为今日状态,将设备今日状态转为设备状态记录
		equipmentStatusRecordList.addAll(this.convertToEquipmentStatusRecord(com.snszyk.core.tool.utils.BeanUtil.copy(accountList, DeviceAccount.class)));
		// 查询除今日之外的内设备状态记录
		equipmentStatusRecordList.addAll(equipmentStatusRecordService.list(ListUtil.map(accountList, DeviceAccount::getId), v.getStartDate(), v.getEndDate(), statusList));
		if (ObjectUtil.isEmpty(equipmentStatusRecordList)) {
			return keyValueDTOList;
		}
		// 填充周期字段
		fillCycle(v.getTimeType(), equipmentStatusRecordList);
		// 获取周期->百分比Map，key-周期，value-百分比
		Map<String, BigDecimal> cycleToPercentageMap = this.getCycleToPercentageMap(equipmentStatusRecordList);
		// 循环设置
		keyValueDTOList.forEach(dto -> {
			Optional.ofNullable(cycleToPercentageMap.get(dto.getKey()))
				.ifPresent(dto::setValue);
		});
		return keyValueDTOList;
	}

	/***
	 * 获取周期->百分比Map，key-周期，value-百分比
	 * @param equipmentStatusRecordList
	 * @return
	 */
	private Map<String, BigDecimal> getCycleToPercentageMap(List<EquipmentStatusRecord> equipmentStatusRecordList) {
		if (ObjectUtil.isEmpty(equipmentStatusRecordList)) {
			return Collections.emptyMap();
		}
		return equipmentStatusRecordList.stream()
			.collect(Collectors.groupingBy(
				EquipmentStatusRecord::getCycle,
				Collectors.collectingAndThen(
					Collectors.toList(),
					records -> {
						// 总数量
						long total = records.size();
						// 在用+ 备用状态的设备数量
						long inUseAndIdleCount = records.stream()
							.filter(record -> EquipmentStatusEnum.IN_USE.getCode() == record.getStatus() || EquipmentStatusEnum.IDLE.getCode() == record.getStatus())
							.count();
						return new BigDecimal(inUseAndIdleCount)
							.multiply(new BigDecimal(StringPool.HUNDRED))
							.divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP);
					}
				)
			));
	}

	/**
	 * 填充周期字段
	 *
	 * @param timeType
	 * @param equipmentStatusRecordList
	 */
	private void fillCycle(TimeTypeEnumV2 timeType, List<EquipmentStatusRecord> equipmentStatusRecordList) {
		if (ObjectUtil.isEmpty(timeType)) {
			throw new IllegalArgumentException("时间类型不能为空");
		}
		if (ObjectUtil.isEmpty(equipmentStatusRecordList)) {
			return;
		}
		switch (timeType) {
			// 近三十天周期时间等于日期
			case THIRTY_DAYS:
				equipmentStatusRecordList.forEach(equipmentStatusRecord -> {
					equipmentStatusRecord.setCycle(equipmentStatusRecord.getRecordDate().format(DateTimeFormatter.ofPattern(KeyValueDTO.DAY_FORMAT)));
				});
				break;
			case MONTH:
				equipmentStatusRecordList.forEach(equipmentStatusRecord -> {
					equipmentStatusRecord.setCycle(YearMonth.from(equipmentStatusRecord.getRecordDate()).format(DateTimeFormatter.ofPattern(KeyValueDTO.MONTH_FORMAT)));
				});
				break;
			case QUARTER:
				equipmentStatusRecordList.forEach(equipmentStatusRecord -> {
					// 获取当前日期所属季度
					Integer quarter = DateUtils.getFormattedQuarter(equipmentStatusRecord.getRecordDate());
					// 当前日期所属年度
					final int year = equipmentStatusRecord.getRecordDate().getYear();
					final String quarterStr = String.format(KeyValueDTO.QUARTER_FORMAT, year, quarter);
					equipmentStatusRecord.setCycle(quarterStr);
				});
				break;
			case YEAR:
				equipmentStatusRecordList.forEach(equipmentStatusRecord -> {
					equipmentStatusRecord.setCycle(equipmentStatusRecord.getRecordDate().format(DateTimeFormatter.ofPattern(KeyValueDTO.YEAR_FORMAT)));
				});
				break;
			default:
				throw new IllegalArgumentException("参数异常");
		}
	}

	/**
	 * 转为设备状态记录
	 *
	 * @param accountList
	 * @return
	 */
	public List<EquipmentStatusRecord> convertToEquipmentStatusRecord(List<DeviceAccount> accountList) {
		if (ObjectUtil.isEmpty(accountList)) {
			return Collections.emptyList();
		}
		final LocalDate localDate = LocalDate.now();
		return accountList.stream()
			.map(DeviceAccount -> convertToEquipmentStatusRecord(DeviceAccount, localDate))
			.collect(Collectors.toList());
	}

	public EquipmentStatusRecord convertToEquipmentStatusRecord(DeviceAccount deviceAccount, LocalDate recordDate) {
		EquipmentStatusRecord statusRecord = new EquipmentStatusRecord();
		statusRecord.setTenantId(deviceAccount.getTenantId());
		statusRecord.setEquipmentId(deviceAccount.getId());
		statusRecord.setStatus(deviceAccount.getStatus());
		statusRecord.setRecordDate(recordDate);
		return statusRecord;
	}
}
