// package com.snszyk.simas.common.service.impl;
//
// import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
// import com.baomidou.mybatisplus.core.metadata.IPage;
// import com.baomidou.mybatisplus.core.toolkit.Wrappers;
// import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
// import com.snszyk.common.constant.SimasConstant;
// import com.snszyk.core.log.exception.ServiceException;
// import com.snszyk.core.mp.base.BaseServiceImpl;
// import com.snszyk.core.mp.utils.PageUtil;
// import com.snszyk.core.secure.utils.AuthUtil;
// import com.snszyk.core.tool.api.R;
// import com.snszyk.core.tool.jackson.JsonUtil;
// import com.snszyk.core.tool.utils.BeanUtil;
// import com.snszyk.core.tool.utils.DateUtil;
// import com.snszyk.core.tool.utils.Func;
// import com.snszyk.message.enums.MessageBizTypeEnum;
// import com.snszyk.simas.common.entity.BizLog;
// import com.snszyk.simas.common.entity.EquipmentAccount;
// import com.snszyk.simas.common.entity.EquipmentInventory;
// import com.snszyk.simas.common.enums.EquipmentInventoryResultEnum;
// import com.snszyk.simas.common.enums.EquipmentStatusEnum;
// import com.snszyk.simas.common.enums.SystemModuleEnum;
// import com.snszyk.simas.common.service.logic.GeneralLogicService;
// import com.snszyk.simas.common.dto.EquipmentInventoryCountDTO;
// import com.snszyk.simas.common.dto.EquipmentInventoryDTO;
// import com.snszyk.simas.inventory.dto.InventoryStatisticsDTO;
// import com.snszyk.simas.common.mapper.EquipmentInventoryMapper;
// import com.snszyk.simas.inventory.entity.InventoryPlan;
// import com.snszyk.simas.inventory.enums.InventoryPlanEnum;
// import com.snszyk.simas.inventory.enums.InventoryStatusEnum;
// import com.snszyk.simas.inventory.mapper.InventoryPlanMapper;
// import com.snszyk.simas.common.service.IBizLogService;
// import com.snszyk.simas.common.service.IEquipmentInventoryService;
// import com.snszyk.simas.common.vo.BizLogVO;
// import com.snszyk.simas.common.vo.EquipmentInventoryVO;
// import com.snszyk.simas.common.wrapper.EquipmentInventoryWrapper;
// import com.snszyk.system.cache.SysCache;
// import com.snszyk.system.entity.Role;
// import com.snszyk.user.cache.UserCache;
// import com.snszyk.user.entity.User;
// import com.snszyk.user.entity.UserDept;
// import com.snszyk.user.entity.UserRole;
// import com.snszyk.user.feign.IUserClient;
// import lombok.AllArgsConstructor;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
//
// import java.util.*;
// import java.util.stream.Collectors;
//
// @AllArgsConstructor
// @Service
// public class EquipmentInventoryServiceImpl extends BaseServiceImpl<EquipmentInventoryMapper, EquipmentInventory> implements IEquipmentInventoryService {
//
// 	private final IEquipmentAccountService equipmentAccountService;
// 	private final InventoryPlanMapper inventoryPlanMapper;
// 	private final IBizLogService bizLogService;
// 	private final GeneralLogicService generalLogicService;
// 	private final IUserClient userClient;
//
// 	@Override
// 	public boolean init(InventoryPlan plan) {
// 		if (Func.isNotEmpty(plan)
// 			&& InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())
// 		    && Func.isNotEmpty(plan.getInventoryDept())){
// 			List<EquipmentAccount> equipmentList = equipmentAccountService.list(Wrappers.<EquipmentAccount>lambdaQuery()
// 				.in(EquipmentAccount::getUseDept, Func.toLongList(plan.getInventoryDept()))
// 				.ne(EquipmentAccount::getStatus, EquipmentStatusEnum.SCRAPPED.getCode()));
//
// 			// 添加备用设备盘点
// 			if (plan.getIsInventoryIdle() == 1){
// 				List<EquipmentAccount> idleEquipmentList = equipmentAccountService.list(Wrappers.<EquipmentAccount>lambdaQuery()
// 					.isNull(EquipmentAccount::getUseDept)
// 					.ne(EquipmentAccount::getStatus, EquipmentStatusEnum.SCRAPPED.getCode()));
// 				if (Func.isNotEmpty(idleEquipmentList)){
// 					equipmentList.addAll(idleEquipmentList);
// 				}
// 			}
//
// 			List<EquipmentInventory> list = equipmentList.stream().map(account ->{
// 				EquipmentInventory equipment = new EquipmentInventory();
// 				equipment.setPlanId(plan.getId());
// 				equipment.setTenantId(plan.getTenantId());
// 				equipment.setEquipmentId(account.getId());
// 				equipment.setName(account.getName());
// 				equipment.setSn(account.getSn());
// 				equipment.setCode(account.getCode());
// 				equipment.setModel(account.getModel());
// 				equipment.setUseDeptId(account.getUseDept());
// 				equipment.setUserId(account.getUserId());
// 				equipment.setStatus(InventoryStatusEnum.WAIT.getCode());// 默认未盘
// 				equipment.setResult(EquipmentInventoryResultEnum.NOT_START.getCode());
// 				equipment.setTimes(0);
// 				return equipment;
// 			}).collect(Collectors.toList());
//
// 			// 发送消息
// 			R<List<UserDept>> r = userClient.userListOfDept(Func.toLongList(plan.getInventoryDept()));
// 			if (r.isSuccess()){
// 				List<UserDept> userDeptList = r.getData();
// 				if (Func.isNotEmpty(userDeptList)){
// 					Set<Long> userIdList = userDeptList.stream().map(UserDept::getUserId).collect(Collectors.toSet());
//
// 					// 获取设备管理员
// 					Role role = SysCache.getRole(plan.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
// 					R<List<UserRole>>  roleResult = userClient.userListOfRole(Arrays.asList(role.getId()));
// 					if (roleResult.isSuccess()){
// 						List<UserRole> userRoleList = roleResult.getData();
// 						if (Func.isNotEmpty(userRoleList)){
// 							Set<Long> adminIdList = userRoleList.stream().map(UserRole::getUserId).collect(Collectors.toSet());
// 							if (Func.isNotEmpty(adminIdList)){
// 								userIdList.addAll(adminIdList);
// 							}
// 						}
// 					}
// 					generalLogicService.sendMessage(plan.getNo(), JsonUtil.toJson(plan), new ArrayList<>(userIdList), MessageBizTypeEnum.SIMAS_EQUIPMENT_INVENTORY_ADD);
// 				}
// 			}
// 			return this.saveBatch(list);
// 		}
// 		return false;
// 	}
//
// 	@Override
// 	public IPage<EquipmentInventoryDTO> accountPage(IPage page, EquipmentInventory record) {
// 		InventoryPlan plan = inventoryPlanMapper.selectById(record.getPlanId());
// 		if (Func.isEmpty(plan)){
// 			throw new ServiceException("计划不存在，盘点任务自动取消！");
// 		}
// 		if (InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			if (Func.isEmpty(record.getResult()) || EquipmentInventoryResultEnum.NOT_START == EquipmentInventoryResultEnum.getByCode(record.getResult())){
// 				LambdaQueryWrapper<EquipmentAccount> queryWrapper = Wrappers.lambdaQuery();
// 				queryWrapper.ne(EquipmentAccount::getStatus, EquipmentStatusEnum.SCRAPPED.getCode());
// 				if (Func.equals(plan.getIsInventoryIdle(), 1)){
// 					queryWrapper.and(wrapper -> wrapper.isNull(EquipmentAccount::getUseDept).or().in(EquipmentAccount::getUseDept, Func.toLongList(plan.getInventoryDept())));
// 				} else {
// 					queryWrapper.in(EquipmentAccount::getUseDept, Func.toLongList(plan.getInventoryDept()));
// 				}
// 				IPage<EquipmentAccount> ipage = equipmentAccountService.page(new Page<>(page.getCurrent(), page.getSize()), queryWrapper);
// 				return PageUtil.toPage(ipage, ipage.getRecords().stream().map(account -> {
// 					EquipmentInventoryDTO dto = Objects.requireNonNull(BeanUtil.copy(account, EquipmentInventoryDTO.class));
// 					dto.setPlanId(plan.getId());
// 					dto.setEquipmentId(account.getId());
// 					dto.setCode(account.getCode());
// 					dto.setModel(account.getModel());
// 					dto.setUseDeptId(account.getUseDept());
//
// 					dto.setUserId(account.getUserId());
// 					dto.setResult(EquipmentInventoryResultEnum.NOT_START.getCode());
// 					dto.setResultName(EquipmentInventoryResultEnum.NOT_START.getName());
// 					dto.setTimes(0);
// 					dto.setStatusName(InventoryStatusEnum.WAIT.getName());
// 					if (Func.isNotEmpty(account.getUseDept())){
// 						dto.setUseDeptName(SysCache.getDeptName(account.getUseDept()));
// 					}
// 					if(Func.isNotEmpty(account.getUserId())){
// 						User user = UserCache.getUser(account.getUserId());
// 						if(Func.isNotEmpty(user)){
// 							dto.setUserName(user.getRealName());
// 						}
// 					}
// 					return dto;
// 				}).collect(Collectors.toList()));
// 			}
// 			return page.setRecords(null);
// 		}
// 		IPage<EquipmentInventory> recordIPage = this.page(new Page<>(page.getCurrent(), page.getSize()), Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, record.getPlanId())
// 			.like(Func.isNotEmpty(record.getName()), EquipmentInventory::getName, record.getName())
// 			.eq(Func.isNotEmpty(record.getUseDeptId()), EquipmentInventory::getUseDeptId, record.getUseDeptId())
// 			.eq(Func.isNotEmpty(record.getMethods()), EquipmentInventory::getMethods, record.getMethods())
// 			.eq(Func.isNotEmpty(record.getResult()), EquipmentInventory::getResult, record.getResult())
// 		    .orderByDesc(EquipmentInventory::getStatus, EquipmentInventory::getCreateTime));
//
// 		List<EquipmentInventoryDTO> dtoList = recordIPage.getRecords().stream().map(r -> {
// 			EquipmentInventoryDTO dto = EquipmentInventoryWrapper.build().entityDTO(r);
// 			if (InventoryStatusEnum.FINISH == InventoryStatusEnum.getByCode(r.getStatus())){
// 				List<BizLog> bizLogList = bizLogService.list(Wrappers.<BizLog>query().lambda()
// 					.eq(BizLog::getModule, SystemModuleEnum.EQUIPMENT_INVENTORY.getCode())
// 					.eq(BizLog::getBizId, r.getId()).eq(BizLog::getBizStatus, InventoryStatusEnum.FINISH.getCode()).orderByDesc(BizLog::getOperateTime));
// 				if (Func.isNotEmpty(bizLogList)){
// 					List<EquipmentInventoryDTO> inventoryRecordDTO = bizLogList.stream().map(bizLog -> {
// 						EquipmentInventory inventory = JsonUtil.parse(bizLog.getBizInfo(), EquipmentInventory.class);
// 						inventory.setInventoryUser(bizLog.getOperateUser());
// 						inventory.setUpdateTime(bizLog.getOperateTime());
// 						return EquipmentInventoryWrapper.build().entityDTO(inventory);
// 					}).collect(Collectors.toList());
// 					dto.setInventoryRecord(inventoryRecordDTO);
// 					String inventoryUserNames = bizLogList.stream().map(o->{
// 						User u = UserCache.getUser(o.getOperateUser());
// 						return u.getRealName();
// 					}).distinct().collect(Collectors.joining("、"));
// 					dto.setInventoryUserName(inventoryUserNames);
// 				}
// 			}
// 			return dto;
// 		}).collect(Collectors.toList());
// 		return PageUtil.toPage(recordIPage, dtoList);
// 	}
//
// 	@Override
// 	public EquipmentInventoryDTO accountInventory(EquipmentInventory record) {
// 		EquipmentInventory inventoryRecord = this.getById(record.getId());
// 		if (Func.isEmpty(inventoryRecord)){
// 			throw new ServiceException("盘点任务不存在！");
// 		}
// 		InventoryPlan plan = inventoryPlanMapper.selectById(inventoryRecord.getPlanId());
// 		if (Func.isEmpty(plan)){
// 			throw new ServiceException("计划不存在，盘点任务自动取消！");
// 		}
// 		if (InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			throw new ServiceException("盘点任务未开始！");
// 		}
// 		if (InventoryPlanEnum.COMPLETE == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			throw new ServiceException("盘点任务已结束！");
// 		}
// 		if (Func.isEmpty(record.getResult())){
// 			throw new ServiceException("盘点结果不能为空！");
// 		}
// 		inventoryRecord.setTimes(inventoryRecord.getTimes() + 1);
// 		inventoryRecord.setInventoryUser(AuthUtil.getUserId());
// 		inventoryRecord.setMethods(record.getMethods());
// 		inventoryRecord.setResult(record.getResult());
// 		inventoryRecord.setRemark(record.getRemark());
// 		inventoryRecord.setStatus(InventoryStatusEnum.FINISH.getCode());
// 		this.updateById(inventoryRecord);
//
// 		// 业务日志
// 		BizLogVO bizLog = new BizLogVO(inventoryRecord);
// 		bizLog.setContent("设备盘点").setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
// 		bizLogService.submit(bizLog);
// 		return EquipmentInventoryWrapper.build().entityDTO(inventoryRecord);
// 	}
//
// 	@Override
// 	public IPage<EquipmentInventoryDTO> appAccountPage(IPage page, EquipmentInventoryVO record) {
// 		boolean isAdmin = this.isSimasAdmin();
// 		String userDeptId = AuthUtil.getDeptId();
// 		InventoryPlan plan = inventoryPlanMapper.selectById(record.getPlanId());
// 		if (Func.isEmpty(plan)){
// 			throw new ServiceException("计划不存在，盘点任务自动取消！");
// 		}
// 		if (InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			throw new ServiceException("计划未开始");
// 		}
//
// 		IPage<EquipmentInventory> recordIPage = this.page(new Page<>(page.getCurrent(), page.getSize()),
// 			Wrappers.lambdaQuery(EquipmentInventory.class)
// 				.eq(EquipmentInventory::getPlanId, record.getPlanId())
// 				.like(Func.isNotEmpty(record.getName()), EquipmentInventory::getName, record.getName())
// 				.eq(Func.isNotEmpty(record.getTab()) && record.getTab() != 0, EquipmentInventory::getStatus, record.getTab())
// 				.eq(!isAdmin, EquipmentInventory::getUseDeptId, userDeptId)
// 				.orderByAsc(EquipmentInventory::getStatus, EquipmentInventory::getCreateTime));
//
// 		if (Func.isNotEmpty(recordIPage.getRecords())){
// 			return PageUtil.toPage(recordIPage, recordIPage.getRecords().stream().map(inventoryRecord -> {
// 				return EquipmentInventoryWrapper.build().entityDTO(inventoryRecord);
// 			}).collect(Collectors.toList()));
// 		}
// 		return page.setRecords(null);
// 	}
//
// 	@Override
// 	public InventoryStatisticsDTO statistics(Long planId) {
// 		boolean isAdmin = this.isSimasAdmin();
// 		String userDeptId = AuthUtil.getDeptId();
// 		InventoryPlan plan = inventoryPlanMapper.selectById(planId);
// 		if (Func.isEmpty(plan)){
// 			throw new ServiceException("计划不存在！");
// 		}
// 		if (InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			throw new ServiceException("计划未开始");
// 		}
//
// 		// 总数量
// 		int all = this.count(Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, planId)
// 			.eq(!isAdmin, EquipmentInventory::getUseDeptId, userDeptId));
// 		// 已盘数量
// 		int inventory = this.count(Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, planId)
// 			.eq(EquipmentInventory::getStatus, InventoryStatusEnum.FINISH.getCode())
// 			.eq(!isAdmin, EquipmentInventory::getUseDeptId, userDeptId));
// 		return new InventoryStatisticsDTO(all, all - inventory, inventory);
// 	}
//
// 	@Override
// 	public EquipmentInventoryCountDTO count(Long planId) {
// 		InventoryPlan plan = inventoryPlanMapper.selectById(planId);
// 		if (Func.isEmpty(plan)){
// 			throw new ServiceException("计划不存在，盘点任务自动取消！");
// 		}
// 		int total = 0;
// 		if (InventoryPlanEnum.NOT_START == InventoryPlanEnum.getByCode(plan.getStatus())){
// 			total = equipmentAccountService.count(Wrappers.<EquipmentAccount>lambdaQuery()
// 				.in(EquipmentAccount::getUseDept, Func.toLongList(plan.getInventoryDept()))
// 				.ne(EquipmentAccount::getStatus, EquipmentStatusEnum.SCRAPPED.getCode()));
// 			return new EquipmentInventoryCountDTO(total, 0, total, 0);
// 		}
//
// 		// 未盘数量
// 		int notStart = this.count(Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, planId)
// 			.eq(EquipmentInventory::getResult, EquipmentInventoryResultEnum.NOT_START.getCode()));
// 		// 正常数量
// 		int normal = this.count(Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, planId)
// 			.eq(EquipmentInventory::getResult, EquipmentInventoryResultEnum.NORMAL.getCode()));
// 		// 盘亏数量
// 		int loss = this.count(Wrappers.lambdaQuery(EquipmentInventory.class)
// 			.eq(EquipmentInventory::getPlanId, planId)
// 			.eq(EquipmentInventory::getResult, EquipmentInventoryResultEnum.LOSS.getCode()));
// 		// 总数量
// 		total = notStart + normal + loss;
// 		return new EquipmentInventoryCountDTO(total, normal, notStart, loss);
// 	}
//
// 	@Override
// 	@Transactional(rollbackFor = Exception.class)
// 	public boolean changeUseDept(EquipmentInventory equipmentInventory) {
// 		if (Func.isEmpty(equipmentInventory.getUseDeptId())){
// 			throw new ServiceException("请选择使用部门！");
// 		}
// 		EquipmentInventory entity = this.getById(equipmentInventory.getId());
// 		entity.setUseDeptId(equipmentInventory.getUseDeptId());
// 		entity.setUserId(equipmentInventory.getUserId());
// 		entity.setChangeUseDept(1);
// //		// 业务日志
// //		BizLogVO bizLog = new BizLogVO(entity);
// //		bizLog.setContent("更换使用部门").setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
// //		bizLogService.submit(bizLog);
// 		return this.updateById(entity);
// 	}
//
// 	/**
// 	 * 判断是否为管理员
// 	 * @return
// 	 */
// 	private boolean isSimasAdmin() {
// 		boolean simasAdmin;
// 		// 数据权限
// 		String roleId = UserCache.getUser(AuthUtil.getUserId()).getRoleId();
// 		Role role = SysCache.getRole(AuthUtil.getTenantId(), SimasConstant.SimasRole.SIMAS_ADMIN);
// 		if (Func.isNotEmpty(roleId) && Func.isNotEmpty(role) && roleId.contains(Func.toStr(role.getId()))){
// 			simasAdmin = true;
// 		} else {
// 			simasAdmin = false;
// 		}
// 		return simasAdmin;
// 	}
//
// 	public static void main(String[] args) {
// 		Set<Integer> list = new HashSet<>();
// 		list.add(1);
// 		list.add(5);
// 		list.add(1);
// 		list.add(7);
// 		list.add(1);
// 		list.add(3);
// 		list.add(1);
// 		System.out.println(JsonUtil.toJson(list));
//
// 	}
// }
