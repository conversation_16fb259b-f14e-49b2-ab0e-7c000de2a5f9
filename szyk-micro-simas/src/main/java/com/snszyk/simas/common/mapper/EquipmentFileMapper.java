/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.EquipmentFileDTO;
import com.snszyk.simas.common.entity.EquipmentFile;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备资料表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface EquipmentFileMapper extends BaseMapper<EquipmentFile> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param equipmentFile
	 * @return
	 */
	List<EquipmentFileVO> page(IPage page, @Param("equipmentFile") EquipmentFileVO equipmentFile);


	List<EquipmentFileDTO> listByEquipmentId(@Param("equipmentId") Long equipmentId);

	int countPreByEquipmentId(@Param("equipmentId") Long equipmentId, @Param("categoryCode") String categoryCode);

	EquipmentFile getByIdIgnoreDel(@Param("id") Long id);
}
