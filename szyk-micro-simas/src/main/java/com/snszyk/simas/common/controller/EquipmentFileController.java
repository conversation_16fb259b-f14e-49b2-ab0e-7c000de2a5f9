/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.service.IEquipmentFileService;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备资料表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipment-file")
@Api(value = "设备资料表", tags = "设备资料表接口")
public class EquipmentFileController extends SzykController {

    private final IEquipmentFileService equipmentFileService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入equipmentFile")
    public R<EquipmentFileVO> detail(Long id) {
        return R.data(equipmentFileService.detail(id));
    }

    /**
     * 自定义分页 设备资料表
     */
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "类型", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "categoryId", value = "设备类型", paramType = "query", dataType = "string")

    })
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入equipmentFile")
    public R<IPage<EquipmentFileVO>> page(@ApiIgnore EquipmentFileVO equipmentFile, Query query) {
        IPage<EquipmentFileVO> pages = equipmentFileService.page(Condition.getPage(query), equipmentFile);
        return R.data(pages);
    }

    /**
     * 新增或修改 设备资料表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "新增或修改（设备通用资料）", notes = "传入equipmentFile")
    public R submit(@Valid @RequestBody EquipmentFileVO equipmentFile) {
        return R.status(equipmentFileService.submit(equipmentFile));
    }

    /**
     * 删除 设备资料表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(equipmentFileService.deleteLogic(Func.toLongList(ids)));
    }

    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "categoryId", value = "设备类型", paramType = "query", dataType = "string")

    })
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "列表", notes = "传入equipmentFile")
    public R<List<EquipmentFileVO>> list(@ApiIgnore EquipmentFileVO equipmentFile) {
        List<EquipmentFileVO> list = equipmentFileService.list(equipmentFile);
        return R.data(list);
    }

    // ================================================前期管理====================================================================

    @PostMapping("/submit-pre-file")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "提交（前期资料管理）", notes = "传入preFile")
    public R submitPreFile(@Valid @RequestBody EquipmentPreFileVO preFile) {
        return R.status(equipmentFileService.submitPreFile(preFile));
    }

    @PostMapping("/ai-tools-add-pre-file")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "ai运维标准添加前期资料", notes = "传入preFile")
    public R aiToolsAddPreFile(@Valid @RequestBody EquipmentPreFileVO preFile) {
        return R.status(equipmentFileService.aiToolsAddPreFile(preFile));
    }

    @PostMapping("/ai-tools-remove-pre-file")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "ai运维标准删除前期资料", notes = "传入fileId")
    public R aiToolsRemovePreFile(@ApiParam(value = "资料ID", required = true) @RequestParam Long fileId) {
        return R.status(equipmentFileService.aiToolsRemovePreFile(fileId));
    }

    /**
     * 自定义分页 设备资料表
     */
    @GetMapping("/pre-file-list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "module", value = "PRE: 前期资料，REGISTER：注册登记资料，OPERATION：设备运维相关资料", paramType = "query", dataType = "string")
    })
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "列表", notes = "传入equipmentFile")
    public R<List<EquipmentFileVO>> preFileList(@ApiIgnore EquipmentFileVO equipmentFile) {
        List<EquipmentFileVO> list = equipmentFileService.preFileList(equipmentFile);
        return R.data(list);
    }

}
