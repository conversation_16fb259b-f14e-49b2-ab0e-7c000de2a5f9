<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.EquipmentFileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentFileResultMap" type="com.snszyk.simas.common.entity.EquipmentFile">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="file_category_id" property="fileCategoryId"/>
        <result column="category_id" property="categoryId"/>
        <result column="attach_id" property="attachId"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="page" resultType="com.snszyk.simas.common.vo.EquipmentFileVO">
        select t.*,
               t2.name as fileCategoryName,
               t3.real_name as createUserName,
               t4.real_name as updateUserName ,
               t2.dict_key as typeKey
               from simas_equipment_file t

                   left join device_file_category t2 on t2.id = t.file_category_id and t2.tenant_id = t.tenant_id
        left join szyk_user t3 on t3.id = t.create_user and t3.is_deleted = 0
        left join szyk_user t4 on t4.id = t.update_user and t4.is_deleted = 0
                   where t.is_deleted = 0
        <if test="equipmentFile.no != null and equipmentFile.no != ''">
            and t.`no` like concat('%', #{equipmentFile.no}, '%')
        </if>
        <if test="equipmentFile.name != null and equipmentFile.name != ''">
            and t.`name` like concat('%', #{equipmentFile.name}, '%')
        </if>
        <if test="equipmentFile.module == null or equipmentFile.module == ''">
            and t.`equipment_id` is null and t.category_id is not null
        </if>
        <if test="equipmentFile.categoryIds != null">
            and
            <foreach collection="equipmentFile.categoryIds" index="index" item="item" open="(" separator="or" close=")">
                t.category_id like concat('%',#{item},'%')
            </foreach>
        </if>
        order by t.create_time desc
    </select>
    <select id="listByEquipmentId" resultType="com.snszyk.simas.common.dto.EquipmentFileDTO"
            parameterType="java.lang.Long">
        select t.id,
               t.tenant_id,
               t.equipment_id,
               t.no,
               t.name,
               t.type,
               t.file_category_id,
               t.category_id,
               t.attach_id,
               t.extension,
               t.remark,
               t.status,
               t.create_user,
               t.create_time,
               t.create_dept,
               t.update_user,
               t.update_time,
               t.is_deleted,
               t2.dict_key  as typeKey
        from simas_equipment_file t
            left join device_file_category t2 on t2.id = t.file_category_id and t2.tenant_id = t.tenant_id and t2.is_deleted = 0
        where t.equipment_id = #{equipmentId}
          and t.is_deleted = 0
        order by t.create_time desc

    </select>
    <select id="countPreByEquipmentId" resultType="java.lang.Integer">

        select count(distinct t.id) as count
        from simas_equipment_file t
                 left join device_file_category t2 on t2.id = t.file_category_id and t2.tenant_id = t.tenant_id and t2.is_deleted = 0
        where t.equipment_id = #{equipmentId}
          and t2.dict_key = #{categoryCode}
        and t.is_deleted = 0


    </select>
    <select id="getByIdIgnoreDel" resultType="com.snszyk.simas.common.entity.EquipmentFile">

        select t.*
        from simas_equipment_file t
        where t.id = #{id}

    </select>


</mapper>
