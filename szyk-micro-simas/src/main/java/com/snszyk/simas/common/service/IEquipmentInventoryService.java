// package com.snszyk.simas.common.service;
//
// import com.baomidou.mybatisplus.core.metadata.IPage;
// import com.snszyk.core.mp.base.BaseService;
// import com.snszyk.simas.common.dto.EquipmentInventoryCountDTO;
// import com.snszyk.simas.common.dto.EquipmentInventoryDTO;
// import com.snszyk.simas.inventory.dto.InventoryStatisticsDTO;
// import com.snszyk.simas.common.entity.EquipmentInventory;
// import com.snszyk.simas.inventory.entity.InventoryPlan;
// import com.snszyk.simas.common.vo.EquipmentInventoryVO;
// import org.apache.poi.ss.formula.functions.T;
//
// public interface IEquipmentInventoryService extends BaseService<EquipmentInventory>  {
//
// 	/**
// 	 * 初始化盘点计划明细
// 	 * @param plan
// 	 * @return
// 	 */
// 	boolean init(InventoryPlan plan);
//
// 	/**
// 	 * 盘点明细分页
// 	 * @param page
// 	 * @param record
// 	 * @return
// 	 */
// 	IPage<EquipmentInventoryDTO> accountPage(IPage page, EquipmentInventory record);
//
// 	/**
// 	 * 盘点明细详情
// 	 * @param record
// 	 * @return
// 	 */
// 	EquipmentInventoryDTO accountInventory(EquipmentInventory record);
//
// 	/**
// 	 * app盘点
// 	 * @param page
// 	 * @param record
// 	 * @return
// 	 */
// 	IPage<EquipmentInventoryDTO> appAccountPage(IPage<T> page, EquipmentInventoryVO record);
//
// 	/**
// 	 * app盘点统计
// 	 * @param planId
// 	 * @return
// 	 */
// 	InventoryStatisticsDTO statistics(Long planId);
//
// 	/**
// 	 * 盘点计划统计
// 	 * @param planId
// 	 * @return
// 	 */
// 	EquipmentInventoryCountDTO count(Long planId);
//
// 	boolean changeUseDept(EquipmentInventory equipmentInventory);
//
// }
