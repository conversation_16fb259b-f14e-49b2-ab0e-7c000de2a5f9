/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.dto.BizLogDTO;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.enums.LogTypeEnum;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.mapper.BizLogMapper;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogPageVO;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.common.vo.BizSearchVO;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.mapper.InspectOrderMapper;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.mapper.LubricateOrderMapper;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.mapper.OverhaulOrderMapper;
import com.snszyk.simas.overhaul.mapper.RepairMapper;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

/**
 * 业务日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Service
@AllArgsConstructor
public class BizLogServiceImpl extends ServiceImpl<BizLogMapper, BizLog> implements IBizLogService {

	private final LubricateOrderMapper lubricateOrderMapper;
	private final OverhaulOrderMapper overhaulOrderMapper;
	private final MaintainOrderMapper maintainOrderMapper;
	private final InspectOrderMapper inspectOrderMapper;
	private final RepairMapper repairMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(BizLogVO vo) {
		List<BizLog> list = this.list(Wrappers.<BizLog>query().lambda()
			.eq(BizLog::getBizId, vo.getBizId()).eq(BizLog::getBizStatus, vo.getBizStatus()));
		if (Func.isNotEmpty(list)) {
			if (list.size() == 1) {
				BizLog entity = list.get(0);
				if (entity.getContent().contains("超期")) {
					return this.updateById(entity);
				}
			}
		}
		BizLog bizLog = Objects.requireNonNull(BeanUtil.copy(vo, BizLog.class));
		return this.save(bizLog);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submitBatch(List<BizLogVO> list) {
		List<BizLog> bizLogList = list.stream().map(vo -> {
			BizLog bizLog;
			BizLog entity = this.getOne(Wrappers.<BizLog>query().lambda()
				.eq(BizLog::getBizId, vo.getBizId()).eq(BizLog::getBizStatus, vo.getBizStatus()));
			if (Func.isNotEmpty(entity)) {
				bizLog = entity;
			} else {
				bizLog = Objects.requireNonNull(BeanUtil.copy(vo, BizLog.class));
			}
			return bizLog;
		}).collect(Collectors.toList());
		return this.saveOrUpdateBatch(bizLogList);
	}

	@Override
	public IPage<BizLogDTO> equipmentOrderPage(IPage<BizLogDTO> page, BizSearchVO search) {
		//工单的类型的参数
		List<String> moduleList = Arrays.asList(SystemModuleEnum.INSPECT_ORDER.getCode(), SystemModuleEnum.LUBRICATE_ORDER.getCode(),
			SystemModuleEnum.MAINTAIN_ORDER.getCode(), SystemModuleEnum.OVERHAUL_ORDER.getCode(),
			SystemModuleEnum.INTERNAL_REPAIR.getCode(), SystemModuleEnum.EXTERNAL_REPAIR.getCode());
		search.setModuleList(moduleList);
		IPage<BizLogDTO> pages = page.setRecords(baseMapper.equipmentOrderPage(page, search));
		if (Func.isNotEmpty(pages.getRecords())) {
			pages.getRecords().forEach(record -> {
				Date createTime = null;
				Date endTime = null;
				String executeUserName = null;
				String bizStatusName = null;
				String bizNo = null;
				String moduleName = null;
				switch (SystemModuleEnum.getByCode(record.getModule())) {
					case INSPECT_ORDER:
						InspectOrder inspectOrder = JSONUtil.toBean(record.getBizInfo(), InspectOrder.class);
						createTime = inspectOrder.getCreateTime();
						endTime = inspectOrder.getSubmitTime();
						if (Func.isNotEmpty(inspectOrder.getExecuteUser())) {
							User executeUser = UserCache.getUser(inspectOrder.getExecuteUser());
							if (Func.isNotEmpty(executeUser)) {
								executeUserName = executeUser.getRealName();
							}
						}
						InspectOrder inspectOrderData = inspectOrderMapper.selectOne(Wrappers.<InspectOrder>query().lambda()
							.eq(InspectOrder::getNo, inspectOrder.getNo()));
						if(inspectOrderData!=null){
							OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(inspectOrderData.getStatus());
							if (statusEnum != null) {
								bizStatusName = statusEnum.getName();
							}
							bizNo = inspectOrder.getNo();
						}
						moduleName = SystemModuleEnum.INSPECT_ORDER.getName();
						break;
					case MAINTAIN_ORDER:
						MaintainOrder maintainOrder = JSONUtil.toBean(record.getBizInfo(), MaintainOrder.class);
						createTime = maintainOrder.getCreateTime();
						endTime = maintainOrder.getSubmitTime();
						if (Func.isNotEmpty(maintainOrder.getExecuteUser())) {
							User executeUser = UserCache.getUser(maintainOrder.getExecuteUser());
							if (Func.isNotEmpty(executeUser)) {
								executeUserName = executeUser.getRealName();
							}
						}
						MaintainOrder maintainOrderData = maintainOrderMapper.selectOne(Wrappers.<MaintainOrder>query().lambda()
							.eq(MaintainOrder::getNo, maintainOrder.getNo()));
						bizStatusName = OrderStatusEnum.getByCode(maintainOrderData.getStatus()).getName();
						bizNo = maintainOrder.getNo();
						moduleName = SystemModuleEnum.MAINTAIN_ORDER.getName();
						break;
					case LUBRICATE_ORDER:
						LubricateOrder lubricateOrder = JSONUtil.toBean(record.getBizInfo(), LubricateOrder.class);
						createTime = lubricateOrder.getCreateTime();
						endTime = lubricateOrder.getSubmitTime();
						if (Func.isNotEmpty(lubricateOrder.getExecuteUser())) {
							User executeUser = UserCache.getUser(lubricateOrder.getExecuteUser());
							if (Func.isNotEmpty(executeUser)) {
								executeUserName = executeUser.getRealName();
							}
						}
						LubricateOrder lubricateOrderData = lubricateOrderMapper.selectOne(Wrappers.<LubricateOrder>query().lambda()
							.eq(LubricateOrder::getNo, lubricateOrder.getNo()));
						bizStatusName = OrderStatusEnum.getByCode(lubricateOrderData.getStatus()).getName();
						bizNo = lubricateOrderData.getNo();
						moduleName = SystemModuleEnum.LUBRICATE_ORDER.getName();
						break;
					case OVERHAUL_ORDER:
						OverhaulOrder overhaulOrder = JSONUtil.toBean(record.getBizInfo(), OverhaulOrder.class);
						createTime = overhaulOrder.getCreateTime();
						endTime = overhaulOrder.getSubmitTime();
						if (Func.isNotEmpty(overhaulOrder.getExecuteUser())) {
							User executeUser = UserCache.getUser(overhaulOrder.getExecuteUser());
							if (Func.isNotEmpty(executeUser)) {
								executeUserName = executeUser.getRealName();
							}
						}
						OverhaulOrder overhaulOrderData = overhaulOrderMapper.selectOne(Wrappers.<OverhaulOrder>query().lambda()
							.eq(OverhaulOrder::getNo, overhaulOrder.getNo()));
						OrderStatusEnum statusEnum = OrderStatusEnum.getByCode(overhaulOrderData.getStatus());
						//null
						if (statusEnum != null) {
							bizStatusName = statusEnum.getName();
						}
						bizNo = overhaulOrderData.getNo();
						moduleName = SystemModuleEnum.OVERHAUL_ORDER.getName();
						break;
					case INTERNAL_REPAIR:
					case EXTERNAL_REPAIR:
						Repair repair = JSONUtil.toBean(record.getBizInfo(), Repair.class);
						createTime = repair.getCreateTime();
						endTime = repair.getSubmitTime();
						moduleName = SystemModuleEnum.INTERNAL_REPAIR.getName();
						if (Func.isNotEmpty(repair.getFollowUser())) {
							User followUser = UserCache.getUser(repair.getFollowUser());
							if (Func.isNotEmpty(followUser)) {
								executeUserName = followUser.getRealName();
							}
							moduleName = SystemModuleEnum.EXTERNAL_REPAIR.getName();
						}
						if (Func.isEmpty(executeUserName) && Func.isNotEmpty(repair.getReceiveUser())) {
							User receiveUser = UserCache.getUser(repair.getReceiveUser());
							if (Func.isNotEmpty(receiveUser)) {
								executeUserName = receiveUser.getRealName();
							}
						}
						Repair repairData = repairMapper.selectOne(Wrappers.<Repair>query().lambda()
							.eq(Repair::getNo, repair.getNo()));
						// null
						if (nonNull(repairData)) {
							bizStatusName = OrderStatusEnum.getByCode(repairData.getStatus()).getName();
							bizNo = repair.getNo();
						}
						break;
					default:
				}
				record.setBizNo(bizNo).setModuleName(moduleName).setCreateTime(createTime)
					.setEndTime(endTime).setOperateUserName(executeUserName).setBizStatusName(bizStatusName);
			});
		}
		return pages;
	}

	@Override
	public IPage<BizLog> page(BizLogVO vo, Query query) {
		if (query.getCurrent() == null || query.getSize() == null) {
			query.setCurrent(-1);
			query.setSize(-1);
		}
		return this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(vo.getBizId()), BizLog::getBizId, vo.getBizId())
			.eq(ObjectUtil.isNotEmpty(vo.getBizNo()), BizLog::getBizNo, vo.getBizNo())
			.eq(ObjectUtil.isNotEmpty(vo.getBizStatus()), BizLog::getBizStatus, vo.getBizStatus())
			.eq(ObjectUtil.isNotEmpty(vo.getModule()), BizLog::getModule, vo.getModule())
			.orderByDesc(BizLog::getOperateTime)
			.page(new Page<>(query.getCurrent(), query.getSize()));

	}

	@Override
	public IPage<BizLogDTO> pageList(BizLogPageVO v) {
		IPage<BizLogDTO> bizLogDTOIPage = baseMapper.pageList(v);
		bizLogDTOIPage.getRecords().forEach(record -> {
			String bizNo = record.getBizNo();
			if (Func.isNotEmpty(bizNo)) {
				record.setContent(record.getContent().concat(" 单号为") + bizNo);
			}
			SystemModuleEnum moduleEnum = SystemModuleEnum.getByCode(record.getModule());
			if (Func.isNotEmpty(moduleEnum)) {
				record.setModuleName(moduleEnum.getName());
			}

			//日志的类型
			record.setLogTypeName(LogTypeEnum.getByCode(record.getLogType()).getName());
		});
		return bizLogDTOIPage;

	}


}
