package com.snszyk.simas.common.job.config;

import com.snszyk.common.constant.LauncherConstant;
import com.snszyk.core.tool.utils.StringUtil;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Slf4j
@Configuration
public class XxlJobConfig {
	@Resource
	private DiscoveryClient discoveryClient;
	private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

	@Value("${xxl.job.executor.appname}")
	private String appName;
	@Value("${xxl.job.executor.logretentiondays}")
	private Integer logRetentionDays;
	@Value("${xxl.job.admin.address}")
	private String adminAddressFormat;
	@Value("${xxl.job.executor.port}")
	private Integer executorPort;
	@Value("${xxl.job.executor.logpath}")
	private String logPath;


	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		logger.info(">>>>>>>>>>> xxl-job config init.");
		// 获取xxl-job-admin 地址
		final String adminAddresses = getAdminAddresses();
		if (StringUtil.isBlank(adminAddresses)) {
			return new XxlJobSpringExecutor();
		}

		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
		xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
		xxlJobSpringExecutor.setAppName(appName);
		xxlJobSpringExecutor.setPort(executorPort);
		xxlJobSpringExecutor.setLogPath(logPath);
		xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
		return xxlJobSpringExecutor;
	}

	/**
	 * 获取xxl-job-admin 地址
	 */
	private String getAdminAddresses() {

		final List<String> services = discoveryClient.getServices();
		log.info("nacos services:{}", services);

		List<ServiceInstance> instances = discoveryClient.getInstances(LauncherConstant.APPLICATION_XXLJOB_ADMIN_NAME);

		if (instances.isEmpty()) {
			return null;
		}
		if (StringUtil.isBlank(adminAddressFormat)) {
			throw new IllegalStateException("xxl-job注册中心地址模版不可为空！");
		}
		return instances.stream()
			.map(instance -> String.format(adminAddressFormat, instance.getHost(), instance.getPort()))
			.collect(Collectors.joining(","));
	}


}
