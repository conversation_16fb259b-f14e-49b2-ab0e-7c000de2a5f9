/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.config.LlmConfig;
import com.snszyk.simas.common.dto.CommandAnswerDto;
import com.snszyk.simas.common.dto.CommandLibraryDto;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.entity.CommandHistory;
import com.snszyk.simas.common.entity.CommandLibrary;
import com.snszyk.simas.common.exception.LlmIdGenException;
import com.snszyk.simas.common.llm.IdGenerationStrategy;
import com.snszyk.simas.common.llm.IdGenerationStrategyFactory;
import com.snszyk.simas.common.service.ICommandHistoryService;
import com.snszyk.simas.common.service.ICommandLibraryService;
import com.snszyk.simas.common.vo.CommandLibraryAVo;
import com.snszyk.simas.common.vo.CommandLibraryPageVo;
import com.snszyk.simas.common.vo.CommandQuestionVo;
import com.snszyk.simas.common.vo.CommandQuestionVoV2;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 指令库 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@AllArgsConstructor
@Service
@Slf4j
public class CommandLibraryLogicService {

	private final ICommandLibraryService commandLibraryService;

	private final LlmConfig llmConfig;


	private final IUserClient userClient;


	private final ICommandHistoryService historyService;

	@Transactional
	public CommandLibraryDto saveOrUpdate(CommandLibraryAVo v) {
		// 名称的唯一性校验
		commandLibraryService.checkName(v.getId(), null);
		CommandLibrary copy = BeanUtil.copy(v, CommandLibrary.class);
		boolean b = commandLibraryService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		return detail(copy.getId());
	}

	public IPage<CommandLibraryDto> pageList(CommandLibraryPageVo v) {
		IPage<CommandLibraryDto> page = commandLibraryService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public CommandLibraryDto detail(Long id) {
		// 通过ID查询数据信息
		CommandLibraryDto dto = commandLibraryService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			CommandLibrary data = commandLibraryService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
			deleteResultDto.setName(data.getAction());

			boolean b = commandLibraryService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


	/**
	 * 智能问答
	 * @param v
	 * @return
	 */
	public CommandAnswerDto answer(CommandQuestionVo v) {

		CommandAnswerDto dto = new CommandAnswerDto();
		String question = v.getQuestion();
		String commandRoute = v.getCommandRoute();
		//根据route获取对应的页面路由
		CommandLibraryDto libraryDto = commandLibraryService.getPageRoute(commandRoute);
		if ((libraryDto) == null) {
			throw new ServiceException("未找到对应的页面路由");
		}
		dto.setPageRoute(libraryDto.getPageRoute());
		String pageElement = libraryDto.getPageElement();
		if (pageElement == null) {
			pageElement = "";
		}
		String pageMenuCode = libraryDto.getPageMenuCode();
		//是否有此菜单的权限
		Long userId = AuthUtil.getUserId();
		if (!checkMenuPermission(pageMenuCode, userId)) {
			dto.setReturnMsg("抱歉，您没有此菜单的访问权限");
			return dto;
		}

		//如果是工单审核的直接跳转
		if (commandRoute.contains("工单审核") || "我要查看汇总: ".equals(question)) {
			return dto;
		}
		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("question", question);
		paramMap.put("elements", pageElement);
		paramMap.put("timestamp", System.currentTimeMillis() + "");
		User user = UserCache.getUser(userId);
		if (user != null) {
			paramMap.put("nick_name", user.getRealName());
		}
		//调用llm接口
		String post = HttpUtil.post(llmConfig.getUrl() + llmConfig.getQueryPath(), JSONObject.toJSONString(paramMap));
		log.info("调用大模型返回的结果:{}", post);
		if (Func.isBlank(post)) {
			throw new ServiceException("调用大模型失败");
		}
		JSONObject jsonObject = JSONObject.parseObject(post);

		String tab = jsonObject.getString("tab");
		dto.setTab(tab);

		HashMap<String, Object> conditionMap = new HashMap<>();
		//jsonObject转为map
		//condition条件的处理
		for (String k : jsonObject.keySet()) {
			Object v1 = jsonObject.get(k);
			if (k.equals("tab") || k.equals("msg") || Func.isBlank(String.valueOf(v1))) {
				continue;
			}
			IdGenerationStrategy strategy = IdGenerationStrategyFactory.getStrategy(k);
			if (strategy != null) {
				String nameId = "";
				try {
					nameId = strategy.getId((String) v1);
				} catch (LlmIdGenException e) {
					return new CommandAnswerDto().setReturnMsg(e.getMessage());

				}
				conditionMap.put(k, nameId);
			} else {
				conditionMap.put(k, v1);
			}

		}
		dto.setCondition((conditionMap));
		String msg = jsonObject.getString("msg");
		if (Func.isNotBlank(msg)) {
			dto.setReturnMsg(msg);
			return dto;
		}
		//添加历史记录
		//如果msg为空的时候 添加
		if (Func.isBlank(msg)) {
			CommandHistory commandHistory = new CommandHistory();
			commandHistory.setCommandRoute(commandRoute);
			commandHistory.setUserId(AuthUtil.getUserId());
			commandHistory.setAction(v.getAction());
			if (Func.isNotBlank(question)) {
				question = question.replace("我要查看汇总: ", "");
			}
			commandHistory.setInputContent(question);
			commandHistory.setEffectiveTime(LocalDateTime.now());
			historyService.save(commandHistory);
		}

		return dto;

	}

	// 新增方法：检查菜单权限
	private boolean checkMenuPermission(String menuCode, Long userId) {
		// 使用 sysClient 来检查权限
		R<Boolean> booleanR = userClient.checkMenuPermission(menuCode, userId);
		return booleanR.getData();
	}


	// 新增方法：检查菜单权限
	private boolean checkRoutePermission(String route, Long userId) {
		// 使用 sysClient 来检查权限
		R<Boolean> booleanR = userClient.checkRoutePermission(route, userId);
		return booleanR.getData();
	}

	public CommandAnswerDto answerV2(CommandQuestionVoV2 v) {

		CommandAnswerDto dto = new CommandAnswerDto();
		String question = v.getQuestion();

		//是否有此菜单的权限
		Long userId = AuthUtil.getUserId();


		HashMap<String, Object> paramMap = new HashMap<>();
		paramMap.put("question", question);
		paramMap.put("timestamp", System.currentTimeMillis() + "");
		//userid
		paramMap.put("user_id", userId.toString());
		//nick_name
		User user = UserCache.getUser(userId);
		if (user != null) {
			paramMap.put("nick_name", user.getRealName());
		}
		//调用llm接口
		String post = HttpUtil.post(llmConfig.getUrl() + llmConfig.getQueryPathV2(), JSONObject.toJSONString(paramMap));
		log.info("调用大模型返回的结果:{}", post);
		if (Func.isBlank(post)) {
			dto.setReturnMsg("大模型服务器繁忙，请稍后再试");
			return dto;
		}
		JSONObject jsonObject;
		try {
			jsonObject = JSONObject.parseObject(post);
		} catch (Exception e) {
			dto.setReturnMsg("服务器繁忙，请稍后再试");
			return dto;
		}
		String pageRoute = jsonObject.getString("菜单编码");
		dto.setPageRoute(pageRoute);

		HashMap<String, Object> conditionMap = new HashMap<>();
		//jsonObject转为map
		//condition条件的处理
		for (String k : jsonObject.keySet()) {
			Object v1 = jsonObject.get(k);
			if (k.equals("菜单编码") || Func.isBlank(String.valueOf(v1))) {
				continue;
			}
			//returnMsg
			if (k.equals("msg")) {
				dto.setReturnMsg((String) v1);
				continue;
			}
			IdGenerationStrategy strategy = IdGenerationStrategyFactory.getStrategy(k);
			if (strategy != null) {
				String nameId = "";
				try {
					nameId = strategy.getId((String) v1);
				} catch (LlmIdGenException e) {
					return new CommandAnswerDto().setReturnMsg(e.getMessage());

				}
				conditionMap.put(k, nameId);
			} else {
				conditionMap.put(k, v1);
			}
		}
		dto.setCondition((conditionMap));
		if (Func.isBlank(pageRoute)) {
			dto.setReturnMsg("您好，请问您要咨询设备点巡检，设备保养，设备润滑，检修中的的哪一个功能呢？");

		}
		if (Func.isNotBlank(pageRoute) && !checkRoutePermission(pageRoute, userId)) {
			dto.setReturnMsg("抱歉，您没有此菜单的访问权限");
			return dto;
		}
		//大模型回复的msg
		String msg = jsonObject.getString("msg");
		if (Func.isNotBlank(msg)) {
			dto.setReturnMsg(msg);
			return dto;
		}
		return dto;

	}


}
