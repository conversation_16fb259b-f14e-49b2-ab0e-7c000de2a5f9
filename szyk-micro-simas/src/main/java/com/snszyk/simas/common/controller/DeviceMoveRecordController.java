/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.enums.MoveSourceEnum;
import com.snszyk.simas.common.service.IDeviceMoveRecordService;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 设备移动记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/device-move-record")
@Api(value = "设备移动记录表", tags = "设备移动记录表接口")
public class DeviceMoveRecordController extends SzykController {

	private final IDeviceMoveRecordService deviceMoveRecordService;

	/**
	 * 分页 设备移动记录表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deviceName", value = "设备名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "newLocationName", value = "移动后位置", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入deviceMoveRecord")
	public R<IPage<DeviceMoveRecordVO>> page(@ApiIgnore DeviceMoveRecordVO deviceMoveRecord, Query query, SzykUser user) {
		deviceMoveRecord.setTenantId(user.getTenantId());
		return R.data(deviceMoveRecordService.page(Condition.getPage(query), deviceMoveRecord));
	}

	/**
	 * 新增 设备移动记录表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增", notes = "传入deviceMoveRecord")
	public R save(@Valid @RequestBody DeviceMoveRecordVO deviceMoveRecord) {
		deviceMoveRecord.setSource(MoveSourceEnum.MANUAL_ADD.getCode());
		return R.status(deviceMoveRecordService.submit(deviceMoveRecord));
	}


}
