// /*
//  *      Copyright (c) 2018-2028
//  */
// package com.snszyk.simas.common.service.logic;
//
// import cn.hutool.core.date.DateTime;
// import cn.hutool.core.date.DateUtil;
// import com.baomidou.mybatisplus.core.metadata.IPage;
// import com.baomidou.mybatisplus.core.toolkit.Wrappers;
// import com.snszyk.common.utils.DateUtils;
// import com.snszyk.core.tool.utils.BeanUtil;
// import com.snszyk.core.tool.utils.CollectionUtil;
// import com.snszyk.core.tool.utils.Func;
// import com.snszyk.core.tool.utils.StringPool;
// import com.snszyk.simas.common.dto.StatisticsDTO;
// import com.snszyk.simas.common.excel.InspectPlanStatisticsExcel;
// import com.snszyk.simas.common.excel.MaintainPlanStatisticsExcel;
// import com.snszyk.simas.common.enums.OrderStatusEnum;
// import com.snszyk.simas.inspect.dto.InspectOrderDTO;
// import com.snszyk.simas.inspect.dto.InspectPlanDTO;
// import com.snszyk.simas.inspect.entity.InspectOrder;
// import com.snszyk.simas.inspect.entity.InspectPlan;
// import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
// import com.snszyk.simas.inspect.entity.InspectRecord;
// import com.snszyk.simas.inspect.service.IInspectOrderService;
// import com.snszyk.simas.inspect.service.IInspectPlanEquipmentService;
// import com.snszyk.simas.inspect.service.IInspectPlanService;
// import com.snszyk.simas.inspect.service.IInspectRecordService;
// import com.snszyk.simas.inspect.wrapper.InspectPlanWrapper;
// import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
// import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
// import com.snszyk.simas.maintain.entity.MaintainOrder;
// import com.snszyk.simas.maintain.entity.MaintainPlan;
// import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
// import com.snszyk.simas.maintain.entity.MaintainRecord;
// import com.snszyk.simas.maintain.service.IMaintainOrderService;
// import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
// import com.snszyk.simas.maintain.service.IMaintainPlanService;
// import com.snszyk.simas.maintain.service.IMaintainRecordService;
// import com.snszyk.simas.maintain.wrapper.MaintainPlanWrapper;
// import com.snszyk.simas.inspect.vo.InspectPlanVO;
// import com.snszyk.simas.maintain.vo.MaintainPlanVO;
// import lombok.AllArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Service;
//
// import java.util.*;
// import java.util.concurrent.atomic.AtomicReference;
// import java.util.stream.Collectors;
//
// /**
//  * 统计相关 逻辑服务实现类
//  *
//  * <AUTHOR>
//  * @date 2024/08/20 11:16
//  **/
// @Slf4j
// @Service
// @AllArgsConstructor
// public class StatisticsLogicService {
//
// 	private final IInspectPlanService inspectPlanService;
// 	private final IInspectPlanEquipmentService planEquipmentService;
// 	private final IInspectOrderService inspectOrderService;
// 	private final IInspectRecordService inspectRecordService;
// 	private final IMaintainPlanService maintainPlanService;
// 	private final IMaintainPlanEquipmentService maintainPlanEquipmentService;
// 	private final IMaintainOrderService maintainOrderService;
// 	private final IMaintainRecordService maintainRecordService;
// 	private static final Integer YEAR_MONTH = 12;
// 	private static final Integer MONTH_DAY = 30;
// 	private static final Integer WEEK_DAY = 7;
//
// 	/**
// 	 * 工单完成情况
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/8/20 19:36
// 	 */
// 	public StatisticsDTO inspectOrderStatistics(Integer queryDate) {
// 		StatisticsDTO dto = new StatisticsDTO();
// 		List<Map<String, Object>> data = new ArrayList<>();
// 		List<InspectOrderDTO> orderList = inspectOrderService.inspectOrderStatistics(queryDate);
// 		if (Func.isNotEmpty(orderList)) {
// 			// Map<String, Object> map = new HashMap<>(16);
// 			// map.put("key", "工单总数");
// 			// map.put("value", orderList.size());
// 			// data.add(map);
// 			Integer data1 = 0;
// 			Integer data2 = 0;
// 			Integer data3 = 0;
// 			Integer data4 = 0;
// 			for (InspectOrderDTO inspectOrder : orderList) {
// 				if (OrderStatusEnum.IN_PROCESS == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
// 					data1++;
// 				}
// 				if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
// 					data2++;
// 				}
// 				if (OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
// 					data3++;
// 				}
// 				if (OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(inspectOrder.getStatus())) {
// 					data4++;
// 				}
// 			}
// 			Map<String, Object> map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IN_PROCESS.getName());
// 			map.put("value", data1);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IS_COMPLETED.getName());
// 			map.put("value", data2);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IS_OVERDUE.getName());
// 			map.put("value", data3);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.OVERDUE_COMPLETED.getName());
// 			map.put("value", data4);
// 			data.add(map);
// 			dto.setInspectOrders(data);
// 		}
// 		return dto;
// 	}
//
// 	/**
// 	 * 保养-工单完成情况
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 15:36
// 	 */
// 	public StatisticsDTO maintainOrderStatistics(Integer queryDate) {
// 		StatisticsDTO dto = new StatisticsDTO();
// 		List<Map<String, Object>> data = new ArrayList<>();
// 		List<MaintainOrderDTO> orderList = maintainOrderService.maintainOrderStatistics(queryDate);
// 		if (Func.isNotEmpty(orderList)) {
// 			// Map<String, Object> map = new HashMap<>(16);
// 			// map.put("key", "工单总数");
// 			// map.put("value", orderList.size());
// 			// data.add(map);
// 			Integer data1 = 0;
// 			Integer data2 = 0;
// 			Integer data3 = 0;
// 			Integer data4 = 0;
// 			Integer data5 = 0;
// 			for (MaintainOrderDTO maintainOrder : orderList) {
// 				if (OrderStatusEnum.IN_PROCESS == OrderStatusEnum.getByCode(maintainOrder.getStatus())
// 					|| OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
// 					data1++;
// 				}
// 				if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
// 					data2++;
// 				}
// 				if (OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
// 					data3++;
// 				}
// 				if (OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
// 					data4++;
// 				}
// 				if (OrderStatusEnum.WAIT_CONFIRM == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
// 					data5++;
// 				}
// 			}
// 			Map<String, Object> map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IN_PROCESS.getName());
// 			map.put("value", data1);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IS_COMPLETED.getName());
// 			map.put("value", data2);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.IS_OVERDUE.getName());
// 			map.put("value", data3);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.OVERDUE_COMPLETED.getName());
// 			map.put("value", data4);
// 			data.add(map);
// 			map = new HashMap<>(16);
// 			map.put("key", OrderStatusEnum.WAIT_CONFIRM.getName());
// 			map.put("value", data5);
// 			data.add(map);
// 			dto.setMaintainOrders(data);
// 		}
// 		return dto;
// 	}
//
// 	/**
// 	 * 点巡检异常统计
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/8/20 19:37
// 	 */
// 	public StatisticsDTO inspectAbnormalStatistics(Integer queryDate) {
// 		StatisticsDTO dto;
// 		switch (queryDate) {
// 			case 0:
// 				dto = this.getLastYearData();
// 				break;
// 			case 1:
// 				dto = this.getLastDaysData(MONTH_DAY);
// 				break;
// 			default:
// 				dto = this.getLastDaysData(WEEK_DAY);
// 		}
// 		return dto;
// 	}
//
// 	/**
// 	 * 近1年
// 	 *
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/8/21 11:25
// 	 */
// 	private StatisticsDTO getLastYearData() {
// 		DateTime startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -12 + 1));
// 		List<InspectRecord> inspectRecordList = inspectRecordService.list(Wrappers.<InspectRecord>lambdaQuery()
// 			.ge(InspectRecord::getInspectTime, startDate));
// 		if (CollectionUtil.isNotEmpty(inspectRecordList)) {
// 			StatisticsDTO result = new StatisticsDTO()
// 				.setDateList(new ArrayList<>())
// 				.setNormalCountList(new ArrayList<>())
// 				.setAbnormalCountList(new ArrayList<>());
// 			// 统计每月的日期、正常设备数、异常设备数
// 			for (int i = 0; i < YEAR_MONTH; i++) {
// 				String date = DateUtil.format(DateUtil.offsetMonth(startDate, i), "yyyy-MM");
// 				// 添加日期
// 				result.getDateList().add(date);
// 				// 添加当月的正常设备数
// 				result.getNormalCountList().add((inspectRecordList.stream()
// 					.filter(inspectRecord -> inspectRecord.getIsAbnormal() == 0)
// 					.filter(inspectRecord -> date.equals(DateUtil.format(inspectRecord.getInspectTime(), "yyyy-MM")))
// 					.map(InspectRecord::getEquipmentId).distinct().collect(Collectors.toList()).size()));
// 				// 添加当月的异常设备数
// 				result.getAbnormalCountList().add((inspectRecordList.stream()
// 					.filter(inspectRecord -> inspectRecord.getIsAbnormal() == 1)
// 					.filter(inspectRecord -> date.equals(DateUtil.format(inspectRecord.getInspectTime(), "yyyy-MM")))
// 					.map(InspectRecord::getEquipmentId).distinct().collect(Collectors.toList()).size()));
// 			}
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 近几天
// 	 *
// 	 * @param lastDays
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/8/21 11:25
// 	 */
// 	private StatisticsDTO getLastDaysData(Integer lastDays) {
// 		DateTime startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -lastDays + 1));
// 		List<InspectRecord> inspectRecordList = inspectRecordService.list(Wrappers.<InspectRecord>lambdaQuery()
// 			.ge(InspectRecord::getInspectTime, startDate));
//
// 		if (CollectionUtil.isNotEmpty(inspectRecordList)) {
// 			StatisticsDTO result = new StatisticsDTO()
// 				.setDateList(new ArrayList<>())
// 				.setNormalCountList(new ArrayList<>())
// 				.setAbnormalCountList(new ArrayList<>());
// 			// 统计每天的日期、报警数、设备数
// 			for (int i = 0; i < lastDays; i++) {
// 				String date = DateUtil.format(DateUtil.offsetDay(startDate, i), "yyyy-MM-dd");
// 				// 添加日期
// 				result.getDateList().add(date);
// 				// 添加当日的正常设备数
// 				result.getNormalCountList().add(inspectRecordList.stream()
// 					.filter(inspectRecord -> inspectRecord.getIsAbnormal() == 0)
// 					.filter(inspectRecord -> date.equals(DateUtil.format(inspectRecord.getInspectTime(), "yyyy-MM-dd")))
// 					.map(InspectRecord::getEquipmentId).distinct().collect(Collectors.toList()).size());
// 				// 添加当日的异常设备数
// 				result.getAbnormalCountList().add(inspectRecordList.stream()
// 					.filter(inspectRecord -> inspectRecord.getIsAbnormal() == 1)
// 					.filter(inspectRecord -> date.equals(DateUtil.format(inspectRecord.getInspectTime(), "yyyy-MM-dd")))
// 					.map(InspectRecord::getEquipmentId).distinct().collect(Collectors.toList()).size());
// 			}
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 保养异常统计
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 15:37
// 	 */
// 	public StatisticsDTO maintainAbnormalStatistics(Integer queryDate) {
// 		StatisticsDTO dto;
// 		switch (queryDate) {
// 			case 0:
// 				dto = this.getMaintainLastYearData();
// 				break;
// 			case 1:
// 				dto = this.getMaintainLastDaysData(MONTH_DAY);
// 				break;
// 			default:
// 				dto = this.getMaintainLastDaysData(WEEK_DAY);
// 		}
// 		return dto;
// 	}
//
// 	/**
// 	 * 保养-近1年
// 	 *
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 15:25
// 	 */
// 	private StatisticsDTO getMaintainLastYearData() {
// 		DateTime startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -12 + 1));
// 		List<MaintainRecord> maintainRecordList = maintainRecordService.list(Wrappers.<MaintainRecord>lambdaQuery()
// 			.ge(MaintainRecord::getMaintainTime, startDate));
// 		if (CollectionUtil.isNotEmpty(maintainRecordList)) {
// 			StatisticsDTO result = new StatisticsDTO()
// 				.setDateList(new ArrayList<>())
// 				.setNormalCountList(new ArrayList<>())
// 				.setAbnormalCountList(new ArrayList<>());
// 			// 统计每月的日期、正常设备数、异常设备数
// 			for (int i = 0; i < YEAR_MONTH; i++) {
// 				String date = DateUtil.format(DateUtil.offsetMonth(startDate, i), "yyyy-MM");
// 				// 添加日期
// 				result.getDateList().add(date);
// 				// 添加当月的正常设备数
// 				result.getNormalCountList().add((maintainRecordList.stream()
// 					.filter(maintainRecord -> maintainRecord.getIsAbnormal() == 0)
// 					.filter(maintainRecord -> date.equals(DateUtil.format(maintainRecord.getMaintainTime(), "yyyy-MM")))
// 					.map(MaintainRecord::getEquipmentId).distinct().collect(Collectors.toList()).size()));
// 				// 添加当月的异常设备数
// 				result.getAbnormalCountList().add((maintainRecordList.stream()
// 					.filter(maintainRecord -> maintainRecord.getIsAbnormal() == 1)
// 					.filter(maintainRecord -> date.equals(DateUtil.format(maintainRecord.getMaintainTime(), "yyyy-MM")))
// 					.map(MaintainRecord::getEquipmentId).distinct().collect(Collectors.toList()).size()));
// 			}
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 保养-近几天
// 	 *
// 	 * @param lastDays
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 15:25
// 	 */
// 	private StatisticsDTO getMaintainLastDaysData(Integer lastDays) {
// 		DateTime startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -lastDays + 1));
// 		List<MaintainRecord> maintainRecordList = maintainRecordService.list(Wrappers.<MaintainRecord>lambdaQuery()
// 			.ge(MaintainRecord::getMaintainTime, startDate));
//
// 		if (CollectionUtil.isNotEmpty(maintainRecordList)) {
// 			StatisticsDTO result = new StatisticsDTO()
// 				.setDateList(new ArrayList<>())
// 				.setNormalCountList(new ArrayList<>())
// 				.setAbnormalCountList(new ArrayList<>());
// 			// 统计每天的日期、报警数、设备数
// 			for (int i = 0; i < lastDays; i++) {
// 				String date = DateUtil.format(DateUtil.offsetDay(startDate, i), "yyyy-MM-dd");
// 				// 添加日期
// 				result.getDateList().add(date);
// 				// 添加当日的正常设备数
// 				result.getNormalCountList().add(maintainRecordList.stream()
// 					.filter(maintainRecord -> maintainRecord.getIsAbnormal() == 0)
// 					.filter(maintainRecord -> date.equals(DateUtil.format(maintainRecord.getMaintainTime(), "yyyy-MM-dd")))
// 					.map(MaintainRecord::getEquipmentId).distinct().collect(Collectors.toList()).size());
// 				// 添加当日的异常设备数
// 				result.getAbnormalCountList().add(maintainRecordList.stream()
// 					.filter(maintainRecord -> maintainRecord.getIsAbnormal() == 1)
// 					.filter(maintainRecord -> date.equals(DateUtil.format(maintainRecord.getMaintainTime(), "yyyy-MM-dd")))
// 					.map(MaintainRecord::getEquipmentId).distinct().collect(Collectors.toList()).size());
// 			}
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 点巡检任务统计
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/8/20 19:38
// 	 */
// 	public StatisticsDTO inspectTaskStatistics(Integer queryDate) {
// 		StatisticsDTO result = new StatisticsDTO();
// 		DateTime startDate;
// 		switch (queryDate) {
// 			case 0:
// 				startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -YEAR_MONTH + 1));
// 				break;
// 			case 1:
// 				startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -MONTH_DAY + 1));
// 				;
// 				break;
// 			default:
// 				startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -WEEK_DAY + 1));
// 				;
// 		}
// 		List<InspectOrder> inspectOrderList = inspectOrderService.list(Wrappers.<InspectOrder>lambdaQuery()
// 			.ge(InspectOrder::getCreateTime, startDate));
// 		if (CollectionUtil.isNotEmpty(inspectOrderList)) {
// 			Integer total = inspectOrderList.stream()
// 				.map(InspectOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer normalCount = inspectOrderList.stream()
// 				.filter(inspectOrder -> Func.isNotEmpty(inspectOrder.getIsAbnormal()) && inspectOrder.getIsAbnormal() == 0)
// 				.map(InspectOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer abnormalCount = inspectOrderList.stream()
// 				.filter(inspectOrder -> Func.isNotEmpty(inspectOrder.getIsAbnormal()) && inspectOrder.getIsAbnormal() == 1)
// 				.map(InspectOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer omissionCount = inspectOrderList.stream()
// 				.filter(inspectOrder ->
// 					OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(inspectOrder.getStatus()))
// 				.map(InspectOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			result.setInspectCount(total).setNormalCount(normalCount)
// 				.setAbnormalCount(abnormalCount).setOmissionCount(omissionCount);
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 保养任务统计
// 	 *
// 	 * @param queryDate
// 	 * @return com.snszyk.simas.dto.StatisticsDTO
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 17:38
// 	 */
// 	public StatisticsDTO maintainTaskStatistics(Integer queryDate) {
// 		StatisticsDTO result = new StatisticsDTO();
// 		DateTime startDate;
// 		switch (queryDate) {
// 			case 0:
// 				startDate = DateUtil.beginOfMonth(DateUtil.offsetMonth(new Date(), -YEAR_MONTH + 1));
// 				break;
// 			case 1:
// 				startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -MONTH_DAY + 1));
// 				;
// 				break;
// 			default:
// 				startDate = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -WEEK_DAY + 1));
// 				;
// 		}
// 		List<MaintainOrder> maintainOrderList = maintainOrderService.list(Wrappers.<MaintainOrder>lambdaQuery()
// 			.ge(MaintainOrder::getCreateTime, startDate));
// 		if (CollectionUtil.isNotEmpty(maintainOrderList)) {
// 			Integer total = maintainOrderList.stream()
// 				.map(MaintainOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer normalCount = maintainOrderList.stream()
// 				.filter(MaintainOrder -> Func.isNotEmpty(MaintainOrder.getIsAbnormal()) && MaintainOrder.getIsAbnormal() == 0)
// 				.map(MaintainOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer abnormalCount = maintainOrderList.stream()
// 				.filter(MaintainOrder -> Func.isNotEmpty(MaintainOrder.getIsAbnormal()) && MaintainOrder.getIsAbnormal() == 1)
// 				.map(MaintainOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			Integer omissionCount = maintainOrderList.stream()
// 				.filter(MaintainOrder ->
// 					OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(MaintainOrder.getStatus()))
// 				.map(MaintainOrder::getEquipmentId)
// 				.distinct().collect(Collectors.toList()).size();
// 			result.setMaintainCount(total).setNormalCount(normalCount)
// 				.setAbnormalCount(abnormalCount).setOmissionCount(omissionCount);
// 			return result;
// 		} else {
// 			return null;
// 		}
// 	}
//
// 	/**
// 	 * 点检计划统计分页
// 	 *
// 	 * @param page
// 	 * @param vo
// 	 * @return ge<InspectPlanDTO>
// 	 * <AUTHOR>
// 	 * @date 2024/8/21 16:56
// 	 */
// 	public IPage<InspectPlanDTO> inspectPlanPage(IPage<InspectPlanDTO> page, InspectPlanVO vo) {
// 		IPage<InspectPlanDTO> pages = inspectPlanService.page(page, vo);
// 		if (Func.isNotEmpty(pages.getRecords())) {
// 			pages.getRecords().forEach(plan -> {
// 				plan.setNormalCount(0).setAbnormalCount(0).setOmissionCount(0);
// 				List<InspectOrder> orderList = inspectOrderService.list(Wrappers.<InspectOrder>query().lambda()
// 					.eq(InspectOrder::getPlanId, plan.getId()));
// 				Set<Long> normalSet = new HashSet<>();
// 				Set<Long> abnormalSet = new HashSet<>();
// 				Set<Long> omissionSet = new HashSet<>();
// 				if (Func.isNotEmpty(orderList)) {
// 					orderList.forEach(order -> {
// 						if (Func.isNotEmpty(order.getIsAbnormal())) {
// 							if (order.getIsAbnormal() == 0) {
// 								normalSet.add(order.getEquipmentId());
// 							}
// 							if (order.getIsAbnormal() == 1) {
// 								abnormalSet.add(order.getEquipmentId());
// 							}
// 						}
// 						if (OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(order.getStatus())) {
// 							omissionSet.add(order.getEquipmentId());
// 						}
// 					});
// 				}
// 				plan.setNormalCount(normalSet.size());
// 				plan.setAbnormalCount(abnormalSet.size());
// 				plan.setOmissionCount(abnormalSet.size());
// 			});
// 		}
// 		return pages;
// 	}
//
// 	/**
// 	 * 保养计划统计分页
// 	 *
// 	 * @param page
// 	 * @param vo
// 	 * @return ge<InspectPlanDTO>
// 	 * <AUTHOR>
// 	 * @date 2024/9/2 17:56
// 	 */
// 	public IPage<MaintainPlanDTO> maintainPlanPage(IPage<MaintainPlanDTO> page, MaintainPlanVO vo) {
// 		IPage<MaintainPlanDTO> pages = maintainPlanService.page(page, vo);
// 		if (Func.isNotEmpty(pages.getRecords())) {
// 			pages.getRecords().forEach(plan -> {
// 				plan.setNormalCount(0).setAbnormalCount(0).setOmissionCount(0);
// 				List<MaintainOrder> orderList = maintainOrderService.list(Wrappers.<MaintainOrder>query().lambda()
// 					.eq(MaintainOrder::getPlanId, plan.getId()));
// 				Set<Long> normalSet = new HashSet<>();
// 				Set<Long> abnormalSet = new HashSet<>();
// 				Set<Long> omissionSet = new HashSet<>();
// 				if (Func.isNotEmpty(orderList)) {
// 					orderList.forEach(order -> {
// 						if (Func.isNotEmpty(order.getIsAbnormal())) {
// 							if (order.getIsAbnormal() == 0) {
// 								normalSet.add(order.getEquipmentId());
// 							}
// 							if (order.getIsAbnormal() == 1) {
// 								abnormalSet.add(order.getEquipmentId());
// 							}
// 						}
// 						if (OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(order.getStatus())) {
// 							omissionSet.add(order.getEquipmentId());
// 						}
// 					});
// 				}
// 				plan.setNormalCount(normalSet.size());
// 				plan.setAbnormalCount(abnormalSet.size());
// 				plan.setOmissionCount(abnormalSet.size());
// 			});
// 		}
// 		return pages;
// 	}
//
// 	/**
// 	 * 导出
// 	 *
// 	 * @param vo
// 	 * @return
// 	 */
// 	public List<InspectPlanStatisticsExcel> exportPlan(InspectPlanVO vo) {
// 		if (Func.isNotEmpty(vo.getStartDate())) {
// 			vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
// 		}
// 		if (Func.isNotEmpty(vo.getEndDate())) {
// 			vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
// 		}
// 		List<InspectPlan> list = inspectPlanService.list(Wrappers.<InspectPlan>query().lambda()
// 			.and(Func.isNotEmpty(vo.getKeywords()), wrapper -> wrapper.like(InspectPlan::getName, vo.getKeywords())
// 				.or().like(InspectPlan::getNo, vo.getKeywords()))
// 			.ge(Func.isNotEmpty(vo.getQueryStartDate()), InspectPlan::getStartDate, vo.getQueryStartDate())
// 			.le(Func.isNotEmpty(vo.getQueryEndDate()), InspectPlan::getStartDate, vo.getQueryEndDate())
// 			.orderByDesc(InspectPlan::getCreateTime));
// 		if (Func.isNotEmpty(list)) {
// 			List<InspectPlanDTO> planList = InspectPlanWrapper.build().listDTO(list);
// 			AtomicReference<Integer> sn = new AtomicReference<>(1);
// 			return planList.stream().map(plan -> {
// 				InspectPlanStatisticsExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, InspectPlanStatisticsExcel.class));
// 				if (Func.isNotEmpty(plan.getStartDate())) {
// 					planExcel.setStartDateStr(com.snszyk.core.tool.utils.DateUtil.formatDate(plan.getStartDate()));
// 				}
// 				if (Func.isNotEmpty(plan.getEndDate())) {
// 					planExcel.setEndDateStr(com.snszyk.core.tool.utils.DateUtil.formatDate(plan.getEndDate()));
// 				}
// 				planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
// 				planExcel.setNormalCount(StringPool.ZERO).setAbnormalCount(StringPool.ZERO).setOmissionCount(StringPool.ZERO);
// 				List<InspectOrder> orderList = inspectOrderService.list(Wrappers.<InspectOrder>query().lambda()
// 					.eq(InspectOrder::getPlanId, plan.getId()));
// 				Set<Long> normalSet = new HashSet<>();
// 				Set<Long> abnormalSet = new HashSet<>();
// 				if (Func.isNotEmpty(orderList)) {
// 					orderList.forEach(order -> {
// 						if (Func.isNotEmpty(order.getIsAbnormal())) {
// 							if (order.getIsAbnormal() == 0) {
// 								normalSet.add(order.getEquipmentId());
// 							} else {
// 								abnormalSet.add(order.getEquipmentId());
// 							}
// 						}
// 					});
// 				}
// 				if (Func.isNotEmpty(normalSet)) {
// 					planExcel.setNormalCount(Func.toStr(normalSet.size()));
// 				}
// 				if (Func.isNotEmpty(abnormalSet)) {
// 					planExcel.setAbnormalCount(Func.toStr(abnormalSet.size()));
// 				}
// 				planExcel.setEquipmentCount(Func.toStr(planEquipmentService.count(Wrappers.<InspectPlanEquipment>query().lambda()
// 					.eq(InspectPlanEquipment::getPlanId, plan.getId()))));
// 				return planExcel;
// 			}).collect(Collectors.toList());
// 		}
// 		return null;
// 	}
//
// 	/**
// 	 * 保养-导出
// 	 *
// 	 * @param vo
// 	 * @return
// 	 */
// 	public List<MaintainPlanStatisticsExcel> exportMaintainPlan(MaintainPlanVO vo) {
// 		if (Func.isNotEmpty(vo.getStartDate())) {
// 			vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
// 		}
// 		if (Func.isNotEmpty(vo.getEndDate())) {
// 			vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
// 		}
// 		List<MaintainPlan> list = maintainPlanService.list(Wrappers.<MaintainPlan>query().lambda()
// 			.and(Func.isNotEmpty(vo.getKeywords()), wrapper -> wrapper.like(MaintainPlan::getName, vo.getKeywords())
// 				.or().like(MaintainPlan::getNo, vo.getKeywords()))
// 			.ge(Func.isNotEmpty(vo.getQueryStartDate()), MaintainPlan::getStartDate, vo.getQueryStartDate())
// 			.le(Func.isNotEmpty(vo.getQueryEndDate()), MaintainPlan::getStartDate, vo.getQueryEndDate())
// 			.orderByDesc(MaintainPlan::getCreateTime));
// 		if (Func.isNotEmpty(list)) {
// 			List<MaintainPlanDTO> planList = MaintainPlanWrapper.build().listDTO(list);
// 			AtomicReference<Integer> sn = new AtomicReference<>(1);
// 			return planList.stream().map(plan -> {
// 				MaintainPlanStatisticsExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, MaintainPlanStatisticsExcel.class));
// 				if (Func.isNotEmpty(plan.getStartDate())) {
// 					planExcel.setStartDateStr(com.snszyk.core.tool.utils.DateUtil.formatDate(plan.getStartDate()));
// 				}
// 				if (Func.isNotEmpty(plan.getEndDate())) {
// 					planExcel.setEndDateStr(com.snszyk.core.tool.utils.DateUtil.formatDate(plan.getEndDate()));
// 				}
// 				planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
// 				planExcel.setNormalCount(StringPool.ZERO).setAbnormalCount(StringPool.ZERO).setOmissionCount(StringPool.ZERO);
// 				List<MaintainOrder> orderList = maintainOrderService.list(Wrappers.<MaintainOrder>query().lambda()
// 					.eq(MaintainOrder::getPlanId, plan.getId()));
// 				Set<Long> normalSet = new HashSet<>();
// 				Set<Long> abnormalSet = new HashSet<>();
// 				if (Func.isNotEmpty(orderList)) {
// 					orderList.forEach(order -> {
// 						if (Func.isNotEmpty(order.getIsAbnormal())) {
// 							if (order.getIsAbnormal() == 0) {
// 								normalSet.add(order.getEquipmentId());
// 							} else {
// 								abnormalSet.add(order.getEquipmentId());
// 							}
// 						}
// 					});
// 				}
// 				if (Func.isNotEmpty(normalSet)) {
// 					planExcel.setNormalCount(Func.toStr(normalSet.size()));
// 				}
// 				if (Func.isNotEmpty(abnormalSet)) {
// 					planExcel.setAbnormalCount(Func.toStr(abnormalSet.size()));
// 				}
// 				planExcel.setEquipmentCount(Func.toStr(maintainPlanEquipmentService.count(Wrappers.<MaintainPlanEquipment>query().lambda()
// 					.eq(MaintainPlanEquipment::getPlanId, plan.getId()))));
// 				return planExcel;
// 			}).collect(Collectors.toList());
// 		}
// 		return null;
// 	}
//
//
// }
//
