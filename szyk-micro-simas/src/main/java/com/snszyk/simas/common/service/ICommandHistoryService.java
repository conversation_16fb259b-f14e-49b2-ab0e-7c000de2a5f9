/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.CommandHistoryDto;
import com.snszyk.simas.common.entity.CommandHistory;
import com.snszyk.simas.common.vo.CommandHistoryPageVo;

import java.util.List;

/**
 * 指令历史 服务类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface ICommandHistoryService extends BaseService<CommandHistory> {



	/**
	 * 分页查询
	 */
	IPage<CommandHistoryDto> pageList(CommandHistoryPageVo v);

	/**
	 * 详情
	 */
	CommandHistoryDto detail(Long id);

	List<CommandHistoryDto> commandHistoryList(Long userId);
}
