/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * EquipmentLabelExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class EquipmentLabelExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String no;

	@ExcelProperty("设备编号")
	private String code;

	@ExcelProperty("设备名称")
	private String name;

	@ExcelProperty("使用部门")
	private String useDeptName;

	@ExcelProperty("NFC")
	private String nfc;

	@ExcelProperty("是否贴码")
	private String isPastedName;

}
