/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * InspectOrderExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class InspectOrderExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ExcelProperty("序号")
	private String sn;

	@ExcelProperty("工单编号")
	private String no;

	@ExcelProperty("计划名称")
	private String orderName;

	@ExcelProperty("设备编号")
	private String equipmentCode;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("点巡检部门")
	private String executeDeptName;

	@ExcelProperty("责任人")
	private String executeUserName;

	@ExcelProperty("使用部门")
	private String useDeptName;

	@ExcelProperty("开始时间")
	private String startTimeStr;

	@ExcelProperty("结束时间")
	private String endTimeStr;

	@ExcelProperty("计划周期")
	private String cycleTypeName;

	@ExcelProperty("检查结果")
	private String inspectResult;

	@ExcelProperty("工单状态")
	private String statusName;

	@ExcelProperty("更新人")
	private String updateUserName;

	@ExcelProperty("更新时间")
	private String updateTimeStr;

}
