package com.snszyk.simas.common.service.logic;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.feign.IMessageClient;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.simas.common.entity.TimeoutRemindSet;
import com.snszyk.simas.common.enums.BizTypeEnum;
import com.snszyk.simas.common.service.ITimeoutRemindSetService;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Role;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class GeneralLogicService {

	private final ITimeoutRemindSetService timeoutRemindSetService;
	private final IUserClient userClient;
	private final IMessageClient messageClient;
	private static BigDecimal timeInterval = new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL);

	public List<Long> soonTimeoutUser(BizTypeEnum bizType, Date endTime, int floatDate, Long userId, Long deptId, String alias) {
		List<Long> userIds = new ArrayList<>();
		// 计算时间差（计划执行时间 + 浮动时间 - 当前时间）
		Duration duration = DateUtils.getCurrentDuration(endTime, floatDate);
		// 1.有指定负责人按照个人提醒时间间隔计算并发送消息 2.未指定负责人需要查询该部门所有润滑人员，然后按照各自设置的提醒时间间隔计算并发送消息
		if (Func.isNotEmpty(userId)) {
			TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
				.eq(TimeoutRemindSet::getUserId, userId)
				.eq(TimeoutRemindSet::getBizType, bizType.getCode()));
			if (Func.isNotEmpty(timeoutRemindSet)) {
				timeInterval = timeoutRemindSet.getTimeInterval();
			}
			// Long seconds = order.getPlanTime().getTime() -  System.currentTimeMillis();
			if (duration.toMillis() <= timeInterval.multiply(new BigDecimal(3600)).multiply(new BigDecimal(1000)).longValue()) {
				userIds.add(userId);
			}
		} else {
			Role role = SysCache.getRole(AuthUtil.getTenantId(), alias);
			if (Func.isNotEmpty(role)) {
				R<List<User>> userR = userClient.userListByDeptRole(deptId, role.getId());
				if (userR.isSuccess() && Func.isNotEmpty(userR.getData())) {
					for (User user : userR.getData()) {
						TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
							.eq(TimeoutRemindSet::getUserId, user.getId())
							.eq(TimeoutRemindSet::getBizType, bizType.getCode()));
						if (Func.isNotEmpty(timeoutRemindSet)) {
							timeInterval = timeoutRemindSet.getTimeInterval();
						}
						if (duration.toMillis() <= timeInterval.multiply(new BigDecimal(3600)).multiply(new BigDecimal(1000)).longValue()) {
							userIds.add(user.getId());
						}
					}
				}
			}
		}
		return userIds;
	}


	/**
	 * 消息发送
	 *
	 * @param no             编号或业务id
	 * @param content
	 * @param userIds
	 * @param messageBizType
	 */
	public void sendMessage(String no, String content, List<Long> userIds, MessageBizTypeEnum messageBizType) {
		log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		// 消息接收人
		final List<ReceiverInfoVo.UserVo> userList = userIds.stream()
			.map(userId -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(userId);
				return userVo;
			}).collect(Collectors.toList());
		// 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
		ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		receiverInfoVo.setUserList(userList);

		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("SIMAS");
		messageVo.setSender("SIMAS");
		messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		messageVo.setTitle(messageBizType.getMessage());
		messageVo.setBizType(messageBizType.getCode());
		messageVo.setBizId(no);
		messageVo.setContent(content);
		messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		messageVo.setReceiverInfoVo(receiverInfoVo);
		messageVo.setCreateUser(AuthUtil.getUserId());
		messageVo.setUpdateUser(AuthUtil.getUserId());

		messageClient.pushMessage(messageVo);
		log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	}

}
