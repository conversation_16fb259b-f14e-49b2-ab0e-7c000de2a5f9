/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.dto.CommandHistoryDto;
import com.snszyk.simas.common.entity.CommandHistory;
import com.snszyk.simas.common.vo.CommandHistoryPageVo;
import org.apache.ibatis.annotations.Param;
/**
 * 指令历史 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface CommandHistoryMapper extends BaseMapper<CommandHistory> {

    IPage<CommandHistoryDto> pageList(@Param("v") CommandHistoryPageVo v);

    CommandHistoryDto detail(Long id);

}
