/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.change.dto.EquipmentChangeDto;
import com.snszyk.simas.change.entity.EquipmentChange;
import com.snszyk.simas.change.vo.EquipmentChangePageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 设备变更表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface EquipmentChangeMapper extends BaseMapper<EquipmentChange> {

	IPage<EquipmentChangeDto> pageList(@Param("v") EquipmentChangePageVo v);

	IPage<EquipmentChangeDto> pageAuditList(@Param("v") EquipmentChangePageVo v);

	EquipmentChangeDto detail(Long id);

}
