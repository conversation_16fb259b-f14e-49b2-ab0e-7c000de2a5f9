/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.core.mp.support.Query;
import com.snszyk.simas.common.dto.BizLogDTO;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.vo.BizLogPageVO;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.common.vo.BizSearchVO;

import java.util.List;

/**
 * 业务日志表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
public interface IBizLogService extends IService<BizLog> {

	/**
	 * 提交
	 *
	 * @param bizLog
	 * @return
	 */
	boolean submit(BizLogVO bizLog);

	/**
	 * 批量提交
	 *
	 * @param list
	 * @return
	 */
	boolean submitBatch(List<BizLogVO> list);

	/**
	 * 设备关联工单分页
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	IPage<BizLogDTO> equipmentOrderPage(IPage<BizLogDTO> page, BizSearchVO search);

	IPage<BizLog> page(BizLogVO vo, Query query);

	IPage<BizLogDTO> pageList(BizLogPageVO v);
}
