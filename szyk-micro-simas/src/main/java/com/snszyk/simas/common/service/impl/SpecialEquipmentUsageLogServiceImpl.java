package com.snszyk.simas.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.entity.SpecialEquipmentUsageLog;
import com.snszyk.simas.common.mapper.SpecialEquipmentUsageLogMapper;
import com.snszyk.simas.common.service.ISpecialEquipmentUsageLogService;
import com.snszyk.simas.common.vo.SpecialEquipmentUsagePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ClassName: SpecialEquipmentUsageLogServiceImpl
 * Package: com.snszyk.simas.service.impl
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:28
 */
@AllArgsConstructor
@Service
public class SpecialEquipmentUsageLogServiceImpl extends BaseServiceImpl<SpecialEquipmentUsageLogMapper, SpecialEquipmentUsageLog> implements ISpecialEquipmentUsageLogService {
	@Override
	public IPage<SpecialEquipmentUsageLog> pageList(SpecialEquipmentUsagePageVo v) {
		return super.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(v.getEquipmentId()), SpecialEquipmentUsageLog::getEquipmentId, v.getEquipmentId())
			.between(ObjectUtil.isNotEmpty(v.getStartDate()) && ObjectUtil.isNotEmpty(v.getEndDate()), SpecialEquipmentUsageLog::getStartDate, v.getStartDate(), v.getEndDate())
			.orderByAsc(SpecialEquipmentUsageLog::getCreateTime)
			.page(v);
	}
}
