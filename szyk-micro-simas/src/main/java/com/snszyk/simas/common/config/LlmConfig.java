package com.snszyk.simas.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Setter
@Getter
@Component
public class LlmConfig {

	@Setter
	@Getter
	@Value("${llm.url}")
	private String url;

	@Getter
	@Value("${llm.query_path}")
	private String queryPath;
	@Getter
	@Value("${llm.query_path_v2}")
	private String queryPathV2;

}
