/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.ApprovalSettingDto;
import com.snszyk.simas.common.entity.ApprovalSetting;
import com.snszyk.simas.common.vo.ApprovalSettingPageVo;

import java.util.List;

/**
 * 工单审核配置 服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IApprovalSettingService extends BaseService<ApprovalSetting> {

	/**
	 * 名称校验
	 */
	void checkName(Long id, String name);

	/**
	 * 分页查询
	 */
	List<ApprovalSettingDto> pageList(ApprovalSettingPageVo v);

	/**
	 * 详情
	 */
	ApprovalSettingDto detail(Long id);

	/**
	 * 是否需要审核
	 */
	boolean isNeedApproval(String type, String tenantId);
}
