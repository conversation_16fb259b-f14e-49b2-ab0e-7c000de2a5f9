/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.SpecialEquipmentInspectRegister;
import com.snszyk.simas.common.vo.SpecialEquipmentInspectRegisterVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 人员表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
public class SpecialEquipmentInspectRegisterWrapper extends BaseEntityWrapper<SpecialEquipmentInspectRegister, SpecialEquipmentInspectRegisterVO> {

	public static SpecialEquipmentInspectRegisterWrapper build() {
		return new SpecialEquipmentInspectRegisterWrapper();
	}

	@Override
	public SpecialEquipmentInspectRegisterVO entityVO(SpecialEquipmentInspectRegister inspectRegister) {
		SpecialEquipmentInspectRegisterVO inspectRegisterVO
			= Objects.requireNonNull(BeanUtil.copy(inspectRegister, SpecialEquipmentInspectRegisterVO.class));
		inspectRegisterVO
			.setInspectTypeName(DictBizCache.getValue("se_inspect_type", inspectRegister.getInspectType()));
		inspectRegisterVO
			.setInspectResultName(DictBizCache.getValue("se_inspect_result", inspectRegister.getInspectResult()));
		User createUser = UserCache.getUser(inspectRegister.getCreateUser());
		if (Func.isNotEmpty(createUser)) {
			inspectRegisterVO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(inspectRegister.getUpdateUser());
		if (Func.isNotEmpty(updateUser)) {
			inspectRegisterVO.setUpdateUserName(updateUser.getRealName());
		}
		return inspectRegisterVO;
	}


}
