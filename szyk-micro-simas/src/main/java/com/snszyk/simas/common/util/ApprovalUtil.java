package com.snszyk.simas.common.util;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.service.IApprovalSettingService;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @since 2025/1/15 17:34
 * 审核配置的工具
 **/
public class ApprovalUtil {
	/**
	 * 是否需要审核 根据工单的类型
	 */
	public static boolean isNeedApproval(String type) {
		IApprovalSettingService bean = SpringUtil.getBean(IApprovalSettingService.class);
		String tenantId = AuthUtil.getTenantId();
		return bean.isNeedApproval(type,tenantId);
	}

	public static boolean isNeedApproval(String type,String tenantId) {
		IApprovalSettingService bean = SpringUtil.getBean(IApprovalSettingService.class);
		return bean.isNeedApproval(type,tenantId);
	}

}
