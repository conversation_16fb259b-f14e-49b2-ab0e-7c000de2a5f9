/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel.template;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * LubricateStandardExcelTemplate
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(30)
@ContentRowHeight(18)
public class LubricateStandardExcelTemplate {
	private static final long serialVersionUID = 1L;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "系统编码"}, index = 0)
	private String code;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "设备名称"}, index = 1)
	private String name;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "SN编号"}, index = 2)
	private String sn;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "润滑部位（必填）"}, index = 3)
	private String monitorName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "润滑手段（必填）"}, index = 4)
	private String methodsName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "油品类型（必填）"}, index = 5)
	private String oilTypeName;

	@ExcelProperty(value = {"填表须知\n" +
		"1、所有必填项不能为空；", "润滑周期（必填，以天为单位）"}, index = 6)
	private String cycleTypeName;

}
