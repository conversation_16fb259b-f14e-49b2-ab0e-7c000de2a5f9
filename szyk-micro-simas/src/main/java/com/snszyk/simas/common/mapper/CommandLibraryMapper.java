/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tenant.annotation.TenantIgnore;

import com.snszyk.simas.common.dto.CommandLibraryDto;
import com.snszyk.simas.common.entity.CommandLibrary;
import com.snszyk.simas.common.vo.CommandLibraryPageVo;
import org.apache.ibatis.annotations.Param;

/**
 * 指令库 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface CommandLibraryMapper extends BaseMapper<CommandLibrary> {

	IPage<CommandLibraryDto> pageList(@Param("v") CommandLibraryPageVo v);

	CommandLibraryDto detail(Long id);

	@TenantIgnore
    CommandLibraryDto getPageRoute(@Param("commandRoute") String commandRoute);
}
