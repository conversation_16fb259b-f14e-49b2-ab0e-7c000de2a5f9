/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.dto.CommonChartDTO;
import com.snszyk.simas.common.dto.PcHomeEquipmentStatusDTO;
import com.snszyk.simas.common.dto.PcHomeNoFinishStatDTO;
import com.snszyk.simas.common.dto.YearWorkSummaryDTO;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.fault.service.IFaultDefectService;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.inspect.service.IInspectOrderService;
import com.snszyk.simas.inspect.vo.InspectOrderVO;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.maintain.service.IMaintainOrderService;
import com.snszyk.simas.maintain.vo.MaintainOrderVO;
import com.snszyk.simas.overhaul.dto.OverhaulOrderDTO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IRepairService;
import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
import com.snszyk.simas.overhaul.vo.RepairVO;
import com.snszyk.system.entity.Menu;
import com.snszyk.system.entity.RoleMenu;
import com.snszyk.system.enums.MenuEnum;
import com.snszyk.system.feign.ISysClient;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * pc端的首页
 **/
@Slf4j
@Service
@AllArgsConstructor
public class PcHomeLogicService {

	private final IInspectOrderService inspectOrderService;
	private final IMaintainOrderService maintainOrderService;
	private final IRepairService repairService;
	private final IFaultDefectService faultDefectService;
	private final ILubricateOrderService lubricateOrderService;
	private final IOverhaulOrderService overhaulOrderService;
	private final ISysClient sysClient;

	/**
	 * 获取未完成的工单统计
	 *
	 * @return com.snszyk.simas.dto.PcHomeNoFinishStatDTO
	 * <AUTHOR>
	 * @date 2024/8/23 15:16
	 */
	public List<CommonChartDTO> pcHomeOrderStatList(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		List<CommonChartDTO> list = new ArrayList<>();

		PcHomeNoFinishStatDTO pcHomeNoFinishStatDTO = pcHomeOrderStat(statusList, dataAuth, tenantId, start, end);
		list.add(new CommonChartDTO("点巡检工单", pcHomeNoFinishStatDTO.getInspectNum().toString()));
		list.add(new CommonChartDTO("计划性检修工单", pcHomeNoFinishStatDTO.getOverhaulNum().toString()));
		list.add(new CommonChartDTO("保养工单", pcHomeNoFinishStatDTO.getMaintainNum().toString()));
		// 内部维修
		list.add(new CommonChartDTO("内部维修工单", pcHomeNoFinishStatDTO.getRepairInnerNum().toString()));
		// 外部维修
		list.add(new CommonChartDTO("外委维修工单", pcHomeNoFinishStatDTO.getRepairExternalNum().toString()));
		list.add(new CommonChartDTO("润滑工单", pcHomeNoFinishStatDTO.getLubricateNum().toString()));
		return list;
	}

	/**
	 * 设备管理员→全部的
	 * <p>
	 * 点检人→分配给自己的 or 分配给部门的       按人查询
	 * <p>
	 * 其他→分配给部门的& 分配人为nul               按部门查询
	 *
	 * @param statusList
	 * @param dataAuth
	 * @param tenantId
	 * @param start
	 * @param end
	 * @return
	 */
	public PcHomeNoFinishStatDTO pcHomeOrderStat(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		PcHomeNoFinishStatDTO statDTO = new PcHomeNoFinishStatDTO();
		// 未完成工单说明：执行中、已超期、待确认、已驳回（验证未通过）的都属于未完成工单。
		// 点检
		Integer inspectCount = handleInspectCount(statusList, dataAuth, tenantId, start, end);
		// 检修
		Integer overhaulCount = handleOverhaulCount(statusList, dataAuth, tenantId, start, end);
		// 保养
		Integer maintainCount = handleMaintainCount(statusList, dataAuth, tenantId, start, end);
		// 内部维修
		String bizType = RepairBizTypeEnum.INTERNAL.getCode();
		Integer innerRepairCount = handleRepairCount(statusList, bizType, dataAuth, tenantId, start, end);
		// 外部维修
		bizType = RepairBizTypeEnum.EXTERNAL.getCode();
		Integer extendRepairCount = handleRepairCount(statusList, bizType, dataAuth, tenantId, start, end);
		// 润滑
		Integer lubricateCount = handleLubricateCount(statusList, dataAuth, tenantId, start, end);
		statDTO.setLubricateNum(lubricateCount);
		statDTO.setRepairExternalNum(extendRepairCount);
		statDTO.setRepairInnerNum(innerRepairCount);
		// 总的维修工单
		statDTO.setRepairNum(innerRepairCount + extendRepairCount);
		// 检修工单
		statDTO.setInspectNum(inspectCount);
		statDTO.setOverhaulNum(overhaulCount);
		statDTO.setMaintainNum(maintainCount);
		// 总的工单的数量
		statDTO.setAllNum(inspectCount + overhaulCount + maintainCount + innerRepairCount + extendRepairCount + lubricateCount);

		return statDTO;
	}

	private Integer handleLubricateCount(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		// 查询当前用户是否有保养菜单
		if (!hasMenuPermission(MenuEnum.LUBRICATE_MENU_CODE.getCode())) {
			return 0;
		}
		LubricateOrderVO vo = new LubricateOrderVO();
		// 时间
		vo.setStartCreateTime(start);
		vo.setEndCreateTime(end);
//		vo.setTenantId(tenantId);
		vo.setStatusList(statusList);
//		vo.setChargeDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//		vo.setChargeUser(AuthUtil.getUserId());
//		vo.setQueryAuthRole(OrderQueryDataAuthEnum.OPERATE_USER.getCode());
//		return lubricateOrderService.handleLubricateCount(vo);
		IPage<LubricateOrderDTO> page = lubricateOrderService.page(new Page<>(1, -1L), vo);
		return ObjectUtil.isEmpty(page.getRecords()) ? 0 : page.getRecords().size();
	}

	private Integer handleRepairCount(List<Integer> statusList, String bizType, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		MenuEnum menuEnum = null;
		// 内部维修
		if (RepairBizTypeEnum.INTERNAL.getCode().equals(bizType)) {
			menuEnum = MenuEnum.INTERNAL_REPAIR_MENU_CODE;
		}
		// 外部维修
		if (RepairBizTypeEnum.EXTERNAL.getCode().equals(bizType)) {
			menuEnum = MenuEnum.EXTERNAL_REPAIR_MENU_CODE;
		}

		// 查询当前用户是否有维修菜单
		assert menuEnum != null;
		if (!hasMenuPermission(menuEnum.getCode())) {
			return 0;
		}


		RepairVO vo = new RepairVO();
		// 时间
		if (Func.isNotEmpty(start)) {
			vo.setStartDate(start.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
		if (Func.isNotEmpty(end)) {
			vo.setEndDate(end.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
//		vo.setTenantId(tenantId);
		vo.setBizType(bizType);
		vo.setStatusList(statusList);
//		if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(vo.getBizType())) {
//			vo.setReceiveUser(AuthUtil.getUserId());
//		}
//		if (RepairBizTypeEnum.EXTERNAL == RepairBizTypeEnum.getByCode(vo.getBizType())) {
//			vo.setFollowUser(AuthUtil.getUserId());
//		}
//		return repairService.handleRepairCount(vo);
		IPage<RepairDTO> page = repairService.page(new Page<>(1, -1L), vo);
		return ObjectUtil.isEmpty(page.getRecords()) ? 0 : page.getRecords().size();
	}

	private Integer handleMaintainCount(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		// 查询当前用户是否有保养菜单
		if (!hasMenuPermission(MenuEnum.MAINTAIN_MENU_CODE.getCode())) {
			return 0;
		}


		MaintainOrderVO maintainOrder = new MaintainOrderVO();
		// 时间
		if (Func.isNotEmpty(start)) {
			maintainOrder.setStartDate(start.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
		if (Func.isNotEmpty(end)) {
			maintainOrder.setEndDate(end.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
//		maintainOrder.setTenantId(tenantId);
		maintainOrder.setStatusList(statusList);
//		maintainOrder.setExecuteUser(AuthUtil.getUserId());
//		maintainOrder.setQueryAuthRole(OrderQueryDataAuthEnum.OPERATE_USER.getCode());
//		maintainOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//		return maintainOrderService.handleMaintainCount(maintainOrder);
		IPage<MaintainOrderDTO> page = maintainOrderService.page(new Page<>(1, -1L), maintainOrder);
		return ObjectUtil.isEmpty(page.getRecords()) ? 0 : page.getRecords().size();
	}

	/**
	 * 未完成的润滑
	 *
	 * @param statusList
	 * @param dataAuth
	 * @param tenantId
	 * @param start
	 * @param end
	 * @return
	 */
	private Integer handleOverhaulCount(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		// 查询当前用户是否有检修菜单
		if (!hasMenuPermission(MenuEnum.OVERHAUL_MENU_CODE.getCode())) {
			return 0;
		}


		OverhaulOrderVO overhaulOrder = new OverhaulOrderVO();
		// 时间
		if (Func.isNotEmpty(start)) {
			overhaulOrder.setStartDate(start.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
		if (Func.isNotEmpty(end)) {
			overhaulOrder.setEndDate(end.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
//		overhaulOrder.setTenantId(tenantId);
//		// 未完成的状态
		overhaulOrder.setStatusList(statusList);
//		overhaulOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//		overhaulOrder.setExecuteUser(AuthUtil.getUserId());
//		overhaulOrder.setQueryAuthRole(OrderQueryDataAuthEnum.OPERATE_USER.getCode());
//
//		if (Func.isNotEmpty(overhaulOrder.getStartDate())) {
//			overhaulOrder.setStartDate(overhaulOrder.getStartDate() + DateUtils.DAY_START_TIME);
//		}
//		if (Func.isNotEmpty(overhaulOrder.getEndDate())) {
//			overhaulOrder.setEndDate(overhaulOrder.getEndDate() + DateUtils.DAY_END_TIME);
//		}
//		return overhaulOrderService.handleOverhaulCount(overhaulOrder);
		IPage<OverhaulOrderDTO> page = overhaulOrderService.page(new Page<>(1, -1L), overhaulOrder);
		return ObjectUtil.isEmpty(page.getRecords()) ? 0 : page.getRecords().size();
	}

	private Integer handleInspectCount(List<Integer> statusList, boolean dataAuth, String tenantId, LocalDateTime start, LocalDateTime end) {
		// 查询当前用户是否有点巡检菜单权限
		if (!hasMenuPermission(MenuEnum.INSPECTION_MENU_CODE.getCode())) {
			return 0;
		}


		InspectOrderVO inspectOrder = new InspectOrderVO();
		if (Func.isNotEmpty(start)) {
			inspectOrder.setStartDate(start.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
		if (Func.isNotEmpty(end)) {
			inspectOrder.setEndDate(end.format(DateTimeFormatter.ofPattern(DateUtils.DAY_MILLISECOND_PATTERN)));
		}
//		inspectOrder.setTenantId(tenantId);
//		inspectOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0));
//		inspectOrder.setExecuteUser(AuthUtil.getUserId());
//		inspectOrder.setQueryAuthRole(OrderQueryDataAuthEnum.OPERATE_USER.getCode());
		inspectOrder.setStatusList(statusList);
//		return inspectOrderService.handleInspectCount(inspectOrder);
		IPage<InspectOrderDTO> page = inspectOrderService.page(new Page<>(1, -1L), inspectOrder);
		return ObjectUtil.isEmpty(page.getRecords()) ? 0 : page.getRecords().size();
	}

	/**
	 *
	 * @param menuCode
	 * @return
	 */
	private boolean hasMenuPermission(String menuCode) {
		// 管理员或超级管理员拥有权限
		if (AuthUtil.isAdmin() || AuthUtil.isAdministrator()) {
			return true;
		}
		// 根据menuCode查询菜单
		R<Menu> menuByCodeR = sysClient.getMenuByCode(menuCode);
		if (!menuByCodeR.isSuccess() || ObjectUtil.isEmpty(menuByCodeR.getData())) {
			return false;
		}
		// 根据menuId和角色id查询角色的菜单权限
		R<List<RoleMenu>> roleMenuListR = sysClient.listRoleMenuBy(menuByCodeR.getData().getId(), AuthUtil.getUser().getRoleId());
		if (!roleMenuListR.isSuccess() || ObjectUtil.isEmpty(roleMenuListR.getData())) {
			return false;
		}
		return true;
	}

	/**
	 * 本年度设备运行情况
	 *
	 * @param year
	 * @return
	 */
	public List<PcHomeEquipmentStatusDTO> curYearEquipmentStatus(Integer year) {
		List<PcHomeEquipmentStatusDTO> list = new ArrayList<>();
		// year 年度开始的时间
		LocalDateTime start = LocalDateTime.of(year, 1, 1, 0, 0, 0);
		String tenantId = AuthUtil.getTenantId();
		List<FaultDefectDTO> faultDefectDTOS = faultDefectService.curYearFault(start, new ArrayList<>(), tenantId);
		// 维修工单的数量 （内+外），已关闭状态除外。
		List<Integer> statusList = Arrays.asList(OrderStatusEnum.WAIT.getCode(), OrderStatusEnum.IN_PROCESS.getCode(),
			OrderStatusEnum.IS_COMPLETED.getCode(), OrderStatusEnum.IS_OVERDUE.getCode(),
			OrderStatusEnum.OVERDUE_COMPLETED.getCode(), OrderStatusEnum.WAIT_CONFIRM.getCode(), OrderStatusEnum.IS_REJECTED.getCode()
		);
		List<RepairDTO> repairList = repairService.curYearRepair(start, statusList, tenantId);
		// 当前的月份
		int month = LocalDateTime.now().getMonthValue();
		for (int i = 1; i <= month; i++) {
			PcHomeEquipmentStatusDTO pcHomeEquipmentStatusDTO = new PcHomeEquipmentStatusDTO();
			pcHomeEquipmentStatusDTO.setMonth(i);
			list.add(pcHomeEquipmentStatusDTO);
		}
		// faultDefectDTOS 根据月份分组
		Map<Integer, List<FaultDefectDTO>> faultMap = faultDefectDTOS.stream().collect(Collectors.groupingBy(e -> e.getReportTime().getMonth() + 1));
		// repairList 根据月份分组
		Map<Integer, List<RepairDTO>> repairMap = repairList.stream().collect(Collectors.groupingBy(e -> e.getCreateTime().getMonth() + 1));

		for (PcHomeEquipmentStatusDTO pcHomeEquipmentStatusDTO : list) {
			if (faultMap.containsKey(pcHomeEquipmentStatusDTO.getMonth())) {
				pcHomeEquipmentStatusDTO.setDefaultNum(faultMap.get(pcHomeEquipmentStatusDTO.getMonth()).size());
			}
			if (repairMap.containsKey(pcHomeEquipmentStatusDTO.getMonth())) {
				pcHomeEquipmentStatusDTO.setRepairNum(repairMap.get(pcHomeEquipmentStatusDTO.getMonth()).size());
			}
		}
//		补充1到12没有的月份，按月份排序
		for (int i = 1; i <= 12; i++) {
			int finalI = i;
			if (!list.stream().anyMatch(e -> e.getMonth() == finalI)) {
				PcHomeEquipmentStatusDTO pcHomeEquipmentStatusDTO = new PcHomeEquipmentStatusDTO();
				pcHomeEquipmentStatusDTO.setMonth(i);
				list.add(pcHomeEquipmentStatusDTO);
			}
		}
		// 按月份排序
		list.sort(Comparator.comparing(PcHomeEquipmentStatusDTO::getMonth));
		return list;
	}

	/**
	 * 年度工作汇总
	 * 统计本年度所有工单数量，分三类计算占比：
	 * 所有工单：不统计已关闭状态的工单，统计全业务范围的工单（点巡检工单、保养工单、润滑工单、内部维修工单、外委维修工单、计划性检修工单）
	 * 正常完成：已完成状态的工单；
	 * 超期完成工单：超期完成状态的工单；
	 * 待完成的工单：处理中、待审核确认、审核驳回的工单；
	 * 超期工单：超期工单；
	 *
	 * @return
	 */
	public YearWorkSummaryDTO curYearWorkSummary(String tenantId) {
		// 本年度的开始时间
		LocalDateTime start = LocalDateTime.of(LocalDateTime.now().getYear(), 1, 1, 0, 0, 0);
		// 本年度的结束时间
		LocalDateTime end = LocalDateTime.of(LocalDateTime.now().getYear(), 12, 31, 23, 59, 59);
		YearWorkSummaryDTO yearWorkSummaryDTO = new YearWorkSummaryDTO();
		// 总的工单的数量,不含已关闭的
		List<Integer> allStatusList = Arrays.asList(OrderStatusEnum.WAIT.getCode(),
			OrderStatusEnum.IN_PROCESS.getCode(),
			OrderStatusEnum.IS_COMPLETED.getCode(),
			OrderStatusEnum.IS_OVERDUE.getCode(),
			OrderStatusEnum.OVERDUE_COMPLETED.getCode(),
			OrderStatusEnum.WAIT_CONFIRM.getCode(),
			OrderStatusEnum.IS_REJECTED.getCode());

		PcHomeNoFinishStatDTO pcHomeNoFinishStatDTO = pcHomeOrderStat(allStatusList, false, tenantId, start, end);
		// 总的工单的数量
		Integer allNum = pcHomeNoFinishStatDTO.getAllNum();
		yearWorkSummaryDTO.setAllNum(allNum);
		// 正常完成工单量
		List<Integer> completedStatus = Collections.singletonList(OrderStatusEnum.IS_COMPLETED.getCode());
		PcHomeNoFinishStatDTO normalCompletedNum = pcHomeOrderStat(completedStatus, false, tenantId, start, end);
		yearWorkSummaryDTO.setNormalCompletedNum(normalCompletedNum.getAllNum());
		// 超期完成工单量
		completedStatus = Collections.singletonList(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		PcHomeNoFinishStatDTO overdueCompletedNum = pcHomeOrderStat(completedStatus, false, tenantId, start, end);
		yearWorkSummaryDTO.setOverdueCompletedNum(overdueCompletedNum.getAllNum());
		// 待完成工单数量
		List<Integer> todoStatus = Arrays.asList(OrderStatusEnum.IN_PROCESS.getCode(),
			OrderStatusEnum.WAIT_CONFIRM.getCode(),
			OrderStatusEnum.IS_REJECTED.getCode());
		PcHomeNoFinishStatDTO todoNum = pcHomeOrderStat(todoStatus, false, tenantId, start, end);
		yearWorkSummaryDTO.setTodoNum(todoNum.getAllNum());
		// 超期工单数量
		List<Integer> overdueStatus = Collections.singletonList(OrderStatusEnum.IS_OVERDUE.getCode());
		PcHomeNoFinishStatDTO overdueNum = pcHomeOrderStat(overdueStatus, false, tenantId, start, end);
		yearWorkSummaryDTO.setOverdueNum(overdueNum.getAllNum());
		return yearWorkSummaryDTO;

	}
}
