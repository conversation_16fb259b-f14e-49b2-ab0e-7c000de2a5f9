/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.entity.OperateLog;
import com.snszyk.simas.common.vo.OperateLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 操作日志表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
public interface OperateLogMapper extends BaseMapper<OperateLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param operateLog
	 * @return
	 */
	List<OperateLog> page(IPage page, @Param("operateLog")OperateLogVO operateLog);

}
