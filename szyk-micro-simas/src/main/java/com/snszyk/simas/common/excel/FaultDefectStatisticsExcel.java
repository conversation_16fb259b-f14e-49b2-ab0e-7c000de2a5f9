/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * FaultDefectStatisticsExcel
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class FaultDefectStatisticsExcel implements Serializable {
	private static final long serialVersionUID = 1L;


	@ExcelProperty("缺陷故障名称")
	private String name;

	@ExcelProperty("缺陷故障类型")
	private String typeName;
	/**
	 * 产生次数
	 */
	@ExcelProperty("产生次数")
	private Long count;
	/**
	 * 维修工单数量
	 */
	@ExcelProperty("维修工单数")
	private Long repairCount;
	/**
	 * 完成次数
	 */
	@ExcelProperty("完成次数")
	private Long completeCount;
	/**
	 * 平均维修时长
	 */
	@ExcelProperty("平均维修时长（小时）")
	private String averageTimeTake;
	/**
	 * 完成率
	 */
	@ExcelProperty("完成率")
	private String completeRate;


}
