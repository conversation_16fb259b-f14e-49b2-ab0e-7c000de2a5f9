<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.common.mapper.OperateLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="operateLogResultMap" type="com.snszyk.simas.common.entity.OperateLog">
        <id column="id" property="id"/>
        <result column="biz_id" property="bizId"/>
        <result column="module" property="module"/>
        <result column="type" property="type"/>
        <result column="request" property="request"/>
        <result column="host" property="host"/>
        <result column="net" property="net"/>
        <result column="status" property="status"/>
        <result column="operate_user" property="operateUser"/>
        <result column="operate_time" property="operateTime"/>
    </resultMap>


    <select id="page" resultMap="operateLogResultMap">
        select * from simas_operate_log where 1=1
        <if test="operateLog.module != null and operateLog.module != ''">
            and module = #{operateLog.module}
        </if>
        <if test="operateLog.type != null and operateLog.type != ''">
            and type = #{operateLog.type}
        </if>
        <if test="operateLog.startDate!=null">
            and operate_time <![CDATA[ >= ]]> #{operateLog.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="operateLog.endDate!=null">
            and operate_time <![CDATA[ <= ]]> #{operateLog.endDate, jdbcType=TIMESTAMP}
        </if>
        and tenant_id = #{operateLog.tenantId}
        order by operate_time desc
    </select>

</mapper>
