/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.EquipmentFileUpdateDto;
import com.snszyk.simas.common.entity.EquipmentFileUpdate;
import com.snszyk.simas.common.vo.EquipmentFileUpdatePageVo;

import java.util.List;

/**
 * 设备文件更新 服务类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface IEquipmentFileUpdateService extends BaseService<EquipmentFileUpdate> {


    /**
    * 分页查询
    */
    IPage<EquipmentFileUpdateDto> pageList(EquipmentFileUpdatePageVo v);

    /**
    * 详情
    */
    EquipmentFileUpdateDto detail(Long id);

    List<EquipmentFileUpdateDto> listByChangeId(Long changeId);
}
