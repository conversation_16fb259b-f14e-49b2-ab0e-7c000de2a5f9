/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * MaintainStandardTemplateExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class MaintainStandardTemplateExcel implements Serializable {
	private static final long serialVersionUID = 1L;


	@ExcelProperty("设备编号")
	private String equipmentCode;

	@ExcelProperty("设备名称")
	private String equipmentName;

	@ExcelProperty("保养部位")
	private String monitorName;

	@ExcelProperty("保养标准")
	private String checkStandard;

	@ExcelProperty("保养方法")
	private String checkMethod;

	@ExcelProperty("是否需要确认")
	private String needConfirmName;

	public MaintainStandardTemplateExcel(){
		super();
	}

	public MaintainStandardTemplateExcel(String equipmentCode, String equipmentName){
		super();
		this.equipmentCode = equipmentCode;
		this.equipmentName = equipmentName;
	}

}
