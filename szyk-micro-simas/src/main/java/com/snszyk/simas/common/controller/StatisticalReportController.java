/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.dto.OrderStatisticsDTO;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.common.excel.*;
import com.snszyk.simas.common.service.logic.StatisticalReportLogicService;
import com.snszyk.simas.common.vo.OrderStatisticsGroupByStatisticsDimensionVO;
import com.snszyk.simas.common.vo.OrderStatisticsVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.dto.RepairDurationDTO;
import com.snszyk.simas.overhaul.vo.RepairDurationPageVO;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 统计报表相关接口 控制器
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/statistical-report")
@Api(value = "统计报表相关接口", tags = "统计报表相关接口")
public class StatisticalReportController extends SzykController {

	private final StatisticalReportLogicService logicService;

	/**
	 * 点巡检统计
	 */
	@GetMapping("/inspect-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "点巡检统计", notes = "传入statisticSearch")
	public R<IPage<InspectOrderDTO>> inspectStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.inspectStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出点巡检统计
	 */
	@GetMapping("/export-inspect-order")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "导出点巡检统计", notes = "传入statisticSearch")
	public void exportInspectOrder(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<InspectOrderStatisticsExcel> list = logicService.exportInspectOrder(statisticSearch);
		ExcelUtil.export(response, "点巡检工单统计列表" + DateUtil.time(), "点巡检工单", list, InspectOrderStatisticsExcel.class);
	}

	/**
	 * 按设备统计统一接口
	 */
	@GetMapping("/equipment-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bizModule", value = "业务模块（INSPECT_ORDER：点巡检，MAINTAIN_ORDER：保养，LUBRICATE_ORDER:润滑，REPAIR：维修）", required = true, paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "按设备统计统一接口", notes = "传入statisticSearch")
	public R<IPage<EquipmentStatisticsDTO>> equipmentStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.equipmentStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出设备点巡检统计
	 */
	@GetMapping("/export-equipment-inspect")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bizModule", value = "业务模块（INSPECT_ORDER：点巡检，MAINTAIN_ORDER：保养，LUBRICATE_ORDER:润滑，REPAIR：维修）", required = true, paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "导出设备点巡检统计", notes = "传入statisticSearch")
	public void exportEquipmentInspect(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<EquipmentInspectStatisticsExcel> list = logicService.exportEquipmentInspect(statisticSearch);
		ExcelUtil.export(response, "设备点巡检工单统计列表" + DateUtil.time(), "设备点巡检工单", list, EquipmentInspectStatisticsExcel.class);
	}

	/**
	 * 保养统计
	 */
	@GetMapping("/maintain-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "保养统计", notes = "传入statisticSearch")
	public R<IPage<MaintainOrderDTO>> maintainStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.maintainStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出保养统计
	 */
	@GetMapping("/export-maintain-order")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "导出保养统计", notes = "传入statisticSearch")
	public void exportMaintainOrder(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<MaintainOrderStatisticsExcel> list = logicService.exportMaintainOrder(statisticSearch);
		ExcelUtil.export(response, "保养工单统计列表" + DateUtil.time(), "保养工单", list, MaintainOrderStatisticsExcel.class);
	}

	/**
	 * 导出设备保养统计
	 */
	@GetMapping("/export-equipment-maintain")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "导出设备保养统计", notes = "传入statisticSearch")
	public void exportEquipmentMaintain(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<EquipmentMaintainStatisticsExcel> list = logicService.exportEquipmentMaintain(statisticSearch);
		ExcelUtil.export(response, "设备保养工单统计列表" + DateUtil.time(), "设备保养工单", list, EquipmentMaintainStatisticsExcel.class);
	}

	/**
	 * 维修统计
	 */
	@GetMapping("/repair-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "维修统计", notes = "传入statisticSearch")
	public R<IPage<RepairDTO>> repairStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.repairStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出维修统计
	 */
	@GetMapping("/export-repair")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出维修统计", notes = "传入statisticSearch")
	public void exportRepair(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<RepairStatisticsExcel> list = logicService.exportRepair(statisticSearch);
		ExcelUtil.export(response, "维修工单统计列表" + DateUtil.time(), "维修工单", list, RepairStatisticsExcel.class);
	}
//
//	/**
//	 * 导出设备维修统计
//	 */
//	@GetMapping("/export-equipment-repair")
//	@ApiImplicitParams({
//		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
//		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
//		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
//	})
//	@ApiOperationSupport(order = 10)
//	@ApiOperation(value = "导出设备维修统计", notes = "传入statisticSearch")
//	public void exportEquipmentRepair(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
//		List<EquipmentRepairStatisticsExcel> list = logicService.exportEquipmentRepair(statisticSearch);
//		ExcelUtil.export(response, "设备维修工单统计列表" + DateUtil.time(), "设备维修工单", list, EquipmentRepairStatisticsExcel.class);
//	}
//
	/**
	 * 故障缺陷统计
	 */
	@GetMapping("/fault-defect-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "故障缺陷统计", notes = "传入statisticSearch")
	public R<IPage<FaultDefectDTO>> faultDefectStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query, SzykUser szykUser) {
		statisticSearch.setTenantId(szykUser.getTenantId());
		return R.data(logicService.faultDefectStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出故障缺陷统计
	 */
	@GetMapping("/export-fault-defect")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导出故障缺陷统计", notes = "传入statisticSearch")
	public void exportFaultDefect(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		List<FaultDefectStatisticsExcel> list = logicService.exportFaultDefect(statisticSearch);
		ExcelUtil.export(response, "故障缺陷统计列表" + DateUtil.time(), "故障缺陷", list, FaultDefectStatisticsExcel.class);
	}
//

	/**
	 * 备件损耗统计
	 */
	@GetMapping("/component-material-statistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "asc", value = "是否升序", paramType = "query", dataType = "boolean"),
		@ApiImplicitParam(name = "sparePartConsumptionType", value = "备品备件消耗类型", paramType = "query", allowableValues = "MAINTAIN_ORDER,LUBRICATE_ORDER,REPAIR_ORDER,OVERHAUL_ORDER"),
	})
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "备件损耗统计", notes = "传入statisticSearch")
	public R<IPage<ComponentMaterialVO>> componentMaterialStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.componentMaterialStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 导出备件损耗统计
	 */
	@GetMapping("/export-component-material")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "asc", value = "是否升序", paramType = "query", dataType = "boolean"),
		@ApiImplicitParam(name = "sparePartConsumptionType", value = "备品备件消耗类型", paramType = "query", allowableValues = "MAINTAIN_ORDER,LUBRICATE_ORDER,REPAIR_ORDER,OVERHAUL_ORDER")
	})
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "导出备件损耗统计", notes = "传入statisticSearch")
	public void exportComponentMaterial(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		logicService.exportComponentMaterial(statisticSearch, response);
	}


	/**
	 * 润滑工单统计
	 */
	@GetMapping("/lubricate-order-statistics")
	@ApiOperationSupport(order = 15, author = "zzp")
	@ApiOperation(value = "润滑工单统计", notes = "传入statisticSearch")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	public R<IPage<LubricateOrderDTO>> lubricateOrderStatistics(@ApiIgnore StatisticSearchVO statisticSearch, Query query) {
		return R.data(logicService.lubricateOrderStatistics(Condition.getPage(query), statisticSearch));
	}

	/**
	 * 润滑工单统计导出
	 */
	@GetMapping("/export-lubricate-order-statistics")
	@ApiOperationSupport(order = 16, author = "zzp")
	@ApiOperation(value = "润滑工单统计导出", notes = "传入statisticSearch")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "工单状态", paramType = "query", dataType = "Integer")
	})
	public void exportLubricateOrderStatistics(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		logicService.exportLubricateOrderStatistics(statisticSearch, response);
	}

	/**
	 * 导出设备润滑统计
	 */
	@GetMapping("/export-equipment-lubricate-statistics")
	@ApiOperationSupport(order = 17, author = "zzp")
	@ApiOperation(value = "导出设备润滑统计", notes = "传入statisticSearch")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "queryDate", value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "bizModule", value = "业务模块（INSPECT_ORDER：点巡检，MAINTAIN_ORDER：保养，LUBRICATE_ORDER:润滑，REPAIR：维修）", required = true, paramType = "query", dataType = "string")
	})
	public void exportEquipmentLubricateStatistics(@ApiIgnore StatisticSearchVO statisticSearch, HttpServletResponse response) {
		logicService.exportEquipmentLubricateStatistics(statisticSearch, response);
	}

	/**
	 * 设备完好率
	 */
	@GetMapping("/health-percentage")
	@ApiOperationSupport(order = 18, author = "zzp")
	@ApiOperation(value = "设备完好率", notes = "传入statisticSearch")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "long"),
	})
	public R<String> healthPercentage(Long deptId) {
		return R.data(logicService.healthPercentage(deptId));
	}

	/**
	 * 导出设备完好率
	 */
	@GetMapping("/export-health-percentage")
	@ApiOperationSupport(order = 19, author = "zzp")
	@ApiOperation(value = "导出设备完好率", notes = "传入statisticSearch")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptId", value = "部门", paramType = "query", dataType = "string"),
	})
	public void exportHealthPercentage(Long deptId, HttpServletResponse response) throws UnsupportedEncodingException {
		logicService.exportHealthPercentage(deptId, response);
	}

	/**
	 * 设备维修率
	 */
	@GetMapping("/repair-percentage")
	@ApiOperationSupport(order = 20, author = "zzp")
	@ApiOperation(value = "设备维修率", notes = "传入statisticSearch")
	public R<String> repairPercentage(@Validated RepairDurationPageVO v) {
		return R.data(logicService.repairPercentage(v));
	}

	/**
	 * 设备维修时长分页
	 */
	@GetMapping("/repair-duration-page")
	@ApiOperationSupport(order = 21, author = "zzp")
	@ApiOperation(value = "设备维修时长分页", notes = "传入statisticSearch")
	public R<IPage<RepairDurationDTO>> repairDurationPage(@Validated RepairDurationPageVO vo) {
		return R.data(logicService.repairDurationPage(vo));
	}

	/**
	 * 导出设备维修率
	 */
	@GetMapping("/export-repair-percentage")
	@ApiOperationSupport(order = 22, author = "zzp")
	@ApiOperation(value = "导出设备维修率", notes = "传入statisticSearch")
	public void exportRepairPercentage(@Validated RepairDurationPageVO vo, HttpServletResponse response) throws UnsupportedEncodingException {
		logicService.exportRepairPercentage(vo, response);
	}

	/**
	 * 工单完成情况
	 */
	@GetMapping("/order-statistics")
	@ApiOperationSupport(order = 23, author = "zzp")
	@ApiOperation(value = "工单统计", notes = "传入statisticSearch")
	public R<List<OrderStatisticsDTO>> orderStatistics(@Validated OrderStatisticsVO vo) {
		return R.data(logicService.orderStatistics(vo));
	}

	@GetMapping("/export-order-statistics")
	@ApiOperationSupport(order = 24, author = "zzp")
	@ApiOperation(value = "工单统计导出", notes = "传入statisticSearch")
	public void exportOrderStatistics(@ApiIgnore OrderStatisticsVO vo, HttpServletResponse response) {
		List<OrderStatisticsDTO> list = logicService.orderStatistics(vo);
		ExcelUtil.export(response, "计划执行率" + DateUtil.time(), "计划执行率", list, OrderStatisticsDTO.class);
	}

	/**
	 *
	 */
	@GetMapping("/order-statistics-group-by-statisticsDimension")
	@ApiOperationSupport(order = 25, author = "zzp")
	@ApiOperation(value = "工单统计分组", notes = "传入statisticSearch")
	public R<IPage<OrderStatisticsDTO>> orderStatisticsGroupByStatisticsDimension(@Validated OrderStatisticsGroupByStatisticsDimensionVO vo) {
		return R.data(logicService.orderStatisticsGroupByStatisticsDimension(vo));
	}


}
