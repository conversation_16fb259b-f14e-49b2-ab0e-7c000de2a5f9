package com.snszyk.simas.common.util;

import com.snszyk.simas.common.enums.MessageContentTypeEnum;
import com.snszyk.simas.common.enums.TicketStatusEnum;
import org.springframework.util.PropertyPlaceholderHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * ClassName: EquipmentFaultMaintenanceMessageUtil
 * Package: com.snszyk.simas.util
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 9:47
 */
public class EquipmentFaultMaintenanceMessageUtil {
	/**
	 * 示例：某某某设备产生新的故障缺陷（123131），设备等级（重要设备），设备位置（一楼产线）
	 */
	private static final String messageTemplate = "{equipmentName}设备{ticketStatusMessageName}{messageContentTypeName}({no}),设备等级({importantLevel}),设备位置({locationName})";
	/**
	 * 前缀
	 */
	private static final String PLACEHOLDER_PREFIX = "{";
	/**
	 * 后缀
	 */
	private static final String PLACEHOLDER_SUFFIX = "}";

	/**
	 * 解析
	 *
	 * @param params
	 * @return
	 */
	public static String parse(Map<String, String> params) {
		PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper(PLACEHOLDER_PREFIX, PLACEHOLDER_SUFFIX);
		return helper.replacePlaceholders(messageTemplate, params::get);
	}

	/**
	 * 构建参数map
	 */
	public static Map<String, String> buildParams(String equipmentName, String no, String importantLevel, String locationName, MessageContentTypeEnum messageContentTypeEnum, TicketStatusEnum ticketStatusEnum) {
		Map<String, String> paramsMap = new HashMap<>(8);
		paramsMap.put("equipmentName", equipmentName);
		paramsMap.put("no", no);
		paramsMap.put("importantLevel", importantLevel);
		paramsMap.put("locationName", locationName);
		paramsMap.put("messageContentTypeName", messageContentTypeEnum.getDesc());
		paramsMap.put("ticketStatusMessageName", ticketStatusEnum.getMessageName());
		return paramsMap;
	}
}
