/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.common.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.core.tool.utils.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * OperateLogExcel
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OperateLogExcel implements Serializable {
	private static final long serialVersionUID = 1L;


	@ExcelProperty("系统模块")
	private String moduleName;

	@ExcelProperty("操作类型")
	private String typeName;

	@ExcelProperty("请求方式")
	private String request;

	@ExcelProperty("操作人员")
	private String operateUserName;

	@ExcelProperty("主机")
	private String host;

	@ExcelProperty("操作地点")
	private String netName;

	@ExcelProperty("操作状态")
	private String statusName;

	@ExcelProperty("操作时间")
	@DateTimeFormat(DateUtil.PATTERN_DATETIME)
	private Date operateTime;

}
