<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsWarehouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="resultMap" type="com.snszyk.simas.spare.entity.SparePartsWarehouse">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="manager" property="manager"/>
        <result column="tel" property="tel"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="page" resultMap="resultMap">
        select * from simas_spare_parts_warehouse where is_deleted = 0
        <if test="keywords!=null and keywords != ''">
            AND (`no` like concat('%',#{keywords},'%') or `name` like
            concat('%',#{keywords},'%'))
        </if>
        order by create_time desc
    </select>

</mapper>
