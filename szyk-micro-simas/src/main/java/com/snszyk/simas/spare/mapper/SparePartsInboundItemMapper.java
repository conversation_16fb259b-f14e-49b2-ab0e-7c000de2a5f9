/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsInboundItem;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件入库明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface SparePartsInboundItemMapper extends BaseMapper<SparePartsInboundItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsInboundItem
	 * @return
	 */
	List<SparePartsInboundItemVO> selectSparePartsInboundItemPage(IPage page, SparePartsInboundItemVO sparePartsInboundItem);

}
