/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.snszyk.common.equipment.dto.MeasureUnitDto;
import com.snszyk.common.equipment.feign.IMeasureUnitClient;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.common.enums.SparePartsDictStatusEnum;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.service.ISparePartsDictService;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsDictPageVo;
import com.snszyk.simas.spare.vo.SparePartsDictSaveOrUpdateVo;
import com.snszyk.simas.spare.vo.SparePartsDictVO;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 备品备件字典 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class SparePartsDictLogicService extends BaseCrudLogicService<SparePartsDictDTO, SparePartsDictVO> {

	private final ISparePartsDictService sparePartsDictService;
	private final ISparePartsWarehouseService sparePartsWarehouseService;
	private final IMeasureUnitClient measureUnitClient;
	private final ISparePartsStockService stockService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsDictService;
	}

	/**
	 * 新增或修改
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public BaseCrudDto saveOrUpdate(SparePartsDictSaveOrUpdateVo v) {
		// 新增或修改校验
		this.saveOrUpdateValid(v);
		// 保存或修改
		return this.sparePartsDictService.save(BeanUtil.copy(v, SparePartsDictVO.class));
	}

	/**
	 * 分页
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsDictDTO> page(SparePartsDictPageVo v) {
		IPage<SparePartsDictDTO> page = super.page(SparePartsDictBeanMapper.INSTANCE.toVO(v));
		if (ObjectUtil.isEmpty(page.getRecords())) {
			return v;
		}
		// 计量单位id
		final List<Long> measureUnitId = ListUtil.distinctMap(page.getRecords(), SparePartsDictDTO::getMeasureUnitId);
		// 获取计量单位map
		final Map<Long, MeasureUnitDto> measureUnitMap = this.getMeasureUnitMap(measureUnitId);
		// 获取库房ids
		final List<Long> defaultWarehouseIds = ListUtil.distinctMap(page.getRecords(), SparePartsDictDTO::getDefaultWarehouseId);
		// 获取库房名称map
		final Map<Long, String> DefaultWarehouseIdToNameMap = this.getDefaultWarehouseIdToNameMap(defaultWarehouseIds);
		// 填充dto
		page.getRecords().forEach(dto -> populateDto(dto, measureUnitMap, DefaultWarehouseIdToNameMap));
		return page;
	}

	/**
	 * 更改设备状态
	 *
	 * @param id
	 * @param statusEnum
	 * @return
	 */
	public Boolean updateStatus(Long id, SparePartsDictStatusEnum statusEnum) {
		final SparePartsDictVO vo = new SparePartsDictVO();
		vo.setId(id);
		vo.setStatus(statusEnum.getCode());
		return ObjectUtil.isNotEmpty(this.sparePartsDictService.save(vo));
	}

	/**
	 * 根据id查询
	 */
	@Override
	public SparePartsDictDTO fetchById(Long id) {
		// 查询详情
		final SparePartsDictDTO dto = this.sparePartsDictService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			return dto;
		}

		// 获取计量单位map
		final Map<Long, MeasureUnitDto> measureUnitMap = new HashMap<>();
		Optional.ofNullable(measureUnitClient.getById(dto.getMeasureUnitId()))
			.map(R::getData)
			.ifPresent(measureUnitDto -> measureUnitMap.put(dto.getMeasureUnitId(), measureUnitDto));

		// 获取库房名称map
		final Map<Long, String> defaultWarehouseIdToNameMap = new HashMap<>();
		Optional.ofNullable(sparePartsWarehouseService.getById(dto.getDefaultWarehouseId()))
			.map(SparePartsWarehouse::getName)
			.ifPresent(name -> defaultWarehouseIdToNameMap.put(dto.getDefaultWarehouseId(), name));

		populateDto(dto, measureUnitMap, defaultWarehouseIdToNameMap);

		return dto;
	}

	/**
	 * 获取库房名称map
	 *
	 * @param defaultWarehouseIds
	 * @return
	 */
	private Map<Long, String> getDefaultWarehouseIdToNameMap(List<Long> defaultWarehouseIds) {
		if (ObjectUtil.isEmpty(defaultWarehouseIds)) {
			return MapUtil.empty();
		}
		// 根据ids查询库房List
		final List<SparePartsWarehouse> sparePartsWarehouses = sparePartsWarehouseService.listByIds(defaultWarehouseIds);
		if (ObjectUtil.isEmpty(sparePartsWarehouses)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(sparePartsWarehouses, SparePartsWarehouse::getId, SparePartsWarehouse::getName);
	}

	/**
	 * 获取计量单位名称map
	 *
	 * @param measureUnitId
	 * @return
	 */
	private Map<Long, MeasureUnitDto> getMeasureUnitMap(List<Long> measureUnitId) {
		// 根据ids查询
		final R<List<MeasureUnitDto>> listR = measureUnitClient.listByIds(measureUnitId);
		if (ObjectUtil.isEmpty(listR) || ObjectUtil.isEmpty(listR.getData())) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(listR.getData(), MeasureUnitDto::getId, Function.identity());
	}

	/**
	 * 新增或修改校验
	 *
	 * @param v
	 */
	private void saveOrUpdateValid(SparePartsDictSaveOrUpdateVo v) {
		// 编号不能重复
		SparePartsDictVO vo = new SparePartsDictVO(v.getId(), v.getNo());
		List<SparePartsDictDTO> dictList = this.sparePartsDictService.list(vo);
		if (ObjectUtil.isNotEmpty(dictList)) {
			throw new BusinessException("编号不能重复");
		}
		vo = new SparePartsDictVO(v.getId(), v.getName(), v.getModel());
		dictList = this.sparePartsDictService.list(vo);
		if (ObjectUtil.isNotEmpty(dictList)) {
			throw new BusinessException("名称和规格型号不能重复");
		}
	}

	/**
	 * 填充 Dto
	 *
	 * @param dto
	 * @param measureUnitMap
	 * @param defaultWarehouseIdToNameMap
	 */
	private void populateDto(SparePartsDictDTO dto, Map<Long, MeasureUnitDto> measureUnitMap, Map<Long, String> defaultWarehouseIdToNameMap) {
		// 设置计量单位名称
		Optional.ofNullable(measureUnitMap.get(dto.getMeasureUnitId()))
			.ifPresent(measureUnitDto -> dto.setMeasureUnitName(measureUnitDto.getName()));
		// 计量单位精度
		Optional.ofNullable(measureUnitMap.get(dto.getMeasureUnitId()))
			.map(MeasureUnitDto::getAccuracy)
			.ifPresent(dto::setMeasureUnitPrecision);
		// 设置 默认库房名称
		Optional.ofNullable(defaultWarehouseIdToNameMap.get(dto.getDefaultWarehouseId()))
			.ifPresent(dto::setDefaultWarehouseName);
		// 状态名称
		Optional.ofNullable(SparePartsDictStatusEnum.getByCode(dto.getStatus()))
			.map(SparePartsDictStatusEnum::getValue)
			.ifPresent(dto::setStatusName);
		// 创建人姓名
		Optional.ofNullable(UserCache.getUser(dto.getCreateUser()))
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(UserCache.getUser(dto.getUpdateUser()))
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));
	}

	/**
	 * 删除
	 *
	 * @param ids
	 * @return
	 */
	public Boolean deleteByIds(List<Long> ids) {
		// 在用的字典idList
		List<Long> inUseDictIds = Lists.newArrayList();
		for (Long id : ids) {
			// 根据字典id查询库存
			final List<SparePartsStockDTO> stockDTOList = stockService.listBy(null, Collections.singleton(id));
			if (ObjectUtil.isNotEmpty(stockDTOList)) {
				inUseDictIds.add(id);
			} else {
				sparePartsDictService.deleteById(id);
			}
		}
		if (ObjectUtil.isNotEmpty(inUseDictIds)) {
			final String inUseDictNames = sparePartsDictService.listByIds(inUseDictIds)
				.stream()
				.map(SparePartsDictDTO::getName)
				.collect(Collectors.joining(StringPool.COMMA));
			throw new BusinessException("备品备件字典【" + inUseDictNames + "】已被使用，不能删除");
		}
		return true;
	}

	/**
	 * 删除校验
	 *
	 * @param ids
	 */
	private void deleteValid(List<Long> ids) {
		final List<SparePartsStockDTO> stockDTOList = stockService.listBy(null, ids);
		if (ObjectUtil.isEmpty(stockDTOList)) {
			return;
		}
		final List<Long> dictIds = ListUtil.distinctMap(stockDTOList, SparePartsStockDTO::getDictId);
		final String inUseDictNames = sparePartsDictService.listByIds(dictIds)
			.stream()
			.map(SparePartsDictDTO::getName)
			.collect(Collectors.joining(StringPool.COMMA));
		throw new BusinessException("备品备件字典【" + inUseDictNames + "】已被使用，不能删除");
	}

	/**
	 * 获取备品备件字典
	 *
	 * @param dictIds
	 * @return
	 */
	public Map<Long, SparePartsDictDTO> getDictMap(List<Long> dictIds) {
		if (ObjectUtil.isEmpty(dictIds)) {
			return MapUtil.empty();
		}
		final List<SparePartsDictDTO> dictList = this.sparePartsDictService.listByIds(dictIds);
		if (ObjectUtil.isEmpty(dictList)) {
			return MapUtil.empty();
		}
		// 获取计量单位ids
		final List<Long> measureUnitId = ListUtil.distinctMap(dictList, SparePartsDictDTO::getMeasureUnitId);
		// 获取计量单位map
		final Map<Long, MeasureUnitDto> measureUnitMap = this.getMeasureUnitMap(measureUnitId);

		return dictList.stream()
			.peek(dto -> {
				Optional.ofNullable(measureUnitMap.get(dto.getMeasureUnitId()))
					.ifPresent(measureUnitDto -> {
						dto.setMeasureUnitName(measureUnitDto.getName());
						dto.setMeasureUnitPrecision(measureUnitDto.getAccuracy());
					});
			})
			.collect(Collectors.toMap(SparePartsDictDTO::getId, Function.identity()));
	}
}
