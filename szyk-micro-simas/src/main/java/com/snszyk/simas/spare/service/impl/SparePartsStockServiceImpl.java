/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsStock;
import com.snszyk.simas.spare.mapper.SparePartsStockMapper;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.vo.SparePartsStockPageVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 备品备件库存 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsStockServiceImpl extends BaseCrudServiceImpl<SparePartsStockMapper, SparePartsStock, SparePartsStockDTO, SparePartsStockVO> implements ISparePartsStockService {

	@Override
	public List<SparePartsStockDTO> listByWarehouseIds(Collection<? extends Serializable> warehouseId) {
		final List<SparePartsStock> list = this.lambdaQuery()
			.in(SparePartsStock::getWarehouseId, warehouseId)
			.orderByDesc(SparePartsStock::getId)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsStockDTO.class);
	}

	@Override
	public List<SparePartsStockDTO> listBy(Long warehouseId, Collection<? extends Serializable> dictIds) {
		final List<SparePartsStock> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(warehouseId), SparePartsStock::getWarehouseId, warehouseId)
			.in(ObjectUtil.isNotEmpty(dictIds), SparePartsStock::getDictId, dictIds)
			.orderByDesc(SparePartsStock::getId)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsStockDTO.class);
	}

	@Override
	public List<SparePartsStockDTO> saveOrUpdateBatch(List<SparePartsStockVO> voList) {
		final List<SparePartsStock> entityList = BeanUtil.copy(voList, SparePartsStock.class);
		super.saveOrUpdateBatch(entityList);
		return BeanUtil.copy(entityList, SparePartsStockDTO.class);
	}

	@Override
	public IPage<SparePartsStockDTO> pageList(SparePartsStockPageVO v) {
		return this.baseMapper.pageList(v);
	}

	@Override
	public List<SparePartsStockDTO> listByIds(List<Long> ids) {
		final List<SparePartsStock> list = super.listByIds(ids);
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsStockDTO.class);
	}

	@Override
	public List<SparePartsStockDTO> updateBatchByIds(List<SparePartsStockVO> voList) {
		final List<SparePartsStock> entityList = BeanUtil.copy(voList, SparePartsStock.class);
		super.updateBatchById(entityList);
		return BeanUtil.copy(entityList, SparePartsStockDTO.class);
	}
}
