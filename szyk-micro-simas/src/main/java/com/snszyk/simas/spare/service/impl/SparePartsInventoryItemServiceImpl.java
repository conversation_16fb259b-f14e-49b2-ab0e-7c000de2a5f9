/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryItemBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsInventoryItemDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryItem;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.mapper.SparePartsInventoryItemMapper;
import com.snszyk.simas.spare.service.ISparePartsInventoryItemService;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemPageVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件盘点记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@AllArgsConstructor
@Service
public class SparePartsInventoryItemServiceImpl extends BaseCrudServiceImpl<SparePartsInventoryItemMapper, SparePartsInventoryItem, SparePartsInventoryItemDTO, SparePartsInventoryItemVO> implements ISparePartsInventoryItemService {

	@Override
	protected Wrapper<SparePartsInventoryItem> beforePage(SparePartsInventoryItemVO vo) {
		if (ObjectUtil.isEmpty(vo)) {
			return Wrappers.emptyWrapper();
		}
		return Wrappers.lambdaQuery(SparePartsInventoryItem.class)
			.eq(ObjectUtil.isNotEmpty(vo.getPlanId()), SparePartsInventoryItem::getPlanId, vo.getPlanId())
			.eq(ObjectUtil.isNotEmpty(vo.getWarehouseId()), SparePartsInventoryItem::getWarehouseId, vo.getWarehouseId())
			.eq(ObjectUtil.isNotEmpty(vo.getInventoryOrderId()), SparePartsInventoryItem::getInventoryOrderId, vo.getInventoryOrderId())
			.eq(ObjectUtil.isNotEmpty(vo.getStatus()), SparePartsInventoryItem::getStatus, vo.getStatus())
			.eq(ObjectUtil.isNotEmpty(vo.getResult()), SparePartsInventoryItem::getResult, vo.getResult())
			.orderByDesc(SparePartsInventoryItem::getId);
	}

	@Override
	public Boolean saveBatch(List<SparePartsInventoryItemVO> itemVOList) {
		return super.saveBatch(SparePartsInventoryItemBeanMapper.INSTANCE.toEntityList(itemVOList));
	}

	@Override
	public List<SparePartsInventoryItemDTO> listByOrderIds(List<Long> orderIds) {
		final List<SparePartsInventoryItem> list = this.lambdaQuery()
			.in(SparePartsInventoryItem::getInventoryOrderId, orderIds)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : SparePartsInventoryItemBeanMapper.INSTANCE.toDTOList(list);
	}

	@Override
	public List<SparePartsInventoryItemDTO> saveOrUpdateBatch(List<SparePartsInventoryItemVO> voList) {

		final List<SparePartsInventoryItem> entityList = SparePartsInventoryItemBeanMapper.INSTANCE.toEntityList(voList);
		super.saveOrUpdateBatch(entityList);

		final List<Long> ids = ListUtil.map(entityList, SparePartsInventoryItem::getId);
		final List<SparePartsInventoryItem> itemList = super.listByIds(ids);
		return SparePartsInventoryItemBeanMapper.INSTANCE.toDTOList(itemList);
	}


	@Override
	public List<SparePartsInventoryItemDTO> listByPlanIds(List<Long> planIds) {
		final List<SparePartsInventoryItem> list = this.lambdaQuery()
			.in(SparePartsInventoryItem::getPlanId, planIds)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : SparePartsInventoryItemBeanMapper.INSTANCE.toDTOList(list);
	}

	@Override
	public Boolean updateBatchByIds(List<SparePartsInventoryItemVO> itemVOList) {
		return super.updateBatchById(SparePartsInventoryItemBeanMapper.INSTANCE.toEntityList(itemVOList));
	}

	@Override
	public Boolean removeByIds(List<Long> ids) {
		return super.removeByIds(ids);
	}

	@Override
	public Integer countItemsByOrderIdAndResult(Long inventoryOrderId, Integer result) {
		// ServiceImpl层只负责数据查询，不包含业务逻辑判断
		// 通用的数据查询方法，可复用于各种业务场景
		return this.lambdaQuery()
			.eq(SparePartsInventoryItem::getInventoryOrderId, inventoryOrderId)
			.eq(ObjectUtil.isNotEmpty(result), SparePartsInventoryItem::getResult, result)
			.count();
	}

	@Override
	public IPage<SparePartsInventoryItemDTO> pageWithConditions(SparePartsInventoryItemPageVO pageVO) {
		// ServiceImpl层只负责数据查询，不包含业务逻辑判断
		if (ObjectUtil.isEmpty(pageVO)) {
			return new Page<>();
		}

		// 创建分页对象
		IPage<SparePartsInventoryItem> page = new Page<>(pageVO.getCurrent(), pageVO.getSize());

		// 统一使用自定义SQL查询，支持所有查询条件
		// 通过动态SQL自动处理是否需要关联字典表
		List<SparePartsInventoryItem> records = baseMapper.selectPageWithSparePartsName(page, pageVO);
		page.setRecords(records);

		// 转换为DTO
		return page.convert(SparePartsInventoryItemBeanMapper.INSTANCE::toDTO);
	}
}
