<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsInventoryOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsInventoryOrderResultMap" type="com.snszyk.simas.spare.entity.SparePartsInventoryOrder">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="no" property="no"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_end_date" property="planEndDate"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="inventory_user_id" property="inventoryUserId"/>
        <result column="inventory_user_name" property="inventoryUserName"/>
        <result column="complete_time" property="completeTime"/>
        <result column="total" property="total"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsInventoryOrderPage" resultMap="sparePartsInventoryOrderResultMap">
        select *
        from simas_spare_parts_inventory_order
        where is_deleted = 0
    </select>
    <select id="pageList" resultType="com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO">
        select
        o.*,
        u.dept_id as "inventory_dept_id"
        from
        simas_spare_parts_inventory_order o
        left join szyk_user u on u.id = o.inventory_user_id and u.is_deleted = 0
        <where>
            o.delete_time = 0
            <if test="v.status != null">
                and o.status = #{v.status}
            </if>
            <if test="v.neStatus != null">
                and o.status != #{v.neStatus}
            </if>
            <if test="v.planName != null and v.planName != ''">
                and o.plan_name like concat('%',#{v.planName},'%')
            </if>
            <if test="v.planStartDate !=null and v.planEndDate!=null">
                and o.plan_start_date between #{v.planStartDate} and #{v.planEndDate}
            </if>
        </where>
        order by o.id desc
    </select>

</mapper>
