package com.snszyk.simas.spare.schedule;

import com.alibaba.nacos.shaded.com.google.protobuf.ServiceException;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 计划排期
 *
 * <AUTHOR>
 * @date 2024/06/15 15:27
 **/
@Slf4j
public class SimasPlanSchedule {

	private static final String[] WEEK = {"SUN","MON","TUE" ,"WED","THU","FRI","SAT"};
	private static final int[] MONTH_DAY = {30, 31};
	private static final int MONTH_FEBRUARY = 2;
	private static final int COMMON_YEAR_FEBRUARY_DAYS = 28;
	private static final int LEAP_YEAR_FEBRUARY_DAYS = 29;

	/**
	 * 日检
	 *
	 * @param executeDate 计划执行实际开始日期
	 * @param disableDate 计划执行终止日期
	 * @param cycleInterval 天数间隔
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/3/17 10:02
	 */
	public static boolean executeDay(Long executeDate, Long disableDate, Integer cycleInterval){
		List<Long> list = new ArrayList<>();
		try {
			// 计划实际开始时间
			long l1 = DateUtils.dayOfFirstMinute(executeDate);
			String startDateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, l1);
			log.info("计划开始时间：==============={}", startDateStr);
			// 当前时间
			long l3 = DateUtils.dayOfFirstMinute(System.currentTimeMillis());
			String currentDateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, l3);
			log.info("当前时间：==============={}", currentDateStr);
			if (disableDate != null) {
				// 超过计划结束时间，跳出
				String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, disableDate);
				Date date = DateUtil.parse(dateStr + " 23:59:59", DateUtil.PATTERN_DATETIME);
				if (DateUtil.now().after(date)) {
					return false;
				}
			}
			if(Func.equals(startDateStr, currentDateStr)){
				// 计划实际开始时间=当前时间
				log.info("计划开始时间=当前时间：{}", true);
				list.add(executeDate);
			} else {
				if(cycleInterval != 0){
					cycleInterval = cycleInterval + 1;
					log.info("计划间隔：{}", cycleInterval);
					Integer days = DateUtils.betweenDay(l1, l3);
					log.info("相差天数：{}", days);
					if(days % cycleInterval == 0){
						list.add(l3);
					}
				} else {
					// 间隔为0
					list.add(l3);
				}
				log.info("日检生成工单日期：==============={}", DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, l3));
			}
		} catch (ServiceException e) {
			log.error("日检异常：{}", e.getMessage());
		}
		if(Func.isNotEmpty(list)){
			return true;
		}
		return false;
	}

	/**
	 * 周检
	 *
	 * @param executeDate 计划执行开始日期
	 * @param disableDate 计划执行终止日期
	 * @param cycleInterval 间隔
	 * @param cycleExecuteDate 选择的每周几
	 * @return java.util.List<java.lang.Long>
	 * <AUTHOR>
	 * @date 2025/3/17 10:02
	 */
	public static boolean executeWeek(Long executeDate,Long disableDate,Integer cycleInterval,String cycleExecuteDate){
		List<Long> list = new ArrayList<>();
		try {
			// 超过计划执行结束时间，跳出
			String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, disableDate);
			Date date = DateUtil.parse(dateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			if (DateUtil.now().after(date)) {
				//return list;
				return false;
			}
		} catch (ServiceException e) {
			log.error("周检异常：{}", e.getMessage());
		}
		// 计划选择的周几
		int cycleWeekDay = 0;
		for (int i = 0; i < WEEK.length; i++) {
			if (WEEK[i].equals(cycleExecuteDate)){
				cycleWeekDay = i + 1;
				break;
			}
		}
		Calendar cd = Calendar.getInstance();
		// 获得今天是星期几，星期日是第一天，星期一是第二天......
		int todayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		// 获得开始执行日期是星期几，星期日是第一天，星期一是第二天......
		cd.setTime(new Date(executeDate));
		int executeDayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		String timeStr1 = DateUtil.format(new Date(executeDate), DateUtil.PATTERN_DATE);
		String timeStr2 = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
		if(Func.equals(timeStr1, timeStr2) && Func.equals(todayOfWeek, cycleWeekDay)){
			//list.add(cd.getTime().getTime());
			//return list;
			return true;
		}
		// 判断执行日期以后得日期
		if(cycleInterval == 0){
			if(Func.equals(todayOfWeek, cycleWeekDay)){
				//list.add(cd.getTime().getTime());
				//return list;
				return true;
			}
		} else {
			Calendar executeCalendar = Calendar.getInstance();
			executeCalendar.setTime(new Date(executeDate));
			// 计算距离下周一还有几天，8 表示从周日开始计数
			int daysUntilNextMonday = 8 - executeDayOfWeek;
			if (daysUntilNextMonday >= 7){
				daysUntilNextMonday = daysUntilNextMonday - 7;
			}
			// 如果当前日期已经是周一，则下周一为7天后
			if (executeDayOfWeek == Calendar.MONDAY) {
				daysUntilNextMonday = 7*cycleInterval*2;
			}
			// 创建一个新的Calendar实例并加上天数
			Calendar nextMonday = (Calendar) executeCalendar.clone();
			nextMonday.add(Calendar.DAY_OF_MONTH, daysUntilNextMonday);
			// 确保下周一是一周的第一天
			while (nextMonday.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
				nextMonday.add(Calendar.DAY_OF_MONTH, cycleInterval*2);
			}
			long time1 = nextMonday.getTime().getTime();
			long time2 = DateUtil.now().getTime();
			if(time2 >= time1 && Func.equals(todayOfWeek, cycleWeekDay)){
				//list.add(cd.getTime().getTime());
				//return list;
				return true;
			}
		}
		return false;
	}

	/**
	 * 月检
	 *
	 * @param firstExecuteDate 计划第一次生成工单的日期 2025-03-05
	 * @param planEndDate 计划终止日期 2025-12-31
	 * @param cycleInterval 间隔
	 * @param cycleExecuteDate 选择的每月几号 1,2
	 * @return
	 */
	public static boolean executeMonth(Long firstExecuteDate, Long planEndDate, Integer cycleInterval, String cycleExecuteDate){
		boolean isExecuted = false;
		List<Long> list = new ArrayList<>();
		int nowYear = DateUtils.nowYear();
		int nowMonth = DateUtils.nowMonth();
		try {
			// 超过计划执行结束时间，跳出
			String planEndDateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, planEndDate);
			Date planEndDateTime = DateUtil.parse(planEndDateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			if (DateUtil.now().after(planEndDateTime)) {
				return isExecuted;
			}
			// 获得今天是本月的多少号
			int today = DateUtils.nowDayOfMonth();
			int planExecuteDay = Integer.valueOf(cycleExecuteDate);
			// 今天不是计划里的选择的日期，则返回
			boolean isOK = false;
			// 今天不是计划里的选择的日期，则返回
			if(!Func.equals(planExecuteDay, today)){
				if(MONTH_FEBRUARY != nowMonth){
					// 当前不是2月，则直接返回
					return false;
				} else {
					// 当前是2月
					// 当天是28号或29号
					if(COMMON_YEAR_FEBRUARY_DAYS == today || LEAP_YEAR_FEBRUARY_DAYS == today){
						// 如果选择的是30号或者31号，则在28号或者29号生成工单
						if(Arrays.stream(MONTH_DAY).anyMatch(i -> i == planExecuteDay)){
							isOK = true;
						}
					}
					if(COMMON_YEAR_FEBRUARY_DAYS == today){
						// 如果选择的是29号，则需要判断当前2月有几天，如果只有28天（平年），则在28号生成工单
						if(LEAP_YEAR_FEBRUARY_DAYS == planExecuteDay && !DateUtils.isLeapYear(nowYear)){
							isOK = true;
						}
					}
				}
			} else {
				isOK = true;
			}
			if(!isOK){
				return false;
			}
			// 计划开始月份
			int planStartMonth;
			if(firstExecuteDate == null){
//				list.add(DateUtil.now().getTime());
				return true;
			}
			// 计划第一次生成工单的月份
			planStartMonth = DateUtils.getMonthOfYear(firstExecuteDate);
			// 当前月份
			// 月份差
			int monthSpace = nowMonth - planStartMonth;
			if (monthSpace == 0){
				//list.add(calendar.getTime().getTime());
				return true;
			}
			// 跨年
			if(monthSpace < 0){
				monthSpace = monthSpace + 12;
			}
			// 执行间隔
			if(cycleInterval != 0){
				if(monthSpace % cycleInterval == 0){
					list.add(DateUtil.now().getTime());
				}
			} else {
				// 间隔为0
				list.add(DateUtil.now().getTime());
			}
		} catch (ServiceException e) {
			log.error("月检异常：{}", e.getMessage());
		}
		if(Func.isNotEmpty(list) && list.size() > 0){
			isExecuted = true;
		}
		return isExecuted;
	}

    /**
     * 日检
	 * @param startTime 计划执行开始日期
	 * @param disableTime 计划执行终止日期
     * @param cycleInterval 天数间隔
     * @return
     */
    public static boolean matchDay(Long startTime, Long disableTime, Integer cycleInterval){
		// 计划开始时间
        long l1 = DateUtils.dayOfFirstMinute(startTime);
		// 当前时间
        long l3 = DateUtils.dayOfFirstMinute(DateUtil.now().getTime());
        if (disableTime != null) {
			try {
				// 超过计划结束时间，跳出
				String dateStr = DateUtils.timestampToDateTime("yyyy-MM-dd", disableTime);
				Date date = DateUtil.parse(dateStr + " 23:59:59", DateUtil.PATTERN_DATETIME);
				if (DateUtil.now().after(date)) {
					return false;
				}
			} catch (ServiceException e) {
				e.printStackTrace();
			}
		}
		if (l1 == l3) {
			// 计划开始时间=当前时间
			return true;
		}else {
			Integer days = DateUtils.betweenDay(l1, l3);
			if(days % (cycleInterval + 1) == 0){
				return true;
			}
		}
        return false;
    }

	/**
	 * 周检
	 * @param executeDate 计划执行开始日期
	 * @param disableDate 计划执行终止日期
	 * @param cycleInterval 间隔
	 * @param cycleExecuteDate 选择的每周几
	 * @return
	 */
	public static boolean week(Long executeDate,Long disableDate,Integer cycleInterval,String cycleExecuteDate){
		try {
			// 超过计划执行结束时间，跳出
			String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, disableDate);
			Date date = DateUtil.parse(dateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			if (DateUtil.now().after(date)) {
				return false;
			}
		} catch (ServiceException e) {
			e.printStackTrace();
		}
		// 计划选择的周几
		int cycleWeekDay = 0;
		for (int i = 0; i < WEEK.length; i++) {
			if (WEEK[i].equals(cycleExecuteDate)){
				cycleWeekDay = i + 1;
				break;
			}
		}
		Calendar cd = Calendar.getInstance();
		// 获得今天是星期几，星期日是第一天，星期一是第二天......
		int todayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		// 获得开始执行日期是星期几，星期日是第一天，星期一是第二天......
		cd.setTime(new Date(executeDate));
		int executeDayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		String timeStr1 = DateUtil.format(new Date(executeDate), DateUtil.PATTERN_DATE);
		String timeStr2 = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
		executeDate = DateUtils.dayOfFirstMinute(executeDate);
		// 第一次生成排班（一周内不考虑间隔）
		if(Func.equals(timeStr1, timeStr2) && Func.equals(todayOfWeek, cycleWeekDay)){
			return true;
		}
		// 判断执行日期以后得日期
		if(cycleInterval == 0){
			if(Func.equals(todayOfWeek, cycleWeekDay)){
				return true;
			}
		} else {
			Calendar executeCalendar = Calendar.getInstance();
			executeCalendar.setTime(new Date(executeDate));

			// 计算距离下周一还有几天，8 表示从周日开始计数
			int daysUntilNextMonday = 8 - executeDayOfWeek;
			// 如果当前日期已经是周一，则下周一为7天后
			if (executeDayOfWeek == Calendar.MONDAY) {
				daysUntilNextMonday = 7*cycleInterval*2;
			}
			// 创建一个新的Calendar实例并加上天数
			Calendar nextMonday = (Calendar) executeCalendar.clone();
			nextMonday.add(Calendar.DAY_OF_MONTH, daysUntilNextMonday);
			// 确保下周一是一周的第一天
			while (nextMonday.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
				nextMonday.add(Calendar.DAY_OF_MONTH, cycleInterval*2);
			}
			long time1 = nextMonday.getTime().getTime();
			long time2 = DateUtil.now().getTime();
			if(time2 >= time1 && Func.equals(todayOfWeek, cycleWeekDay)){
				return true;
			}
		}
		return false;
	}

	/**
	 * 当天是否存在匹配的周检任务
	 * 周检逻辑：计划开始时间所在周的周几在指定时间段内之前，则当周执行第一次，否则下一周执行第一次
	 * @param startDate 计划开始时间
	 * @param endDate 计划结束时间
	 * @param interval 周期间隔
	 * @param cycleExecuteDate 指定工作时间
	 * @return true：存在 false：不存在
	 */
	public static boolean matchWeek(Long startDate, Long endDate, Integer interval,String cycleExecuteDate) {
		try {
			// 超过计划执行结束时间，跳出
			String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, endDate);
			Date date = DateUtil.parse(dateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			if (DateUtil.now().after(date)) {
				return false;
			}
		} catch (ServiceException e) {
			e.printStackTrace();
		}
		// 计算开始时间
		Calendar cd = Calendar.getInstance();
		// 获得今天是星期几，星期日是第一天，星期一是第二天......
		int todayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		// 获得开始执行日期是星期几，星期日是第一天，星期一是第二天......
		cd.setTime(new Date(startDate));
		// 计算计划开始时间是周几
		int startDayOfWeek = cd.get(Calendar.DAY_OF_WEEK);
		// 结束时间戳
		long endDayTime = DateUtils.dayOfFirstMinute(endDate);
		// 当前时间
		long nowDayTime = DateUtils.dayOfFirstMinute(System.currentTimeMillis());

		// 原始数组
		List<String> originalWeekList = Func.toStrList(cycleExecuteDate);
		List<Integer> originalDayWeekList = new ArrayList<>();
		for (int i = 0; i < originalWeekList.size(); i++) {
			originalDayWeekList.add(getWeekDay(originalWeekList.get(i)));
		}
		// 给originalDayWeekList排序，升序
		Collections.sort(originalDayWeekList);
		System.out.println("原始数组：" + originalWeekList);

		// 一周选择多天
		if (originalWeekList.size() > 1){
			// 获取第一个执行日期（默认下周执行）
			int first_cycle = 0;
			// 计划开始时间所在周执行首次排班
			if ((originalDayWeekList.get(0) == 1 && startDayOfWeek <= originalDayWeekList.get(1)) || startDayOfWeek <= originalDayWeekList.get(0)){
				// 判断当前时间是不是和计划开始时间是在同一周
				// 周天时间
				Calendar sundayCalendar = (Calendar) cd.clone();
				sundayCalendar.add(Calendar.DAY_OF_MONTH, 8 - startDayOfWeek);
				// 周日时间戳
				long sundayTime = DateUtils.dayOfFirstMinute(sundayCalendar.getTime().getTime());
				// 执行第一次
				if (nowDayTime <= sundayTime && originalDayWeekList.contains(todayOfWeek)){
					return true;
				}
				if (interval > 0){
					first_cycle = 7*(interval + 1);
				}
			}
			// 下周一和计划执行日期的天数差
			int nextMondayDaysBetween = 9 - startDayOfWeek;
			if (nextMondayDaysBetween >= 7){
				nextMondayDaysBetween = nextMondayDaysBetween - 7;
			}
			cd.add(Calendar.DAY_OF_MONTH, nextMondayDaysBetween);
			// 下周一时间戳
			long nextMondayTime = cd.getTime().getTime();
			nextMondayTime = DateUtils.dayOfFirstMinute(nextMondayTime);
			// 判断是否超过结束时间
			if (nextMondayTime > nowDayTime){
				return false;
			}
			// 时间差（天）
			int daysBetween = (int)((endDayTime - nextMondayTime) / (1000 * 60 * 60 * 24));
			Calendar nextCalendar = Calendar.getInstance();
			nextCalendar.setTime(new Date(nextMondayTime));
			for (int i = first_cycle; i <= daysBetween; i = i + 7*(interval + 1)) {
				nextCalendar.add(Calendar.DAY_OF_MONTH, i);
				nextMondayTime = DateUtils.dayOfFirstMinute(nextCalendar.getTime().getTime());
				if (nextMondayTime > nowDayTime){
					return false;
				}
				int betweenTime = (int)((nowDayTime - nextMondayTime) / (1000 * 60 * 60 * 24));
				if (betweenTime <= 7 && originalDayWeekList.contains(todayOfWeek)){
					return true;
				}
			}
		}else {
			// 每周只有一天执行
			return week(startDate, endDate, interval, cycleExecuteDate);
		}
		return false;
	}

	/**
	 * 获取星期几
	 * @param day
	 * @return
	 */
	private static int getWeekDay(String day) {
		if (!Arrays.asList(WEEK).contains(day)){
			throw new RuntimeException("日期参数有误");
		}
		// 计划选择的周几
		int cycleWeekDay = 0;
		for (int i = 0; i < WEEK.length; i++) {
			if (WEEK[i].equals(day)){
				cycleWeekDay = i + 1;
				break;
			}
		}
		return cycleWeekDay;
	}

	public static void main(String[] args) {
//		String text1 = "2024-11-30";
//		Temporal temporal1 = LocalDate.parse(text1);
//		String text2 = "2025-01-15";
//		Temporal temporal2 = LocalDate.parse(text2);
//		// 方法返回为相差月份
//		long l = ChronoUnit.MONTHS.between(temporal1, temporal2);
//		System.out.println(l);
		String start = "2025-03-29";
		String end = "2025-04-01";
		int interval = 1;
		String cycleExecuteDate = "26,27,28";

		long l1 = DateUtils.dayOfFirstMinute(DateUtil.parse(start, DateUtil.PATTERN_DATE).getTime());
		log.info("计划开始时间：==============={}", l1);
		// 当前时间
		long l3 = DateUtils.dayOfFirstMinute(DateUtil.parse(end, DateUtil.PATTERN_DATE).getTime());
		log.info("当前时间：==============={}", l3);
		Integer days = DateUtils.betweenDay(l1, l3);
		log.info("间隔天数：==============={}", days);
		boolean flag = matchMonth(DateUtil.parse(start, DateUtil.PATTERN_DATE).getTime(), DateUtil.parse(end, DateUtil.PATTERN_DATE).getTime(), interval, cycleExecuteDate);
		System.out.println(flag);
//		Long l1 = DateUtil.parse("2024-10-25", DateUtil.PATTERN_DATE).getTime();
//		Long l3 = DateUtil.parse("2024-10-28", DateUtil.PATTERN_DATE).getTime();
//		Integer days = DateUtils.betweenDay(l1, l3);
//		System.out.println(days);
//
//		Integer interval = 1;
//		// 获取当前日期的Calendar实例
//		Calendar today = Calendar.getInstance();
//		today.setTime(DateUtil.parse("2024-09-19", DateUtil.PATTERN_DATE));
//
//		// 计算当前日期间隔一周后的日期
//		Calendar oneWeekLater = (Calendar) today.clone();
//		oneWeekLater.add(Calendar.WEEK_OF_YEAR, 2);
//
//		// 获取间隔一周后日期的周一
//		Calendar nextWeekMonday = (Calendar) oneWeekLater.clone();
//		nextWeekMonday.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//
//		// 输出结果
//		System.out.println("当前日期间隔一周后的周一是: " + nextWeekMonday.getTime());
//
//		LocalDate date1 = LocalDate.now();
//		LocalDate date2 = LocalDate.of(2024,10,3);
//		long weeksBetween = ChronoUnit.WEEKS.between(date1, date2);
//		// 输出结果
//		System.out.println("两个日期之间相隔 " + weeksBetween + " 周");
//
//
//		Calendar currentDate = Calendar.getInstance();
//		currentDate.setTime(DateUtil.parse("2024-09-16", DateUtil.PATTERN_DATE));
//		// 确定当前日期是一周中的第几天
//		int currentDayOfWeek = currentDate.get(Calendar.DAY_OF_WEEK);
//
//		// 计算距离下周一还有几天，8 表示从周日开始计数
//		int daysUntilNextMonday = 8 - currentDayOfWeek;
//
//		// 如果当前日期已经是周一，则下周一为7天后
//		if (currentDayOfWeek == Calendar.MONDAY) {
//			daysUntilNextMonday = 7*interval*2;
//		}
//
//		// 创建一个新的Calendar实例并加上天数
//		Calendar nextMonday = (Calendar) currentDate.clone();
//		nextMonday.add(Calendar.DAY_OF_MONTH, daysUntilNextMonday);
//
//		// 确保下周一是一周的第一天
//		while (nextMonday.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
//			nextMonday.add(Calendar.DAY_OF_MONTH, interval*2);
//		}
//
//		// 输出结果
//		System.out.println("当前日期的下周一: " + nextMonday.getTime());

	}

    /**
     * 月检
     * @param executeDate 计划执行开始日期 2024-09-19
     * @param disableDate 计划执行终止日期 2024-10-24
     * @param cycleInterval 间隔
     * @param cycleExecuteTimeStr 选择的每月几号 1,2
     * @return
     */
    public static boolean matchMonth(Long executeDate, Long disableDate, Integer cycleInterval, String cycleExecuteTimeStr){
		List<String> list = null;
		Date endDate = null;
		Date startDate = null;
		try {
			list = Func.toStrList(cycleExecuteTimeStr);
			// 超过计划执行结束时间，跳出
			String dateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, disableDate);
			endDate = DateUtil.parse(dateStr + DateUtils.DAY_END_TIME, DateUtil.PATTERN_DATETIME);
			// 未到计划执行时间，跳出
			String startDateStr = DateUtils.timestampToDateTime(DateUtil.PATTERN_DATE, executeDate);
			startDate = DateUtil.parse(startDateStr + DateUtils.DAY_START_TIME, DateUtil.PATTERN_DATETIME);
		} catch (ServiceException e) {
			throw new RuntimeException(e);
		}
		for (String cycleExecuteDate : list){
			if (DateUtil.now().after(endDate) || DateUtil.now().before(startDate)) {
				continue;
			}
			// 获得今天是本月的多少号
			int today = DateUtils.nowDayOfMonth();
			int planExecuteDay = Integer.valueOf(cycleExecuteDate).intValue();
			// 不是计划里的选择的月的几号，则返回
			if(!Func.equals(planExecuteDay, today)){
				continue;
			}
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new Date(executeDate));
			// 月份差
			int monthSpace = DateUtils.betweenMonth(executeDate, new Date().getTime());
			if (monthSpace == 0){
				return true;
			}
			calendar.set(Calendar.DAY_OF_MONTH, Integer.valueOf(cycleExecuteDate).intValue());
			// 排期
			for (int i = cycleInterval; i < monthSpace; i = i + cycleInterval + 1) {
				calendar.add(Calendar.MONTH,cycleInterval + 1);
				String timeStr1 = DateUtil.format(calendar.getTime(), DateUtil.PATTERN_DATE);
				String timeStr2 = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
				if(Func.equals(timeStr1, timeStr2)){
					return true;
				}
			}
		}
        return false;
    }

    /**
     * 月份间隔
     * @param time1
     * @param time2
     * @return
     */
    private static int getMonthSpace(long time1,long time2){
        Calendar cal1 = new GregorianCalendar();
        cal1.setTime(new Date(time1));
        Calendar cal2 = new GregorianCalendar();
        cal2.setTime(new Date(time2));
        int result =(cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR)) * 12 + cal1.get(Calendar.MONTH)- cal2.get(Calendar.MONTH);
        System.out.println(result);
        return Math.abs(result);
    }


}
