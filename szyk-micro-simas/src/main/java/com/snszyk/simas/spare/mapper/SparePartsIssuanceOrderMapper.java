/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsIssuanceOrder;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件领用单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface SparePartsIssuanceOrderMapper extends BaseMapper<SparePartsIssuanceOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsIssuanceOrder
	 * @return
	 */
	List<SparePartsIssuanceOrderVO> selectSparePartsIssuanceOrderPage(IPage page, SparePartsIssuanceOrderVO sparePartsIssuanceOrder);

}
