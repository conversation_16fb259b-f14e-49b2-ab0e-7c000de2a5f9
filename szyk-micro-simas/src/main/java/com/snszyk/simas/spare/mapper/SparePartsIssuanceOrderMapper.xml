<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsIssuanceOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsIssuanceOrderResultMap" type="com.snszyk.simas.spare.entity.SparePartsIssuanceOrder">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="receive_dept_id" property="receiveDeptId"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="remark" property="remark"/>
        <result column="audit_user" property="auditUser"/>
        <result column="audit_time" property="auditTime"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="total" property="total"/>
    </resultMap>


    <select id="selectSparePartsIssuanceOrderPage" resultMap="sparePartsIssuanceOrderResultMap">
        select * from simas_spare_parts_issuance_order where is_deleted = 0
    </select>

</mapper>
