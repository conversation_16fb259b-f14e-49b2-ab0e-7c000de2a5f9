/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsIssuanceItemDTO;
import com.snszyk.simas.spare.entity.SparePartsIssuanceItem;
import com.snszyk.simas.spare.mapper.SparePartsIssuanceItemMapper;
import com.snszyk.simas.spare.service.ISparePartsIssuanceItemService;
import com.snszyk.simas.spare.vo.SparePartsIssuanceItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件领用单明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@AllArgsConstructor
@Service
public class SparePartsIssuanceItemServiceImpl extends BaseCrudServiceImpl<SparePartsIssuanceItemMapper, SparePartsIssuanceItem, SparePartsIssuanceItemDTO, SparePartsIssuanceItemVO> implements ISparePartsIssuanceItemService {


	@Override
	public Boolean deleteByIssuanceOrderId(Long issuanceOrderId) {
		return this.lambdaUpdate()
			.eq(SparePartsIssuanceItem::getIssuanceOrderId, issuanceOrderId)
			.remove();
	}

	@Override
	public Boolean saveBatch(List<SparePartsIssuanceItemVO> itemList) {
		return super.saveBatch(BeanUtil.copy(itemList, SparePartsIssuanceItem.class));
	}

	@Override
	public List<SparePartsIssuanceItemDTO> listByIssuanceOrderId(Long issuanceOrderId) {
		final List<SparePartsIssuanceItem> list = this.lambdaQuery()
			.eq(SparePartsIssuanceItem::getIssuanceOrderId, issuanceOrderId)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsIssuanceItemDTO.class);
	}
}
