/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.dto.SparePartsIssuanceItemDTO;
import com.snszyk.simas.spare.service.ISparePartsIssuanceItemService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsIssuanceItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 备品备件领用单明细 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@AllArgsConstructor
@Service
public class SparePartsIssuanceItemLogicService extends BaseCrudLogicService<SparePartsIssuanceItemDTO, SparePartsIssuanceItemVO> {

	private final ISparePartsIssuanceItemService sparePartsIssuanceItemService;
	private final SparePartsDictLogicService sparePartsDictLogicService;
	private final ISparePartsWarehouseService sparePartsWarehouseService;
	private final SparePartsStockLogicService stockLogicService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsIssuanceItemService;
	}

	/**
	 * 批量保存
	 *
	 * @param orderId  领用单id
	 * @param itemList
	 * @return
	 */
	public Boolean saveBatch(Long orderId, List<SparePartsIssuanceItemVO> itemList) {
		if (ObjectUtil.isEmpty(itemList) || ObjectUtil.isEmpty(orderId)) {
			throw new BusinessException("保存备品备件领用单明细失败！");
		}
		itemList.forEach(item -> item.setIssuanceOrderId(orderId));

		return sparePartsIssuanceItemService.saveBatch(itemList);

	}

	/**
	 * 查询领用单明细
	 *
	 * @param issuanceOrderId
	 * @return
	 */
	public List<SparePartsIssuanceItemDTO> listByIssuanceOrderId(Long issuanceOrderId) {
		// 根据领用单id查询明细
		List<SparePartsIssuanceItemDTO> itemDTOList = sparePartsIssuanceItemService.listByIssuanceOrderId(issuanceOrderId);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return Collections.emptyList();
		}
		// 获取备件所有仓库id
		final List<Long> warehouseIds = ListUtil.distinctMap(itemDTOList, SparePartsIssuanceItemDTO::getWarehouseId);
		// 获取仓库map
		Map<Long, String> warehouseMap = sparePartsWarehouseService.getwarehouseMap(warehouseIds);
		// 获取备品备件字典ids
		final List<Long> dictIds = ListUtil.distinctMap(itemDTOList, SparePartsIssuanceItemDTO::getDictId);
		// 查询备品备件字典
		Map<Long, SparePartsDictDTO> idToDTOMap = sparePartsDictLogicService.getDictMap(dictIds);
		// 查询库存Map,key-库存id，value-当前库存数量
		Map<Long, BigDecimal> idToCurrentQuantityMap = stockLogicService.getStockIdToCurrentQuantityMap(ListUtil.map(itemDTOList, SparePartsIssuanceItemDTO::getStockId));
		itemDTOList.forEach(itemDTO -> {
			// 封装字典信息
			Optional.ofNullable(idToDTOMap.get(itemDTO.getDictId()))
				.ifPresent(dictDTO -> SparePartsDictBeanMapper.INSTANCE.setSparePartsIssuanceItemDTO(itemDTO, dictDTO));
			//  设置仓库名称
			Optional.ofNullable(warehouseMap.get(itemDTO.getWarehouseId()))
				.ifPresent(warehouseName -> itemDTO.setWarehouseName(warehouseName));
			// 当前库存数量
			Optional.ofNullable(idToCurrentQuantityMap.get(itemDTO.getStockId()))
				.ifPresent(currentQuantity -> itemDTO.setCurrentQuantity(currentQuantity));
		});
		return itemDTOList;
	}
}
