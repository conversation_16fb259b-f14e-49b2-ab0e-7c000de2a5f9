/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsOutboundOrder;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件出库单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface SparePartsOutboundOrderMapper extends BaseMapper<SparePartsOutboundOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsOutboundOrder
	 * @return
	 */
	List<SparePartsOutboundOrderVO> selectSparePartsOutboundOrderPage(IPage page, SparePartsOutboundOrderVO sparePartsOutboundOrder);

}
