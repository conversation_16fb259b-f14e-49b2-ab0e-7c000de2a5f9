/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.vo.SparePartsStockPageVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 备品备件库存 服务类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISparePartsStockService extends IBaseCrudService<SparePartsStockDTO, SparePartsStockVO> {

	List<SparePartsStockDTO> listByWarehouseIds(Collection<? extends Serializable> warehouseId);

	List<SparePartsStockDTO> listBy(Long warehouseId, Collection<? extends Serializable> dictIds);

	List<SparePartsStockDTO> saveOrUpdateBatch(List<SparePartsStockVO> voList);

	IPage<SparePartsStockDTO> pageList(SparePartsStockPageVO v);

	List<SparePartsStockDTO> listByIds(List<Long> ids);

	List<SparePartsStockDTO> updateBatchByIds(List<SparePartsStockVO> voList);
}
