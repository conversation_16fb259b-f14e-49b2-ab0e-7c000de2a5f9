/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.vo.SparePartsDictVO;

import java.util.List;

/**
 * 备品备件字典 服务类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface ISparePartsDictService extends IBaseCrudService<SparePartsDictDTO, SparePartsDictVO> {


	@Override
	SparePartsDictDTO save(SparePartsDictVO sparePartsDictVO);

	/**
	 * 根据ids删除
	 *
	 * @param ids
	 * @return
	 */
	Boolean removeByIds(List<Long> ids);

	/**
	 * 根据ids查询
	 *
	 * @param ids
	 * @return
	 */

	List<SparePartsDictDTO> listByIds(List<Long> ids);
}
