package com.snszyk.simas.spare.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.dto.SparePartsWarehouseDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.mapper.SparePartsWarehouseMapper;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsWarehouseVO;
import com.snszyk.simas.spare.wrapper.SparePartsWarehouseWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Service
public class SparePartsWarehouseServiceImpl extends BaseServiceImpl<SparePartsWarehouseMapper, SparePartsWarehouse> implements ISparePartsWarehouseService {
	private final ISparePartsStockService stockService;

	@Override
	public IPage<SparePartsWarehouseDTO> page(IPage<SparePartsWarehouseDTO> page, String keywords) {
		List<SparePartsWarehouse> list = this.baseMapper.page(page, keywords);
		return page.setRecords(SparePartsWarehouseWrapper.build().listDTO(list));
	}

	@Override
	public boolean submit(SparePartsWarehouseVO vo) {
		int total = count(Wrappers.<SparePartsWarehouse>query().lambda().eq(SparePartsWarehouse::getName, vo.getName())
			.ne(Func.isNotEmpty(vo.getId()), SparePartsWarehouse::getId, vo.getId()));
		if (total > 0) {
			throw new ServiceException("仓库已存在");
		}
		SparePartsWarehouse entity = SparePartsWarehouseWrapper.build().entity(vo);
		if (StringUtils.isEmpty(entity.getId())) {
			entity.setNo(BizCodeUtil.generate("SPW"));
		}
		return this.saveOrUpdate(entity);
	}

	@Override
	public boolean remove(Long id) {

		final List<SparePartsStockDTO> stockDTOList = stockService.listBy(id, null);
		if (ObjectUtil.isNotEmpty(stockDTOList)) {
			throw new ServiceException("该仓库下存在备品备件，无法删除");
		}
		return removeById(id);
	}

	@Override
	public boolean lock(List<Long> ids) {
		return this.update(Wrappers.<SparePartsWarehouse>update().lambda().set(SparePartsWarehouse::getLockStock, 1).in(SparePartsWarehouse::getId, ids));
	}

	@Override
	public boolean unlock(List<Long> ids) {
		return this.update(Wrappers.<SparePartsWarehouse>update().lambda().set(SparePartsWarehouse::getLockStock, 0).in(SparePartsWarehouse::getId, ids));
	}

	@Override
	public Map<Long, String> getwarehouseMap(List<Long> ids) {
		if (ObjectUtil.isEmpty(ids)) {
			return MapUtil.empty();
		}
		final List<SparePartsWarehouse> list = super.listByIds(ids);
		if (ObjectUtil.isEmpty(list)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(list, SparePartsWarehouse::getId, SparePartsWarehouse::getName);
	}

	@Override
	public List<SparePartsWarehouse> listBy(Collection<? extends Serializable> ids, Integer LockStatus) {
		return this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(ids), SparePartsWarehouse::getId, ids)
			.eq(ObjectUtil.isNotEmpty(LockStatus), SparePartsWarehouse::getLockStock, LockStatus)
			.list();
	}
}
