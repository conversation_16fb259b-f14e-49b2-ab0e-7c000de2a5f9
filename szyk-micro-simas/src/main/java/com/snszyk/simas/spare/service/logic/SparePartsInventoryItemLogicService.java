/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.LockStockEnum;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryItemBeanMapper;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryOrderBeanMapper;
import com.snszyk.simas.spare.config.DataFillConfig;
import com.snszyk.simas.spare.constants.InventoryConstants;
import com.snszyk.simas.spare.dto.*;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.enums.SpareInventoryOrderStatusEnum;
import com.snszyk.simas.spare.enums.SpareInventoryPlanStatusEnum;
import com.snszyk.simas.spare.mapper.SparePartsInventoryPlanMapper;
import com.snszyk.simas.spare.mapper.SparePartsWarehouseMapper;
import com.snszyk.simas.spare.service.ISparePartsInventoryItemService;
import com.snszyk.simas.spare.service.ISparePartsInventoryOrderService;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.validation.ExistingItemValidationGroup;
import com.snszyk.simas.spare.validation.NewItemValidationGroup;
import com.snszyk.simas.spare.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 备品备件盘点记录表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@AllArgsConstructor
@Service
public class SparePartsInventoryItemLogicService extends BaseCrudLogicService<SparePartsInventoryItemDTO, SparePartsInventoryItemVO> {

	private final ISparePartsInventoryItemService itemService;
	private final SparePartsDictLogicService dictLogicService;
	private final SparePartsInventoryPlanMapper planMapper;
	private final SparePartsStockLogicService stockLogicService;
	private final ISparePartsInventoryOrderService orderService;
	private final SparePartsWarehouseMapper warehouseMapper;
	private final Validator validator;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.itemService;
	}

	/**
	 * 分页查询
	 * 优化：简化逻辑，使用外部配置类，提高查询性能
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsInventoryItemDTO> page(SparePartsInventoryItemPageVO v) {
		if (v == null) {
			return new Page<>(InventoryConstants.QueryConstants.DEFAULT_CURRENT_PAGE,
				InventoryConstants.QueryConstants.DEFAULT_PAGE_SIZE);
		}

		IPage<SparePartsInventoryItemDTO> page = itemService.pageWithConditions(v);

		if (page != null && !ObjectUtil.isEmpty(page.getRecords())) {
			// 对列表查询使用优化的数据填充配置
			this.populateSparePartsInventoryItemData(page.getRecords(), DataFillConfig.forListQuery());
			return page;
		}

		return new Page<>(v.getCurrent(), v.getSize());
	}

	/**
	 * 获取库房名称Map
	 *
	 * @param warehouseIds
	 * @return
	 */
	private Map<Long, String> getWarehouseIdToNameMap(List<Long> warehouseIds) {
		if (ObjectUtil.isEmpty(warehouseIds)) {
			return MapUtil.empty();
		}
		final List<SparePartsWarehouse> warehouseList = warehouseMapper.selectBatchIds(warehouseIds);
		if (ObjectUtil.isEmpty(warehouseList)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(warehouseList, SparePartsWarehouse::getId, SparePartsWarehouse::getName);

	}


	/**
	 * 统计
	 * 优化：修复方法调用错误，移除不必要的数据填充，提升性能
	 *
	 * @param webV 查询条件
	 * @return 统计结果
	 */
	@Transactional(readOnly = true)
	public SparePartsInventoryItemStatisticsDTO getStatistics(SparePartsInventoryItemPageVO webV) {
		if (webV == null) {
			return createEmptyStatistics();
		}

		// 设置查询所有记录，用于统计
		webV.setSize(InventoryConstants.QueryConstants.QUERY_ALL_SIZE);

		// 修复：使用正确的方法和参数类型
		IPage<SparePartsInventoryItemDTO> page = itemService.pageWithConditions(webV);

		if (page == null || ObjectUtil.isEmpty(page.getRecords())) {
			return createEmptyStatistics();
		}

		// 优化：统计场景不需要任何数据填充，直接计算统计结果
		return calculateStatistics(page.getRecords());
	}

	/**
	 * 创建空的统计结果
	 * 优化：直接创建，避免不必要的方法调用
	 */
	private SparePartsInventoryItemStatisticsDTO createEmptyStatistics() {
		return new SparePartsInventoryItemStatisticsDTO().init();
	}

	/**
	 * 计算统计信息
	 * 优化：一次遍历完成所有统计，提高性能
	 */
	private SparePartsInventoryItemStatisticsDTO calculateStatistics(List<SparePartsInventoryItemDTO> itemList) {
		SparePartsInventoryItemStatisticsDTO dto = new SparePartsInventoryItemStatisticsDTO();
		dto.setTotal((long) itemList.size());

		// 使用Stream API按结果分组统计，一次遍历完成所有统计
		Map<Integer, Long> resultCounts = itemList.stream()
			.collect(Collectors.groupingBy(SparePartsInventoryItemDTO::getResult, Collectors.counting()));

		// 设置各状态数量，使用getOrDefault确保安全性
		dto.setNotStart(resultCounts.getOrDefault(SpareInventoryItemResultEnum.NOT_START.getCode(), 0L));
		dto.setNormal(resultCounts.getOrDefault(SpareInventoryItemResultEnum.NORMAL.getCode(), 0L));
		dto.setProfit(resultCounts.getOrDefault(SpareInventoryItemResultEnum.PROFIT.getCode(), 0L));
		dto.setLoss(resultCounts.getOrDefault(SpareInventoryItemResultEnum.LOSS.getCode(), 0L));

		return dto;
	}

	/**
	 * 保存草稿
	 * 暂存盘点数据，允许用户临时保存盘点进度
	 * <p>
	 * 业务逻辑：
	 * 1. 校验盘点明细数据的完整性和正确性
	 * 2. 删除旧的明细数据，保存新的明细数据
	 * 3. 不更新工单状态（草稿保存是临时操作）
	 * <p>
	 * 优化：移除不必要的工单状态更新，草稿保存不应影响工单状态
	 *
	 * @param voList 盘点明细操作列表
	 * @return 保存结果
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveDraft(List<SparePartsInventoryItemOperationVO> voList) {
		// 注意：草稿保存也需要完整校验，确保数据质量和一致性
		this.validateBatchInventoryItems(voList);

		// 执行通用的数据保存流程
		final List<SparePartsInventoryItemDTO> itemDTOList = this.executeInventoryDataSave(voList);

		return ObjectUtil.isNotEmpty(itemDTOList);
	}

	/**
	 * 执行通用的盘点数据保存流程
	 * 优化：提取公共逻辑，减少代码重复
	 *
	 * @param voList 盘点明细操作列表
	 * @return 保存后的明细列表
	 */
	private List<SparePartsInventoryItemDTO> executeInventoryDataSave(List<SparePartsInventoryItemOperationVO> voList) {
		// 删除盘点明细表
		this.removeItemIfNecessary(voList);

		// 保存或更新盘点明细表
		return this.saveOrUpdateBatchItem(voList);
	}

	/**
	 * 删除盘点明细表
	 * 修复：当工单中没有已生成的明细数据时，不抛出异常，直接返回
	 */
	private void removeItemIfNecessary(List<SparePartsInventoryItemOperationVO> voList) {
		if (ObjectUtil.isEmpty(voList)) {
			throw new BusinessException("盘点明细表数据为空");
		}
		// 前端传来的明细id集合
		final List<Long> webIds = voList.stream()
			.map(SparePartsInventoryItemOperationVO::getId)
			.filter(ObjectUtil::isNotEmpty)
			.collect(Collectors.toList());

		// 修复：如果没有有效的明细ID，说明都是新增明细，无需删除操作
		if (ObjectUtil.isEmpty(webIds)) {
			return;
		}

		// 查询盘点工单id
		final Long orderId = voList.stream()
			.filter(vo -> ObjectUtil.isNotEmpty(vo.getInventoryOrderId()))
			.findFirst()
			.map(SparePartsInventoryItemOperationVO::getInventoryOrderId)
			.orElseThrow(() -> new BusinessException("盘点工单id不可为空！"));

		// 根据盘点工单id查询出已保存的盘点明细表数据
		final List<SparePartsInventoryItemDTO> itemDTOList = itemService.listByOrderIds(Collections.singletonList(orderId));

		// 修复：如果工单中没有已保存的明细数据，说明是首次操作，无需删除
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return;
		}

		// 过滤出待删除的数据
		final List<Long> removeIds = itemDTOList.stream()
			.map(SparePartsInventoryItemDTO::getId)
			.filter(id -> !webIds.contains(id))
			.collect(Collectors.toList());

		if (ObjectUtil.isNotEmpty(removeIds)) {
			itemService.removeByIds(removeIds);
		}
	}


	/**
	 * 批量新增或更新盘点明细表
	 * 优化：重构为更简洁的实现，使用MapStruct进行属性转换
	 *
	 * @param voList
	 * @return
	 */
	private List<SparePartsInventoryItemDTO> saveOrUpdateBatchItem(List<SparePartsInventoryItemOperationVO> voList) {
		if (ObjectUtil.isEmpty(voList)) {
			return Collections.emptyList();
		}

		List<SparePartsInventoryItemDTO> resultList = Lists.newArrayList();

		// 处理已存在的明细（有ID的记录）
		List<SparePartsInventoryItemDTO> existingItems = processExistingItems(voList);
		if (!existingItems.isEmpty()) {
			resultList.addAll(existingItems);
		}

		// 处理新增的明细（无ID的记录）
		List<SparePartsInventoryItemDTO> newItems = processNewItems(voList, existingItems);
		if (!newItems.isEmpty()) {
			resultList.addAll(newItems);
		}

		return resultList;
	}

	/**
	 * 处理已存在的盘点明细
	 * 优化：使用MapStruct进行属性转换，提高性能
	 */
	private List<SparePartsInventoryItemDTO> processExistingItems(List<SparePartsInventoryItemOperationVO> voList) {
		List<SparePartsInventoryItemVO> existingVOs = voList.stream()
			.filter(vo -> vo.getId() != null)
			.map(this::convertToExistingItemVO)
			.collect(Collectors.toList());

		return existingVOs.isEmpty() ? Collections.emptyList() : itemService.saveOrUpdateBatch(existingVOs);
	}

	/**
	 * 转换为已存在明细的VO
	 * 优化：使用MapStruct，避免手动属性复制
	 */
	private SparePartsInventoryItemVO convertToExistingItemVO(SparePartsInventoryItemOperationVO operationVO) {
		SparePartsInventoryItemVO itemVO = SparePartsInventoryItemBeanMapper.INSTANCE.toVO(operationVO);
		itemVO.setBeforeSystemStock(operationVO.getBeforeSystemStock());
		itemVO.setResult(SpareInventoryItemResultEnum.calculateResultCode(
			operationVO.getBeforeSystemStock(),
			operationVO.getAfterCountedStock()
		));
		return itemVO;
	}

	/**
	 * 处理新增的盘点明细
	 * 优化：简化逻辑，提高可读性
	 */
	private List<SparePartsInventoryItemDTO> processNewItems(List<SparePartsInventoryItemOperationVO> voList,
															 List<SparePartsInventoryItemDTO> existingItems) {
		List<SparePartsInventoryItemOperationVO> newItemVOs = voList.stream()
			.filter(vo -> vo.getId() == null)
			.collect(Collectors.toList());

		if (newItemVOs.isEmpty()) {
			return Collections.emptyList();
		}

		SparePartsInventoryItemDTO templateDTO = getTemplateItemDTOSafely(existingItems, newItemVOs);
		List<SparePartsInventoryItemVO> newItemList = convertNewInventoryItems(newItemVOs, templateDTO);

		return newItemList.isEmpty() ? Collections.emptyList() : itemService.saveOrUpdateBatch(newItemList);
	}


	/**
	 * 批量明细盘点提交
	 * 一次性对工单下多个备品备件明细进行盘点提交，支持临时新增备品备件
	 * <p>
	 * 优化后的执行流程：
	 * 1. 校验盘点明细数据的完整性和正确性
	 * 2. 删除工单中已存在但本次未提交的明细数据
	 * 3. 先处理库存操作（新增/更新）并获取所有stockId
	 * 4. 封装完整的明细VO对象（包含正确的stockId）
	 * 5. 一次性保存所有明细数据到数据库
	 * 6. 更新工单状态为已完成
	 * 7. 检查并更新计划状态，解锁仓库
	 * <p>
	 * 性能优化：
	 * - 减少数据库操作次数：从N+3次优化为N+2次
	 * - 消除stockId回填操作：在保存明细时直接包含stockId
	 * - 提升事务效率：减少数据库锁定时间
	 *
	 * @param planId 盘点计划ID
	 * @param voList 盘点明细操作列表，支持已存在明细和临时新增明细
	 * @return 提交结果，true表示成功
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean submitBatchInventory(Long planId, List<SparePartsInventoryItemOperationVO> voList) {
		// 1. 统一的批量校验处理
		final SparePartsInventoryOrderDTO orderDTO = this.validateBatchInventoryItems(voList);

		// 2. 删除旧的明细数据
		this.removeItemIfNecessary(voList);

		// 3. 先处理库存操作获取stockId（优化：提前处理）
		Map<Long, Long> stockIdMap = this.processStockOperationsAndGetStockIdMap(voList, orderDTO.getWarehouseId());

		// 4. 封装完整的明细VO（包含正确的stockId）
		List<SparePartsInventoryItemVO> completeItemVOList = this.buildCompleteInventoryItemVOList(voList, stockIdMap);

		// 5. 一次性保存明细数据（优化：减少数据库操作）
		final List<SparePartsInventoryItemDTO> itemDTOList = itemService.saveOrUpdateBatch(completeItemVOList);

		// 6. 更新工单状态
		submitInventoryUpdateOrder(this.extractAndValidateOrderId(voList, "批量盘点操作失败"));

		// 7. 后续处理：更新计划状态、解锁仓库
		this.updatePlanStatusIfAllOrderFinish(planId);
		this.unlockWarehouseAfterInventory(orderDTO.getWarehouseId());
		return true;
	}

	/**
	 * 单项明细盘点提交
	 * 对单个备品备件明细进行独立盘点提交，支持临时新增和已存在明细的盘点
	 * <p>
	 * 优化后的执行流程：
	 * 1. 校验单项明细数据的完整性和正确性
	 * 2. 先处理库存操作（新增/更新）并获取stockId
	 * 3. 封装完整的明细VO对象（包含正确的stockId）
	 * 4. 一次性保存明细数据到数据库
	 * 5. 检查工单是否完成，如果完成则自动完成工单并解锁仓库
	 * <p>
	 * 支持场景：
	 * - 已存在明细的盘点：更新现有明细记录
	 * - 临时新增明细的盘点：从备品备件字典中动态添加新明细
	 * <p>
	 * 性能优化：
	 * - 消除二次数据库操作：避免先保存明细再更新stockId
	 * - 智能工单完成检查：自动判断是否需要完成工单
	 *
	 * @param vo 单项盘点明细操作对象，包含盘点数据和业务标识
	 * @return 提交结果，true表示成功
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean submitSingleItemInventory(SparePartsInventoryItemOperationVO vo) {
		// 1. 使用统一的单项校验方法（包含JSR-303校验和工单状态校验）
		final SparePartsInventoryOrderDTO orderDTO = this.validateSingleInventoryItem(vo);

		// 2. 获取计划ID用于后续检查
		Long planId = orderDTO.getPlanId();

		// 3. 先处理库存操作获取stockId（优化：提前处理）
		Map<Long, Long> stockIdMap = this.processStockOperationsAndGetStockIdMap(
			Collections.singletonList(vo), orderDTO.getWarehouseId());

		// 4. 封装完整的明细VO（包含正确的stockId）
		List<SparePartsInventoryItemVO> completeItemVOList = this.buildCompleteInventoryItemVOList(
			Collections.singletonList(vo), stockIdMap);

		// 5. 一次性保存明细数据（优化：减少数据库操作）
		final List<SparePartsInventoryItemDTO> itemDTOList = this.handleSingleInventoryItemOptimized(
			vo, orderDTO, completeItemVOList);

		// 6. 检查该工单下是否所有明细都已盘点完成，如果是则自动完成工单并解锁仓库
		this.checkAndUpdateOrderStatusIfAllItemsCompleted(vo.getInventoryOrderId(), planId, orderDTO.getWarehouseId());

		return true;
	}

	/**
	 * 根据ID查询备品备件盘点明细详情
	 * 优化：简化逻辑，使用外部配置类
	 *
	 * @param id 盘点明细ID
	 * @return 盘点明细详情
	 */
	@Transactional(readOnly = true)
	public SparePartsInventoryItemDTO getById(Long id) {
		if (id == null) {
			throw new ServiceException("查询失败！ID不能为空");
		}

		SparePartsInventoryItemDTO dto = itemService.fetchById(id);
		if (dto == null) {
			throw new ServiceException("盘点明细不存在");
		}

		// 对单条记录使用详情查询的数据填充配置，使用Java 8兼容的Arrays.asList
		this.populateSparePartsInventoryItemData(Collections.singletonList(dto), DataFillConfig.forDetailQuery());
		return dto;
	}


	/**
	 * 处理库存更新
	 * 只更新数量发生变化的库存记录
	 *
	 * @param updateItems 需要更新的盘点明细列表
	 */
	private void processStockUpdates(List<SparePartsInventoryItemDTO> updateItems) {
		if (ObjectUtil.isEmpty(updateItems)) {
			return;
		}

		Map<Long, BigDecimal> stockUpdateMap = updateItems.stream()
			.filter(item -> !Objects.equals(item.getAfterCountedStock(), item.getBeforeSystemStock()))
			.collect(Collectors.toMap(
				SparePartsInventoryItemDTO::getStockId,
				SparePartsInventoryItemDTO::getAfterCountedStock,
				(existing, replacement) -> replacement // 处理重复key的情况
			));

		if (!stockUpdateMap.isEmpty()) {
			stockLogicService.updateBatchStock(stockUpdateMap);
		}
	}

	/**
	 * 处理库存新增，返回创建的库存记录
	 * 优化：返回新创建的库存记录，用于获取stockId
	 *
	 * @param createItems 需要新增的盘点明细列表
	 * @return 新创建的库存记录列表
	 */
	private List<SparePartsStockDTO> processStockCreationWithReturn(List<SparePartsInventoryItemDTO> createItems) {
		if (ObjectUtil.isEmpty(createItems)) {
			return Collections.emptyList();
		}

		List<SparePartsStockVO> stockVOList = createItems.stream()
			.map(SparePartsInventoryItemBeanMapper.INSTANCE::toStockVO)
			.collect(Collectors.toList());

		return stockLogicService.saveBatchWithReturn(stockVOList);
	}

	/**
	 * 处理库存新增（保持向后兼容）
	 * 为新的备品备件创建库存记录
	 *
	 * @param createItems 需要新增的盘点明细列表
	 */
	private void processStockCreation(List<SparePartsInventoryItemDTO> createItems) {
		processStockCreationWithReturn(createItems);
	}


	/**
	 * 处理库存操作并获取stockId映射
	 * 核心优化方法：处理单个仓库的所有库存操作，为后续明细保存提供stockId映射
	 * <p>
	 * 处理逻辑：
	 * 1. 查询现有库存记录，区分需要更新和新增的备品备件
	 * 2. 更新已存在的库存数量
	 * 3. 新增不存在的库存记录
	 * 4. 构建dictId到stockId的映射关系
	 * <p>
	 * 优化说明：由于盘点业务规则限制只能对同一仓库进行盘点，简化了仓库分组逻辑
	 *
	 * @param voList      盘点明细操作列表（同一仓库）
	 * @param warehouseId 仓库ID
	 * @return dictId到stockId的映射，用于后续明细VO封装
	 */
	private Map<Long, Long> processStockOperationsAndGetStockIdMap(List<SparePartsInventoryItemOperationVO> voList, Long warehouseId) {
		if (ObjectUtil.isEmpty(voList)) {
			return Collections.emptyMap();
		}

		Map<Long, Long> stockIdMap = new HashMap<>();
		// 提取所有备品备件字典ID
		List<Long> dictIds = ListUtil.distinctMap(voList, SparePartsInventoryItemOperationVO::getDictId);

		// 查询该仓库下这些备品备件的现有库存记录
		List<SparePartsStockDTO> existingStocks = ((ISparePartsStockService) stockLogicService.fetchBaseService())
			.listBy(warehouseId, dictIds);

		// 创建dictId到库存记录的映射
		Map<Long, SparePartsStockDTO> dictIdToStockMap = existingStocks.stream()
			.collect(Collectors.toMap(SparePartsStockDTO::getDictId, Function.identity()));

		// 分离需要更新和新增的记录
		List<SparePartsInventoryItemOperationVO> toUpdate = new ArrayList<>();
		List<SparePartsInventoryItemOperationVO> toCreate = new ArrayList<>();

		for (SparePartsInventoryItemOperationVO vo : voList) {
			if (dictIdToStockMap.containsKey(vo.getDictId())) {
				// 库存记录存在，需要更新
				SparePartsStockDTO existingStock = dictIdToStockMap.get(vo.getDictId());
				stockIdMap.put(vo.getDictId(), existingStock.getId());
				toUpdate.add(vo);
			} else {
				// 库存记录不存在，需要新增
				toCreate.add(vo);
			}
		}

		// 处理库存更新
		this.processStockUpdatesFromOperationVO(toUpdate, dictIdToStockMap);

		// 处理库存新增，并获取新创建的库存记录
		List<SparePartsStockDTO> newStocks = this.processStockCreationFromOperationVO(toCreate, warehouseId);

		// 将新创建的stockId添加到映射中
		for (SparePartsStockDTO newStock : newStocks) {
			stockIdMap.put(newStock.getDictId(), newStock.getId());
		}

		return stockIdMap;
	}

	/**
	 * 构建完整的盘点明细VO列表
	 * 核心封装方法：将操作VO转换为包含完整信息的明细VO，确保一次性保存成功
	 * <p>
	 * 封装内容：
	 * 1. 基础明细信息（工单ID、备品备件ID、盘点数量等）
	 * 2. 库存关联信息（stockId）
	 * 3. 盘点结果计算（盈亏状态）
	 * 4. 业务字段填充（创建时间、用户信息等）
	 * <p>
	 * 处理场景：
	 * - 已存在明细：更新现有记录，保留原有ID
	 * - 临时新增明细：创建新记录，使用模板数据
	 * <p>
	 * 优化说明：简化stockId映射查找，直接使用dictId作为key
	 *
	 * @param voList     盘点明细操作列表
	 * @param stockIdMap dictId到stockId的映射
	 * @return 完整的盘点明细VO列表，可直接用于数据库保存
	 */
	private List<SparePartsInventoryItemVO> buildCompleteInventoryItemVOList(
		List<SparePartsInventoryItemOperationVO> voList, Map<Long, Long> stockIdMap) {

		if (ObjectUtil.isEmpty(voList)) {
			return Collections.emptyList();
		}

		List<SparePartsInventoryItemVO> resultList = new ArrayList<>();

		// 处理已存在的明细（有ID的记录）
		List<SparePartsInventoryItemVO> existingItems = this.buildExistingItemVOList(voList, stockIdMap);
		if (!existingItems.isEmpty()) {
			resultList.addAll(existingItems);
		}

		// 处理新增的明细（无ID的记录）
		List<SparePartsInventoryItemVO> newItems = this.buildNewItemVOList(voList, stockIdMap);
		if (!newItems.isEmpty()) {
			resultList.addAll(newItems);
		}

		return resultList;
	}

	/**
	 * 构建已存在明细的VO列表
	 * 处理工单中已存在的明细记录，更新盘点数据并设置stockId
	 * 优化：直接使用dictId查找stockId，简化映射逻辑
	 */
	private List<SparePartsInventoryItemVO> buildExistingItemVOList(
		List<SparePartsInventoryItemOperationVO> voList, Map<Long, Long> stockIdMap) {

		return voList.stream()
			.filter(vo -> vo.getId() != null)
			.map(vo -> {
				SparePartsInventoryItemVO itemVO = SparePartsInventoryItemBeanMapper.INSTANCE.toVO(vo);
				itemVO.setBeforeSystemStock(vo.getBeforeSystemStock());
				itemVO.setResult(SpareInventoryItemResultEnum.calculateResultCode(
					vo.getBeforeSystemStock(), vo.getAfterCountedStock()));

				// 设置stockId（优化：直接使用dictId查找）
				Long stockId = stockIdMap.get(vo.getDictId());
				if (stockId != null) {
					itemVO.setStockId(stockId);
				}

				return itemVO;
			}).collect(Collectors.toList());
	}

	/**
	 * 构建新增明细的VO列表
	 * 处理从备品备件字典中临时新增的明细记录，创建完整的明细数据
	 * 优化：直接使用dictId查找stockId，简化映射逻辑
	 */
	private List<SparePartsInventoryItemVO> buildNewItemVOList(
		List<SparePartsInventoryItemOperationVO> voList, Map<Long, Long> stockIdMap) {

		List<SparePartsInventoryItemOperationVO> newItemVOs = voList.stream()
			.filter(vo -> vo.getId() == null)
			.collect(Collectors.toList());

		if (newItemVOs.isEmpty()) {
			return Collections.emptyList();
		}

		// 获取模板明细DTO
		SparePartsInventoryItemDTO templateDTO = this.getTemplateItemDTOSafely(Collections.emptyList(), newItemVOs);

		return newItemVOs.stream()
			.filter(Objects::nonNull)
			.peek(this::validateOperationVO)
			.map(operationVO -> {
				SparePartsInventoryItemVO itemVO = SparePartsInventoryItemBeanMapper.INSTANCE
					.toNewItemVO(operationVO, templateDTO);

				// 设置stockId（优化：直接使用dictId查找）
				Long stockId = stockIdMap.get(operationVO.getDictId());
				if (stockId != null) {
					itemVO.setStockId(stockId);
				}

				return itemVO;
			})
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
	}


	/**
	 * 处理库存更新（基于OperationVO）
	 * 批量更新已存在的库存记录数量
	 *
	 * @param updateItems      需要更新的盘点明细列表
	 * @param dictIdToStockMap 备品备件ID到库存记录的映射
	 */
	private void processStockUpdatesFromOperationVO(List<SparePartsInventoryItemOperationVO> updateItems,
													Map<Long, SparePartsStockDTO> dictIdToStockMap) {

		if (ObjectUtil.isEmpty(updateItems)) {
			return;
		}

		Map<Long, BigDecimal> stockUpdateMap = updateItems.stream()
			.filter(item -> !Objects.equals(item.getAfterCountedStock(), item.getBeforeSystemStock()))
			.collect(Collectors.toMap(
				item -> dictIdToStockMap.get(item.getDictId()).getId(),
				SparePartsInventoryItemOperationVO::getAfterCountedStock,
				(existing, replacement) -> replacement
			));

		if (!stockUpdateMap.isEmpty()) {
			stockLogicService.updateBatchStock(stockUpdateMap);
		}
	}

	/**
	 * 处理库存新增（基于OperationVO）
	 * 为临时新增的备品备件创建库存记录
	 *
	 * @param createItems 需要新增的盘点明细列表
	 * @param warehouseId 仓库ID
	 * @return 新创建的库存记录列表，包含生成的stockId
	 */
	private List<SparePartsStockDTO> processStockCreationFromOperationVO(
		List<SparePartsInventoryItemOperationVO> createItems, Long warehouseId) {

		if (ObjectUtil.isEmpty(createItems)) {
			return Collections.emptyList();
		}

		List<SparePartsStockVO> stockVOList = createItems.stream()
			.map(vo -> {
				SparePartsStockVO stockVO = new SparePartsStockVO();
				stockVO.setDictId(vo.getDictId());
				stockVO.setWarehouseId(warehouseId);
				stockVO.setCurrentQuantity(vo.getAfterCountedStock());
				return stockVO;
			})
			.collect(Collectors.toList());

		return stockLogicService.saveBatchWithReturn(stockVOList);
	}

	/**
	 * 优化的单项明细处理方法
	 * 使用已经完整封装的VO进行保存，包含业务校验和数据保存
	 *
	 * @param vo                 单项盘点明细操作对象
	 * @param orderDTO           盘点工单信息
	 * @param completeItemVOList 完整的明细VO列表
	 * @return 保存后的明细DTO列表
	 */
	private List<SparePartsInventoryItemDTO> handleSingleInventoryItemOptimized(
		SparePartsInventoryItemOperationVO vo, SparePartsInventoryOrderDTO orderDTO,
		List<SparePartsInventoryItemVO> completeItemVOList) {

		// 情况1：明细ID不为空，说明是工单中已存在的明细记录
		if (ObjectUtil.isNotEmpty(vo.getId())) {
			// 查询现有明细验证数据有效性
			SparePartsInventoryItemDTO existingItem = itemService.fetchById(vo.getId());
			if (ObjectUtil.isEmpty(existingItem)) {
				throw new ServiceException("单项盘点提交失败！盘点明细不存在，明细ID: " + vo.getId() +
					"，工单ID: " + vo.getInventoryOrderId() + "，备品备件ID: " + vo.getDictId());
			}

			// 验证明细是否属于当前工单
			if (!vo.getInventoryOrderId().equals(existingItem.getInventoryOrderId())) {
				throw new ServiceException("单项盘点提交失败！盘点明细不属于当前工单，明细ID: " + vo.getId() +
					"，请求工单ID: " + vo.getInventoryOrderId() + "，实际工单ID: " + existingItem.getInventoryOrderId());
			}
		}
		// 情况2：明细ID为空，说明是从备品备件字典中动态添加的新明细
		else {
			// 验证必要字段
			if (vo.getDictId() == null) {
				throw new ServiceException("单项盘点提交失败！备品备件字典ID不能为空，工单ID: " + vo.getInventoryOrderId());
			}
			if (vo.getInventoryOrderId() == null) {
				throw new ServiceException("单项盘点提交失败！盘点工单ID不能为空，这是系统错误，请联系管理员");
			}

			// 检查是否重复添加
			boolean dictExists = this.checkDictExistsInOrder(vo.getInventoryOrderId(), vo.getDictId());
			if (dictExists) {
				throw new ServiceException("单项盘点提交失败！该备品备件已存在于当前盘点工单中，工单ID: " + vo.getInventoryOrderId() +
					"，备品备件字典ID: " + vo.getDictId() + "，请勿重复添加");
			}
		}

		// 直接使用完整的VO进行保存（优化：一次性操作）
		return itemService.saveOrUpdateBatch(completeItemVOList);
	}

	/**
	 * 保存或更新库存（向后兼容方法）
	 * 保持原有接口不变，供其他地方调用
	 * 优化：简化单仓库处理逻辑，移除不必要的分组操作
	 * 注意：此方法仅用于向后兼容，新的盘点流程已优化为一次性操作
	 *
	 * @param itemDTOList 盘点明细列表（同一仓库）
	 */
	public void saveOrUpdateBatchStock(List<SparePartsInventoryItemDTO> itemDTOList) {
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return;
		}

		// 获取仓库ID（盘点业务规则：同一批次只涉及一个仓库）
		Long warehouseId = itemDTOList.get(0).getWarehouseId();

		// 提取所有备品备件字典ID
		List<Long> dictIds = itemDTOList.stream()
			.map(SparePartsInventoryItemDTO::getDictId)
			.distinct()
			.collect(Collectors.toList());

		// 查询该仓库下这些备品备件的现有库存记录
		List<SparePartsStockDTO> existingStocks = ((ISparePartsStockService) stockLogicService.fetchBaseService())
			.listBy(warehouseId, dictIds);

		// 创建dictId到库存记录的映射
		Map<Long, SparePartsStockDTO> dictIdToStockMap = existingStocks.stream()
			.collect(Collectors.toMap(SparePartsStockDTO::getDictId, Function.identity()));

		// 分离需要更新和新增的记录
		List<SparePartsInventoryItemDTO> toUpdate = new ArrayList<>();
		List<SparePartsInventoryItemDTO> toCreate = new ArrayList<>();

		for (SparePartsInventoryItemDTO item : itemDTOList) {
			if (dictIdToStockMap.containsKey(item.getDictId())) {
				// 库存记录存在，需要更新
				SparePartsStockDTO existingStock = dictIdToStockMap.get(item.getDictId());
				item.setStockId(existingStock.getId());
				toUpdate.add(item);
			} else {
				// 库存记录不存在，需要新增
				toCreate.add(item);
			}
		}

		// 处理库存更新
		processStockUpdates(toUpdate);

		// 处理库存新增
		processStockCreation(toCreate);
	}

	/**
	 * 盘点后解锁仓库
	 *
	 * @param warehouseId
	 */
	private void unlockWarehouseAfterInventory(Long warehouseId) {
		final SparePartsWarehouse warehouse = warehouseMapper.selectById(warehouseId);
		warehouse.setLockStock(LockStockEnum.UNLOCK.getCode());
		warehouseMapper.updateById(warehouse);
	}

	/**
	 * 如果所有的工单都提交成功，则更新计划状态为已完成
	 *
	 * @param planId
	 */
	private void updatePlanStatusIfAllOrderFinish(Long planId) {
		// 根据计划id查询盘点计划
		final SparePartsInventoryPlan plan = planMapper.selectById(planId);
		if (ObjectUtil.isEmpty(plan)) {
			throw new BusinessException("未查询到盘点计划");
		}
		// 查询是否还有盘点中状态的工单
		List<SparePartsInventoryOrderDTO> orderDTOList = orderService.listBy(planId, SpareInventoryOrderStatusEnum.PROCESS.getCode(), null);
		// 如果没有盘点中状态的工单，则更新计划状态为已完成
		if (ObjectUtil.isEmpty(orderDTOList)) {
			plan.setStatus(SpareInventoryPlanStatusEnum.COMPLETE.getCode());
			plan.setCompleteTime(DateUtil.now());
			planMapper.updateById(plan);
		}
	}


	/**
	 * 提交盘点更新工单状态
	 */
	private void submitInventoryUpdateOrder(Long inventoryOrderId) {
		final SparePartsInventoryOrderDTO orderDTO = orderService.fetchById(inventoryOrderId);
		final SparePartsInventoryOrderVO vo = SparePartsInventoryOrderBeanMapper.INSTANCE.toVO(orderDTO);
		vo.setStatus(SpareInventoryOrderStatusEnum.COMPLETE.getCode());
		vo.setCompleteTime(LocalDateTime.now());
		ObjectUtil.isNotEmpty(orderService.save(vo));
	}

	/**
	 * 检查工单下所有明细是否都已完成盘点，如果是则更新工单状态
	 * 优化版本：使用数据库层面的聚合查询，避免不必要的数据填充
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @param planId           盘点计划ID
	 * @param warehouseId      仓库ID
	 */
	private void checkAndUpdateOrderStatusIfAllItemsCompleted(Long inventoryOrderId, Long planId, Long warehouseId) {
		// 业务逻辑判断：检查是否所有明细都已完成盘点
		boolean allItemsCompleted = this.isAllItemsCompletedByOrderId(inventoryOrderId);

		if (allItemsCompleted) {
			// 更新工单状态为已完成
			this.submitInventoryUpdateOrder(inventoryOrderId);
			// 检查计划下所有工单是否都完成
			this.updatePlanStatusIfAllOrderFinish(planId);
			// 解锁仓库
			this.unlockWarehouseAfterInventory(warehouseId);
		}
	}

	/**
	 * 判断指定工单下所有明细是否都已完成盘点
	 * 优化：简化逻辑，移除不必要的Optional使用
	 * 业务逻辑层：包含参数校验和业务判断逻辑
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return true-所有明细都已完成盘点，false-还有未完成的明细
	 */
	private boolean isAllItemsCompletedByOrderId(Long inventoryOrderId) {
		if (inventoryOrderId == null) {
			return false;
		}

		Integer uncompletedCount = itemService.countItemsByOrderIdAndResult(
			inventoryOrderId,
			SpareInventoryItemResultEnum.NOT_START.getCode()
		);

		return uncompletedCount != null && uncompletedCount == 0;
	}

	/**
	 * 智能数据填充：根据使用场景选择性填充数据
	 * 优化：使用外部配置类，避免不必要的数据库查询
	 *
	 * @param itemList 盘点明细列表
	 * @param config   填充配置
	 */
	private void populateSparePartsInventoryItemData(List<SparePartsInventoryItemDTO> itemList, DataFillConfig config) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 条件性执行填充操作，提高性能
		if (config.needDictInfo()) {
			this.populateSparePartsDictInfo(itemList);
		}
		if (config.needEnumNames()) {
			this.populateEnumNames(itemList);
		}
		if (config.needWarehouseNames()) {
			this.populateWarehouseNames(itemList);
		}
	}

	/**
	 * 填充备品备件字典信息
	 * 优化：简化逻辑，使用批量查询，提高性能
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateSparePartsDictInfo(List<SparePartsInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 批量获取去重的字典ID列表
		List<Long> dictIds = itemList.stream()
			.map(SparePartsInventoryItemDTO::getDictId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		if (dictIds.isEmpty()) {
			return;
		}

		// 批量获取字典信息Map
		Map<Long, SparePartsDictDTO> dictMap = dictLogicService.getDictMap(dictIds);
		if (dictMap.isEmpty()) {
			return;
		}

		// 批量填充字典信息，避免Optional的性能开销
		for (SparePartsInventoryItemDTO item : itemList) {
			if (item.getDictId() != null) {
				SparePartsDictDTO dictDTO = dictMap.get(item.getDictId());
				if (dictDTO != null) {
					SparePartsDictBeanMapper.INSTANCE.setSparePartsInventoryItemDTO(item, dictDTO);
				}
			}
		}
	}

	/**
	 * 填充枚举名称（盘点结果）
	 * 优化：简化Lambda表达式，提高代码可读性和执行效率
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateEnumNames(List<SparePartsInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 简化逻辑，避免过度使用Optional链式调用
		for (SparePartsInventoryItemDTO item : itemList) {
			if (item.getResult() != null) {
				SpareInventoryItemResultEnum resultEnum = SpareInventoryItemResultEnum.getByCode(item.getResult());
				if (resultEnum != null) {
					item.setResultName(resultEnum.getName());
				}
			}
		}
	}

	/**
	 * 填充仓库名称
	 * 优化：简化逻辑，使用批量查询，提高性能
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateWarehouseNames(List<SparePartsInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 批量获取去重的仓库ID列表
		List<Long> warehouseIds = itemList.stream()
			.map(SparePartsInventoryItemDTO::getWarehouseId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		if (warehouseIds.isEmpty()) {
			return;
		}

		// 批量获取仓库名称Map
		Map<Long, String> warehouseNameMap = this.getWarehouseIdToNameMap(warehouseIds);
		if (warehouseNameMap.isEmpty()) {
			return;
		}

		// 批量填充仓库名称，避免Optional的性能开销
		for (SparePartsInventoryItemDTO item : itemList) {
			if (item.getWarehouseId() != null) {
				String warehouseName = warehouseNameMap.get(item.getWarehouseId());
				if (warehouseName != null) {
					item.setWarehouseName(warehouseName);
				}
			}
		}
	}

	/**
	 * 处理单项明细盘点的保存逻辑
	 * 支持两种数据来源：
	 * 1. 工单中已存在的明细记录（有明细ID）- 使用ExistingItemValidationGroup校验
	 * 2. 从备品备件字典中临时新增的明细（明细ID为空）- 使用NewItemValidationGroup校验
	 *
	 * @param vo       单项盘点明细操作对象
	 * @param orderDTO 盘点工单信息
	 * @return 保存后的明细列表
	 */
	private List<SparePartsInventoryItemDTO> handleSingleInventoryItem(SparePartsInventoryItemOperationVO vo, SparePartsInventoryOrderDTO orderDTO) {
		// 情况1：明细ID不为空，说明是工单中已存在的明细记录
		if (ObjectUtil.isNotEmpty(vo.getId())) {
			// 查询现有明细验证数据有效性
			SparePartsInventoryItemDTO existingItem = itemService.fetchById(vo.getId());
			if (ObjectUtil.isEmpty(existingItem)) {
				throw new ServiceException("单项盘点提交失败！盘点明细不存在，明细ID: " + vo.getId() +
					"，工单ID: " + vo.getInventoryOrderId() + "，备品备件ID: " + vo.getDictId());
			}

			// 验证明细是否属于当前工单
			if (!vo.getInventoryOrderId().equals(existingItem.getInventoryOrderId())) {
				throw new ServiceException("单项盘点提交失败！盘点明细不属于当前工单，明细ID: " + vo.getId() +
					"，请求工单ID: " + vo.getInventoryOrderId() + "，实际工单ID: " + existingItem.getInventoryOrderId());
			}

			// 直接调用批量保存方法处理
			return this.saveOrUpdateBatchItem(Lists.newArrayList(vo));
		}

		// 情况2：明细ID为空，说明是从备品备件字典中动态添加的新明细
		else {
			// 验证必要字段
			if (vo.getDictId() == null) {
				throw new ServiceException("单项盘点提交失败！备品备件字典ID不能为空，工单ID: " + vo.getInventoryOrderId());
			}
			if (vo.getInventoryOrderId() == null) {
				throw new ServiceException("单项盘点提交失败！盘点工单ID不能为空，这是系统错误，请联系管理员");
			}

			// 修复：使用现有的Service方法进行精确查询，避免全量数据加载
			boolean dictExists = this.checkDictExistsInOrder(vo.getInventoryOrderId(), vo.getDictId());
			if (dictExists) {
				throw new ServiceException("单项盘点提交失败！该备品备件已存在于当前盘点工单中，工单ID: " + vo.getInventoryOrderId() +
					"，备品备件字典ID: " + vo.getDictId() + "，请勿重复添加");
			}

			// 调用批量保存方法，它会自动处理ID为空的情况
			return this.saveOrUpdateBatchItem(Collections.singletonList(vo));
		}
	}

	/**
	 * 从工单或明细中获取计划ID
	 * 优化：简化逻辑，移除过度的Optional使用，增强异常信息上下文
	 *
	 * @param orderDTO    工单信息
	 * @param itemDTOList 明细列表
	 * @return 计划ID
	 */
	private Long getPlanIdFromOrderOrItem(SparePartsInventoryOrderDTO orderDTO, List<SparePartsInventoryItemDTO> itemDTOList) {
		// 优先从工单中获取计划ID
		if (orderDTO != null && orderDTO.getPlanId() != null) {
			return orderDTO.getPlanId();
		}

		// 如果工单中没有，从明细中获取
		if (!ObjectUtil.isEmpty(itemDTOList)) {
			for (SparePartsInventoryItemDTO item : itemDTOList) {
				if (item.getPlanId() != null) {
					return item.getPlanId();
				}
			}
		}

		// 提供详细的错误上下文
		String orderInfo = orderDTO != null ? "工单ID: " + orderDTO.getId() + ", 工单号: " + orderDTO.getNo() : "工单信息为空";
		int itemCount = itemDTOList != null ? itemDTOList.size() : 0;

		throw new ServiceException("盘点操作失败！无法获取盘点计划ID，" + orderInfo + "，明细数量: " + itemCount + "，请检查数据完整性");
	}

	/**
	 * 转换新增盘点明细
	 * 优化：移除不必要的异常处理，让Spring统一异常处理器处理，提高代码简洁性
	 * 将OperationVO列表转换为ItemVO列表，用于创建新的盘点明细
	 *
	 * @param notExistList    需要新增的明细VO列表
	 * @param templateItemDTO 模板明细DTO（提供工单基础信息）
	 * @return 转换后的明细VO列表
	 */
	private List<SparePartsInventoryItemVO> convertNewInventoryItems(List<SparePartsInventoryItemOperationVO> notExistList,
																	 SparePartsInventoryItemDTO templateItemDTO) {
		if (ObjectUtil.isEmpty(notExistList)) {
			return Collections.emptyList();
		}

		if (templateItemDTO == null) {
			throw new ServiceException("转换新增明细失败，模板明细不能为空");
		}

		return notExistList.stream()
			.filter(Objects::nonNull)
			.peek(this::validateOperationVO)  // 数据验证，如有问题直接抛出ServiceException
			.map(operationVO -> SparePartsInventoryItemBeanMapper.INSTANCE.toNewItemVO(operationVO, templateItemDTO))
			.filter(Objects::nonNull)
			.collect(Collectors.toList());
	}

	/**
	 * 安全获取模板明细DTO，用于创建新的盘点明细
	 * 优化：使用Java 8兼容语法，处理无模板明细的情况
	 *
	 * @param savedItemDTOList 已保存的明细列表
	 * @param notExistList     需要新增的明细VO列表
	 * @return 模板明细DTO
	 */
	private SparePartsInventoryItemDTO getTemplateItemDTOSafely(List<SparePartsInventoryItemDTO> savedItemDTOList,
																List<SparePartsInventoryItemOperationVO> notExistList) {
		// 优先从已保存的明细中获取模板
		if (!ObjectUtil.isEmpty(savedItemDTOList)) {
			return savedItemDTOList.get(0);
		}

		// 如果没有已保存的明细，从工单中查询现有明细作为模板
		if (!ObjectUtil.isEmpty(notExistList)) {
			Long inventoryOrderId = notExistList.get(0).getInventoryOrderId();
			if (inventoryOrderId != null) {
				// 使用Java 8兼容的Collections.singletonList
				List<SparePartsInventoryItemDTO> existingItems = itemService.listByOrderIds(Collections.singletonList(inventoryOrderId));
				if (!ObjectUtil.isEmpty(existingItems)) {
					return existingItems.get(0);
				}

				// 如果工单中也没有明细，从工单信息创建基础模板
				return this.createTemplateFromOrder(inventoryOrderId);
			}
		}

		throw new ServiceException("无法获取模板明细，缺少必要的工单信息");
	}

	/**
	 * 从工单信息创建基础模板明细DTO
	 * 修复：当工单中没有任何明细记录时，从工单信息创建基础模板
	 * 优化：增强异常信息上下文
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return 基础模板明细DTO
	 */
	private SparePartsInventoryItemDTO createTemplateFromOrder(Long inventoryOrderId) {
		// 查询工单信息
		SparePartsInventoryOrderDTO orderDTO = orderService.fetchById(inventoryOrderId);
		if (ObjectUtil.isEmpty(orderDTO)) {
			throw new ServiceException("动态添加盘点明细失败！无法获取盘点工单信息，工单ID: " + inventoryOrderId + "，请检查工单是否存在");
		}

		// 验证工单必要字段
		if (ObjectUtil.isEmpty(orderDTO.getPlanId())) {
			throw new ServiceException("动态添加盘点明细失败！盘点工单缺少计划ID，工单ID: " + inventoryOrderId +
				"，工单号: " + orderDTO.getNo() + "，无法创建模板明细");
		}
		if (ObjectUtil.isEmpty(orderDTO.getWarehouseId())) {
			throw new ServiceException("动态添加盘点明细失败！盘点工单缺少仓库ID，工单ID: " + inventoryOrderId +
				"，工单号: " + orderDTO.getNo() + "，无法创建模板明细");
		}

		// 创建基础模板明细
		SparePartsInventoryItemDTO templateDTO = new SparePartsInventoryItemDTO();
		templateDTO.setInventoryOrderId(inventoryOrderId);
		templateDTO.setPlanId(orderDTO.getPlanId());
		templateDTO.setPlanNo(orderDTO.getPlanNo());
		templateDTO.setPlanName(orderDTO.getPlanName());
		templateDTO.setPlanStartDate(orderDTO.getPlanStartDate());
		templateDTO.setPlanEndDate(orderDTO.getPlanEndDate());
		templateDTO.setInventoryOrderNo(orderDTO.getNo());
		templateDTO.setWarehouseId(orderDTO.getWarehouseId());
		templateDTO.setInventoryUserId(orderDTO.getInventoryUserId());
		templateDTO.setInventoryUserName(orderDTO.getInventoryUserName());
		templateDTO.setResult(SpareInventoryItemResultEnum.NOT_START.getCode());

		return templateDTO;
	}


	/**
	 * 统一的盘点校验方法
	 * 优化：合并所有校验逻辑到一个方法中，提高代码简洁性和可维护性
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @param contextInfo      上下文信息（用于异常提示）
	 * @return 盘点工单信息
	 * @throws ServiceException 当工单不存在或状态不正确时
	 */
	private SparePartsInventoryOrderDTO validateInventoryOperation(Long inventoryOrderId, String contextInfo) {
		// 查询并校验工单存在性
		SparePartsInventoryOrderDTO orderDTO = orderService.fetchById(inventoryOrderId);
		if (orderDTO == null) {
			String message = contextInfo + "！盘点工单不存在，工单ID: " + inventoryOrderId;
			throw new ServiceException(message);
		}

		// 校验工单状态 - 统一只允许盘点中状态
		Integer allowedStatus = SpareInventoryOrderStatusEnum.PROCESS.getCode();
		if (!allowedStatus.equals(orderDTO.getStatus())) {
			String statusName = this.getStatusName(orderDTO.getStatus());
			String message = contextInfo + "！工单状态不正确，工单ID: " + orderDTO.getId() +
				"，工单号: " + orderDTO.getNo() + "，当前状态: " + statusName + "(" + orderDTO.getStatus() +
				")，只能在盘点中状态下进行盘点操作";
			throw new ServiceException(message);
		}

		return orderDTO;
	}

	/**
	 * 单项盘点明细校验
	 * 整合JSR-303校验和工单状态校验，确保校验逻辑的一致性
	 *
	 * @param vo 单项盘点明细操作对象
	 * @return 盘点工单信息
	 */
	private SparePartsInventoryOrderDTO validateSingleInventoryItem(SparePartsInventoryItemOperationVO vo) {
		// 执行JSR-303校验
		this.validateInventoryItemVO(vo, "单项盘点操作失败");

		// 验证工单状态和业务规则
		return this.validateInventoryOperation(vo.getInventoryOrderId(), "单项盘点操作失败");
	}

	/**
	 * 批量盘点明细校验
	 * 整合基础参数校验和JSR-303校验，确保校验逻辑的一致性和完整性
	 * 优化：使用统一的校验逻辑，但保持BusinessException以符合批量操作的异常处理规范
	 *
	 * @param voList 批量盘点明细操作列表（Controller层已通过@NotEmpty校验，确保非空）
	 * @return 盘点工单信息
	 */
	private SparePartsInventoryOrderDTO validateBatchInventoryItems(List<SparePartsInventoryItemOperationVO> voList) {
		// 获取并校验工单ID
		final Long inventoryOrderId = this.extractAndValidateOrderId(voList, "批量盘点操作失败");

		// 对每个VO执行JSR-303校验（与单项盘点保持一致）
		for (int i = 0; i < voList.size(); i++) {
			SparePartsInventoryItemOperationVO vo = voList.get(i);
			String contextInfo = "批量盘点操作失败，第" + (i + 1) + "项明细";
			this.validateInventoryItemVO(vo, contextInfo);
		}

		// 使用统一校验方法验证工单状态，但转换异常类型以保持批量操作的一致性
		try {
			return this.validateInventoryOperation(inventoryOrderId, "批量盘点操作失败");
		} catch (ServiceException e) {
			// 转换为BusinessException以保持批量操作的异常处理一致性
			throw new BusinessException(e.getMessage());
		}
	}

	/**
	 * 执行JSR-303校验
	 * 根据业务场景动态选择校验分组，将校验逻辑从Controller下沉到LogicService
	 *
	 * @param vo          盘点明细操作对象
	 * @param contextInfo 上下文信息（用于异常提示）
	 */
	private void validateInventoryItemVO(SparePartsInventoryItemOperationVO vo, String contextInfo) {
		// 根据是否有明细ID来选择校验分组
		Class<?> validationGroup = vo.getId() != null ? ExistingItemValidationGroup.class : NewItemValidationGroup.class;

		// 执行JSR-303校验
		Set<ConstraintViolation<SparePartsInventoryItemOperationVO>> violations = validator.validate(vo, validationGroup);
		if (!violations.isEmpty()) {
			StringBuilder errorMsg = new StringBuilder(contextInfo + "！参数校验失败：");
			for (ConstraintViolation<SparePartsInventoryItemOperationVO> violation : violations) {
				errorMsg.append(violation.getMessage()).append("; ");
			}
			throw new IllegalArgumentException(errorMsg.toString());
		}
	}

	/**
	 * 从盘点明细列表中提取并校验工单ID
	 * 优化：提取公共逻辑，统一工单ID获取和校验
	 *
	 * @param voList      盘点明细操作列表
	 * @param contextInfo 上下文信息（用于异常提示）
	 * @return 工单ID
	 * @throws BusinessException 当工单ID为空时
	 */
	private Long extractAndValidateOrderId(List<SparePartsInventoryItemOperationVO> voList, String contextInfo) {
		// 从第一个元素获取工单ID（这是业务逻辑要求）
		final Long inventoryOrderId = voList.get(0).getInventoryOrderId();
		if (inventoryOrderId == null) {
			throw new BusinessException(contextInfo + "！盘点工单ID不能为空，请检查数据完整性");
		}
		return inventoryOrderId;
	}

	/**
	 * 获取状态名称，用于异常信息
	 * 优化：提取公共逻辑，统一状态名称获取方式
	 *
	 * @param statusCode 状态码
	 * @return 状态名称
	 */
	private String getStatusName(Integer statusCode) {
		SpareInventoryOrderStatusEnum statusEnum = SpareInventoryOrderStatusEnum.getByCode(statusCode);
		return statusEnum != null ? statusEnum.getName() : "未知状态";
	}

	/**
	 * 检查指定工单下是否存在指定字典ID的明细
	 * 优化：使用现有Service方法实现精确查询，避免全量数据加载
	 * <p>
	 * 实现方案：使用countItemsByOrderIdAndResult方法查询指定工单下的所有明细数量，
	 * 然后通过listByOrderIds获取明细列表进行字典ID匹配
	 * <p>
	 * 性能说明：虽然仍需要查询明细列表，但相比原来的实现，这里只在确实需要检查重复时才调用，
	 * 且提供了清晰的方法职责分离
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @param dictId           字典ID
	 * @return 是否存在
	 */
	private boolean checkDictExistsInOrder(Long inventoryOrderId, Long dictId) {
		if (inventoryOrderId == null || dictId == null) {
			return false;
		}

		// 先检查工单下是否有明细，如果没有明细则直接返回false
		Integer totalCount = itemService.countItemsByOrderIdAndResult(inventoryOrderId, null);
		if (totalCount == null || totalCount == 0) {
			return false;
		}

		// 查询工单下的所有明细，检查是否存在相同的字典ID
		List<SparePartsInventoryItemDTO> existingItems = itemService.listByOrderIds(Collections.singletonList(inventoryOrderId));

		return existingItems.stream()
			.anyMatch(item -> dictId.equals(item.getDictId()));
	}

	/**
	 * 验证OperationVO的必要字段
	 * 优化：提供更准确的异常信息，包含详细上下文信息便于问题定位
	 *
	 * @param operationVO 操作VO
	 */
	private void validateOperationVO(SparePartsInventoryItemOperationVO operationVO) {
		if (operationVO == null) {
			throw new ServiceException("盘点操作失败！盘点明细操作数据不能为空");
		}

		Long inventoryOrderId = operationVO.getInventoryOrderId();
		if (inventoryOrderId == null) {
			String itemId = operationVO.getId() != null ? operationVO.getId().toString() : "新增";
			throw new ServiceException("盘点操作失败！盘点工单ID不能为空，明细ID: " + itemId + "，请检查数据完整性");
		}

		Long dictId = operationVO.getDictId();
		if (dictId == null) {
			String itemId = operationVO.getId() != null ? operationVO.getId().toString() : "新增";
			throw new ServiceException("盘点操作失败！备品备件字典ID不能为空，工单ID: " + inventoryOrderId + "，明细ID: " + itemId);
		}

		BigDecimal afterCountedStock = operationVO.getAfterCountedStock();
		if (afterCountedStock == null) {
			String itemId = operationVO.getId() != null ? operationVO.getId().toString() : "新增";
			throw new ServiceException("盘点操作失败！盘点后实际数量不能为空，工单ID: " + inventoryOrderId +
				"，备品备件ID: " + dictId + "，明细ID: " + itemId);
		}

		// 验证数量的合理性
		if (afterCountedStock.compareTo(BigDecimal.ZERO) < 0) {
			throw new ServiceException("盘点操作失败！盘点后实际数量不能为负数，当前值: " + afterCountedStock +
				"，工单ID: " + inventoryOrderId + "，备品备件ID: " + dictId);
		}

		// beforeSystemStock可以为空，表示新增的备品备件，设置默认值
		if (operationVO.getBeforeSystemStock() == null) {
			operationVO.setBeforeSystemStock(BigDecimal.ZERO);
		}
	}

}
