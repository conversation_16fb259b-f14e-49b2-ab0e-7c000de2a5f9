/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.service.logic.SparePartsInventoryOrderLogicService;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 备品备件盘点记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsinventoryorder")
@Api(value = "备品备件盘点工单", tags = "备品备件盘点工单")
@ApiSupport(order = 56, author = "zhangzhenpu")
@Validated
public class SparePartsInventoryOrderController extends BaseCrudController {

	private final SparePartsInventoryOrderLogicService sparePartsInventoryOrderLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsInventoryOrderLogicService;
	}


	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "SparePartsInventoryOrderPageVO")
	public R<IPage<SparePartsInventoryOrderDTO>> page(SparePartsInventoryOrderPageVO v) {
		return R.data(sparePartsInventoryOrderLogicService.page(v));
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<SparePartsInventoryOrderDTO> getById(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsInventoryOrderLogicService.getById(id));
	}

	/**
	 * 完成
	 */
	@PutMapping("/complete/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "完成盘点", notes = "完成盘点")
	public R<Boolean> complete(@PathVariable("id") @NotNull @ApiParam(value = "盘点工单id", required = true) Long id) {
		return R.data(sparePartsInventoryOrderLogicService.complete(id));
	}

}
