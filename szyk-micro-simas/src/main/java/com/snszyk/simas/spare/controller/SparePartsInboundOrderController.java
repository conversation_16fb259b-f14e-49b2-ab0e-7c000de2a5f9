/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.spare.dto.SparePartsInboundOrderDTO;
import com.snszyk.simas.spare.service.logic.SparePartsInboundOrderLogicService;
import com.snszyk.simas.spare.vo.SparePartsInStorageCompletionRemarkVO;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 备品备件库存 控制器
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsinboundorder")
@Api(value = "备品备件入库单", tags = "备品备件入库单接口")
@Validated
@ApiSupport(order = 51, author = "zhangzhenpu")
public class SparePartsInboundOrderController extends BaseCrudController {


	private final SparePartsInboundOrderLogicService sparePartsInboundOrderLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsInboundOrderLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "SparePartsInboundOrderVo")
	public R<Boolean> save(@RequestBody @Validated SparePartsInboundOrderSaveVO v) {
		return R.data(sparePartsInboundOrderLogicService.save(v));
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "SparePartsInboundOrderVo")
	public R<IPage<SparePartsInboundOrderDTO>> page(SparePartsInboundOrderPageVO v) {
		return R.data(sparePartsInboundOrderLogicService.page(v));
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<SparePartsInboundOrderDTO> getById(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsInboundOrderLogicService.getById(id));
	}

	/**
	 * 入库单完结备注
	 */
	@PostMapping("/completionRemark")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "入库单完结备注", notes = "传入id")
	public R<Boolean> completionRemark(@RequestBody @Validated SparePartsInStorageCompletionRemarkVO v) {
		return R.status(sparePartsInboundOrderLogicService.saveOrUpdateCompletionRemark(v));
	}

}
