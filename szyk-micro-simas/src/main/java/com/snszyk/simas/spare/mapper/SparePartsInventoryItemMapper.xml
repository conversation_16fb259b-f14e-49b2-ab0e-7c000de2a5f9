<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsInventoryItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsInventoryItemResultMap" type="com.snszyk.simas.spare.entity.SparePartsInventoryItem">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_end_date" property="planEndDate"/>
        <result column="inventory_order_id" property="inventoryOrderId"/>
        <result column="inventory_order_no" property="inventoryOrderNo"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="stock_id" property="stockId"/>
        <result column="dict_id" property="dictId"/>
        <result column="before_system_stock" property="beforeSystemStock"/>
        <result column="after_counted_stock" property="afterCountedStock"/>
        <result column="result" property="result"/>
        <result column="inventory_user_id" property="inventoryUserId"/>
        <result column="inventory_user_name" property="inventoryUserName"/>
        <result column="remark" property="remark"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsInventoryItemPage" resultMap="sparePartsInventoryItemResultMap">
        select *
        from simas_spare_parts_inventory_item
        where is_deleted = 0
    </select>

    <!-- 统一的分页查询方法，支持所有查询条件 -->
    <select id="selectPageWithSparePartsName" resultMap="sparePartsInventoryItemResultMap">
        SELECT i.*
        FROM simas_spare_parts_inventory_item i
        <if test="pageVO.dictName != null and pageVO.dictName != ''">
            LEFT JOIN simas_spare_parts_dict d ON i.dict_id = d.id AND d.delete_time = 0
        </if>
        WHERE i.delete_time = 0
        <if test="pageVO.inventoryOrderId != null">
            AND i.inventory_order_id = #{pageVO.inventoryOrderId}
        </if>
        <if test="pageVO.planId != null">
            AND i.plan_id = #{pageVO.planId}
        </if>
        <if test="pageVO.warehouseId != null">
            AND i.warehouse_id = #{pageVO.warehouseId}
        </if>
        <if test="pageVO.resultEnum != null">
            AND i.result = #{pageVO.resultEnum.code}
        </if>
        <if test="pageVO.dictName != null and pageVO.dictName != ''">
            AND d.name LIKE CONCAT('%', #{pageVO.dictName}, '%')
        </if>
        ORDER BY i.create_time DESC
    </select>

</mapper>
