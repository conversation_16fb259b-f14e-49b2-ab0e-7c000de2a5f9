<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsInboundItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsInboundItemResultMap" type="com.snszyk.simas.spare.entity.SparePartsInboundItem">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="inbound_order_id" property="inboundOrderId"/>
        <result column="dict_id" property="dictId"/>
        <result column="inbound_quantity" property="inboundQuantity"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsInboundItemPage" resultMap="sparePartsInboundItemResultMap">
        select * from simas_spare_parts_inbound_item where is_deleted = 0
    </select>

</mapper>
