/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.spare.dto.SparePartsWarehouseDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.vo.SparePartsWarehouseVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备点巡检标准表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class SparePartsWarehouseWrapper extends BaseEntityWrapper<SparePartsWarehouse, SparePartsWarehouseVO> {

	public static SparePartsWarehouseWrapper build() {
		return new SparePartsWarehouseWrapper();
	}

	@Override
	public SparePartsWarehouseVO entityVO(SparePartsWarehouse entity) {
		SparePartsWarehouseVO vo = Objects.requireNonNull(BeanUtil.copy(entity, SparePartsWarehouseVO.class));
		return vo;
	}

	public SparePartsWarehouse entity(SparePartsWarehouseVO vo) {
		SparePartsWarehouse entity = Objects.requireNonNull(BeanUtil.copy(vo, SparePartsWarehouse.class));
		return entity;
	}

	public SparePartsWarehouseDTO entityDTO(SparePartsWarehouse entity) {
		SparePartsWarehouseDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, SparePartsWarehouseDTO.class));
		if (Func.isNotEmpty(entity.getManager())) {
			User user = UserCache.getUser(entity.getManager());
			if (Func.isNotEmpty(user)) {
				dto.setManagerName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getCreateUser())) {
			User user = UserCache.getUser(entity.getCreateUser());
			if (Func.isNotEmpty(user)) {
				dto.setCreateUserName(user.getRealName());
			}
		}
		if (Func.isNotEmpty(entity.getUpdateUser())) {
			User user = UserCache.getUser(entity.getUpdateUser());
			if (Func.isNotEmpty(user)) {
				dto.setUpdateUserName(user.getRealName());
			}
		}
		return dto;
	}

	public List<SparePartsWarehouseDTO> listDTO(List<SparePartsWarehouse> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
