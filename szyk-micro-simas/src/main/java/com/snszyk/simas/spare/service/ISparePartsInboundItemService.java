/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsInboundItemDTO;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;

import java.util.List;

/**
 * 备品备件入库明细 服务类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISparePartsInboundItemService extends IBaseCrudService<SparePartsInboundItemDTO, SparePartsInboundItemVO> {
	/**
	 * 批量保存
	 *
	 * @param vList
	 * @return
	 */
	Boolean saveBatch(List<SparePartsInboundItemVO> vList);

	/**
	 * 根据入库单id查询入库单明细
	 *
	 * @param inboundOrderId
	 * @return
	 */
	List<SparePartsInboundItemDTO> listByInboundOrderId(Long inboundOrderId);
}
