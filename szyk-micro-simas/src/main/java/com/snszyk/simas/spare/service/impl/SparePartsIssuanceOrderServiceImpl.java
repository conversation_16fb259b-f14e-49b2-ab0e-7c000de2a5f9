/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsIssuanceOrderBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsIssuanceOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsIssuanceOrder;
import com.snszyk.simas.spare.mapper.SparePartsIssuanceOrderMapper;
import com.snszyk.simas.spare.service.ISparePartsIssuanceOrderService;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 备品备件领用单 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@AllArgsConstructor
@Service
public class SparePartsIssuanceOrderServiceImpl extends BaseCrudServiceImpl<SparePartsIssuanceOrderMapper, SparePartsIssuanceOrder, SparePartsIssuanceOrderDTO, SparePartsIssuanceOrderVO> implements ISparePartsIssuanceOrderService {
	@Override
	protected Wrapper<SparePartsIssuanceOrder> beforePage(SparePartsIssuanceOrderVO vo) {
		if (ObjectUtil.isEmpty(vo)) {
			return Wrappers.emptyWrapper();
		}
		return Wrappers.lambdaQuery(SparePartsIssuanceOrder.class)
			.like(ObjectUtil.isNotEmpty(vo.getLikeNo()), SparePartsIssuanceOrder::getNo, vo.getLikeNo())
			.like(ObjectUtil.isNotEmpty(vo.getLikeName()), SparePartsIssuanceOrder::getName, vo.getLikeName())
			.eq(ObjectUtil.isNotEmpty(vo.getStatus()), SparePartsIssuanceOrder::getStatus, vo.getStatus())
			.ge(ObjectUtil.isNotEmpty(vo.getStartCreateTime()), SparePartsIssuanceOrder::getCreateTime, vo.getStartCreateTime())
			.lt(ObjectUtil.isNotEmpty(vo.getEndCreateTime()), SparePartsIssuanceOrder::getCreateTime, vo.getEndCreateTime())
			.orderByDesc(SparePartsIssuanceOrder::getId);
	}

	@Override
	public SparePartsIssuanceOrderDTO save(SparePartsIssuanceOrderVO vo) {
		SparePartsIssuanceOrder entity = SparePartsIssuanceOrderBeanMapper.INSTANCE.toEntity(vo);
		 super.saveOrUpdate(entity);
		 return SparePartsIssuanceOrderBeanMapper.INSTANCE.toDTO(entity);
	}

	@Override
	public Boolean updateStatus(Long id, Integer status) {
		return this.lambdaUpdate()
			.eq(SparePartsIssuanceOrder::getId, id)
			.set(SparePartsIssuanceOrder::getStatus, status)
			.update();
	}
}
