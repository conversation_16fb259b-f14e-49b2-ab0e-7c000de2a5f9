/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.QuantityMapsDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryItemDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.enums.SpareInventoryOrderStatusEnum;
import com.snszyk.simas.spare.mapper.SparePartsInventoryPlanMapper;
import com.snszyk.simas.spare.service.ISparePartsInventoryItemService;
import com.snszyk.simas.spare.service.ISparePartsInventoryOrderService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemOperationVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 备品备件盘点记录表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@AllArgsConstructor
@Service
public class SparePartsInventoryOrderLogicService extends BaseCrudLogicService<SparePartsInventoryOrderDTO, SparePartsInventoryOrderVO> {

	private final ISparePartsInventoryOrderService sparePartsInventoryOrderService;
	private final ISparePartsWarehouseService warehouseService;
	private final SparePartsInventoryItemLogicService itemLogicService;
	private final ISparePartsInventoryItemService itemService;
	private final SparePartsInventoryPlanMapper planMapper;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsInventoryOrderService;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsInventoryOrderDTO> page(SparePartsInventoryOrderPageVO v) {
		// 默认查询已启动的工单
		v.setNeStatus(SpareInventoryOrderStatusEnum.NOT_START.getCode());
		// 分页查询
		IPage<SparePartsInventoryOrderDTO> page = sparePartsInventoryOrderService.pageList(v);
		if (ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		// 获取库房ids
		final List<Long> warehouseIds = ListUtil.distinctMap(page.getRecords(), SparePartsInventoryOrderDTO::getWarehouseId);
		// 获取库房map，key-id,value:名称
		Map<Long, String> warehouseIdToNameMap = getWarehouseMap(warehouseIds);

		// 优化：使用统一查询方法，避免重复数据库调用
		List<Long> orderIds = ListUtil.map(page.getRecords(), SparePartsInventoryOrderDTO::getId);
		QuantityMapsDTO quantityMaps = getInventoryQuantityMaps(orderIds);

		// 填充属性
		page.getRecords().forEach(dto -> populateDto(dto, warehouseIdToNameMap, quantityMaps.getHasInventoryMap(), quantityMaps.getTotalMap()));

		return page;
	}

	/**
	 * 获取工单统计信息（已盘点数量和总数量）
	 * 优化：合并重复查询，使用Java 8兼容语法，返回强类型DTO
	 * <p>
	 * 设计优势：
	 * 1. 性能优化 - 一次数据库查询获取两种统计结果，避免重复查询
	 * 2. 类型安全 - 使用强类型DTO替代Map<String, Map<Long, Long>>
	 * 3. 代码可读性 - 明确的字段名比字符串key更直观
	 * 4. 便于扩展 - 后续可以轻松添加新的统计维度
	 *
	 * @param orderIds 工单ID列表
	 * @return 包含已盘点数量和总数量的DTO
	 */
	public QuantityMapsDTO getInventoryQuantityMaps(List<Long> orderIds) {
		if (ObjectUtil.isEmpty(orderIds)) {
			return QuantityMapsDTO.empty();
		}

		List<SparePartsInventoryItemDTO> itemDTOList = itemService.listByOrderIds(orderIds);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return QuantityMapsDTO.empty();
		}

		// 使用Java 8 Stream API一次性分组并计算统计结果
		Map<Long, List<SparePartsInventoryItemDTO>> groupedByOrder = itemDTOList.stream()
			.collect(Collectors.groupingBy(SparePartsInventoryItemDTO::getInventoryOrderId));

		Map<Long, Long> hasInventoryMap = groupedByOrder.entrySet().stream()
			.collect(Collectors.toMap(
				Map.Entry::getKey,
				entry -> entry.getValue().stream()
					.filter(item -> !SpareInventoryItemResultEnum.NOT_START.getCode().equals(item.getResult()))
					.count()
			));

		Map<Long, Long> totalMap = groupedByOrder.entrySet().stream()
			.collect(Collectors.toMap(
				Map.Entry::getKey,
				entry -> (long) entry.getValue().size()
			));

		return QuantityMapsDTO.of(hasInventoryMap, totalMap);
	}

	/**
	 * 获取库房map
	 *
	 * @param warehouseIds
	 * @return
	 */
	private Map<Long, String> getWarehouseMap(List<Long> warehouseIds) {
		// 根据ids查询库房List
		final List<SparePartsWarehouse> sparePartsWarehouses = warehouseService.listByIds(warehouseIds);
		if (ObjectUtil.isEmpty(sparePartsWarehouses)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(sparePartsWarehouses, SparePartsWarehouse::getId, SparePartsWarehouse::getName);
	}

	/**
	 * 填充dto
	 *
	 * @param dto
	 * @param warehouseIdToNameMap 库房map
	 * @return
	 */
	private void populateDto(SparePartsInventoryOrderDTO dto, Map<Long, String> warehouseIdToNameMap, Map<Long, Long> hasInventoryQuantityMap, Map<Long, Long> totalQuantityMap) {
		// 设置库房名称
		Optional.ofNullable(warehouseIdToNameMap.get(dto.getWarehouseId()))
			.ifPresent(dto::setWarehouseName);
		// 创建人姓名
		Optional.ofNullable(UserCache.getUser(dto.getCreateUser()))
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(UserCache.getUser(dto.getUpdateUser()))
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));
		// 盘点工单状态
		Optional.ofNullable(SpareInventoryOrderStatusEnum.getByCode(dto.getStatus()))
			.ifPresent(statusEnum -> dto.setStatusName(statusEnum.getName()));
		// 封装已盘点数量
		Optional.ofNullable(hasInventoryQuantityMap.getOrDefault(dto.getId(), 0L))
			.ifPresent(hasInventoryQuantity -> dto.setHasInventoryQuantity(Math.toIntExact(hasInventoryQuantity)));
		// 总数
		Optional.ofNullable(totalQuantityMap.getOrDefault(dto.getId(), 0L))
			.ifPresent(totalQuantity -> dto.setTotalQuantity(Math.toIntExact(totalQuantity)));
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public SparePartsInventoryOrderDTO getById(Long id) {
		// 根据 id查询
		final SparePartsInventoryOrderDTO dto = sparePartsInventoryOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			return dto;
		}
		// 查询盘点工单信息
		final SparePartsInventoryPlan plan = planMapper.selectById(dto.getPlanId());
		if (ObjectUtil.isEmpty(plan)) {
			throw new BusinessException("未查询到盘点计划");
		}
		dto.setPlanCreateUserId(plan.getCreateUser());
		Optional.ofNullable(UserCache.getUser(plan.getCreateUser()))
			.ifPresent(user -> dto.setPlanCreateUserName(user.getRealName()));
		// 获取库房名称map
		final Map<Long, String> WarehouseIdToNameMap = new HashMap<>();
		Optional.ofNullable(warehouseService.getById(dto.getWarehouseId()))
			.map(SparePartsWarehouse::getName)
			.ifPresent(name -> WarehouseIdToNameMap.put(dto.getWarehouseId(), name));
		// 使用统一查询方法，避免重复数据库调用
		QuantityMapsDTO quantityMaps = getInventoryQuantityMaps(Lists.newArrayList(id));
		// 填充dto
		populateDto(dto, WarehouseIdToNameMap, quantityMaps.getHasInventoryMap(), quantityMaps.getTotalMap());

		return dto;
	}

	/**
	 * 盘点完成
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean complete(Long id) {
		// 完成盘点校验
		final SparePartsInventoryOrderDTO orderDTO = this.completeValid(id);
		// 根据盘点工单id查询盘点明细
		final List<SparePartsInventoryItemDTO> itemList = itemService.listByOrderIds(Lists.newArrayList(id));
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("盘点工单不存在盘点明细");
		}
		final List<SparePartsInventoryItemOperationVO> itemVOList = itemList.stream()
			.map(item -> {

				final SparePartsInventoryItemOperationVO vo = BeanUtil.copy(item, SparePartsInventoryItemOperationVO.class);
				// 直接完结盘点，盘点数量等于库存数量
				assert vo != null;
				vo.setAfterCountedStock(item.getBeforeSystemStock());
				// 计算盘点结果
				vo.setResultEnum(SpareInventoryItemResultEnum.calculateResult(vo.getBeforeSystemStock(), vo.getAfterCountedStock()));
				return vo;
			}).collect(Collectors.toList());

		// 调用盘点逻辑服务完成盘点
		return itemLogicService.submitBatchInventory(orderDTO.getPlanId(), itemVOList);
	}

	/**
	 * 完成盘点校验
	 *
	 * @param id
	 */
	private SparePartsInventoryOrderDTO completeValid(Long id) {

		final SparePartsInventoryOrderDTO dto = sparePartsInventoryOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("盘点工单不存在");
		}
		if (SpareInventoryOrderStatusEnum.COMPLETE.getCode().equals(dto.getStatus())) {
			throw new BusinessException("盘点工单已完成,无需完成");
		}
		return dto;
	}

}
