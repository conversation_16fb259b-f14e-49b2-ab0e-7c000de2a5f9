/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.config;

import static com.snszyk.simas.spare.constants.InventoryConstants.DataFillFlags.*;

/**
 * 数据填充配置类
 * 优化：从LogicService中抽离，提高代码组织性和复用性
 * 设计理由：
 * 1. 配置类职责单一，便于维护和测试
 * 2. 可在多个Service中复用，避免重复定义
 * 3. 便于扩展新的填充场景
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public final class DataFillConfig {

    private final boolean needDictInfo;
    private final boolean needEnumNames;
    private final boolean needWarehouseNames;

    private DataFillConfig(boolean needDictInfo, boolean needEnumNames, boolean needWarehouseNames) {
        this.needDictInfo = needDictInfo;
        this.needEnumNames = needEnumNames;
        this.needWarehouseNames = needWarehouseNames;
    }

    /**
     * 完整数据填充（默认场景）
     */
    public static DataFillConfig all() {
        return new DataFillConfig(NEED_DICT_INFO, NEED_ENUM_NAMES, NEED_WAREHOUSE_NAMES);
    }

    /**
     * 列表查询场景
     */
    public static DataFillConfig forListQuery() {
        return new DataFillConfig(NEED_DICT_INFO, NEED_ENUM_NAMES, NEED_WAREHOUSE_NAMES);
    }

    /**
     * 详情查询场景
     */
    public static DataFillConfig forDetailQuery() {
        return new DataFillConfig(NEED_DICT_INFO, NEED_ENUM_NAMES, NEED_WAREHOUSE_NAMES);
    }

    /**
     * 状态检查场景（仅需要枚举名称）
     */
    public static DataFillConfig forStatusCheck() {
        return new DataFillConfig(SKIP_DICT_INFO, NEED_ENUM_NAMES, SKIP_WAREHOUSE_NAMES);
    }

    /**
     * 最小化填充（性能优先场景）
     */
    public static DataFillConfig minimal() {
        return new DataFillConfig(SKIP_DICT_INFO, SKIP_ENUM_NAMES, SKIP_WAREHOUSE_NAMES);
    }

    // Getter methods
    public boolean needDictInfo() {
        return needDictInfo;
    }

    public boolean needEnumNames() {
        return needEnumNames;
    }

    public boolean needWarehouseNames() {
        return needWarehouseNames;
    }
}
