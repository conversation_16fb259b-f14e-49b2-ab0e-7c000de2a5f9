/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.crud.Update;
import com.snszyk.simas.spare.dto.SparePartsOutboundOrderDTO;
import com.snszyk.simas.spare.service.logic.SparePartsOutboundOrderLogicService;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderSaveOrUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 备品备件出库单 控制器
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsoutboundorder")
@Api(value = "备品备件出库单", tags = "备品备件出库单接口")
@ApiSupport(author = "zhangzhenpu", order = 54)
@Validated
public class SparePartsOutboundOrderController extends BaseCrudController {

	private final SparePartsOutboundOrderLogicService sparePartsOutboundOrderLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsOutboundOrderLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "SparePartsOutboundOrderVo")
	public R<Boolean> save(@RequestBody @Validated SparePartsOutboundOrderSaveOrUpdateVO v) {
		return R.data(sparePartsOutboundOrderLogicService.save(v));
	}

	/**
	 * 修改
	 *
	 * @param v
	 * @return
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改", notes = "SparePartsOutboundOrderVo")
	public R<Boolean> update(@RequestBody @Validated(value = Update.class) SparePartsOutboundOrderSaveOrUpdateVO v) {
		return R.data(sparePartsOutboundOrderLogicService.update(v));
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "SparePartsOutboundOrderPageVO")
	public R<IPage<SparePartsOutboundOrderDTO>> page(SparePartsOutboundOrderPageVO v) {
		return R.data(sparePartsOutboundOrderLogicService.page(v));
	}


	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<SparePartsOutboundOrderDTO> getById(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsOutboundOrderLogicService.getById(id));
	}

	/**
	 * @param id
	 * @return
	 */
	@PutMapping("/cancel/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "撤销", notes = "传入id")
	public R<Boolean> cancel(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsOutboundOrderLogicService.cancel(id));
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */

	@DeleteMapping("/{id}")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "传入id")
	public R<Boolean> removeById(@NotNull @PathVariable("id") Long id) {
		return R.status(sparePartsOutboundOrderLogicService.removeById(id));
	}


}
