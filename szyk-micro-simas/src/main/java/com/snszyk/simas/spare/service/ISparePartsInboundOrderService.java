/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsInboundOrderDTO;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderVO;

/**
 * 备品备件库存 服务类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface ISparePartsInboundOrderService extends IBaseCrudService<SparePartsInboundOrderDTO, SparePartsInboundOrderVO> {
	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	IPage<SparePartsInboundOrderDTO> pageList(SparePartsInboundOrderPageVO v);
}
