package com.snszyk.simas.spare.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.spare.dto.SparePartsWarehouseDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.vo.SparePartsWarehouseVO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ISparePartsWarehouseService extends BaseService<SparePartsWarehouse> {

	IPage<SparePartsWarehouseDTO> page(IPage<SparePartsWarehouseDTO> page, String keywords);

	boolean submit(SparePartsWarehouseVO vo);

	boolean remove(Long id);

	boolean lock(List<Long> ids);

	boolean unlock(List<Long> ids);

	Map<Long, String> getwarehouseMap(List<Long> ids);

	List<SparePartsWarehouse> listBy(Collection<? extends Serializable> ids, Integer LockStatus);
}
