/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.entity.SparePartsDict;
import com.snszyk.simas.spare.mapper.SparePartsDictMapper;
import com.snszyk.simas.spare.service.ISparePartsDictService;
import com.snszyk.simas.spare.vo.SparePartsDictVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件字典 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class SparePartsDictServiceImpl extends BaseCrudServiceImpl<SparePartsDictMapper, SparePartsDict, SparePartsDictDTO, SparePartsDictVO> implements ISparePartsDictService {

	@Override
	protected Wrapper<SparePartsDict> beforePage(SparePartsDictVO v) {
		if (ObjectUtil.isEmpty(v)) {
			return Wrappers.emptyWrapper();
		}
		return Wrappers.lambdaQuery(SparePartsDict.class)
			.ne(ObjectUtil.isNotEmpty(v.getNeId()), SparePartsDict::getId, v.getNeId())
			.eq(StringUtil.isNotBlank(v.getName()), SparePartsDict::getName, v.getName())
			.eq(StringUtil.isNotBlank(v.getNo()), SparePartsDict::getNo, v.getNo())
			.eq(StringUtil.isNotBlank(v.getModel()), SparePartsDict::getModel, v.getModel())
			.eq(ObjectUtil.isNotEmpty(v.getMeasureUnitId()), SparePartsDict::getMeasureUnitId, v.getMeasureUnitId())
			.like(StringUtil.isNotBlank(v.getLikeName()), SparePartsDict::getName, v.getLikeName())
			.like(StringUtil.isNotBlank(v.getLikeNo()), SparePartsDict::getNo, v.getLikeNo())
			.eq(ObjectUtil.isNotEmpty(v.getStatus()), SparePartsDict::getStatus, v.getStatus())
			.orderByDesc(SparePartsDict::getCreateTime);
	}

	@Override
	public SparePartsDictDTO save(SparePartsDictVO vo) {
		SparePartsDict entity = SparePartsDictBeanMapper.INSTANCE.toEntity(vo);
		super.saveOrUpdate(entity);
		return SparePartsDictBeanMapper.INSTANCE.toDTO(entity);
	}

	@Override
	public Boolean removeByIds(List<Long> ids) {
		return super.removeByIds(ids);
	}

	@Override
	public List<SparePartsDictDTO> listByIds(List<Long> ids) {
		final List<SparePartsDict> partsDictList = super.listByIds(ids);
		return ObjectUtil.isEmpty(partsDictList) ? Collections.emptyList() : BeanUtil.copy(partsDictList, SparePartsDictDTO.class);
	}
}
