/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsOutboundOrderDTO;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderVO;

/**
 * 备品备件出库单 服务类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface ISparePartsOutboundOrderService extends IBaseCrudService<SparePartsOutboundOrderDTO, SparePartsOutboundOrderVO> {
	Boolean updateStatus(Long id, Integer status);

	@Override
	SparePartsOutboundOrderDTO save(SparePartsOutboundOrderVO vo);
}
