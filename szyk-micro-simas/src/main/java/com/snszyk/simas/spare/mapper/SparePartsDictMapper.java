/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsDict;
import com.snszyk.simas.spare.vo.SparePartsDictVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件字典 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface SparePartsDictMapper extends BaseMapper<SparePartsDict> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsDict
	 * @return
	 */
	List<SparePartsDictVO> selectSparePartsDictPage(IPage page, SparePartsDictVO sparePartsDict);

}
