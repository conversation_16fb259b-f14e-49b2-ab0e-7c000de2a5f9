/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsOutboundItemDTO;
import com.snszyk.simas.spare.entity.SparePartsOutboundItem;
import com.snszyk.simas.spare.mapper.SparePartsOutboundItemMapper;
import com.snszyk.simas.spare.service.ISparePartsOutboundItemService;
import com.snszyk.simas.spare.vo.SparePartsOutboundItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件出库明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@AllArgsConstructor
@Service
public class SparePartsOutboundItemServiceImpl extends BaseCrudServiceImpl<SparePartsOutboundItemMapper, SparePartsOutboundItem, SparePartsOutboundItemDTO, SparePartsOutboundItemVO> implements ISparePartsOutboundItemService {


	@Override
	public Boolean saveBatch(List<SparePartsOutboundItemVO> VOList) {
		return super.saveBatch(BeanUtil.copy(VOList, SparePartsOutboundItem.class));
	}

	@Override
	public Boolean deleteByOutboundOrderId(Long outboundOrderId) {
		return this.lambdaUpdate()
			.eq(SparePartsOutboundItem::getOutboundOrderId, outboundOrderId)
			.remove();
	}

	@Override
	public List<SparePartsOutboundItemDTO> listByOrderId(Long orderId) {
		final List<SparePartsOutboundItem> list = this.lambdaQuery()
			.eq(SparePartsOutboundItem::getOutboundOrderId, orderId)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsOutboundItemDTO.class);
	}
}
