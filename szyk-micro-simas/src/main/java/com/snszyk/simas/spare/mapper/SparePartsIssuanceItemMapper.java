/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsIssuanceItem;
import com.snszyk.simas.spare.vo.SparePartsIssuanceItemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件领用单明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface SparePartsIssuanceItemMapper extends BaseMapper<SparePartsIssuanceItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsIssuanceItem
	 * @return
	 */
	List<SparePartsIssuanceItemVO> selectSparePartsIssuanceItemPage(IPage page, SparePartsIssuanceItemVO sparePartsIssuanceItem);

}
