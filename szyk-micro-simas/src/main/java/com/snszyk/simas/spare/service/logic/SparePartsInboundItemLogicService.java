/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.dto.SparePartsInboundItemDTO;
import com.snszyk.simas.spare.service.ISparePartsInboundItemService;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 备品备件入库明细 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsInboundItemLogicService extends BaseCrudLogicService<SparePartsInboundItemDTO, SparePartsInboundItemVO> {

	private final ISparePartsInboundItemService sparePartsInboundItemService;
	private final SparePartsDictLogicService sparePartsDictLogicService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsInboundItemService;
	}

	/**
	 * 保存入库单详情
	 *
	 * @param inboundOrderId 入库单id
	 * @param warehouseId    库房id
	 * @param VOList
	 * @return
	 */
	public Boolean save(Long inboundOrderId, Long warehouseId, List<SparePartsInboundItemVO> VOList) {
		VOList.forEach(v -> {
			v.setInboundOrderId(inboundOrderId);
			v.setWarehouseId(warehouseId);
		});
		return sparePartsInboundItemService.saveBatch(VOList);
	}

	/**
	 * 查询入库单明细
	 *
	 * @param inboundOrderId
	 * @return
	 */
	public List<SparePartsInboundItemDTO> listByInboundOrderId(Long inboundOrderId) {
		// 查询入库单明细
		List<SparePartsInboundItemDTO> itemDTOList = sparePartsInboundItemService.listByInboundOrderId(inboundOrderId);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return Collections.emptyList();
		}
		// 获取备品备件字典ids
		final List<Long> dictIds = ListUtil.map(itemDTOList, SparePartsInboundItemDTO::getDictId);
		// 查询备品备件字典
		Map<Long, SparePartsDictDTO> idToDTOMap = sparePartsDictLogicService.getDictMap(dictIds);

		itemDTOList.forEach(itemDTO -> {
			SparePartsDictDTO dictDTO = idToDTOMap.get(itemDTO.getDictId());
			if (ObjectUtil.isNotEmpty(dictDTO)) {
				SparePartsDictBeanMapper.INSTANCE.setSparePartsInboundItemDTO(itemDTO, dictDTO);
			}
		});
		return itemDTOList;
	}
}
