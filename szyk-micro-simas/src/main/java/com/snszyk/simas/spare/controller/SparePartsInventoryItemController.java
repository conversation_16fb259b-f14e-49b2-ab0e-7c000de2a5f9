/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.spare.dto.SparePartsInventoryItemDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryItemStatisticsDTO;
import com.snszyk.simas.spare.service.logic.SparePartsInventoryItemLogicService;
import com.snszyk.simas.spare.validation.ExistingItemValidationGroup;
import com.snszyk.simas.spare.validation.NewItemValidationGroup;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemOperationVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemPageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;


/**
 * 备品备件盘点记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsinventoryitem")
@Api(value = "备品备件盘点明细", tags = "备品备件盘点明细")
@Validated
@ApiSupport(order = 57, author = "zhangzhenpu")
public class SparePartsInventoryItemController extends BaseCrudController {

	private final SparePartsInventoryItemLogicService sparePartsInventoryItemLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsInventoryItemLogicService;
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "SparePartsInventoryItemVO")
	public R<IPage<SparePartsInventoryItemDTO>> page(@Validated SparePartsInventoryItemPageVO v) {
		return R.data(sparePartsInventoryItemLogicService.page(v));
	}


	@GetMapping("/statistics")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "统计", notes = "SparePartsInventoryItemVO")
	public R<SparePartsInventoryItemStatisticsDTO> getStatistics(@Validated SparePartsInventoryItemPageVO v) {
		return R.data(sparePartsInventoryItemLogicService.getStatistics(v));
	}

	/**
	 * 暂存盘点草稿
	 *
	 * @param voList 盘点明细操作列表
	 * @return 保存结果
	 */
	@PutMapping("/draft")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "暂存盘点草稿", notes = "临时保存盘点数据")
	public R<Boolean> saveDraft(@NotEmpty(message = "盘点明细列表不能为空") @RequestBody List<SparePartsInventoryItemOperationVO> voList) {
		return R.data(sparePartsInventoryItemLogicService.saveDraft(voList));
	}

	/**
	 * 批量明细盘点提交
	 */
	@PutMapping("/submit/{id}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "批量明细盘点提交", notes = "一次性对工单下多个备品备件明细进行盘点提交")
	public R<Boolean> submitBatchInventory(@NotNull @PathVariable("id") Long planId,
	                                     @NotEmpty(message = "盘点明细列表不能为空") @RequestBody List<SparePartsInventoryItemOperationVO> voList) {
		return R.data(sparePartsInventoryItemLogicService.submitBatchInventory(planId, voList));
	}

	/**
	 * 备品备件盘点明细详情查询
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "备品备件盘点明细详情", notes = "根据ID查询单条备品备件盘点明细详情")
	public R<SparePartsInventoryItemDTO> detail(@ApiParam(value = "盘点明细ID", required = true) @NotNull @PathVariable("id") Long id) {
		return R.data(sparePartsInventoryItemLogicService.getById(id));
	}

	/**
	 * 单项明细盘点提交
	 */
	@PutMapping("/app/submit-single")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "单项明细盘点提交", notes = "对单个备品备件明细进行独立盘点提交，支持临时新增和已存在明细")
	public R<Boolean> submitSingleItemInventory(@RequestBody SparePartsInventoryItemOperationVO vo) {
		return R.data(sparePartsInventoryItemLogicService.submitSingleItemInventory(vo));
	}
}
