package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.spare.dto.SparePartsWarehouseDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsWarehouseVO;
import com.snszyk.simas.spare.wrapper.SparePartsWarehouseWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/spare-parts/warehouse")
@Api(value = "备品备件库房管理", tags = "备品备件库房管理表接口")
public class SparePartsWarehouseController extends SzykController {

	private final ISparePartsWarehouseService sparePartsWarehouseService;


	@PostMapping("/submit")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "提交", notes = "传入lubricateMethods")
	public R<Boolean> submit(@Valid @RequestBody SparePartsWarehouseVO vo){
		boolean res =sparePartsWarehouseService.submit(vo);
		if (!res){
			return R.fail("更新失败");
		}
		return R.data(res);
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入no")
	public R<SparePartsWarehouseDTO> detail(String no){
		SparePartsWarehouse warehouse = sparePartsWarehouseService.getOne(Condition.getQueryWrapper(new SparePartsWarehouse().setNo(no)));
		return R.data(SparePartsWarehouseWrapper.build().entityDTO(warehouse));
	}

	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "列表", notes = "传入lubricateMethods")
	public R<List<SparePartsWarehouse>> list(SparePartsWarehouse warehouse){
		return R.data(sparePartsWarehouseService.list(Condition.getQueryWrapper(warehouse)));
	}

	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入keywords")
	public R<IPage<SparePartsWarehouseDTO>> page(@RequestParam(value = "keywords", required = false) String keywords, Query query){
		IPage<SparePartsWarehouseDTO> page = sparePartsWarehouseService.page(Condition.getPage(query), keywords);
		return R.data(page);
	}

	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除", notes = "传入id")
	public R<Boolean> remove(@ApiParam(value = "主键", required = true) @RequestParam Long id){
		return R.status(sparePartsWarehouseService.remove(id));
	}

}
