<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsOutboundItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsOutboundItemResultMap" type="com.snszyk.simas.spare.entity.SparePartsOutboundItem">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="outbound_order_id" property="outboundOrderId"/>
        <result column="stock_id" property="stockId"/>
        <result column="dict_id" property="dictId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="outbound_quantity" property="outboundQuantity"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsOutboundItemPage" resultMap="sparePartsOutboundItemResultMap">
        select * from simas_spare_parts_outbound_item where is_deleted = 0
    </select>

</mapper>
