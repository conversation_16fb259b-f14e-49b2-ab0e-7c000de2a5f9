/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.spare.enums.SpareInventoryPlanStatusEnum;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 盘点计划表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
public class SparePartsInventoryPlanWrapper extends BaseEntityWrapper<SparePartsInventoryPlan, SparePartsInventoryPlanVO> {

	public static SparePartsInventoryPlanWrapper build() {
		return new SparePartsInventoryPlanWrapper();
	}

	@Override
	public SparePartsInventoryPlanVO entityVO(SparePartsInventoryPlan entity) {
		SparePartsInventoryPlanVO vo = Objects.requireNonNull(BeanUtil.copy(entity, SparePartsInventoryPlanVO.class));
		return vo;
	}

	public SparePartsInventoryPlanDTO entityDTO(SparePartsInventoryPlan entity) {
		SparePartsInventoryPlanDTO dto = Objects.requireNonNull(BeanUtil.copy(entity, SparePartsInventoryPlanDTO.class));
		// 盘点人员
		if (Func.isNotEmpty(entity.getInventoryUser())) {
			List<String> userNames = new ArrayList<>();
			String[] users = entity.getInventoryUser().split(",");
			for (String userId : users) {
				User user = UserCache.getUser(Func.toLong(userId));
				if (Func.isNotEmpty(user)) {
					userNames.add(user.getRealName());
				}
			}
			dto.setInventoryUserName(String.join(",", userNames));
		}

		dto.setStatusName(SpareInventoryPlanStatusEnum.getByCode(entity.getStatus()).getName());
		if (Func.isNotEmpty(entity.getCreateUser())) {
			dto.setCreateUserName(UserCache.getUser(entity.getCreateUser()).getRealName());
		}
		if (Func.isNotEmpty(entity.getUpdateUser())) {
			dto.setUpdateUserName(UserCache.getUser(entity.getUpdateUser()).getRealName());
		}
		return dto;
	}


	public List<SparePartsInventoryPlanDTO> listDTO(List<SparePartsInventoryPlan> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
