/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.common.enums.LockStockEnum;
import com.snszyk.simas.common.enums.SimasEnum;
import com.snszyk.simas.common.processor.SparePartsStockChangeLogProcessor;
import com.snszyk.simas.spare.beanmapper.SparePartsIssuanceOrderBeanMapper;
import com.snszyk.simas.spare.beanmapper.SparePartsOutboundOrderBeanMapper;
import com.snszyk.simas.spare.beanmapper.SparePartsStockBeanMapper;
import com.snszyk.simas.spare.constant.SpareConstant;
import com.snszyk.simas.spare.dto.*;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.IssuanceOrderStatusEnum;
import com.snszyk.simas.spare.enums.OutboundOrderStatusEnum;
import com.snszyk.simas.spare.event.SparePartsStockEvent;
import com.snszyk.simas.spare.service.*;
import com.snszyk.simas.spare.vo.*;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 备品备件出库单 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@AllArgsConstructor
@Service
public class SparePartsOutboundOrderLogicService extends BaseCrudLogicService<SparePartsOutboundOrderDTO, SparePartsOutboundOrderVO> {

	private final ISparePartsOutboundOrderService outboundOrderService;
	private final SparePartsOutboundItemLogicService outboundItemLogicService;
	private final ISparePartsOutboundItemService outboundItemService;
	private final ISparePartsWarehouseService warehouseService;
	private final ISparePartsStockService stockService;
	private final ISparePartsIssuanceOrderService issuanceOrderService;
	private final ISparePartsDictService dictService;
	private final ISparePartsIssuanceItemService issuanceItemService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.outboundOrderService;
	}

	/**
	 * 保存或新增
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean save(SparePartsOutboundOrderSaveOrUpdateVO v) {
		// 保存或修改校验
		this.saveOrUpdateValid(v);
		// 转为vo
		SparePartsOutboundOrderVO vo = SparePartsOutboundOrderBeanMapper.INSTANCE.toVO(v, OutboundOrderStatusEnum.IN_COMPLETE.getCode());
		// 生成单号
		vo.setNo(BizCodeUtil.generate(SpareConstant.OUTBOUND_PREFIX));
		// 计算出库总数量
		final BigDecimal totalQuantity = ListUtil.sumByBigDecimalFunction(v.getItemList(), SparePartsOutboundItemVO::getOutboundQuantity);
		vo.setTotalQuantity(totalQuantity);
		// 保存出库单表
		SparePartsOutboundOrderDTO dto = outboundOrderService.save(vo);
		// 保存出库明细表
		outboundItemLogicService.saveBatch(dto.getId(), v.getItemList());
		// 库存表扣减
		this.deductStock(v.getItemList());
		// 请领出库更新领用单表
		updateIssuanceOrderStatusIfNecessary(v.getIssuanceOrderId());
		// 保存日志
		SparePartsStockChangeLogProcessor.saveOutBoundLog(v.getItemList());
		return ObjectUtil.isNotEmpty(dto);
	}

	/**
	 * 修改出库单
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean update(SparePartsOutboundOrderSaveOrUpdateVO v) {
		// 保存或修改校验
		this.saveOrUpdateValid(v);
		// 转为vo
		SparePartsOutboundOrderVO vo = SparePartsOutboundOrderBeanMapper.INSTANCE.toVO(v, OutboundOrderStatusEnum.IN_COMPLETE.getCode());
		// 计算出库总数量
		final BigDecimal totalQuantity = ListUtil.sumByBigDecimalFunction(v.getItemList(), SparePartsOutboundItemVO::getOutboundQuantity);
		vo.setTotalQuantity(totalQuantity);
		// 修改出库单表
		SparePartsOutboundOrderDTO dto = outboundOrderService.save(vo);
		// 删除旧出库明细
		outboundItemService.deleteByOutboundOrderId(dto.getId());
		// 保存出库明细表
		outboundItemLogicService.saveBatch(dto.getId(), v.getItemList());
		// 库存表扣减
		this.deductStock(v.getItemList());
		// 如有必要更新领用单表
		updateIssuanceOrderStatusIfNecessary(v.getIssuanceOrderId());
		// 保存日志
		SparePartsStockChangeLogProcessor.saveOutBoundLog(v.getItemList());
		return true;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsOutboundOrderDTO> page(SparePartsOutboundOrderPageVO v) {
		// 分页查询
		final IPage<SparePartsOutboundOrderDTO> page = outboundOrderService.page(SparePartsOutboundOrderBeanMapper.INSTANCE.toVO(v));
		if (ObjectUtil.isEmpty(page.getPages())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		page.getRecords().forEach(dto -> populateDto(dto));
		return page;
	}

	/**
	 * 出库单详情
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public SparePartsOutboundOrderDTO getById(Long id) {
		// 查询详情
		final SparePartsOutboundOrderDTO dto = outboundOrderService.fetchById(id);
		if (ObjectUtil.isNotEmpty(dto)) {
			// 领用单名称
			Optional.ofNullable(dto.getIssuanceOrderId())
				.map(issuanceOrderService::fetchById)
				.map(SparePartsIssuanceOrderDTO::getName)
				.ifPresent(name -> dto.setIssuanceOrderName(name));
			// 获取库房名称map
			Optional.ofNullable(warehouseService.getById(dto.getWarehouseId()))
				.map(SparePartsWarehouse::getName)
				.ifPresent(name -> dto.setWarehouseName(name));
			// 填充dto
			populateDto(dto);
			// 查询出库明细
			final List<SparePartsOutboundItemDTO> itemList = outboundItemLogicService.listByOrderId(id);
			dto.setItemList(itemList);
		}
		return dto;
	}

	/**
	 * 获取库房名称Map
	 *
	 * @param dto
	 * @return
	 */
	private void setWarehouseMap(SparePartsOutboundOrderDTO dto) {
		if (ObjectUtil.isEmpty(dto)) {
			return;
		}
		List<Long> warehouseIds = Lists.newArrayList();
		// 其他出库
		if (ObjectUtil.isNotEmpty(dto.getWarehouseId())) {
			warehouseIds.add(dto.getWarehouseId());
		}
		// 请领出库
		if (ObjectUtil.isNotEmpty(dto.getIssuanceOrderId())) {
			// 请领明细
			final List<SparePartsIssuanceItemDTO> issuanceItemDTOList = issuanceItemService.listByIssuanceOrderId(dto.getIssuanceOrderId());
			if (ObjectUtil.isNotEmpty(issuanceItemDTOList)) {
				warehouseIds = ListUtil.distinctMap(issuanceItemDTOList, SparePartsIssuanceItemDTO::getWarehouseId);
			}
		}
		if (ObjectUtil.isNotEmpty(warehouseIds)) {
			// 仓库LIst
			final List<SparePartsWarehouse> warehouseList = warehouseService.listByIds(warehouseIds);
			if (ObjectUtil.isNotEmpty(warehouseList)) {
				final String warehouseNames = warehouseList.stream()
					.map(SparePartsWarehouse::getName)
					.collect(Collectors.joining(StringPool.COMMA));
				dto.setWarehouseName(warehouseNames);
			}
		}

	}

	/**
	 * 撤销
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean cancel(Long id) {
		// 撤销校验
		final SparePartsOutboundOrderDTO dto = this.cancelValid(id);
		// 修改出库单状态为撤销
		final Boolean flag = outboundOrderService.updateStatus(id, OutboundOrderStatusEnum.CANCEL.getCode());
		// 恢复领用单状态
		this.recoverIssuanceOrderStatusIfNecessary(dto.getIssuanceOrderId());
		// 出库撤销后，备件库存数量恢复
		this.recoverStock(dto.getId());
		return flag;
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	public Boolean removeById(Long id) {
		// 删除校验
		this.removeValid(id);
		return this.outboundOrderService.deleteById(id);
	}

	/**
	 * 撤销后恢复库存
	 *
	 * @param id
	 * @return
	 */
	private Boolean recoverStock(Long id) {
		// 查询出库明细
		final List<SparePartsOutboundItemDTO> itemList = outboundItemLogicService.listByOrderId(id);
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("撤销失败！未查询到出库明细！");
		}
		// 出库id-出库数量map
		final Map<Long, BigDecimal> stockIdToOutboundQuantityMap = ListUtil.toMap(itemList, SparePartsOutboundItemDTO::getStockId, SparePartsOutboundItemDTO::getOutboundQuantity);
		// 查询库存表
		final List<SparePartsStockDTO> stockDTOList = stockService.listByIds(Lists.newArrayList(stockIdToOutboundQuantityMap.keySet()));
		if (ObjectUtil.isEmpty(stockDTOList)) {
			throw new BusinessException("撤销失败！未查询到库存信息");
		}
		final List<SparePartsStockVO> stockVOList = stockDTOList.stream()
			.map(dto -> {
				SparePartsStockVO stockVO = SparePartsStockBeanMapper.INSTANCE.toVO(dto);
				// 出库数量
				final BigDecimal outboundQuantity = stockIdToOutboundQuantityMap.get(dto.getId());
				// 恢复库存
				stockVO.setCurrentQuantity(dto.getCurrentQuantity().add(outboundQuantity));
				return stockVO;
			}).collect(Collectors.toList());
		return ObjectUtil.isNotEmpty(stockService.saveOrUpdateBatch(stockVOList));
	}

	/**
	 * 恢复领用单状态
	 *
	 * @param issuanceOrderId
	 */
	private Boolean recoverIssuanceOrderStatusIfNecessary(Long issuanceOrderId) {
		if (ObjectUtil.isEmpty(issuanceOrderId)) {
			return false;
		}
		return issuanceOrderService.updateStatus(issuanceOrderId, IssuanceOrderStatusEnum.WAIT_CHECKOUT.getCode());
	}

	/**
	 * 填充字段
	 *
	 * @param dto
	 */
	private void populateDto(SparePartsOutboundOrderDTO dto) {
		// 领用部门名称
		Optional.ofNullable(SysCache.getDept(dto.getReceiveDeptId()))
			.ifPresent(dept -> dto.setReceiveDeptName(dept.getDeptName()));
		// 领用人姓名
		Optional.ofNullable(UserCache.getUser(dto.getReceiveUserId()))
			.ifPresent(user -> dto.setReceiveUserName(user.getRealName()));
		// 创建人姓名
		Optional.ofNullable(UserCache.getUser(dto.getCreateUser()))
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(UserCache.getUser(dto.getUpdateUser()))
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));
		// 出库类型名称
		dto.setOutboundTypeName(DictBizCache.getValue(SimasEnum.OUTBOUND_TYPE.getName(), dto.getOutboundType()));
		// 出库状态名称
		Optional.ofNullable(OutboundOrderStatusEnum.getByCode(dto.getStatus()))
			.ifPresent(outboundOrderStatusEnum -> dto.setStatusName(outboundOrderStatusEnum.getName()));
		// 用途名称
		dto.setOutboundUseName(DictBizCache.getValue(SimasEnum.OUTBOUND_USE.getName(), dto.getOutboundUse()));
		// 是否允许撤销
		dto.setAllowCancel(isAllowCancel(dto.getCreateTime()));
		// 设置出库库房名称map
		this.setWarehouseMap(dto);
	}

	/**
	 * 库存扣减
	 *
	 * @param itemList
	 */
	private void deductStock(List<SparePartsOutboundItemVO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("库存扣减失败！备品备件不可为空！");
		}
		// 前端传来备件库存id-出库数量Map
		final Map<Long, BigDecimal> webStockIdToQuantityMap = ListUtil.toMap(itemList, SparePartsOutboundItemVO::getStockId, SparePartsOutboundItemVO::getOutboundQuantity);
		// 备件库存ids
		final List<Long> stockIds = ListUtil.map(itemList, SparePartsOutboundItemVO::getStockId);
		// 查询库存DTOList
		List<SparePartsStockDTO> stockDTOList = stockService.listByIds(stockIds);

		if (ObjectUtil.isEmpty(stockDTOList)) {
			throw new BusinessException("库存扣减失败！库存不存在！");
		}
		// 转vo
		final List<SparePartsStockVO> stockVOList = stockDTOList.stream()
			.map(stockDTO -> {
				SparePartsStockVO vo = SparePartsStockBeanMapper.INSTANCE.toVO(stockDTO);
				// 计算扣减后的数量
				final BigDecimal currentQuantity = vo.getCurrentQuantity().subtract(webStockIdToQuantityMap.get(vo.getId()));
				// final Integer currentQuantity = vo.getCurrentQuantity() - webStockIdToQuantityMap.get(vo.getId());
				if (currentQuantity.compareTo(new BigDecimal(BigInteger.ZERO)) < 0) {
					throw new BusinessException("库存扣减失败！库存不足！");
				}
				vo.setCurrentQuantity(currentQuantity);
				return vo;
			}).collect(Collectors.toList());
		// 修改库存表
		stockDTOList = stockService.saveOrUpdateBatch(stockVOList);

		SpringUtil.publishEvent(new SparePartsStockEvent(this, stockDTOList));
	}

	/**
	 * 如有必要更新领用单表状态
	 *
	 * @param issuanceOrderId
	 */
	private void updateIssuanceOrderStatusIfNecessary(Long issuanceOrderId) {
		if (ObjectUtil.isEmpty(issuanceOrderId)) {
			return;
		}
		// 查询领用单
		final SparePartsIssuanceOrderDTO issuanceOrderDTO = issuanceOrderService.fetchById(issuanceOrderId);
		if (ObjectUtil.isEmpty(issuanceOrderDTO)) {
			throw new BusinessException("更新领用单不存在");
		}
		// 转vo
		final SparePartsIssuanceOrderVO issuanceOrderVO = SparePartsIssuanceOrderBeanMapper.INSTANCE.toVO(issuanceOrderDTO);
		// 更新状态为完结
		issuanceOrderVO.setStatus(IssuanceOrderStatusEnum.IS_COMPLETED.getCode());
		issuanceOrderService.save(issuanceOrderVO);

	}

	/**
	 * 保存或修改校验
	 *
	 * @param v
	 */
	private void saveOrUpdateValid(SparePartsOutboundOrderSaveOrUpdateVO v) {
		// 仓库状态校验
		this.warehouseValid(v);
		// 库存校验
		this.stockValid(v.getItemList());
		// 请领出库需校验请领单状态
		this.issuanceOrderValid(v.getIssuanceOrderId());
		// 修改出库单需校验出库状态
		this.outboundOrderValidIfUpdate(v.getId());

	}

	/**
	 * 出库单状态校验，修改时校验
	 *
	 * @param id
	 */
	private void outboundOrderValidIfUpdate(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			return;
		}
		// 查询出库单
		final SparePartsOutboundOrderDTO outboundOrderDTO = outboundOrderService.fetchById(id);
		// 只有撤销状态出库单才可以修改
		if (!OutboundOrderStatusEnum.CANCEL.getCode().equals(outboundOrderDTO.getStatus())) {
			throw new BusinessException("出库单状态异常！出库单状态为【" + OutboundOrderStatusEnum.getByCode(outboundOrderDTO.getStatus()) + "】，不允许修改！");
		}
	}

	/**
	 * 请领单校验
	 *
	 * @param issuanceOrderId
	 */
	private void issuanceOrderValid(Long issuanceOrderId) {
		if (ObjectUtil.isEmpty(issuanceOrderId)) {
			return;
		}
		// 查询请领单
		final SparePartsIssuanceOrderDTO issuanceOrderDTO = issuanceOrderService.fetchById(issuanceOrderId);
		if (ObjectUtil.isEmpty(issuanceOrderDTO)) {
			throw new BusinessException("请领单不存在");
		}
		// 只有已审核待出库状态的请领单才可以出库
		if (!IssuanceOrderStatusEnum.WAIT_CHECKOUT.getCode().equals(issuanceOrderDTO.getStatus())) {
			// 请领单状态名称
			final String issuanceOrderStatusName = IssuanceOrderStatusEnum.getByCode(issuanceOrderDTO.getStatus()).getName();

			throw new BusinessException("出库失败！请领单状态异常！请领单状态为【" + issuanceOrderStatusName + "】，请领单状态为【待出库】才可以出库！");
		}
	}

	/**
	 * 库存校验
	 *
	 * @param itemList
	 */
	private void stockValid(List<SparePartsOutboundItemVO> itemList) {
		// 库存id-出库数量Map
		final Map<Long, BigDecimal> stockIdToOutboundQuantity = ListUtil.toMap(itemList, SparePartsOutboundItemVO::getStockId, SparePartsOutboundItemVO::getOutboundQuantity);
		// 获取库存ids
		final List<Long> stockIds = ListUtil.distinctMap(itemList, SparePartsOutboundItemVO::getStockId);
		// 根据库存ids查询库存信息
		List<SparePartsStockDTO> dbStockDTOList = stockService.listByIds(stockIds);

		dbStockDTOList.forEach(dbStockDTO -> {
			// 获取前端传来的出库数量
			final BigDecimal webOutboundQuantity = stockIdToOutboundQuantity.get(dbStockDTO.getId());
			// 获取当前库存
			final BigDecimal dbCurrentQuantity = dbStockDTO.getCurrentQuantity();
			// 库存不足
			if (dbCurrentQuantity.compareTo(dbCurrentQuantity) < 0) {
				final SparePartsDictDTO dictDTO = dictService.fetchById(dbStockDTO.getDictId());
				if (ObjectUtil.isEmpty(dbStockDTO)) {
					throw new BusinessException("备品备件【" + dbStockDTO.getDictName() + "】不存在！");
				}
				throw new BusinessException("备品备件【" + dictDTO.getName() + "】库存不足！当前库存为【" + dbCurrentQuantity + "】，出库数量为【" + webOutboundQuantity + "】");
			}
		});

	}

	/**
	 * 仓库锁定状态校验
	 *
	 * @param v
	 */
	private void warehouseValid(SparePartsOutboundOrderSaveOrUpdateVO v) {
		// 查出仓库ids
		final List<Long> warehouseIds = ListUtil.distinctMap(v.getItemList(), SparePartsOutboundItemVO::getWarehouseId);
		// 查询仓库信息
		List<SparePartsWarehouse> warehouseList = warehouseService.listByIds(warehouseIds);
		if (ObjectUtil.isEmpty(warehouseList)) {
			throw new BusinessException("新增出库单失败！未查询到仓库信息");
		}
		final String lockWarehouseNames = warehouseList.stream()
			.filter(warehouse -> LockStockEnum.LOCK.getCode().equals(warehouse.getLockStock()))
			.map(SparePartsWarehouse::getName)
			.collect(Collectors.joining(StringPool.COMMA));
		if (ObjectUtil.isNotEmpty(lockWarehouseNames)) {
			throw new BusinessException("新增出库单失败！仓库【" + lockWarehouseNames + "】已锁定，不允许操作");
		}
	}


	/**
	 * 撤销校验
	 *
	 * @param id
	 */
	private SparePartsOutboundOrderDTO cancelValid(Long id) {
		// 查询出库单
		final SparePartsOutboundOrderDTO dto = outboundOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("撤销失败！出库单不存在");
		}
		// 查询出库单明细
		final List<SparePartsOutboundItemDTO> itemList = outboundItemLogicService.listByOrderId(id);
		if (ObjectUtil.isNotEmpty(itemList)) {
			// 获取明细上所有仓库ids
			final List<Long> warehouseIds = ListUtil.distinctMap(itemList, SparePartsOutboundItemDTO::getWarehouseId);
			// 查询锁定状态的仓库
			List<SparePartsWarehouse> lockedWarehouseList = warehouseService.listBy(warehouseIds, LockStockEnum.LOCK.getCode());
			if (ObjectUtil.isNotEmpty(lockedWarehouseList)) {
				// 已锁定的仓库名称
				final String lockedWarehouseNames = lockedWarehouseList.stream()
					.map(SparePartsWarehouse::getName)
					.collect(Collectors.joining(StringPool.COMMA));
				throw new BusinessException("撤销失败！仓库【" + lockedWarehouseNames + "】已锁定，不允许操作");
			}

		}
		// 单据在完成24小时后，不可撤销
		if (!isAllowCancel(dto.getCreateTime())) {
			throw new BusinessException("单据在完成24小时后，不可撤销");
		}
		return dto;
	}

	/**
	 * 是否允许撤销
	 * 超过24小时后不允许撤销
	 *
	 * @param createTime 创建时间
	 * @return
	 */
	private static Boolean isAllowCancel(Date createTime) {
		if (ObjectUtil.isEmpty(createTime)) {
			return false;
		}
		// 单据在完成24小时后，不可撤销
		if (DateUtil.plusDays(createTime, 1).before(DateUtil.now())) {
			return false;
		}
		return true;
	}


	/**
	 * 删除校验
	 *
	 * @param id
	 */
	private void removeValid(Long id) {
		// 根据id 查询出库单
		final SparePartsOutboundOrderDTO dto = outboundOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("删除失败！出库单不存在");
		}
		// 只有撤销状态的出库单才能删除
		if (!OutboundOrderStatusEnum.CANCEL.getCode().equals(dto.getStatus())) {
			throw new BusinessException("删除失败！出库单状态为【" + OutboundOrderStatusEnum.getByCode(dto.getStatus()).getName() + "】，不能删除");
		}
	}
}
