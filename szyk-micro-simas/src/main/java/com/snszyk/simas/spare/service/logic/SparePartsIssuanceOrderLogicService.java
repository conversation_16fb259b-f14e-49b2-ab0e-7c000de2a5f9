/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.simas.common.enums.AuditStatusEnum;
import com.snszyk.simas.common.enums.LockStockEnum;
import com.snszyk.simas.common.processor.SparePartsReceiveLogProcessor;
import com.snszyk.simas.spare.beanmapper.SparePartsIssuanceOrderBeanMapper;
import com.snszyk.simas.spare.constant.SpareConstant;
import com.snszyk.simas.spare.dto.SparePartsIssuanceItemDTO;
import com.snszyk.simas.spare.dto.SparePartsIssuanceOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.IssuanceOrderStatusEnum;
import com.snszyk.simas.spare.enums.SparePartsIssuanceOrderActionEnum;
import com.snszyk.simas.spare.service.ISparePartsIssuanceItemService;
import com.snszyk.simas.spare.service.ISparePartsIssuanceOrderService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.*;
import com.snszyk.system.cache.SysCache;
import com.snszyk.user.entity.User;
import com.snszyk.user.feign.IUserClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 备品备件领用单 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@AllArgsConstructor
@Service
public class SparePartsIssuanceOrderLogicService extends BaseCrudLogicService<SparePartsIssuanceOrderDTO, SparePartsIssuanceOrderVO> {

	private final ISparePartsIssuanceOrderService sparePartsIssuanceOrderService;
	private final ISparePartsIssuanceItemService issuanceItemService;
	private final SparePartsIssuanceItemLogicService issuanceItemLogicService;
	private final IUserClient userClient;
	private final ISparePartsWarehouseService sparePartsWarehouseService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsIssuanceOrderService;
	}

	/**
	 * 分页
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsIssuanceOrderDTO> page(SparePartsIssuanceOrderPageVO v) {
		// 分页查询
		final IPage<SparePartsIssuanceOrderDTO> page = sparePartsIssuanceOrderService.page(SparePartsIssuanceOrderBeanMapper.INSTANCE.toVO(v));
		if (ObjectUtil.isEmpty(page.getPages())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		// 并合并领用人、审核人、创建人、更新人id
		final Set<Long> userIds = mergeUserIds(page.getRecords());
		// 获取userMap
		Map<Long, String> userIdToNameMap = this.getUserIdToNameMap(userIds);
		// 填充字段
		page.getRecords().forEach(dto -> populateDto(dto, userIdToNameMap));
		return page;
	}

	/**
	 * 保存或修改
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveOrUpdate(SparePartsIssuanceOrderSaveOrUpdateVO v) {
		// 领用校验
		this.saveCheck(v);

		SparePartsIssuanceOrderActionEnum actionEnum = SparePartsIssuanceOrderActionEnum.INIT;
		// 保存领用单表
		final SparePartsIssuanceOrderVO vo = BeanUtil.copy(v, SparePartsIssuanceOrderVO.class);
		// 计算领用备件总数
		BigDecimal totalQuantity = ListUtil.sumByBigDecimalFunction(v.getItemList(), SparePartsIssuanceItemVO::getIssuanceQuantity);
		vo.setTotalQuantity(totalQuantity);
		vo.setStatus(IssuanceOrderStatusEnum.WAIT.getCode());
		// 新增生成单号
		if (ObjectUtil.isEmpty(v.getId())) {
			// 生成单号
			vo.setNo(BizCodeUtil.generate(SpareConstant.ISSUANCE_PREFIX));
		} else {
			// 删除旧领用明细
			issuanceItemService.deleteByIssuanceOrderId(vo.getId());
			actionEnum = SparePartsIssuanceOrderActionEnum.RE_SUBMIT;
		}
		final SparePartsIssuanceOrderDTO dto = sparePartsIssuanceOrderService.save(vo);
		// 保存领用明细表
		Boolean flag = issuanceItemLogicService.saveBatch(dto.getId(), v.getItemList());
		// 保存日志
		SparePartsReceiveLogProcessor.saveBizLog(actionEnum, dto.getId(), JSON.toJSONString(vo), null);
		return flag;
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public SparePartsIssuanceOrderDTO getById(Long id) {
		final SparePartsIssuanceOrderDTO dto = sparePartsIssuanceOrderService.fetchById(id);
		if (ObjectUtil.isNotEmpty(dto)) {
			// 合并用户id
			final Set<Long> userIds = mergeUserIds(Lists.newArrayList(dto));
			// 根据id 获取用户
			final Map<Long, String> userIdToNameMap = getUserIdToNameMap(userIds);
			populateDto(dto, userIdToNameMap);
			// 查询领用明细
			final List<SparePartsIssuanceItemDTO> itemList = issuanceItemLogicService.listByIssuanceOrderId(id);
			dto.setItemList(itemList);
		}
		return dto;
	}

	/**
	 * 撤回领用单
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean cancel(Long id) {
		// 查询领用单
		final SparePartsIssuanceOrderDTO issuanceOrderDTO = this.sparePartsIssuanceOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(issuanceOrderDTO)) {
			throw new BusinessException("领用单不存在");
		}
		if (IssuanceOrderStatusEnum.IS_COMPLETED.getCode().equals(issuanceOrderDTO.getStatus())) {
			throw new BusinessException("已完成的领用单不能撤回");
		}
		SparePartsIssuanceOrderVO vo = SparePartsIssuanceOrderBeanMapper.INSTANCE.toVO(issuanceOrderDTO);
		vo.setStatus(IssuanceOrderStatusEnum.CANCEL.getCode());
		// 修改领用单状态
		final SparePartsIssuanceOrderDTO dto = this.sparePartsIssuanceOrderService.save(vo);
		// 保存日志
		SparePartsReceiveLogProcessor.saveBizLog(SparePartsIssuanceOrderActionEnum.CANCEL, id, JSON.toJSONString(id), null);
		return ObjectUtil.isNotEmpty(dto);
	}

	/**
	 * 审核
	 *
	 * @param id
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean approval(Long id, SparePartsIssuanceOrderApprovalVO v) {
		// 查询领用单
		SparePartsIssuanceOrderDTO dto = this.sparePartsIssuanceOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("领用单不存在");
		}
		// 非待审核状态
		if (!IssuanceOrderStatusEnum.WAIT.getCode().equals(dto.getStatus())) {
			throw new BusinessException("审批失败！领用单状态异常");
		}
		SparePartsIssuanceOrderActionEnum actionEnum = null;
		// dto转换
		SparePartsIssuanceOrderVO vo = SparePartsIssuanceOrderBeanMapper.INSTANCE.toVO(dto);
		vo.setAuditTime(LocalDateTime.now());
		vo.setAuditUserId(AuthUtil.getUserId());
		vo.setRejectReason(v.getRejectReason());
		// 审核通过
		if (v.getAuditStatusEnum() == AuditStatusEnum.PASS) {
			vo.setStatus(IssuanceOrderStatusEnum.WAIT_CHECKOUT.getCode());
			actionEnum = SparePartsIssuanceOrderActionEnum.AUDIT_PASS;
		} else {
			vo.setStatus(IssuanceOrderStatusEnum.REJECT.getCode());
			actionEnum = SparePartsIssuanceOrderActionEnum.AUDIT_FAIL;
		}
		// 保存审核信息
		dto = this.sparePartsIssuanceOrderService.save(vo);
		// 保存日志
		SparePartsReceiveLogProcessor.saveBizLog(actionEnum, vo.getId(), JSON.toJSONString(vo), vo.getRejectReason());
		return ObjectUtil.isNotEmpty(dto);
	}

	/**
	 * 领用单保存校验
	 *
	 * @param v
	 */
	private void saveCheck(SparePartsIssuanceOrderSaveOrUpdateVO v) {
		// 查出仓库ids
		final List<Long> warehouseIds = ListUtil.distinctMap(v.getItemList(), SparePartsIssuanceItemVO::getWarehouseId);
		// 校验仓库状态是否锁定
		List<SparePartsWarehouse> lockedWarehouseList = sparePartsWarehouseService.listBy(warehouseIds, LockStockEnum.LOCK.getCode());
		if (ObjectUtil.isNotEmpty(lockedWarehouseList)) {
			// 已锁定的仓库名称
			final String lockedWarehouseNames = lockedWarehouseList.stream()
				.map(SparePartsWarehouse::getName)
				.collect(Collectors.joining(StringPool.COMMA));
			throw new BusinessException("新增领用单失败！仓库【" + lockedWarehouseNames + "】已锁定，不允许操作");
		}
	}

	/**
	 * 填充字段
	 *
	 * @param dto
	 * @param userIdToNameMap
	 */
	private void populateDto(SparePartsIssuanceOrderDTO dto, Map<Long, String> userIdToNameMap) {
		// 领用人姓名
		Optional.ofNullable(userIdToNameMap.get(dto.getReceiveUserId()))
			.ifPresent(dto::setReceiveUserName);
		// 审核人姓名
		Optional.ofNullable(userIdToNameMap.get(dto.getAuditUserId()))
			.ifPresent(dto::setAuditUserName);
		// 创建人姓名
		Optional.ofNullable(userIdToNameMap.get(dto.getCreateUser()))
			.ifPresent(dto::setCreateUserName);
		// 修改人姓名
		Optional.ofNullable(userIdToNameMap.get(dto.getUpdateUser()))
			.ifPresent(dto::setUpdateUserName);
		// 部门名称
		Optional.ofNullable(SysCache.getDept(dto.getReceiveDeptId()))
			.ifPresent(dept -> dto.setReceiveDeptName(dept.getDeptName()));
		// 领用状态名称
		Optional.ofNullable(IssuanceOrderStatusEnum.getByCode(dto.getStatus()))
			.ifPresent(statusEnum -> dto.setStatusName(statusEnum.getName()));

	}

	/**
	 * 获取用户id和用户名map
	 *
	 * @param userIds
	 * @return
	 */
	private Map<Long, String> getUserIdToNameMap(Set<Long> userIds) {
		if (ObjectUtil.isEmpty(userIds)) {
			return MapUtil.empty();
		}
		final R<List<User>> listR = userClient.userListByIds(Lists.newArrayList(userIds));
		if (ObjectUtil.isEmpty(listR) || ObjectUtil.isEmpty(listR.getData())) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(listR.getData(), User::getId, User::getName);
	}

	/**
	 * 合并用户id(领用人、创建人、更新人id)
	 *
	 * @param dtoList
	 * @return
	 */
	private Set<Long> mergeUserIds(List<SparePartsIssuanceOrderDTO> dtoList) {
		if (ObjectUtil.isEmpty(dtoList)) {
			return Collections.emptySet();
		}
		return dtoList.stream()
			.flatMap(dto -> Stream.of(dto.getCreateUser(), dto.getUpdateUser(), dto.getReceiveUserId(), dto.getAuditUserId()))
			.collect(Collectors.toSet());
	}

}
