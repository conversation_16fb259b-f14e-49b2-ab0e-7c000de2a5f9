/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsIssuanceItemDTO;
import com.snszyk.simas.spare.vo.SparePartsIssuanceItemVO;

import java.util.List;

/**
 * 备品备件领用单明细 服务类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface ISparePartsIssuanceItemService extends IBaseCrudService<SparePartsIssuanceItemDTO, SparePartsIssuanceItemVO> {

	Boolean deleteByIssuanceOrderId(Long issuanceOrderId);

	Boolean saveBatch(List<SparePartsIssuanceItemVO> itemList);

	List<SparePartsIssuanceItemDTO> listByIssuanceOrderId(Long issuanceOrderId);
}
