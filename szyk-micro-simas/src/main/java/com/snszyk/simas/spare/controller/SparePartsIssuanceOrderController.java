/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.crud.Update;
import com.snszyk.simas.spare.dto.SparePartsIssuanceOrderDTO;
import com.snszyk.simas.spare.service.logic.SparePartsIssuanceOrderLogicService;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderApprovalVO;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderSaveOrUpdateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 备品备件领用单 控制器
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsissuanceorder")
@Api(value = "备品备件领用单", tags = "备品备件领用单接口")
@ApiSupport(author = "zhangzhenpu", order = 53)
@Validated
public class SparePartsIssuanceOrderController extends BaseCrudController {

	private final SparePartsIssuanceOrderLogicService sparePartsIssuanceOrderLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsIssuanceOrderLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "SparePartsIssuanceOrderVo")
	public R<Boolean> save(@RequestBody @Validated SparePartsIssuanceOrderSaveOrUpdateVO v) {
		return R.data(sparePartsIssuanceOrderLogicService.saveOrUpdate(v));
	}

	/**
	 * 保存
	 */
	@PutMapping("/update")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改", notes = "SparePartsIssuanceOrderVo")
	public R<Boolean> update(@RequestBody @Validated(value = {Update.class}) SparePartsIssuanceOrderSaveOrUpdateVO v) {
		return R.data(sparePartsIssuanceOrderLogicService.saveOrUpdate(v));
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "SparePartsIssuanceOrderPageVO")
	public R<IPage<SparePartsIssuanceOrderDTO>> page(SparePartsIssuanceOrderPageVO v) {
		return R.data(sparePartsIssuanceOrderLogicService.page(v));
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<SparePartsIssuanceOrderDTO> getById(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsIssuanceOrderLogicService.getById(id));
	}

	/**
	 * @param id
	 * @return
	 */

	@PutMapping("/cancel/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "撤销", notes = "传入id")
	public R<Boolean> cancel(@PathVariable("id") @NotNull Long id) {
		return R.data(sparePartsIssuanceOrderLogicService.cancel(id));
	}

	/**
	 * 审核
	 */
	@PutMapping("/approval/{id}")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "审核", notes = "传入id")
	public R<Boolean> approval(@PathVariable("id") @NotNull Long id, @RequestBody @NotNull SparePartsIssuanceOrderApprovalVO v) {
		return R.data(sparePartsIssuanceOrderLogicService.approval(id, v));
	}

}
