/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsInboundItemDTO;
import com.snszyk.simas.spare.entity.SparePartsInboundItem;
import com.snszyk.simas.spare.mapper.SparePartsInboundItemMapper;
import com.snszyk.simas.spare.service.ISparePartsInboundItemService;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件入库明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsInboundItemServiceImpl extends BaseCrudServiceImpl<SparePartsInboundItemMapper, SparePartsInboundItem, SparePartsInboundItemDTO, SparePartsInboundItemVO> implements ISparePartsInboundItemService {


	@Override
	public Boolean saveBatch(List<SparePartsInboundItemVO> vList) {
		return super.saveBatch(BeanUtil.copy(vList, SparePartsInboundItem.class));
	}

	@Override
	public List<SparePartsInboundItemDTO> listByInboundOrderId(Long inboundOrderId) {
		final List<SparePartsInboundItem> list = this.lambdaQuery()
			.eq(SparePartsInboundItem::getInboundOrderId, inboundOrderId)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, SparePartsInboundItemDTO.class);

	}
}
