package com.snszyk.simas.spare.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.dto.DictDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanCountDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanQueryVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanVO;

import java.util.Date;
import java.util.List;

public interface ISparePartsInventoryPlanService extends BaseService<SparePartsInventoryPlan> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<SparePartsInventoryPlanDTO> page(IPage<SparePartsInventoryPlan> page, SparePartsInventoryPlanQueryVO vo);

	/**
	 * 详情
	 *
	 * @param no
	 * @return
	 */
	SparePartsInventoryPlanDTO detail(String no);

	/**
	 * 提交
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(SparePartsInventoryPlanVO vo);

	/**
	 * 启动
	 *
	 * @param no
	 * @return
	 */
	boolean start(String no);

	/**
	 * 停止
	 *
	 * @param no
	 * @return
	 */
	boolean stop(String no);

	/**
	 * 统计
	 *
	 * @param module
	 * @return
	 */
	SparePartsInventoryPlanCountDTO count(String module);

	List<DictDTO> getPlanWarehouse(Long id);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<SparePartsInventoryPlanDTO> appPage(IPage<SparePartsInventoryPlan> page, SparePartsInventoryPlanQueryVO vo);


	/**
	 * 移动端详情
	 *
	 * @param no
	 * @return
	 */
	SparePartsInventoryPlanDTO appDetail(String no);

	/**
	 * @param leStartDate 小于等于开始时间
	 * @param status      状态
	 * @return
	 */
	List<SparePartsInventoryPlanDTO> listBy(Date leStartDate, Integer status);
}
