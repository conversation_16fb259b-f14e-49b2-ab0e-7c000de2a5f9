/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsIssuanceOrderDTO;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderVO;

/**
 * 备品备件领用单 服务类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
public interface ISparePartsIssuanceOrderService extends IBaseCrudService<SparePartsIssuanceOrderDTO, SparePartsIssuanceOrderVO> {

	Boolean updateStatus(Long id, Integer status);

	@Override
	SparePartsIssuanceOrderDTO save(SparePartsIssuanceOrderVO vo);
}
