/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;

import java.util.List;

/**
 * 备品备件盘点记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface ISparePartsInventoryOrderService extends IBaseCrudService<SparePartsInventoryOrderDTO, SparePartsInventoryOrderVO> {

	Boolean updateBatchByIds(List<SparePartsInventoryOrderVO> orderVOList);

	Boolean saveOrUpdateBatch(List<SparePartsInventoryOrderVO> orderVOList);

	IPage<SparePartsInventoryOrderDTO> pageList(SparePartsInventoryOrderPageVO v);

	List<SparePartsInventoryOrderDTO> listBy(Long planId, Integer status, Integer neStatus);
}
