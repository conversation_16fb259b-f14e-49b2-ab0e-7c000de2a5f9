/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryOrderBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryOrder;
import com.snszyk.simas.spare.mapper.SparePartsInventoryOrderMapper;
import com.snszyk.simas.spare.service.ISparePartsInventoryOrderService;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 备品备件盘点记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@AllArgsConstructor
@Service
public class SparePartsInventoryOrderServiceImpl extends BaseCrudServiceImpl<SparePartsInventoryOrderMapper, SparePartsInventoryOrder, SparePartsInventoryOrderDTO, SparePartsInventoryOrderVO> implements ISparePartsInventoryOrderService {


	@Override
	public Boolean updateBatchByIds(List<SparePartsInventoryOrderVO> orderVOList) {
		return super.updateBatchById(SparePartsInventoryOrderBeanMapper.INSTANCE.toEntity(orderVOList));
	}

	@Override
	public Boolean saveOrUpdateBatch(List<SparePartsInventoryOrderVO> orderVOList) {
		return super.saveOrUpdateBatch(SparePartsInventoryOrderBeanMapper.INSTANCE.toEntity(orderVOList));
	}

	@Override
	public IPage<SparePartsInventoryOrderDTO> pageList(SparePartsInventoryOrderPageVO v) {
		return baseMapper.pageList(v);
	}

	@Override
	public List<SparePartsInventoryOrderDTO> listBy(Long planId, Integer status, Integer neStatus) {
		final List<SparePartsInventoryOrder> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(planId), SparePartsInventoryOrder::getPlanId, planId)
			.eq(ObjectUtil.isNotEmpty(status), SparePartsInventoryOrder::getStatus, status)
			.ne(ObjectUtil.isNotEmpty(neStatus), SparePartsInventoryOrder::getStatus, neStatus)
			.list();
		return ObjectUtil.isEmpty(list) ? null : BeanUtil.copy(list, SparePartsInventoryOrderDTO.class);
	}
}
