/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.constants;

/**
 * 盘点相关常量定义
 * 优化：消除魔法值，提高代码可维护性
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public final class InventoryConstants {

    private InventoryConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 统计结果键名常量
     */
    public static final class StatisticsKeys {
        public static final String UNCOMPLETED = "uncompleted";
        public static final String NORMAL = "normal";
        public static final String PROFIT = "profit";
        public static final String LOSS = "loss";
        public static final String TOTAL = "total";
        public static final String HAS_INVENTORY = "hasInventory";

        private StatisticsKeys() {}
    }

    /**
     * 数据填充选项常量
     */
    public static final class DataFillFlags {
        public static final boolean NEED_DICT_INFO = true;
        public static final boolean NEED_ENUM_NAMES = true;
        public static final boolean NEED_WAREHOUSE_NAMES = true;
        public static final boolean SKIP_DICT_INFO = false;
        public static final boolean SKIP_ENUM_NAMES = false;
        public static final boolean SKIP_WAREHOUSE_NAMES = false;

        private DataFillFlags() {}
    }

    /**
     * 查询相关常量
     */
    public static final class QueryConstants {
        public static final long QUERY_ALL_SIZE = -1L;
        public static final long DEFAULT_PAGE_SIZE = 10L;
        public static final long DEFAULT_CURRENT_PAGE = 1L;

        private QueryConstants() {}
    }
}
