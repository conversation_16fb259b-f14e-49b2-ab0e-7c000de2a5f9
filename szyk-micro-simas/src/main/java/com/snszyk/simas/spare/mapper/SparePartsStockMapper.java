/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsStock;
import com.snszyk.simas.spare.vo.SparePartsStockPageVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件字典 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface SparePartsStockMapper extends BaseMapper<SparePartsStock> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsStock
	 * @return
	 */
	List<SparePartsStockVO> selectSparePartsStockPage(IPage page, SparePartsStockVO sparePartsStock);

	IPage<SparePartsStockDTO> pageList(@Param("v") SparePartsStockPageVO v);
}
