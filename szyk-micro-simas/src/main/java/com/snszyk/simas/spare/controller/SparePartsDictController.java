/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.enums.SparePartsDictStatusEnum;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.service.logic.SparePartsDictLogicService;
import com.snszyk.simas.spare.vo.SparePartsDictPageVo;
import com.snszyk.simas.spare.vo.SparePartsDictSaveOrUpdateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 备品备件字典 控制器
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsdict")
@Api(value = "备品备件字典", tags = "备品备件字典接口")
@Validated
@ApiSupport(order = 50, author = "zhangzhenpu")
public class SparePartsDictController {

	private final SparePartsDictLogicService sparePartsDictLogicService;


	/**
	 * 保存
	 */
	@PostMapping("/saveOrUpdate")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增", notes = "SparePartsDictVo")
	public R<BaseCrudDto> createOrUpdate(@RequestBody @Validated SparePartsDictSaveOrUpdateVo v) {
		return R.data(sparePartsDictLogicService.saveOrUpdate(v));
	}


	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "SparePartsDictVo")
	public R<IPage<SparePartsDictDTO>> page(SparePartsDictPageVo v) {
		return R.data(sparePartsDictLogicService.page(v));
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<SparePartsDictDTO> fetchById(@PathVariable @Validated Long id) {
		return R.data(sparePartsDictLogicService.fetchById(id));
	}

	/**
	 * 删除
	 */

	@DeleteMapping
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "批量删除", notes = "ids")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "ids", value = "ids", required = true, dataType = "String", paramType = "query")
	})
	public R<Boolean> deleteByIds(@NotEmpty @RequestParam("ids") List<Long> ids) {
		return R.data(sparePartsDictLogicService.deleteByIds(ids));
	}

	/**
	 * 停用启用
	 */
	@PutMapping("/status/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "停用启用", notes = "id")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "statusEnum", value = "状态", required = true, dataType = "Integer", paramType = "query"),
		@ApiImplicitParam(name = "id", value = "id", required = true, dataType = "Long", paramType = "path")
	})
	public R<Boolean> updateStatus(@PathVariable @NotNull Long id, @NotNull SparePartsDictStatusEnum statusEnum) {
		return R.data(sparePartsDictLogicService.updateStatus(id, statusEnum));
	}

}
