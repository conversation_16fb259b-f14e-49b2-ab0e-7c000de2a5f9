package com.snszyk.simas.spare.schedule;// package com.snszyk.simas.inventory.schedule;

import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanDTO;
import com.snszyk.simas.spare.enums.SpareInventoryPlanStatusEnum;
import com.snszyk.simas.spare.service.ISparePartsInventoryPlanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class SpareInventoryJobHandle {
	private final ISparePartsInventoryPlanService planService;

	/**
	 * 盘点计划启动
	 */
	@XxlJob("startSparePlanJobHandler")
	public ReturnT<String> startSparePlanJobHandler(String param) throws ParseException {
		XxlJobLogger.log("################备品备件盘点计划定时任务-START-################");
		SimpleDateFormat timeFormat = new SimpleDateFormat(DateUtil.PATTERN_DATE);

		// 格式化当前时间
		String formattedTime = timeFormat.format(DateUtil.now());
		Date timeOnlyDate = timeFormat.parse(formattedTime);
		// 查询待开始状态的盘点计
		final List<SparePartsInventoryPlanDTO> planDTOList = planService.listBy(timeOnlyDate, SpareInventoryPlanStatusEnum.NOT_START.getCode());
		if (ObjectUtil.isNotEmpty(planDTOList)) {
			// 开始启动盘点计划
			planDTOList.forEach(plan -> {
				planService.start(plan.getNo());
			});
			// 记录今天启动的盘点计划编号
			final String no = planDTOList.stream()
				.map(item -> item.getNo())
				.collect(Collectors.joining(","));
			// 打印日志记录
			XxlJobLogger.log("启动盘点计划编号：{}", no);
		}
		XxlJobLogger.log("################备品备件盘点计划定时任务-END-################");
		return ReturnT.SUCCESS;
	}
}
