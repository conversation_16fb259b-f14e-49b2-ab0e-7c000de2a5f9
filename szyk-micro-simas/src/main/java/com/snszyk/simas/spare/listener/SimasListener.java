package com.snszyk.simas.spare.listener;

import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.service.logic.GeneralLogicService;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.event.SparePartsStockEvent;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.service.logic.SparePartsDictLogicService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备全生命周期管理监听Listener
 *
 * <AUTHOR>
 * @date 2024/08/16 15:16
 **/
@Slf4j
@AllArgsConstructor
@Configuration
public class SimasListener {

	private final ISparePartsWarehouseService sparePartsWarehouseService;
	private final GeneralLogicService generalLogicService;
	private final SparePartsDictLogicService dictLogicService;

	@Async
	@EventListener
	public void sparePartsStockListener(SparePartsStockEvent event) {
		log.info("==========================备品备件库存监听 START===============================");
		List<SparePartsStockDTO> stockDTOList = event.getStockDTOList();
		if (ObjectUtil.isNotEmpty(stockDTOList)) {
			return;
		}
		// 获取仓库ids
		List<Long> warehouseIds = ListUtil.distinctMap(stockDTOList, SparePartsStockDTO::getWarehouseId);
		// 根据仓库id查询仓库信息map
		final Map<Long, SparePartsWarehouse> warehousesMap = sparePartsWarehouseService.listByIds(warehouseIds)
			.stream()
			.collect(Collectors.toMap(SparePartsWarehouse::getId, Function.identity()));
		// 获取备品备件字典ids
		final List<Long> dictIds = ListUtil.map(stockDTOList, SparePartsStockDTO::getDictId);
		// 查询备品备件字典
		final Map<Long, SparePartsDictDTO> dictMap = dictLogicService.getDictMap(dictIds);

		stockDTOList.forEach(stock -> {
			// 获取备品备件字典
			final SparePartsDictDTO dictDTO = dictMap.get(stock.getDictId());
			// 获取仓库信息
			final SparePartsWarehouse warehouse = warehousesMap.get(stock.getWarehouseId());
			// 设置了安全库存并且实际库存小于安全库存
			if (ObjectUtil.isNotEmpty(warehouse) && ObjectUtil.isNotEmpty(dictDTO) && ObjectUtil.isNotEmpty(dictDTO.getSafeStockAmount())
				&& stock.getCurrentQuantity().compareTo(dictDTO.getSafeStockAmount()) < 0) {
				// 拼接消息（“备品备件名称 + 型号 + 库房”，少于“安全库存”，请及时补充）
				MessageBizTypeEnum messageBizType = MessageBizTypeEnum.SIMAS_SPARE_PARTS_STOCK_ALARM;
				messageBizType.setMessage(dictDTO.getName(), dictDTO.getModel(), warehouse.getName(), String.valueOf(stock.getCurrentQuantity()), String.valueOf(dictDTO.getSafeStockAmount()));
				generalLogicService.sendMessage(Func.toStr(stock.getId()), Func.toStr(stock), Arrays.asList(warehouse.getManager()), messageBizType);
			}

		});


		log.info("==========================备品备件库存监听 END===============================");
	}
}
