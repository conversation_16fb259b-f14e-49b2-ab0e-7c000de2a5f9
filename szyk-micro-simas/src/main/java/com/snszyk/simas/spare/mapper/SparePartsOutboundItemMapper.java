/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsOutboundItem;
import com.snszyk.simas.spare.vo.SparePartsOutboundItemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件出库明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface SparePartsOutboundItemMapper extends BaseMapper<SparePartsOutboundItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsOutboundItem
	 * @return
	 */
	List<SparePartsOutboundItemVO> selectSparePartsOutboundItemPage(IPage page, SparePartsOutboundItemVO sparePartsOutboundItem);

}
