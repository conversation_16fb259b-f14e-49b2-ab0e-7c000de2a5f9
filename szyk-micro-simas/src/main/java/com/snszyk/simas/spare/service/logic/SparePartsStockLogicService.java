/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.event.SparePartsStockEvent;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.vo.SparePartsStockPageVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 备品备件库存 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsStockLogicService extends BaseCrudLogicService<SparePartsStockDTO, SparePartsStockVO> {

	private final ISparePartsStockService sparePartsStockService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsStockService;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsStockDTO> page(SparePartsStockPageVO v) {
		// 分页查询
		return sparePartsStockService.pageList(v);
	}

	/**
	 * 获取库存数量
	 *
	 * @param ids
	 * @return
	 */
	public Map<Long, BigDecimal> getStockIdToCurrentQuantityMap(List<Long> ids) {
		if (ObjectUtil.isEmpty(ids)) {
			return MapUtil.empty();
		}
		final List<SparePartsStockDTO> stockDTOList = sparePartsStockService.listByIds(ids);
		if (ObjectUtil.isEmpty(stockDTOList)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(stockDTOList, SparePartsStockDTO::getId, SparePartsStockDTO::getCurrentQuantity);

	}

	/**
	 * 批量更新库存
	 *
	 * @param stockIdToCurrentQuantityMap key-库存id,value-库存数量
	 * @return
	 */
	public Boolean updateBatchStock(Map<Long, BigDecimal> stockIdToCurrentQuantityMap) {
		if (ObjectUtil.isEmpty(stockIdToCurrentQuantityMap)) {
			throw new BusinessException("库存数量不能为空");
		}
		// 待更新的库存VO
		final List<SparePartsStockVO> updateStockVOList = stockIdToCurrentQuantityMap.entrySet()
			.stream()
			.map(entry -> {
				final SparePartsStockVO stockVO = new SparePartsStockVO();
				stockVO.setId(entry.getKey());
				stockVO.setCurrentQuantity(entry.getValue());
				return stockVO;
			}).collect(Collectors.toList());
		final List<SparePartsStockDTO> stockDTOList = sparePartsStockService.updateBatchByIds(updateStockVOList);
		// 发布库存变更事件
		SpringUtil.publishEvent(new SparePartsStockEvent(this, stockDTOList));

		return ObjectUtil.isNotEmpty(stockDTOList);
	}

	/**
	 * 增加库存（向后兼容方法）
	 *
	 * @param stockVOList
	 * @return 是否保存成功
	 */
	public Boolean saveBatch(List<SparePartsStockVO> stockVOList) {
		return ObjectUtil.isNotEmpty(sparePartsStockService.saveOrUpdateBatch(stockVOList));
	}

	/**
	 * 增加库存并返回保存后的记录
	 * 用于需要获取新生成stockId的场景
	 *
	 * @param stockVOList
	 * @return 保存后的库存DTO列表（包含生成的ID）
	 */
	public List<SparePartsStockDTO> saveBatchWithReturn(List<SparePartsStockVO> stockVOList) {
		return sparePartsStockService.saveOrUpdateBatch(stockVOList);
	}
}
