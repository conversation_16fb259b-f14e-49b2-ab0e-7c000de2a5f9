/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsDictBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsDictDTO;
import com.snszyk.simas.spare.dto.SparePartsOutboundItemDTO;
import com.snszyk.simas.spare.service.ISparePartsOutboundItemService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.SparePartsOutboundItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 备品备件出库明细 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@AllArgsConstructor
@Service
public class SparePartsOutboundItemLogicService extends BaseCrudLogicService<SparePartsOutboundItemDTO, SparePartsOutboundItemVO> {

	private final ISparePartsOutboundItemService outboundItemService;
	private final ISparePartsWarehouseService warehouseService;
	private final SparePartsDictLogicService sparePartsDictLogicService;
	private final SparePartsStockLogicService stockLogicService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.outboundItemService;
	}

	/**
	 * 批量保存
	 *
	 * @param outboundOrderId 出库单id
	 * @param itemList
	 * @return
	 */
	public Boolean saveBatch(Long outboundOrderId, List<SparePartsOutboundItemVO> itemList) {
		itemList.forEach(item -> item.setOutboundOrderId(outboundOrderId));
		return outboundItemService.saveBatch(itemList);
	}

	/**
	 * 查询出库单明细
	 *
	 * @param orderId
	 * @return
	 */
	public List<SparePartsOutboundItemDTO> listByOrderId(Long orderId) {
		// 根据出单id查询明细
		final List<SparePartsOutboundItemDTO> itemDTOList = outboundItemService.listByOrderId(orderId);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return Collections.emptyList();
		}
		// 获取备件所有仓库id
		final List<Long> warehouseIds = ListUtil.distinctMap(itemDTOList, SparePartsOutboundItemDTO::getWarehouseId);
		// 获取仓库map
		Map<Long, String> warehouseMap = warehouseService.getwarehouseMap(warehouseIds);
		// 获取备品备件字典ids
		final List<Long> dictIds = ListUtil.distinctMap(itemDTOList, SparePartsOutboundItemDTO::getDictId);
		// 查询备品备件字典
		Map<Long, SparePartsDictDTO> idToDTOMap = sparePartsDictLogicService.getDictMap(dictIds);
		// 查询库存Map,key-库存id，value-当前库存数量
		Map<Long, BigDecimal> idToCurrentQuantityMap = stockLogicService.getStockIdToCurrentQuantityMap(ListUtil.map(itemDTOList, SparePartsOutboundItemDTO::getStockId));
		itemDTOList.forEach(itemDTO -> {
			// 封装字典信息
			Optional.ofNullable(idToDTOMap.get(itemDTO.getDictId()))
				.ifPresent(dictDTO -> SparePartsDictBeanMapper.INSTANCE.setSparePartsOutboundItemDTO(itemDTO, dictDTO));
			//  设置仓库名称
			Optional.ofNullable(warehouseMap.get(itemDTO.getWarehouseId()))
				.ifPresent(warehouseName -> itemDTO.setWarehouseName(warehouseName));
			// 当前库存数量
			Optional.ofNullable(idToCurrentQuantityMap.get(itemDTO.getStockId()))
				.ifPresent(currentQuantity -> itemDTO.setCurrentQuantity(currentQuantity));
		});
		return itemDTOList;
	}
}
