/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.supplier.dto.SupplierDto;
import com.snszyk.common.supplier.feign.ISupplierClient;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.SimasEnum;
import com.snszyk.simas.common.processor.SparePartsStockChangeLogProcessor;
import com.snszyk.simas.spare.beanmapper.SparePartsInboundItemBeanMapper;
import com.snszyk.simas.spare.constant.SpareConstant;
import com.snszyk.simas.spare.dto.SparePartsInboundItemDTO;
import com.snszyk.simas.spare.dto.SparePartsInboundOrderDTO;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.SparePartsWarehouseLockStatus;
import com.snszyk.simas.spare.service.ISparePartsInboundOrderService;
import com.snszyk.simas.spare.service.ISparePartsStockService;
import com.snszyk.simas.spare.service.ISparePartsWarehouseService;
import com.snszyk.simas.spare.vo.*;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 备品备件库存 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsInboundOrderLogicService extends BaseCrudLogicService<SparePartsInboundOrderDTO, SparePartsInboundOrderVO> {

	private final ISparePartsInboundOrderService sparePartsInboundOrderService;
	private final SparePartsInboundItemLogicService inboundItemLogicService;
	private final ISparePartsStockService stockService;
	private final ISupplierClient supplierClient;
	private final ISparePartsWarehouseService sparePartsWarehouseService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.sparePartsInboundOrderService;
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */

	@Transactional(readOnly = true)
	public SparePartsInboundOrderDTO getById(Long id) {
		// 查询详情
		final SparePartsInboundOrderDTO dto = sparePartsInboundOrderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			return dto;
		}
		// 获取供应商Map
		final Map<Long, String> supplierIdToNameMap = new HashMap<>();
		Optional.ofNullable(supplierClient.getById(dto.getSupplierId()))
			.map(R::getData)
			.map(SupplierDto::getName)
			.ifPresent(name -> supplierIdToNameMap.put(dto.getSupplierId(), name));
		// 获取库房名称map
		final Map<Long, String> WarehouseIdToNameMap = new HashMap<>();
		Optional.ofNullable(sparePartsWarehouseService.getById(dto.getWarehouseId()))
			.map(SparePartsWarehouse::getName)
			.ifPresent(name -> WarehouseIdToNameMap.put(dto.getWarehouseId(), name));
		// 填充dto
		populateDto(dto, supplierIdToNameMap, WarehouseIdToNameMap);

		// 查询入库明细
		final List<SparePartsInboundItemDTO> itemList = inboundItemLogicService.listByInboundOrderId(id);
		dto.setItemList(itemList);

		return dto;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SparePartsInboundOrderDTO> page(SparePartsInboundOrderPageVO v) {
		// 分页查询
		final IPage<SparePartsInboundOrderDTO> page = sparePartsInboundOrderService.pageList(v);
		if (ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		// 供应商ids
		final List<Long> supplierIds = ListUtil.map(page.getRecords(), SparePartsInboundOrderDTO::getSupplierId);
		// 供应商map,key-id,value:名称
		Map<Long, String> supplierIdToNameMap = getSupplierMap(supplierIds);
		// 获取库房ids
		final List<Long> warehouseIds = ListUtil.distinctMap(page.getRecords(), SparePartsInboundOrderDTO::getWarehouseId);
		// 获取库房map，key-id,value:名称
		Map<Long, String> warehouseIdToNameMap = getWarehouseMap(warehouseIds);

		page.getRecords().forEach(dto -> populateDto(dto, supplierIdToNameMap, warehouseIdToNameMap));
		return page;
	}

	/**
	 * 填充dto
	 *
	 * @param dto
	 * @param supplierIdToNameMap  供应商map
	 * @param warehouseIdToNameMap 库房map
	 * @return
	 */
	private void populateDto(SparePartsInboundOrderDTO dto, Map<Long, String> supplierIdToNameMap, Map<Long, String> warehouseIdToNameMap) {
		// 设置供应商名称
		Optional.ofNullable(supplierIdToNameMap.get(dto.getSupplierId()))
			.ifPresent(dto::setSupplierName);
		// 设置库房名称
		Optional.ofNullable(warehouseIdToNameMap.get(dto.getWarehouseId()))
			.ifPresent(dto::setWarehouseName);

		// 设置入库类型
		Optional.ofNullable(DictBizCache.getValue(SimasEnum.INBOUND_TYPE.getName(), dto.getInboundType()))
			.ifPresent(dto::setInboundTypeName);
		// 入库人姓名
		Optional.ofNullable(UserCache.getUser(dto.getInboundUserId()))
			.ifPresent(user -> dto.setInboundUserName(user.getRealName()));
		// 创建人姓名
		Optional.ofNullable(UserCache.getUser(dto.getCreateUser()))
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(UserCache.getUser(dto.getUpdateUser()))
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));

		// 完结备注人姓名
		Optional.ofNullable(dto.getCompletionRemarkUserId())
			.map(userId -> UserCache.getUser(userId))
			.ifPresent(user -> dto.setCompletionRemarkUserName(user.getRealName()));
	}

	/**
	 * 获取库房map
	 *
	 * @param warehouseIds
	 * @return
	 */
	private Map<Long, String> getWarehouseMap(List<Long> warehouseIds) {
		// 根据ids查询库房List
		final List<SparePartsWarehouse> sparePartsWarehouses = sparePartsWarehouseService.listByIds(warehouseIds);
		if (ObjectUtil.isEmpty(sparePartsWarehouses)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(sparePartsWarehouses, SparePartsWarehouse::getId, SparePartsWarehouse::getName);
	}

	/**
	 * 获取供应商 map
	 *
	 * @param supplierIds
	 * @return
	 */
	private Map<Long, String> getSupplierMap(List<Long> supplierIds) {
		if (ObjectUtil.isEmpty(supplierIds)) {
			return MapUtil.empty();
		}
		// 远程调用
		final R<List<SupplierDto>> listR = supplierClient.listByIds(supplierIds);
		if (ObjectUtil.isEmpty(listR.getData())) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(listR.getData(), SupplierDto::getId, SupplierDto::getName);
	}

	/**
	 * 保存
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean save(SparePartsInboundOrderSaveVO v) {
		// 保存校验
		this.saveCheck(v);

		SparePartsInboundOrderVO vo = BeanUtil.copy(v, SparePartsInboundOrderVO.class);
		// 生成库单号
		vo.setNo(BizCodeUtil.generate(SpareConstant.INBOUND_PREFIX));
		// 保存入库单据
		SparePartsInboundOrderDTO dto = sparePartsInboundOrderService.save(vo);
		// 保存入库单详情
		inboundItemLogicService.save(dto.getId(), dto.getWarehouseId(), v.getItemList());
		// 增加库存
		boolean flag = addStock(v.getWarehouseId(), v.getItemList());
		// 保存入库日志
		SparePartsStockChangeLogProcessor.saveInBoundLog(v.getItemList(), v.getWarehouseId());
		return flag;
	}

	/**
	 * 保存校验
	 *
	 * @param v
	 */
	private void saveCheck(SparePartsInboundOrderSaveVO v) {
		// 查询仓库
		SparePartsWarehouse warehouse = sparePartsWarehouseService.getById(v.getWarehouseId());
		if (SparePartsWarehouseLockStatus.LOCK.getStatus().equals(warehouse.getLockStock())) {
			throw new ServiceException("该仓库已锁定，不允许操作");
		}
	}

	/**
	 * 增加库存
	 *
	 * @param warehouseId
	 * @param itemList
	 * @return
	 */
	public Boolean addStock(Long warehouseId, List<SparePartsInboundItemVO> itemList) {
		if (ObjectUtil.isEmpty(warehouseId) || ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("入库失败！仓库id或备品备件明细不可为空");
		}
		// 备品备件字典id->入库数量
		final Map<Long, BigDecimal> dictIdToInboundQuantityMap = itemList.stream()
			.collect(Collectors.toMap(SparePartsInboundItemVO::getDictId, SparePartsInboundItemVO::getInboundQuantity, (a, b) -> a));

		// 前端传来即将入库的备品备件id集合
		final Set<Long> webDictIds = dictIdToInboundQuantityMap.keySet();
		// 查询已经存在的库存dtoList
		List<SparePartsStockDTO> existStockDtoList = stockService.listBy(warehouseId, webDictIds);
		if (ObjectUtil.isNotEmpty(existStockDtoList)) {
			// 计算需要更新的库存dtoList
			List<SparePartsStockVO> updateList = reduceUpdateStockVoList(existStockDtoList, dictIdToInboundQuantityMap);
			// 更新存在的备品备件库存
			stockService.saveOrUpdateBatch(updateList);
		}

		// 过滤出不存在的备品备件
		final List<SparePartsInboundItemVO> notExistItemList = filternotExistItemList(itemList, existStockDtoList);
		if (ObjectUtil.isNotEmpty(notExistItemList)) {
			// 添加不存在的备品备件
			List<SparePartsStockVO> stockVOList = notExistItemList.stream()
				.map(itemVO -> SparePartsInboundItemBeanMapper.INSTANCE.toStockVO(warehouseId, itemVO))
				.collect(Collectors.toList());
			// 新增备品备件库存
			stockService.saveOrUpdateBatch(stockVOList);
		}
		return true;

	}


	/**
	 * 计算库存voList
	 *
	 * @param existStockDtoList
	 * @param dictIdToInboundQuantityMap
	 * @return
	 */
	private List<SparePartsStockVO> reduceUpdateStockVoList(List<SparePartsStockDTO> existStockDtoList, Map<Long, BigDecimal> dictIdToInboundQuantityMap) {
		return existStockDtoList.stream()
			.map(existStockDto -> {
				SparePartsStockVO vo = BeanUtil.copy(existStockDto, SparePartsStockVO.class);
				// 入库数量
				final BigDecimal inboundQuantity = dictIdToInboundQuantityMap.get(existStockDto.getDictId());
				// 更新当前库存字段
				vo.setCurrentQuantity(vo.getCurrentQuantity().add(inboundQuantity));
				return vo;
			}).collect(Collectors.toList());
	}

	/**
	 * 过滤出不存在的备品备件
	 *
	 * @param itemVoList
	 * @param existStockDtoList
	 * @return
	 */
	private List<SparePartsInboundItemVO> filternotExistItemList(List<SparePartsInboundItemVO> itemVoList, List<SparePartsStockDTO> existStockDtoList) {
		// 如果不存在已存在的备品备件明细
		if (ObjectUtil.isEmpty(existStockDtoList)) {
			return itemVoList;
		}
		// 已经存在库存的字典ids
		final List<Long> existStockDictIds = ListUtil.map(existStockDtoList, SparePartsStockDTO::getDictId);

		return itemVoList.stream()
			.filter(item -> !existStockDictIds.contains(item.getDictId()))
			.collect(Collectors.toList());

	}

	/**
	 * 保存完结备注
	 *
	 * @param v
	 * @return
	 */
	public Boolean saveOrUpdateCompletionRemark(SparePartsInStorageCompletionRemarkVO v) {
		// 根据id查询入库单信息
		SparePartsInboundOrderDTO inboundOrderDTO = sparePartsInboundOrderService.fetchById(v.getId());
		if (ObjectUtil.isEmpty(inboundOrderDTO)) {
			throw new ServiceException("入库单不存在");
		}
		final SparePartsInboundOrderVO vo = BeanUtil.copy(inboundOrderDTO, SparePartsInboundOrderVO.class);
		vo.setCompletionRemark(v.getCompletionRemark());
		vo.setCompletionRemarkUserId(AuthUtil.getUserId());
		vo.setCompletionRemarkDateTime(LocalDateTime.now());
		return sparePartsInboundOrderService.save(vo) != null;
	}
}
