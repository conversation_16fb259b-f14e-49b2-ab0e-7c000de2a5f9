/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryOrder;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件盘点记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface SparePartsInventoryOrderMapper extends BaseMapper<SparePartsInventoryOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsInventoryOrder
	 * @return
	 */
	List<SparePartsInventoryOrderVO> selectSparePartsInventoryOrderPage(IPage page, SparePartsInventoryOrderVO sparePartsInventoryOrder);

	Page<SparePartsInventoryOrderDTO> pageList(@Param("v") SparePartsInventoryOrderPageVO v);
}
