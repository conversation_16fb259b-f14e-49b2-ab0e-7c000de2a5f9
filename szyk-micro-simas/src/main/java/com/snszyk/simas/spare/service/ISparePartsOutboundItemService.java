/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service;

import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.spare.dto.SparePartsOutboundItemDTO;
import com.snszyk.simas.spare.vo.SparePartsOutboundItemVO;

import java.util.List;

/**
 * 备品备件出库明细 服务类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface ISparePartsOutboundItemService extends IBaseCrudService<SparePartsOutboundItemDTO, SparePartsOutboundItemVO> {

	Boolean saveBatch(List<SparePartsOutboundItemVO> VOList);

	Boolean deleteByOutboundOrderId(Long outboundOrderId);

	List<SparePartsOutboundItemDTO> listByOrderId(Long orderId);
}
