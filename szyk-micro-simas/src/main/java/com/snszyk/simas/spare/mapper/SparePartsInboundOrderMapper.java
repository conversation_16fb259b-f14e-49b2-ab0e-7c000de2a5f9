/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsInboundOrder;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 备品备件库存 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface SparePartsInboundOrderMapper extends BaseMapper<SparePartsInboundOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsInboundOrder
	 * @return
	 */
	List<SparePartsInboundOrderVO> selectSparePartsInboundOrderPage(IPage page, SparePartsInboundOrderVO sparePartsInboundOrder);

}
