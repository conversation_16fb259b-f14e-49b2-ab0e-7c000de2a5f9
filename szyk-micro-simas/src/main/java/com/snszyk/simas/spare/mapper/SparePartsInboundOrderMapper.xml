<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsInboundOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsInboundOrderResultMap" type="com.snszyk.simas.spare.entity.SparePartsInboundOrder">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="no" property="no"/>
        <result column="inbound_type" property="inboundType"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="inbound_date" property="inboundDate"/>
        <result column="inbound_user_id" property="inboundUserId"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="remark" property="remark"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsInboundOrderPage" resultMap="sparePartsInboundOrderResultMap">
        select * from simas_spare_parts_inbound_order where is_deleted = 0
    </select>

</mapper>
