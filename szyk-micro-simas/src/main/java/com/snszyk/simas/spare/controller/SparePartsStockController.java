/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.service.logic.SparePartsStockLogicService;
import com.snszyk.simas.spare.vo.SparePartsStockPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 备品备件字典 控制器
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sparepartsstock")
@Api(value = "备品备件库存", tags = "备品备件库存接口")
@ApiSupport(order = 52, author = "zhangzhenpu")
@Validated
public class SparePartsStockController extends BaseCrudController {

	private final SparePartsStockLogicService sparePartsStockLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return sparePartsStockLogicService;
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "SparePartsStockPageVO")
	public R<IPage<SparePartsStockDTO>> page(SparePartsStockPageVO v) {
		return R.data(sparePartsStockLogicService.page(v));
	}

}
