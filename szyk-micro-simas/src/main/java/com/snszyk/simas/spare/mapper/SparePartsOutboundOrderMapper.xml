<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsOutboundOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsOutboundOrderResultMap" type="com.snszyk.simas.spare.entity.SparePartsOutboundOrder">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="no" property="no"/>
        <result column="outbound_type" property="outboundType"/>
        <result column="outbound_use" property="outboundUse"/>
        <result column="outbound_date" property="outboundDate"/>
        <result column="issuance_order_id" property="issuanceOrderId"/>
        <result column="receive_dept_id" property="receiveDeptId"/>
        <result column="receive_user_id" property="receiveUserId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="remark" property="remark"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsOutboundOrderPage" resultMap="sparePartsOutboundOrderResultMap">
        select * from simas_spare_parts_outbound_order where is_deleted = 0
    </select>

</mapper>
