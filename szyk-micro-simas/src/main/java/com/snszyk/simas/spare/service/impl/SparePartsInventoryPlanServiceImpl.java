package com.snszyk.simas.spare.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.mp.utils.PageUtil;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.dto.DictDTO;
import com.snszyk.simas.common.enums.LockStockEnum;
import com.snszyk.simas.common.service.logic.GeneralLogicService;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryItemBeanMapper;
import com.snszyk.simas.spare.beanmapper.SparePartsInventoryOrderBeanMapper;
import com.snszyk.simas.spare.dto.*;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.enums.SpareInventoryOrderStatusEnum;
import com.snszyk.simas.spare.enums.SpareInventoryPlanStatusEnum;
import com.snszyk.simas.spare.mapper.SparePartsInventoryPlanMapper;
import com.snszyk.simas.spare.mapper.SparePartsWarehouseMapper;
import com.snszyk.simas.spare.service.*;
import com.snszyk.simas.spare.service.logic.SparePartsInventoryItemLogicService;
import com.snszyk.simas.spare.service.logic.SparePartsStockLogicService;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanQueryVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanVO;
import com.snszyk.simas.spare.wrapper.SparePartsInventoryPlanWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@AllArgsConstructor
@Service
public class SparePartsInventoryPlanServiceImpl extends BaseServiceImpl<SparePartsInventoryPlanMapper, SparePartsInventoryPlan> implements ISparePartsInventoryPlanService {
	private final ISparePartsWarehouseService sparePartsWarehouseService;
	private final ApplicationContext applicationContext;
	private final ISparePartsStockService stockService;
	private final ISparePartsInventoryOrderService orderService;
	private final SzykRedis szykRedis;
	private final GeneralLogicService generalLogicService;
	private final ISparePartsInventoryItemService itemService;
	private final SparePartsWarehouseMapper warehouseMapper;
	private final SparePartsStockLogicService stockLogicService;
	private final SparePartsInventoryItemLogicService itemLogicService;
	private static final String NO_PREFIX = "spare_parts_inventory_no:";

	/**
	 * 生成单号
	 *
	 * @param planNo
	 * @return
	 */
	private String generateNo(String planNo) {
		String key = NO_PREFIX + planNo;
		Long increment = szykRedis.incr(key);
		return planNo + String.format("%03d", increment);
	}

	@Override
	public IPage<SparePartsInventoryPlanDTO> page(IPage<SparePartsInventoryPlan> page, SparePartsInventoryPlanQueryVO plan) {
		IPage<SparePartsInventoryPlan> ipage = this.baseMapper.selectPage(page, Wrappers.lambdaQuery(SparePartsInventoryPlan.class)
			.like(Func.isNotEmpty(plan.getName()), SparePartsInventoryPlan::getName, plan.getName())
			.eq(Func.isNotEmpty(plan.getStatus()), SparePartsInventoryPlan::getStatus, plan.getStatus())
			.ge(Func.isNotEmpty(plan.getStartDate()), SparePartsInventoryPlan::getStartDate, plan.getStartDate() + DateUtils.DAY_START_TIME)
			.le(Func.isNotEmpty(plan.getEndDate()), SparePartsInventoryPlan::getStartDate, plan.getEndDate() + DateUtils.DAY_END_TIME)
			.orderByDesc(SparePartsInventoryPlan::getCreateTime)
		);
		if (ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(page.getCurrent(), page.getSize());
		}
		// 获取所有盘点计划id
		final List<Long> planIds = ListUtil.map(ipage.getRecords(), SparePartsInventoryPlan::getId);
		// 获取盘点计划id对应已盘点的数量Map
		Map<Long, Long> totalQuantityMap = this.getTotalQuantityMap(planIds);
		// 获取盘点计划id对应已盘点的数量Map
		Map<Long, Long> hasInventoryQuantityMap = this.getHasInventoryQuantityMap(planIds);
		// 获取所有盘点仓库id
		final List<Long> warehouseIds = ipage.getRecords().stream()
			.flatMap(item -> Func.toLongList(item.getWarehouseId()).stream())
			.distinct()
			.collect(Collectors.toList());
		// 获取仓库 map
		Map<Long, String> warehouseNameMap = this.getWarehouseMap(warehouseIds);

		return page.convert(item -> {
			SparePartsInventoryPlanDTO planDTO = SparePartsInventoryPlanWrapper.build().entityDTO(item);
			// 总数
			planDTO.setTotalQuantity(totalQuantityMap.getOrDefault(item.getId(), 0L));
			// 已经盘点数量
			planDTO.setInventory(hasInventoryQuantityMap.getOrDefault(item.getId(), 0L));
			// 盘点名称
			planDTO.setStatusName(SpareInventoryPlanStatusEnum.getByCode(item.getStatus()).getName());
			// 仓库名称
			final String names = Func.toLongList(item.getWarehouseId()).stream()
				.map(warehouseId -> warehouseNameMap.get(warehouseId))
				.filter(ObjectUtil::isNotEmpty)
				.collect(Collectors.joining("、"));
			planDTO.setWarehouse(names);

			return planDTO;
		});
	}

	private Map<Long, Long> getTotalQuantityMap(List<Long> planIds) {
		if (ObjectUtil.isEmpty(planIds)) {
			return MapUtil.empty();
		}
		// 根据计划id查询盘点明细List
		final List<SparePartsInventoryItemDTO> itemDTOList = itemService.listByPlanIds(planIds);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return MapUtil.empty();
		}
		return itemDTOList.stream()
			.collect(Collectors.groupingBy(SparePartsInventoryItemDTO::getPlanId, Collectors.counting()));
	}

	private Map<Long, String> getWarehouseMap(List<Long> warehouseIds) {
		if (ObjectUtil.isEmpty(warehouseIds)) {
			return MapUtil.empty();
		}
		final List<SparePartsWarehouse> warehouseList = sparePartsWarehouseService.listByIds(warehouseIds);
		if (ObjectUtil.isEmpty(warehouseList)) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(warehouseList, SparePartsWarehouse::getId, SparePartsWarehouse::getName);
	}

	/**
	 * 获取已盘点工单数量
	 *
	 * @param planIds
	 * @return
	 */
	private Map<Long, Long> getHasInventoryQuantityMap(List<Long> planIds) {
		if (ObjectUtil.isEmpty(planIds)) {
			return MapUtil.empty();
		}
		// 根据计划id查询盘点明细List
		final List<SparePartsInventoryItemDTO> itemDTOList = itemService.listByPlanIds(planIds);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return MapUtil.empty();
		}
		return itemDTOList.stream()
			.filter(item -> !SpareInventoryItemResultEnum.NOT_START.getCode().equals(item.getResult()))
			.collect(Collectors.groupingBy(SparePartsInventoryItemDTO::getPlanId, Collectors.counting()));
	}

	@Override
	public SparePartsInventoryPlanDTO detail(String no) {
		SparePartsInventoryPlan plan = this.getOne(Wrappers.<SparePartsInventoryPlan>lambdaQuery().eq(SparePartsInventoryPlan::getNo, no));
		if (ObjectUtil.isEmpty(plan)) {
			throw new BusinessException("未查询到盘点单");
		}
		SparePartsInventoryPlanDTO dto = SparePartsInventoryPlanWrapper.build().entityDTO(plan);
		if (Func.isNotEmpty(plan.getWarehouseId())) {
			List<SparePartsWarehouse> warehouseList = sparePartsWarehouseService.listByIds(Func.toLongList(plan.getWarehouseId()));
			if (Func.isNotEmpty(warehouseList)) {
				dto.setWarehouse(warehouseList.stream().map(SparePartsWarehouse::getName).collect(Collectors.joining("、")));
			}
		}
		return dto;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean start(String no) {
		// 校验
		SparePartsInventoryPlan plan = this.startPlanValid(no);
		// 锁仓库
		sparePartsWarehouseService.lock(Func.toLongList(plan.getWarehouseId()));
		// 更新计划状态
		plan.setStatus(SpareInventoryPlanStatusEnum.PROCESS.getCode());
		plan.setStartDate(DateUtil.now());
		final boolean flag = updateById(plan);
		// 启动工单
		startOrder(plan);
		// 盘点人id
		final List<Long> userIds = Func.toLongList(plan.getInventoryUser());
		// 发送消息给盘点人id
		if (Func.isNotEmpty(userIds)) {
			generalLogicService.sendMessage(plan.getNo(), JSONUtil.toJsonStr(plan), userIds, MessageBizTypeEnum.SIMAS_SPARE_PARTS_INVENTORY_ADD);
		}
		return flag;
	}

	/**
	 * 批量更新工单状态
	 *
	 * @param plan
	 */
	private void startOrder(SparePartsInventoryPlan plan) {
		// 根据计划id查询工单List
		final List<SparePartsInventoryOrderDTO> orderDTOList = orderService.listBy(plan.getId(), null, null);
		if (ObjectUtil.isEmpty(orderDTOList)) {
			throw new ServiceException("启动失败！未查询到工单信息");
		}
		final List<SparePartsInventoryOrderVO> orderVOList = orderDTOList.stream()
			.map(orderDTO -> {
				final SparePartsInventoryOrderVO orderVO = new SparePartsInventoryOrderVO();
				orderVO.setId(orderDTO.getId());
				orderVO.setStatus(SpareInventoryOrderStatusEnum.PROCESS.getCode());
				return orderVO;
			}).collect(Collectors.toList());
		orderService.updateBatchByIds(orderVOList);
	}

	/**
	 * 初始化
	 *
	 * @param plan
	 * @return
	 */
	private void init(SparePartsInventoryPlan plan) {
		if (StringUtil.isBlank(plan.getWarehouseId())) {
			throw new ServiceException("初始化盘点工单失败！仓库信息不可为空");
		}
		// 查询仓库信息
		List<SparePartsWarehouse> warehouseList = sparePartsWarehouseService.listByIds(Func.toLongList(plan.getWarehouseId()));
		if (ObjectUtil.isEmpty(warehouseList)) {
			throw new ServiceException("初始化盘点工单失败！未查询到仓库信息");
		}
		// 仓库ids
		final List<Long> warehouseIds = ListUtil.distinctMap(warehouseList, SparePartsWarehouse::getId);
		// 获取该库房下的所有备品备件
		final List<SparePartsStockDTO> stockDTOList = stockService.listByWarehouseIds(warehouseIds);
		if (ObjectUtil.isEmpty(stockDTOList)) {
			throw new ServiceException("初始化盘点工单失败！未查询到该库房下的备品备件信息");
		}
		// 将备品备件按照库房id分组
		final Map<Long, List<SparePartsStockDTO>> warehouseIdToStockListMap = stockDTOList.stream()
			.collect(Collectors.groupingBy(SparePartsStockDTO::getWarehouseId, Collectors.toList()));

		final List<SparePartsInventoryOrderVO> orderVOList = warehouseList.stream()
			.map(warehouse -> {
				SparePartsInventoryOrderVO orderVo = SparePartsInventoryOrderBeanMapper.INSTANCE.toVO(warehouse, plan);
				// 手动创建盘点工单id
				orderVo.setId(IdWorker.getId());
				// 生成单号
				orderVo.setNo(generateNo(plan.getNo()));
				// 获取仓库下所有备品备件库存信息
				List<SparePartsStockDTO> stockList = warehouseIdToStockListMap.get(warehouse.getId());
				// 构建盘点明细VO
				final List<SparePartsInventoryItemVO> itemVOList = stockList.stream()
					.map(stock -> SparePartsInventoryItemBeanMapper.INSTANCE.toVO(plan, orderVo, stock))
					.collect(Collectors.toList());
				// 保存盘点明细
				itemService.saveBatch(itemVOList);
				// 工单总数
				orderVo.setTotalQuantity(itemVOList.size());
				return orderVo;
			}).collect(Collectors.toList());

		// 批量保存盘点工单
		orderService.saveOrUpdateBatch(orderVOList);
		szykRedis.del(NO_PREFIX + plan.getNo());
	}

	/**
	 * 计划开始校验
	 *
	 * @param no
	 * @return
	 */
	private SparePartsInventoryPlan startPlanValid(String no) {
		SparePartsInventoryPlan plan = this.getOne(Wrappers.<SparePartsInventoryPlan>lambdaQuery().eq(SparePartsInventoryPlan::getNo, no));
		if (ObjectUtil.isEmpty(plan)) {
			throw new BusinessException("未查询到盘点单");
		}
		// 非待盘点状态
		if (SpareInventoryPlanStatusEnum.NOT_START != SpareInventoryPlanStatusEnum.getByCode(plan.getStatus())) {
			throw new BusinessException("启动计划失败！计划状态异常");
		}
		return plan;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean stop(String no) {
		// 停止校验
		SparePartsInventoryPlan plan = this.stopPlanValid(no);

		final LocalDateTime currentDateTime = LocalDateTime.now();
		// 查询所有未完结的工单
		final List<SparePartsInventoryOrderDTO> unCompleteOrderList = orderService.listBy(plan.getId(), null, SpareInventoryOrderStatusEnum.COMPLETE.getCode());
		if (ObjectUtil.isNotEmpty(unCompleteOrderList)) {
			// 获取未完结工单id
			final List<Long> unCompleteOrderIds = ListUtil.map(unCompleteOrderList, SparePartsInventoryOrderDTO::getId);
			// 停止计划修改明细状态
			this.stopPlanUpdateBatchItemByOrderIds(unCompleteOrderIds);
			// 停止计划批量修改未完成的工单状态
			this.stopPlanUpdateBatchOrder(unCompleteOrderIds, currentDateTime);
		}
		// 停止计划批量更新库存表
		this.stopPlanUpdateBatchStock(plan.getId());
		// 停止计划解锁所有仓库
		this.stopPlanUnlockWarehouse(plan.getWarehouseId());
		// 设置完成时间
		plan.setCompleteTime(Date.from(currentDateTime.atZone(ZoneId.systemDefault()).toInstant()));
		plan.setStatus(SpareInventoryPlanStatusEnum.COMPLETE.getCode());
		return updateById(plan);
	}

	/**
	 * 停止计划批量更新库存表
	 *
	 * @param planId
	 */
	private void stopPlanUpdateBatchStock(Long planId) {
		// 根据计划id查询明细
		final List<SparePartsInventoryItemDTO> itemDTOList = itemService.listByPlanIds(Lists.newArrayList(planId));
		// 调用更新库存方法
		itemLogicService.saveOrUpdateBatchStock(itemDTOList);
	}

	/**
	 * 停止计划批量修改明细
	 *
	 * @param unCompleteOrderIds
	 */
	private void stopPlanUpdateBatchItemByOrderIds(List<Long> unCompleteOrderIds) {
		// 查询明细
		final List<SparePartsInventoryItemDTO> unCompleteItemDTOList = itemService.listByOrderIds(unCompleteOrderIds);
		if (ObjectUtil.isEmpty(unCompleteItemDTOList)) {
			throw new ServiceException("停止计划失败！未查询到盘点明细");
		}
		final List<SparePartsInventoryItemVO> itemVOList = unCompleteItemDTOList.stream()
			.map(itemDTO -> {
				SparePartsInventoryItemVO itemVO = new SparePartsInventoryItemVO();
				itemVO.setId(itemDTO.getId());
				itemVO.setAfterCountedStock(itemDTO.getBeforeSystemStock());
				// 停止计划默认结果为正常
				itemVO.setResult(SpareInventoryItemResultEnum.NORMAL.getCode());
				return itemVO;
			}).collect(Collectors.toList());
		itemService.updateBatchByIds(itemVOList);
	}

	/**
	 * 停止计划解锁所有仓库
	 *
	 * @param warehouseIds
	 */
	private void stopPlanUnlockWarehouse(String warehouseIds) {
		if (StringUtil.isBlank(warehouseIds)) {
			throw new ServiceException("解锁仓库失败！仓库信息不可为空");
		}
		// 仓库ids
		final List<Long> warehouseIdList = Func.toLongList(warehouseIds);

		warehouseMapper.update(null, Wrappers.lambdaUpdate(SparePartsWarehouse.class)
			.in(SparePartsWarehouse::getId, warehouseIdList)
			.set(SparePartsWarehouse::getLockStock, LockStockEnum.UNLOCK.getCode()));

	}

	/**
	 * 停止计划更新未完成状态的工单
	 *
	 * @param unCompleteOrderList
	 * @param completeTime
	 */
	private void stopPlanUpdateBatchOrder(List<Long> unCompleteOrderList, LocalDateTime completeTime) {
		if (ObjectUtil.isEmpty(unCompleteOrderList)) {
			throw new ServiceException("停止计划失败！未查询到未完成的工单");
		}
		final List<SparePartsInventoryOrderVO> orderVOList = unCompleteOrderList.stream()
			.map(orderId -> {
				final SparePartsInventoryOrderVO orderVO = new SparePartsInventoryOrderVO();
				orderVO.setId(orderId);
				orderVO.setStatus(SpareInventoryOrderStatusEnum.COMPLETE.getCode());
				orderVO.setCompleteTime(completeTime);
				return orderVO;
			}).collect(Collectors.toList());

		orderService.updateBatchByIds(orderVOList);
	}

	/**
	 * 停止计划校验
	 *
	 * @param no
	 * @return
	 */
	private SparePartsInventoryPlan stopPlanValid(String no) {

		SparePartsInventoryPlan plan = this.getOne(Wrappers.<SparePartsInventoryPlan>lambdaQuery().eq(SparePartsInventoryPlan::getNo, no));
		if (ObjectUtil.isEmpty(plan)) {
			throw new BusinessException("未查询到盘点计划");
		}
		// 只有盘点中状态可以停止
		if (SpareInventoryPlanStatusEnum.PROCESS != SpareInventoryPlanStatusEnum.getByCode(plan.getStatus())) {
			throw new BusinessException("停止失败！盘点单状态不正确");
		}
		return plan;
	}


	@Override
	public SparePartsInventoryPlanCountDTO count(String module) {
		int notStart = this.count(Wrappers.lambdaQuery(SparePartsInventoryPlan.class).eq(SparePartsInventoryPlan::getStatus, SpareInventoryPlanStatusEnum.NOT_START.getCode()));
		int process = this.count(Wrappers.lambdaQuery(SparePartsInventoryPlan.class).eq(SparePartsInventoryPlan::getStatus, SpareInventoryPlanStatusEnum.PROCESS.getCode()));
		int finish = this.count(Wrappers.lambdaQuery(SparePartsInventoryPlan.class).eq(SparePartsInventoryPlan::getStatus, SpareInventoryPlanStatusEnum.COMPLETE.getCode()));
		int total = notStart + process + finish;
		return new SparePartsInventoryPlanCountDTO(total, finish, notStart, process);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(SparePartsInventoryPlanVO vo) {
		SparePartsInventoryPlan plan = Objects.requireNonNull(BeanUtil.copy(vo, SparePartsInventoryPlan.class));
		plan.setStatus(SpareInventoryPlanStatusEnum.NOT_START.getCode());

		if (Func.isEmpty(vo.getWarehouseId())) {
			throw new ServiceException("请选择仓库");
		}
		plan.setNo(BizCodeUtil.generate(SimasConstant.BizCode.SPARE_PARTS_INVENTORY_PLAN));
		List<SparePartsWarehouse> warehouses = sparePartsWarehouseService.listByIds(Func.toLongList(vo.getWarehouseId()));

		// 存在没有备品备件的仓库
		List<String> exceptionWarehouse = new ArrayList<>();
		// 存在未完成的盘点计划的仓库
		List<String> unCompletePlanWarehouse = new ArrayList<>();
		for (SparePartsWarehouse item : warehouses) {
			final List<SparePartsStockDTO> stockDTOList = stockService.listBy(item.getId(), null);
			if (ObjectUtil.isEmpty(stockDTOList)) {
				exceptionWarehouse.add(item.getName());
			}
			int t = this.count(Wrappers.lambdaQuery(SparePartsInventoryPlan.class)
				.eq(SparePartsInventoryPlan::getWarehouseId, item.getId())
				.ne(SparePartsInventoryPlan::getStatus, SpareInventoryPlanStatusEnum.COMPLETE.getCode()));
			if (t > 0) {
				unCompletePlanWarehouse.add(item.getName());
			}
		}
		if (Func.isNotEmpty(exceptionWarehouse)) {
			throw new ServiceException(StringUtil.format("【{}】没有备件，请重新选择！", StringUtil.join(exceptionWarehouse, ",")));
		}
		if (Func.isNotEmpty(unCompletePlanWarehouse)) {
			throw new ServiceException(StringUtil.format("【{}】存在未完成的盘点计划，请仔细核对！", StringUtil.join(unCompletePlanWarehouse, ",")));
		}
		if (Func.isNotEmpty(warehouses)) {
			List<Long> managerUsers = warehouses.stream().map(SparePartsWarehouse::getManager).distinct().collect(Collectors.toList());
			plan.setInventoryUser(StringUtil.join(managerUsers, StringPool.COMMA));
		}
		// 保存计划
		final boolean flag = save(plan);
		// 初始化
		this.init(plan);

		return flag;
	}

	@Override
	public List<DictDTO> getPlanWarehouse(Long id) {
		SparePartsInventoryPlan plan = this.getById(id);
		List<SparePartsWarehouse> warehouseList = sparePartsWarehouseService.listByIds(Func.toLongList(plan.getWarehouseId()));
		if (Func.isEmpty(warehouseList)) {
			log.info("=====================仓库已删除：" + plan.getWarehouseId());
			return Collections.emptyList();
		}
		return warehouseList.stream().map(w -> new DictDTO(w.getId(), w.getName())).collect(Collectors.toList());
	}

	@Override
	public IPage<SparePartsInventoryPlanDTO> appPage(IPage<SparePartsInventoryPlan> page, SparePartsInventoryPlanQueryVO plan) {
		IPage<SparePartsInventoryPlan> ipage = this.baseMapper.selectPage(page, Wrappers.lambdaQuery(SparePartsInventoryPlan.class)
			.like(Func.isNotEmpty(plan.getName()), SparePartsInventoryPlan::getName, plan.getName())
			.eq(Func.isNotEmpty(plan.getStatus()), SparePartsInventoryPlan::getStatus, plan.getStatus())
			.ne(SparePartsInventoryPlan::getStatus, SpareInventoryPlanStatusEnum.NOT_START.getCode())
			.ge(Func.isNotEmpty(plan.getStartDate()), SparePartsInventoryPlan::getStartDate, plan.getStartDate() + DateUtils.DAY_START_TIME)
			.le(Func.isNotEmpty(plan.getEndDate()), SparePartsInventoryPlan::getStartDate, plan.getEndDate() + DateUtils.DAY_END_TIME)
			.and(wrapper -> wrapper.like(SparePartsInventoryPlan::getInventoryUser, AuthUtil.getUserId()))
			.orderByDesc(SparePartsInventoryPlan::getCreateTime)
		);
		return PageUtil.toPage(ipage, ipage.getRecords()
			.stream()
			.map(item -> baseDetail(item))
			.collect(Collectors.toList()));
	}

	@Override
	public SparePartsInventoryPlanDTO appDetail(String no) {
		SparePartsInventoryPlan plan = this.getOne(Wrappers.lambdaQuery(SparePartsInventoryPlan.class).eq(SparePartsInventoryPlan::getNo, no));
		return baseDetail(plan);
	}

	@Override
	public List<SparePartsInventoryPlanDTO> listBy(Date leStartDate, Integer status) {
		final List<SparePartsInventoryPlan> planList = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(status), SparePartsInventoryPlan::getStatus, status)
			.le(ObjectUtil.isNotEmpty(leStartDate), SparePartsInventoryPlan::getStartDate, leStartDate)
			.list();
		return ObjectUtil.isEmpty(planList) ? Collections.emptyList() : BeanUtil.copy(planList, SparePartsInventoryPlanDTO.class);
	}


	/**
	 * app计划详情（包含数据权限）
	 *
	 * @param plan
	 * @return
	 */
	private SparePartsInventoryPlanDTO baseDetail(SparePartsInventoryPlan plan) {
		if (Func.isEmpty(plan)) {
			throw new ServiceException("盘点计划不存在");
		}
		// 模块名称
		String moduleName = "";
		// 盘点数量
		int inventory = 0;
		// 盘点仓库
		String warehouse = "";
		// 盘点部门
		String inventoryDeptName = "";

		List<Long> warehouseIdList = Func.toLongList(plan.getWarehouseId());
		List<Long> filterIdList = warehouseIdList;

		List<SparePartsWarehouse> warehouseList = sparePartsWarehouseService.list(Wrappers.lambdaQuery(SparePartsWarehouse.class)
			.eq(SparePartsWarehouse::getManager, AuthUtil.getUserId())
			.in(SparePartsWarehouse::getId, Func.toLongList(plan.getWarehouseId())));
		if (Func.isNotEmpty(warehouseList)) {
			filterIdList = warehouseList.stream().map(SparePartsWarehouse::getId).filter(id -> warehouseIdList.contains(id)).collect(Collectors.toList());
		}
		plan.setWarehouseId(StringUtil.join(filterIdList, ","));
		plan.setInventoryUser(Func.toStr(AuthUtil.getUserId()));
		// 获取盘点工单id对应已盘点的数量Map
		Map<Long, Long> hasInventoryQuantityMap = this.getHasInventoryQuantityMap(Lists.newArrayList(plan.getId()));
		// 获取工单总数量Map
		Map<Long, Long> totalQuantityMap = this.getTotalQuantityMap(Lists.newArrayList(plan.getId()));

		if (Func.isNotEmpty(filterIdList)) {
			warehouse = sparePartsWarehouseService.listByIds(filterIdList).stream().map(SparePartsWarehouse::getName).collect(Collectors.joining("、"));
		}

		return SparePartsInventoryPlanWrapper.build().entityDTO(plan)
			.setTotalQuantity(totalQuantityMap.getOrDefault(plan.getId(), 0L))
			.setInventory(hasInventoryQuantityMap.getOrDefault(plan.getId(), 0L))
			.setWarehouse(warehouse)
			.setInventoryDeptName(inventoryDeptName);
	}
}
