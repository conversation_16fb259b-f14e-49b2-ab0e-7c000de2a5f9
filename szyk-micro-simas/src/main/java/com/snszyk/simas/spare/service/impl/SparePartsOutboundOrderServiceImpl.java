/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.beanmapper.SparePartsOutboundOrderBeanMapper;
import com.snszyk.simas.spare.dto.SparePartsOutboundOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsOutboundOrder;
import com.snszyk.simas.spare.mapper.SparePartsOutboundOrderMapper;
import com.snszyk.simas.spare.service.ISparePartsOutboundOrderService;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 备品备件出库单 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@AllArgsConstructor
@Service
public class SparePartsOutboundOrderServiceImpl extends BaseCrudServiceImpl<SparePartsOutboundOrderMapper, SparePartsOutboundOrder, SparePartsOutboundOrderDTO, SparePartsOutboundOrderVO> implements ISparePartsOutboundOrderService {

	@Override
	protected Wrapper<SparePartsOutboundOrder> beforePage(SparePartsOutboundOrderVO vo) {
		if (ObjectUtil.isEmpty(vo)) {
			return Wrappers.emptyWrapper();
		}
		return Wrappers.lambdaQuery(SparePartsOutboundOrder.class)
			.like(ObjectUtil.isNotEmpty(vo.getLikeNo()), SparePartsOutboundOrder::getNo, vo.getLikeNo())
			.eq(ObjectUtil.isNotEmpty(vo.getStatus()), SparePartsOutboundOrder::getStatus, vo.getStatus())
			.eq(ObjectUtil.isNotEmpty(vo.getOutboundType()), SparePartsOutboundOrder::getOutboundType, vo.getOutboundType())
			.between(ObjectUtil.isNotEmpty(vo.getStartOutboundDate()) && ObjectUtil.isNotEmpty(vo.getEndOutboundDate()), SparePartsOutboundOrder::getOutboundDate, vo.getStartOutboundDate(), vo.getEndOutboundDate())
			.orderByDesc(SparePartsOutboundOrder::getId);
	}

	@Override
	public Boolean updateStatus(Long id, Integer status) {
		return this.lambdaUpdate()
			.eq(SparePartsOutboundOrder::getId, id)
			.set(SparePartsOutboundOrder::getStatus, status)
			.update();
	}

	@Override
	public SparePartsOutboundOrderDTO save(SparePartsOutboundOrderVO vo) {
		SparePartsOutboundOrder entity = SparePartsOutboundOrderBeanMapper.INSTANCE.toEntity(vo);
		super.saveOrUpdate(entity);
		return SparePartsOutboundOrderBeanMapper.INSTANCE.toDTO(entity);
	}

}
