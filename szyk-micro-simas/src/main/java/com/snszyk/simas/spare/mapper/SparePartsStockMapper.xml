<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsStockMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsStockResultMap" type="com.snszyk.simas.spare.entity.SparePartsStock">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="dict_id" property="dictId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="current_quantity" property="currentQuantity"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsStockPage" resultMap="sparePartsStockResultMap">
        select *
        from simas_spare_parts_stock
        where is_deleted = 0
    </select>
    <select id="pageList" resultType="com.snszyk.simas.spare.dto.SparePartsStockDTO">
        SELECT s.*,
        w.`name` as "warehouse_name",
        d.`name` as "dict_name",
        d.`model`,
        d.`measure_unit_id`,
        d.`no` as "dict_no",
        u.`name` as "measure_unit_name",
        u.accuracy as "measure_unit_precision"
        FROM simas_spare_parts_stock s
        left join simas_spare_parts_warehouse w on s.warehouse_id = w.`id` and w.is_deleted = 0
        left join simas_spare_parts_dict d on d.id = s.dict_id and d.delete_time = 0
        left join device_measure_unit u on u.id = d.measure_unit_id and u.is_deleted = 0
        <where>
            s.delete_time = 0
            <if test="v.dictName != null and v.dictName != ''">
                and d.`name` like concat('%',#{v.dictName},'%')
            </if>
            <if test="v.dictNo != null and v.dictNo!=''">
                and d.no like concat('%',#{v.dictNo},'%')
            </if>
            <if test="v.warehouseName != null and v.warehouseName != ''">
                and w.`name` like concat('%',#{v.warehouseName},'%')
            </if>
            <if test="v.warehouseId != null">
                and s.warehouse_id = #{v.warehouseId}
            </if>
            <if test="v.warehouseIds != null and v.warehouseIds.size() > 0">
                and s.warehouse_id in
                <foreach collection="v.warehouseIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
        order by s.id desc

    </select>

</mapper>
