package com.snszyk.simas.spare.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.DictDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanCountDTO;
import com.snszyk.simas.spare.dto.SparePartsInventoryPlanDTO;
import com.snszyk.simas.spare.service.ISparePartsInventoryPlanService;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanQueryVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryPlanVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/spare-parts/inventoryplan")
@Api(value = "备品备件盘点计划", tags = "备品备件盘点计划")
@ApiSupport(order = 55)
public class SparePartsInventoryPlanController extends SzykController {

	private final ISparePartsInventoryPlanService inventoryPlanService;


	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "传入inventoryPlanVO")
	public R<IPage<SparePartsInventoryPlanDTO>> page(SparePartsInventoryPlanQueryVO vo, Query query) {
		return R.data(inventoryPlanService.page(Condition.getPage(query), vo));
	}

	@GetMapping("/detail")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "详情", notes = "传入no")
	public R<SparePartsInventoryPlanDTO> detail(@ApiParam(value = "盘点编码", required = true) @RequestParam String no) {
		return R.data(inventoryPlanService.detail(no));
	}

	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "提交", notes = "传入inventoryPlanVO")
	public R submit(@Valid @RequestBody SparePartsInventoryPlanVO vo) {
		return R.status(inventoryPlanService.submit(vo));
	}

	@GetMapping("/start")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "手动启动", notes = "盘点编码")
	public R start(@ApiParam(value = "盘点编码", required = true) @RequestParam String no) {
		return R.status(inventoryPlanService.start(no));
	}

	@GetMapping("/stop")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "手动终止", notes = "盘点编码")
	public R top(@ApiParam(value = "盘点编码", required = true) @RequestParam String no) {
		return R.status(inventoryPlanService.stop(no));
	}

	@GetMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "盘点id")
	public R remove(@ApiParam(value = "盘点id", required = true) @RequestParam Long id) {
		return R.status(inventoryPlanService.removeById(id));
	}

	@GetMapping("/count")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "pc盘点计划列表统计(暂时无用)", notes = "备品备件：SPARE_PARTS  设备：EQUIPMENT")
	public R<SparePartsInventoryPlanCountDTO> count(String module) {
		return R.data(inventoryPlanService.count(module));
	}

	@GetMapping("/warehouse")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "盘点计划关联仓库列表（暂时无用）", notes = "传计划id")
	public R<List<DictDTO>> getPlanWarehouse(Long id) {
		return R.data(inventoryPlanService.getPlanWarehouse(id));
	}

	@GetMapping("/app-page")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "分页（暂时无用）", notes = "传入inventoryPlanVO")
	public R<IPage<SparePartsInventoryPlanDTO>> appPage(SparePartsInventoryPlanQueryVO vo, Query query) {
		return R.data(inventoryPlanService.appPage(Condition.getPage(query), vo));
	}

	@GetMapping("/app-detail")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "app详情（暂时无用）", notes = "传入no")
	public R<SparePartsInventoryPlanDTO> appDetail(@ApiParam(value = "盘点编码", required = true) @RequestParam String no) {
		return R.data(inventoryPlanService.appDetail(no));
	}
}
