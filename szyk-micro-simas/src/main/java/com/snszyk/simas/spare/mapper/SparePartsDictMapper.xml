<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.spare.mapper.SparePartsDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sparePartsDictResultMap" type="com.snszyk.simas.spare.entity.SparePartsDict">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="measure_unit_id" property="measureUnitId"/>
        <result column="default_warehouse_id" property="defaultWarehouseId"/>
        <result column="safe_stock_amount" property="safeStockAmount"/>
        <result column="remark" property="remark"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectSparePartsDictPage" resultMap="sparePartsDictResultMap">
        select * from simas_spare_parts_dict where is_deleted = 0
    </select>

</mapper>
