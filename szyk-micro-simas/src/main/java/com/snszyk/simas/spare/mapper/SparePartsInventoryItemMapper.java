/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.mapper;

import com.snszyk.simas.spare.entity.SparePartsInventoryItem;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemVO;
import com.snszyk.simas.spare.vo.SparePartsInventoryItemPageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 备品备件盘点记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
public interface SparePartsInventoryItemMapper extends BaseMapper<SparePartsInventoryItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sparePartsInventoryItem
	 * @return
	 */
	List<SparePartsInventoryItemVO> selectSparePartsInventoryItemPage(IPage page, SparePartsInventoryItemVO sparePartsInventoryItem);

	/**
	 * 统一的分页查询方法
	 * 支持所有查询条件，包括备品备件名称的模糊查询
	 * 通过动态SQL自动处理是否需要关联字典表
	 *
	 * @param page 分页参数
	 * @param pageVO 查询条件（支持dictName、inventoryOrderId等所有条件）
	 * @return 盘点明细列表
	 */
	List<SparePartsInventoryItem> selectPageWithSparePartsName(IPage<SparePartsInventoryItem> page, @Param("pageVO") SparePartsInventoryItemPageVO pageVO);

}
