/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsInboundOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsInboundOrder;
import com.snszyk.simas.spare.mapper.SparePartsInboundOrderMapper;
import com.snszyk.simas.spare.service.ISparePartsInboundOrderService;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsInboundOrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 备品备件库存 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@AllArgsConstructor
@Service
public class SparePartsInboundOrderServiceImpl extends BaseCrudServiceImpl<SparePartsInboundOrderMapper, SparePartsInboundOrder, SparePartsInboundOrderDTO, SparePartsInboundOrderVO> implements ISparePartsInboundOrderService {


	@Override
	public IPage<SparePartsInboundOrderDTO> pageList(SparePartsInboundOrderPageVO v) {
		final Page<SparePartsInboundOrder> page = super.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(v.getInboundType()), SparePartsInboundOrder::getInboundType, v.getInboundType())
			.eq(ObjectUtil.isNotEmpty(v.getWarehouseId()), SparePartsInboundOrder::getWarehouseId, v.getWarehouseId())
			.between(ObjectUtil.isNotEmpty(v.getStartInboundDate()) && ObjectUtil.isNotEmpty(v.getEndInboundDate()), SparePartsInboundOrder::getInboundDate, v.getStartInboundDate(), v.getEndInboundDate())
			.eq(ObjectUtil.isNotEmpty(v.getSupplierId()), SparePartsInboundOrder::getSupplierId, v.getSupplierId())
			.orderByDesc(SparePartsInboundOrder::getId)
			.page(new Page<>(v.getCurrent(), v.getSize()));
		return page.convert(e -> BeanUtil.copy(e, SparePartsInboundOrderDTO.class));
	}

}
