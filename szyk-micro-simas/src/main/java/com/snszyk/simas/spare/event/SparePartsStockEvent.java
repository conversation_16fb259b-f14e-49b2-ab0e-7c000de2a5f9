package com.snszyk.simas.spare.event;

import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class SparePartsStockEvent extends ApplicationEvent {

	private static final long serialVersionUID = 1L;

	/**
	 * 台账列表
	 */
	private List<SparePartsStockDTO> stockDTOList;


	public SparePartsStockEvent(Object source) {
		super(source);
	}


	public SparePartsStockEvent(Object source, List<SparePartsStockDTO> stockDTOList) {
		super(source);
		this.stockDTOList = stockDTOList;
	}

	public List<SparePartsStockDTO> getStockDTOList() {
		return stockDTOList;
	}


}
