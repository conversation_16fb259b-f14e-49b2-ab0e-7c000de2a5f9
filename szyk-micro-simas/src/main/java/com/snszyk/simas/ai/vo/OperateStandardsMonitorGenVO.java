package com.snszyk.simas.ai.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 运维标准的部位
 */
@Data
@ApiModel(value = "OperateStandardsMonitorGenVO", description = "OperateStandardsMonitorGenVO")
public class OperateStandardsMonitorGenVO {
	//字段 部位id,部位名,部位类型id,部位类型名
	@ApiModelProperty(value = "部位id",required = true)
	private String monitorId;

	@ApiModelProperty(value = "部位名",required = true)
	private String monitorName;

	@ApiModelProperty(value = "部位类型名",required = true)
	private String monitorTypeName;

}
