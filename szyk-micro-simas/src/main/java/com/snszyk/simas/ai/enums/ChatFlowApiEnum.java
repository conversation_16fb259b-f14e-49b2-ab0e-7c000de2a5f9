package com.snszyk.simas.ai.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description dify chatflow应用api枚举
 * @Date 下午4:58 2025/4/9
 * @Param
 * @return
 **/
@Getter
public enum ChatFlowApiEnum {
	CHAT_MESSAGES("发送对话消息", "/chat-messages"),
	STOP("停止响应", "/chat-messages/{taskId}/stop"),
	MESSAGES("获取会话历史消息", "/messages"),
	CONVERSATIONS("获取会话列表", "/conversations"),
	DELETE_CONVERSATION("删除会话", "/conversations/{conversationId}");

	private String desc;
	private String uri;

	ChatFlowApiEnum(String desc, String uri) {
		this.desc = desc;
		this.uri = uri;
	}

}
