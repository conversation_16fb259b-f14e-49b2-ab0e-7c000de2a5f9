package com.snszyk.simas.ai.enums;

/**
 * Dify API URL枚举类
 */
public enum DifyApiUrl {

    /**
     * 通过文件创建文档
     */
    CREATE_DOCUMENT_BY_FILE("/v1/datasets/%s/document/create-by-file"),

    /**
     * 更新文档元数据
     */
    UPDATE_DOCUMENT_METADATA("/v1/datasets/%s/documents/metadata"),

    /**
     * 删除文档
     */
    DELETE_DOCUMENT("/v1/datasets/%s/documents/%s"),

    /**
     * 运行工作流
     */
    RUN_WORKFLOW("/v1/workflows/run");

    private final String path;

    DifyApiUrl(String path) {
        this.path = path;
    }

    public String getPath() {
        return path;
    }

    /**
     * 获取完整的URL
     * 
     * @param serverUrl 服务器基础URL
     * @param params    URL参数
     * @return 完整的URL
     */
    public String getFullUrl(String serverUrl, Object... params) {
        String formattedPath = String.format(path, params);
        return serverUrl + formattedPath;
    }
}