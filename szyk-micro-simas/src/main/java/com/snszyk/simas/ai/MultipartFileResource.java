package com.snszyk.simas.ai;

import org.springframework.core.io.Resource;
import java.io.*;
import java.net.URI;
import java.net.URL;

/**
 * 自定义的MultipartFile资源类
 * 用于处理文件上传的资源封装
 */
public class MultipartFileResource implements Resource {
    private final File file; // 持有的物理文件对象
    private final String simasFileName; // 文件名

    /**
     * 构造函数
     * 
     * @param file          物理文件对象
     * @param simasFileName 文件名
     */
    public MultipartFileResource(File file, String simasFileName) {
        this.file = file;
        this.simasFileName = simasFileName;
    }

    /**
     * 获取SIMAS文件名
     * 
     * @return 文件名
     */
    public String getSimasFileName() {
        return simasFileName;
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return new FileInputStream(file);
    }

    @Override
    public boolean exists() {
        return file.exists();
    }

    @Override
    public boolean isOpen() {
        return false;
    }

    @Override
    public URL getURL() throws IOException {
        return file.toURI().toURL();
    }

    @Override
    public URI getURI() throws IOException {
        return file.toURI();
    }

    @Override
    public File getFile() throws IOException {
        return file;
    }

    @Override
    public long contentLength() throws IOException {
        return file.length();
    }

    @Override
    public long lastModified() throws IOException {
        return file.lastModified();
    }

    @Override
    public Resource createRelative(String relativePath) throws IOException {
        return new MultipartFileResource(new File(file, relativePath), getSimasFileName());
    }

    @Override
    public String getFilename() {
        return getSimasFileName();
    }

    @Override
    public String getDescription() {
        return "MultipartFileResource for file: " + file.getName();
    }
}