package com.snszyk.simas.ai.enums;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Dify元数据键值枚举
 */
@Component
public class DifyMetadataKey {

    public static final DifyMetadataKey SIMAS_FILE_ID;
    public static final DifyMetadataKey SIMAS_DEVICE_ID;
    public static final DifyMetadataKey SIMAS_TENANT_ID;
    public static final DifyMetadataKey SIMAS_FILE_LINK;

    private String id;
    private String name;

    static {
        SIMAS_FILE_ID = new DifyMetadataKey();
        SIMAS_DEVICE_ID = new DifyMetadataKey();
        SIMAS_TENANT_ID = new DifyMetadataKey();
        SIMAS_FILE_LINK = new DifyMetadataKey();
    }

    @Value("${dify.metadata.simas-file-id.id}")
    public void setSimasFileId(String id) {
        SIMAS_FILE_ID.id = id;
    }

    @Value("${dify.metadata.simas-file-id.name}")
    public void setSimasFileName(String name) {
        SIMAS_FILE_ID.name = name;
    }

    @Value("${dify.metadata.simas-device-id.id}")
    public void setSimasDeviceId(String id) {
        SIMAS_DEVICE_ID.id = id;
    }

    @Value("${dify.metadata.simas-device-id.name}")
    public void setSimasDeviceName(String name) {
        SIMAS_DEVICE_ID.name = name;
    }

    @Value("${dify.metadata.simas-tenant-id.id}")
    public void setSimasTenantId(String id) {
        SIMAS_TENANT_ID.id = id;
    }

    @Value("${dify.metadata.simas-tenant-id.name}")
    public void setSimasTenantName(String name) {
        SIMAS_TENANT_ID.name = name;
    }

    @Value("${dify.metadata.simas-file-link.id}")
    public void setSimasFileLinkId(String id) {
        SIMAS_FILE_LINK.id = id;
    }

    @Value("${dify.metadata.simas-file-link.name}")
    public void setSimasFileLinkName(String name) {
        SIMAS_FILE_LINK.name = name;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}