package com.snszyk.simas.ai.contorller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.ai.dto.OperateStandardsMonitorGenResultDTO;
import com.snszyk.simas.ai.dto.RepairSuggestGenDTO;
import com.snszyk.simas.ai.service.AiToolsService;
import com.snszyk.simas.ai.vo.OperateStandardsGenVO;
import com.snszyk.simas.ai.vo.RepairSuggestGenVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;

@RestController
@RequestMapping("/ai-tools")
public class AiToolsController {

    @Autowired
    private AiToolsService aiToolsService;



    /**
     * 删除文档
     *
     * @param fileId 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/delete-document")
    public String deleteDocument(@RequestParam String fileId) {
        return aiToolsService.deleteDocument(fileId);
    }

    /**
     * 运行工作流
     *
     * @param query         查询内容
     * @param orderType     订单类型
     * @param simasDeviceId 设备ID
     * @return 工作流执行结果
     */
    @PostMapping("/run-workflow")
    public JSONObject runWorkflow(@RequestParam String query,
            @RequestParam String orderType,
            @RequestParam String simasDeviceId) {
        return aiToolsService.runWorkflow(query, orderType, simasDeviceId);
    }

    /**
     * 生成运维标准
     *
     * @param operateStandardsGenVO 生成运维标准的请求参数
     * @return 生成的运维标准列表
     */
    @PostMapping("/generate-operate-standards")
    @ApiOperation(value = "生成运维标准", notes = "传入设备信息和部位信息，使用优化的提示词模板返回详细的运维标准，包括部位详细信息、专业术语、维护标准、方法、技术规范等")
    public R<OperateStandardsMonitorGenResultDTO> generateOperateStandards(
            @RequestBody @Valid OperateStandardsGenVO operateStandardsGenVO) {
        OperateStandardsMonitorGenResultDTO operateStandardsMonitorGenResultDTO = aiToolsService
                .generateOperateStandards(operateStandardsGenVO);
        return R.data(operateStandardsMonitorGenResultDTO);
    }

    /**
     * 生成维修建议
     *
     * @param vo 生成维修建议的请求参数
     * @return 生成的维修建议
     */
    @PostMapping("/generate-repair-suggest")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "生成维修建议", notes = "传入")
    public R<RepairSuggestGenDTO> generateRepairSuggest(@RequestBody @Valid RepairSuggestGenVO vo) {
        RepairSuggestGenDTO result = aiToolsService.generateRepairSuggest(vo);
        return R.data(result);
    }

    /**
     * 生成维修建议（流式）
     *
     * @param vo 生成维修建议的请求参数
     * @return 生成的维修建议流
     */
    @PostMapping("/generate-repair-suggest-stream")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "生成维修建议stream", notes = "传入")
    public ResponseEntity<SseEmitter> generateRepairSuggestStream(@RequestBody @Valid RepairSuggestGenVO vo) {
        ResponseEntity<SseEmitter> result = aiToolsService.generateRepairSuggestStream(vo);
        return result;
    }

    /**
     * 同步故障案例数据到Dify
     *
     * @return 同步结果
     */
    @GetMapping("/defectCaseSync")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "同步故障案例数据到dify", notes = "传入")
    public R<String> sync() {
        aiToolsService.defectCaseSync();
        return R.data("success");
    }
}
