package com.snszyk.simas.ai.service;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.ai.DifyChatFlowClient;
import com.snszyk.simas.ai.dto.chatflow.ConversationMessagesResponseDTO;
import com.snszyk.simas.ai.dto.chatflow.ConversationResponseDTO;
import com.snszyk.simas.ai.entity.chatflow.ConversationMessagesRequest;
import com.snszyk.simas.ai.entity.chatflow.ConversationMessagesResponse;
import com.snszyk.simas.ai.entity.chatflow.ConversationRequest;
import com.snszyk.simas.ai.entity.chatflow.ConversationResponse;
import com.snszyk.simas.ai.vo.chatflow.ConversationMessagesRequestVO;
import com.snszyk.simas.ai.vo.chatflow.ConversationsRequestVO;
import com.snszyk.simas.ai.vo.chatflow.QaRequestVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.Select;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description ai助手
 * @Date 上午8:44 2025/4/10
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class AiAssistantService {
    private final JdbcTemplate jdbcTemplate;
	private final DifyChatFlowClient difyChatFlowClient;

    public Object executeSql(String sql) {
		List<Map<String, Object>> resultList = new ArrayList<>();
		if (Func.isBlank(sql)) {
			return resultList;
		}
		// 去除块注释（/*...*/）
		String withoutBlockComments = sql.replaceAll("(?s)/\\*.*?\\*/", " ");

		// 去除行注释（--...）
		String withoutLineComments = withoutBlockComments.replaceAll("--.*", " ");

		// 合并连续空白字符，并去除首尾空格后转为大写
		String processedSQL = withoutLineComments
			.replaceAll("\\s+", " ")
			.replace("&gt;", ">")
			.replace("&lt;", "<")
			.trim()
			.toUpperCase();
		// 校验SQL语句是否是SELECT语句
		if (!isSelectStatementAccurate(processedSQL)) {
			log.error("仅允许执行SELECT语句：{}",sql);
			return resultList;
		}
        try {
            // 执行查询，最多返回20条数据
            List<Map<String, Object>> queryResult = jdbcTemplate.queryForList(processedSQL);
			if (CollectionUtil.isNotEmpty(queryResult) && queryResult.size() > 20) {
				return queryResult.subList(0, 20);
			}
           	return queryResult;
        } catch (Exception e) {
           log.error("执行SQL异常", e);
        }
        return resultList;
    }

	/**
	 * 智能问答（流式输出）
	 * 包括知识问答、智能问数等
	 * @param requestVO
	 * @return
	 */
	public ResponseEntity<SseEmitter> chatByStreaming(QaRequestVO requestVO) {
		return difyChatFlowClient.chatMessagesByStreaming(requestVO);
	}

	/**
	 * 智能问答（流式输出）
	 * 包括知识问答、智能问数等
	 * @param requestVO
	 * @return
	 */
	public ResponseEntity<SseEmitter> stopChatStreaming(QaRequestVO requestVO) {
		return difyChatFlowClient.stopChatStreaming(requestVO);
	}

	/**
	 * 获取会话列表
	 * @param requestVO
	 * @return
	 */
	public ConversationResponseDTO conversations(ConversationsRequestVO requestVO) {
		ConversationRequest request = BeanUtil.copy(requestVO, ConversationRequest.class);
		request.setUser(AuthUtil.getUserId().toString());
		ResponseEntity<ConversationResponse> conversationsResponseEntity = difyChatFlowClient.getConversations(request);
		ConversationResponse conversationResponse = conversationsResponseEntity.getBody();
		return BeanUtil.copy(conversationResponse, ConversationResponseDTO.class);
	}

	/**
	 * 获取会话列表
	 * @param conversationId 会话ID
	 * @return
	 */
	public Boolean deleteConversation(String conversationId) {
		ResponseEntity<Boolean> responseEntity = difyChatFlowClient.deleteConversation(conversationId);
		return responseEntity.getBody();
	}


	/**
	 * 获取会话历史消息
	 * @param requestVO
	 * @return
	 */
	public ConversationMessagesResponseDTO messages(ConversationMessagesRequestVO requestVO) {
		ConversationMessagesRequest request = BeanUtil.copy(requestVO, ConversationMessagesRequest.class);
		request.setUser(AuthUtil.getUserId().toString());

		ResponseEntity<ConversationMessagesResponse> responseEntity =  difyChatFlowClient.getConversationMessages(request);
		ConversationMessagesResponse conversationMessagesResponse = responseEntity.getBody();
		return BeanUtil.copy(conversationMessagesResponse, ConversationMessagesResponseDTO.class);
	}

	/**
	 * 校验SQL语句是否是SELECT语句
	 * @param sql
	 * @return
	 */
	public static boolean isSelectStatementAccurate(String sql) {
		try {
			Statement statement = CCJSqlParserUtil.parse(sql);
			return statement instanceof Select;
		} catch (JSQLParserException e) {
			return false; // 解析失败视为非SELECT
		}
	}
}
