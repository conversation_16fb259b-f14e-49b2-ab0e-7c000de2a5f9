package com.snszyk.simas.ai.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 运维标准生成的结果
 */
@Data
@ApiModel(value = "OperateStandardsGenDTO", description = "OperateStandardsGenDTO")
public class OperateStandardsMonitorGenDTO {

	//字段 部位id,部位名,部位类型id,部位类型名
	@ApiModelProperty(value = "部位id")
	private String monitorId;

	@ApiModelProperty(value = "部位名")
	private String monitorName;

	@ApiModelProperty(value = "部位类型名")
	private String monitorTypeName;

	@ApiModelProperty(value = "部位类型")
	private Integer monitorType;

	@ApiModelProperty(value = "标准")
	private String standards;

	@ApiModelProperty(value = "方法")
	private String method;


}
