///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.ai;
//
//import com.alibaba.fastjson.JSONObject;
//import com.snszyk.core.boot.ctrl.SzykController;
//import com.snszyk.core.tool.api.R;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.io.IOException;
//import java.util.HashMap;
//
///**
// *
//dify测试
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("ai/test")
//@Api(value = "ai/test", tags = "test")
//public class DifyTestController extends SzykController {
//	private final DifyClient client;
//
//	@ApiOperation("测试")
//	@GetMapping("/uploadFile")
//	public R<String> test() throws IOException {
//		String s = client.uploadFile("1909424513033302017", "1909424111915360257", "263842",
//			"D:\\home\\通义灵码的使用说明.docx", "通义灵码的使用说明.docx");
//		return R.data(s);
//	}
//
//	//runWorkflow
//
//	@ApiOperation("运维标准test")
//	@GetMapping("/runWorkflow")
//	public R<JSONObject> runWorkflow() {
//		HashMap<String, Object> map = new HashMap<>();
//		map.put("order_type", "润滑");
//		map.put("simas_device_id", "1");
//		JSONObject jsonObject = client.runWorkflow("高压三相异步电动机有哪些部位,以及分析各个部位的润滑标准和润滑方法",
//			map, "1");
//		return R.data(jsonObject);
//
//	}
//}
