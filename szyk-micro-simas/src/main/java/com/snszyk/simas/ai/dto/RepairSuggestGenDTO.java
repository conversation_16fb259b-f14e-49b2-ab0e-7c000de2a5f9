package com.snszyk.simas.ai.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 维修建议的结果
 */
@Data
@ApiModel(value = "RepairSuggestGenDTO", description = "RepairSuggestGenDTO")
public class RepairSuggestGenDTO {

	@ApiModelProperty(value = "建议")
	private String suggest;

	@ApiModelProperty(value = "来源")
	private List<String> sourceList=new ArrayList<>();


}
