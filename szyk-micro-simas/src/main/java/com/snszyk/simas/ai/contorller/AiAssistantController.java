package com.snszyk.simas.ai.contorller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.ai.dto.chatflow.ConversationMessagesResponseDTO;
import com.snszyk.simas.ai.dto.chatflow.ConversationResponseDTO;
import com.snszyk.simas.ai.service.AiAssistantService;
import com.snszyk.simas.ai.vo.chatflow.ConversationMessagesRequestVO;
import com.snszyk.simas.ai.vo.chatflow.ConversationsRequestVO;
import com.snszyk.simas.ai.vo.chatflow.ExecuteSQLRequestVO;
import com.snszyk.simas.ai.vo.chatflow.QaRequestVO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.validation.Valid;

/**
 * 智能助手控制器
 *
 * <AUTHOR>
 * @Date 2025/04/10 17:31
 */
@AllArgsConstructor
@RestController
@RequestMapping("/ai-assistant")
public class AiAssistantController {

	private final AiAssistantService aiAssistantService;

	/**
	 * 执行SQL（仅允许执行SELECT语句）
	 * @param requestVO
	 * @return
	 */
	@PostMapping("/execute-sql")
	public R executeSql(@RequestBody @Valid ExecuteSQLRequestVO requestVO) {
		return R.data(aiAssistantService.executeSql(requestVO.getSql()));
	}

	/**
	 * 智能问答（流式输出）
	 * @param requestVO
	 * @return
	 */
	@PostMapping(path = "/chat-stream")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "智能问答（流式输出）", notes = "传入")
	public ResponseEntity<SseEmitter> chatMessagesByStreaming(@RequestBody @Valid QaRequestVO requestVO) {
		return aiAssistantService.chatByStreaming(requestVO);
	}

	/**
	 * 停止响应
	 * @param requestVO
	 * @return
	 */
	@PostMapping(path = "/stop-chat-stream")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "停止响应", notes = "传入")
	public ResponseEntity<SseEmitter> stop(@RequestBody @Valid QaRequestVO requestVO) {
		return aiAssistantService.stopChatStreaming(requestVO);
	}

	/**
	 * 获取会话列表
	 * @param requestVO
	 * @return
	 */
	@PostMapping(path = "/conversations")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "获取会话列表", notes = "传入")
	public R<ConversationResponseDTO> converstations(@RequestBody @Valid ConversationsRequestVO requestVO) {
		return R.data(aiAssistantService.conversations(requestVO));
	}

	/**
	 * 删除会话
	 * @param conversationId 会话ID
	 * @return
	 */
	@DeleteMapping(path = "/conversations/{conversationId}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "删除会话", notes = "传入")
	public R<Boolean> deleteConversation(@PathVariable("conversationId") String conversationId) {
		return R.data(aiAssistantService.deleteConversation(conversationId));
	}

	/**
	 * 获取会话历史消息
	 * @param requestVO
	 * @return
	 */
	@PostMapping(path = "/messages")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "获取会话历史消息", notes = "传入")
	public R<ConversationMessagesResponseDTO> messages(@RequestBody @Valid ConversationMessagesRequestVO requestVO) {
		return R.data(aiAssistantService.messages(requestVO));
	}

}
