package com.snszyk.simas.ai;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.ai.entity.chatflow.ConversationMessagesRequest;
import com.snszyk.simas.ai.entity.chatflow.ConversationMessagesResponse;
import com.snszyk.simas.ai.dto.DifySourceDTO;
import com.snszyk.simas.ai.entity.chatflow.ConversationRequest;
import com.snszyk.simas.ai.entity.chatflow.ConversationResponse;
import com.snszyk.simas.ai.enums.ChatFlowApiEnum;
import com.snszyk.simas.ai.enums.DifyDbType;
import com.snszyk.simas.ai.enums.OutputModeEnum;
import com.snszyk.simas.ai.vo.chatflow.ChatMessagesRequestVO;
import com.snszyk.simas.ai.vo.chatflow.ChatMessagesStopRequestVO;
import com.snszyk.simas.ai.vo.chatflow.QaRequestVO;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.enums.SystemCodeEnum;
import com.snszyk.system.feign.ISysClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

/**
 * Dify平台ChatFlow调用客户端
 * <AUTHOR>
 * @Date 2025/04/12 15:00
 */
@Slf4j
@Component
public class DifyChatFlowClient {
	@Value("${dify.enabled:false}")
	private boolean difyEnabled;

	@Value("${dify.assistant_api_key}")
	private String apiKey;

	@Value("${dify.server_url}")
	private String serverUrl;
	private static final String API_VERSION = "v1";
	private static final Long SSE_TIMEOUT = 60_000L;
	private final ExecutorService executor = Executors.newCachedThreadPool();

	private final ISysClient sysClient;

	public DifyChatFlowClient(ISysClient sysClient) {
		this.sysClient = sysClient;
	}

	/**
	 * @return org.springframework.http.ResponseEntity<org.springframework.web.servlet.mvc.method.annotation.SseEmitter>
	 * <AUTHOR>
	 * @Description ChatFlow的流式调用
	 * @Param query 查询内容
	 **/
	public ResponseEntity<SseEmitter> chatMessagesByStreaming(QaRequestVO requestVO) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过智能助手对话操作。query: {}", requestVO.getQuery());
			SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
			try {
				emitter.send(SseEmitter.event().data("{\"message\":\"Dify功能未启用\"}"));
				emitter.complete();
			} catch (Exception e) {
				emitter.completeWithError(e);
			}
			return ResponseEntity.ok(emitter);
		}

		// 构建请求URL
		String url = serverUrl + "/" + API_VERSION + ChatFlowApiEnum.CHAT_MESSAGES.getUri();
		log.warn("Dify请求地址：{}", url);
		// 构建请求体
		ChatMessagesRequestVO requestBody = new ChatMessagesRequestVO();
		// 用户输入内容
		requestBody.setQuery(requestVO.getQuery());
		// 设置自定义传参
		Map<String, Object> inputs = new HashMap<>();
		inputs.put("tenant_id", AuthUtil.getTenantId());
		inputs.put("token", AuthUtil.getHeader());
		inputs.put("current_user", AuthUtil.getUserId().toString());
		inputs.put("current_dept", AuthUtil.getDeptId());
		inputs.put("current_time", DateUtil.format(DateUtil.now(), "yyyy-MM-dd HH:mm:ss"));

		// 添加external_tenant_id参数
		String externalTenantId = "000000"; // 默认值
		try {
			R<String> r = sysClient.getExternalTenantId(AuthUtil.getTenantId(), SystemCodeEnum.CMKS.name());
			if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
				externalTenantId = r.getData();
				log.debug("获取到external_tenant_id: {}", externalTenantId);
			} else {
				log.warn("未能获取到external_tenant_id，使用默认值: {}", externalTenantId);
			}

		} catch (Exception e) {
			log.error("获取external_tenant_id失败，使用默认值: {}", externalTenantId, e);
		}
		inputs.put("external_tenant_id", externalTenantId);

		requestBody.setInputs(inputs);

		// 传入会话id，来维持当前会话
		if (Func.isNotBlank(requestVO.getConversationId())) {
			requestBody.setConversationId(requestVO.getConversationId());
		}
		// 设置输出模式为流式输出
		requestBody.setResponseMode(OutputModeEnum.STREAM.getCode());
		// 用户标识
		requestBody.setUser(AuthUtil.getUserId().toString());

		// 创建SseEmitter对象，服务器超时时间设置为60s
		SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

		emitter.onCompletion(() -> log.info("响应结束"));
		emitter.onTimeout(() -> {
			log.warn("响应超时");
			emitter.complete();
		});

		// 启动新线程发送请求并处理响应
		executor.execute(() -> {
			connectAndHandle(url, requestBody, emitter, this::handleChatMessages);
		});

		return ResponseEntity.ok(emitter);
	}

	/**
	 * @return org.springframework.http.ResponseEntity<org.springframework.web.servlet.mvc.method.annotation.SseEmitter>
	 * <AUTHOR>
	 * @Description 停止响应
	 * @Param taskId：任务id
	 **/
	public ResponseEntity<SseEmitter> stopChatStreaming(QaRequestVO requestVO) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过停止对话操作。taskId: {}", requestVO.getTaskId());
			return ResponseEntity.ok(null);
		}

		if (Func.isBlank(requestVO.getTaskId())) {
			return ResponseEntity.ok(null);
		}
		// 构建请求URL
		String url = serverUrl + "/" + API_VERSION + ChatFlowApiEnum.STOP.getUri().replace("{taskId}", requestVO.getTaskId());
		// 构建请求体
		ChatMessagesStopRequestVO requestBody = new ChatMessagesStopRequestVO();
		// 用户标识
		requestBody.setUser(AuthUtil.getUserId().toString());

		// 创建SseEmitter对象，服务器超时时间设置为60s
		SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

		emitter.onCompletion(() -> log.info("响应结束"));
		emitter.onTimeout(() -> {
			log.warn("响应超时");
			emitter.complete();
		});

		// 启动新线程发送请求并处理响应
		executor.execute(() -> {
			connectAndHandle(url, requestBody, emitter, this::stopChatMessages);
		});

		return ResponseEntity.ok(emitter);
	}

	private <T> void connectAndHandle(String url, T requestBody, SseEmitter emitter, BiConsumer<InputStream, SseEmitter> responseHandler) {
		HttpURLConnection connection = null;
		InputStream responseStream = null;
		try {
			// 创建HttpURLConnection对象
			connection = buildHttpConnection(url);

			// 写入请求体
			try (OutputStream os = connection.getOutputStream()) {
				byte[] input = JSON.toJSONString(requestBody).getBytes(StandardCharsets.UTF_8);
				os.write(input, 0, input.length);
			} catch (IOException ex) {
				log.error("请求异常");
				emitter.completeWithError(ex);
			}

			// 读取响应流
			int responseCode = connection.getResponseCode();
			if (responseCode == HttpURLConnection.HTTP_OK) {
				responseHandler.accept(connection.getInputStream(), emitter);
			} else {
				emitter.completeWithError(new IOException("响应异常：" + responseCode));
			}
		} catch (Exception e) {
			log.error("响应异常：{}", e.getMessage());
			emitter.completeWithError(e);
		} finally {
			// 释放资源
			try {
				if (responseStream != null) {
					responseStream.close();
				}
			} catch (IOException e) {
				log.warn("输入流关闭异常", e);
			}
			if (connection != null) {
				connection.disconnect();
			}
		}
	}

	/**
	 * 获取会话列表
	 * @param requestParams
	 * @return
	 */
	public ResponseEntity<ConversationResponse> getConversations(ConversationRequest requestParams) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过获取会话列表操作");
			return ResponseEntity.ok(new ConversationResponse());
		}

		// 构建请求URL
		String url = serverUrl + "/" + API_VERSION + ChatFlowApiEnum.CONVERSATIONS.getUri();
		// 构造请求头
		Map<String, String> headers = new HashMap<>();
		headers.put("Authorization", "Bearer " + apiKey);
		headers.put("Content-Type", "application/json");

		HttpRequest request = HttpUtil.createGet(url);
		request.headerMap(headers, true);
		request.form("limit", requestParams.getLimit());
		request.form("user", requestParams.getUser());
		request.form("sort_by", requestParams.getSortBy());
		request.form("last_id", requestParams.getLastId());

		// 发起调用
		HttpResponse response = request.execute();

		if (!response.isOk()) {
			throw new RuntimeException("获取会话列表异常");
		}
		String body = response.body();
		ConversationResponse conversationResponse = JSONUtil.toBean(body, ConversationResponse.class);
		return ResponseEntity.ok(conversationResponse);
	}

	/**
	 * 删除会话
	 * @param conversationId
	 * @return
	 */
	public ResponseEntity<Boolean> deleteConversation(String conversationId) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过删除会话操作。conversationId: {}", conversationId);
			return ResponseEntity.ok(true);
		}

		// 构建请求URL
		String url = serverUrl + "/" + API_VERSION + ChatFlowApiEnum.DELETE_CONVERSATION.getUri().replace("{conversationId}", conversationId);
		// 构造请求头
		Map<String, String> headers = new HashMap<>();
		headers.put("Authorization", "Bearer " + apiKey);
		headers.put("Content-Type", "application/json");

		HttpRequest request = HttpUtil.createRequest(Method.DELETE, url);
		request.headerMap(headers, true);

		JSONObject requestBody = new JSONObject();
		requestBody.put("user", AuthUtil.getUserId().toString());
		request.body(JSONUtil.toJsonStr(requestBody));

		// 发起调用
		HttpResponse response = request.execute();

		if (!response.isOk()) {
			throw new RuntimeException("删除会话异常");
		}
		String body = response.body();
		Map<String, String> bodyJson = JSONUtil.toBean(body, Map.class);
		String result = bodyJson.get("result");
		return ResponseEntity.ok("success".equals(result));
	}

	/**
	 * 获取会话历史消息
	 * @param requestParams
	 * @return
	 */
	public ResponseEntity<ConversationMessagesResponse> getConversationMessages(ConversationMessagesRequest requestParams) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过获取会话历史消息操作");
			return ResponseEntity.ok(new ConversationMessagesResponse());
		}

		// 构建请求URL
		String url = serverUrl + "/" + API_VERSION + ChatFlowApiEnum.MESSAGES.getUri();
		// 构造请求头
		Map<String, String> headers = new HashMap<>();
		headers.put("Authorization", "Bearer " + apiKey);
		headers.put("Content-Type", "application/json");

		HttpRequest request = HttpUtil.createGet(url);
		request.headerMap(headers, true);
		request.form("conversation_id", requestParams.getConversationId());
		request.form("first_id", requestParams.getFirstId());
		request.form("limit", requestParams.getLimit());
		request.form("user", requestParams.getUser());

		// 发起调用
		HttpResponse response = request.execute();

		if (!response.isOk()) {
			throw new RuntimeException("获取会话历史消息异常");
		}
		String body = response.body();
		ConversationMessagesResponse conversationMessagesResponse = JSONUtil.toBean(body, ConversationMessagesResponse.class);
		return ResponseEntity.ok(conversationMessagesResponse);
	}

	/**
	 * connection构建
	 * @param url
	 * @return
	 * @throws IOException
	 */
	@NotNull
	private HttpURLConnection buildHttpConnection(String url) throws IOException {
		HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
		connection.setRequestMethod("POST");
		connection.setDoOutput(true);
		connection.setRequestProperty("Content-Type", "application/json");
		connection.setRequestProperty("Authorization", "Bearer " + apiKey);
		connection.setRequestProperty("Accept", "text/event-stream");
		connection.setRequestProperty("Accept-Encoding", "identity");
		connection.setRequestProperty("Connection", "keep-alive");
		connection.setRequestProperty("Accept-Charset", "UTF-8");
		return connection;
	}

	/**
	 * 处理对话消息响应流
	 * @param inputStream
	 * @param emitter
	 */
	private void handleChatMessages(InputStream inputStream, SseEmitter emitter) {
		String line;
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
			while ((line = reader.readLine()) != null) {
				if (Func.isBlank(line)) {
					continue;
				}
				log.warn("dify智能助手返回结果：==============={}\n", StringEscapeUtils.unescapeJava(line));

				if (line.startsWith("data: ")) {
					// 如果已经有一个完整的块，则发送并重置构建器
					line = line.substring(6);
					JSONObject jsonObject = JSON.parseObject(line);
					String event = jsonObject.getString("event");
					if ("message".equals(event)) {
						emitter.send(SseEmitter.event().data(jsonObject.toJSONString()));
					}
					if ("message_end".equals(event)) {
						JSONObject metadata = jsonObject.getJSONObject("metadata");
						if (metadata != null) {
							JSONArray jsonArray = metadata.getJSONArray("retriever_resources");
							extractSourceList(emitter, jsonArray);
						}
					}
				}
			}
			emitter.complete();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * <AUTHOR>
	 * @Description 添加来源文件
	 * @Date 下午5:39 2025/4/14
	 * @Param [emitter, sourceArray]
	 * @return void
	 **/

	public static void extractSourceList(SseEmitter emitter, JSONArray sourceArray) throws IOException {
		// 来源的文件
		List<DifySourceDTO> sourceList = new ArrayList<>();

		// 来源的文件是否重复
		List<String> repeatCheckList = new ArrayList<>();
		JSONObject sourceObject = new JSONObject();
		sourceObject.put("event", "source_list");

		if (sourceArray != null) {
			for (Object o : sourceArray) {
				JSONObject metadata = (JSONObject) o;
				String datasetName = metadata.getString("dataset_name");
				// 如果是故障案例库
				if (DifyDbType.FAULT_CASE_DATABASE.getDescription().equals(datasetName)) {
					DifySourceDTO sourceDTO = new DifySourceDTO();
					sourceDTO.setSourceType(DifyDbType.FAULT_CASE_DATABASE.getDescription());
					String fileName = metadata.getString("document_name");
					//原始的文件名是 ZC202504200001刮板输送机:卡阻.md ,前14位是单号,刮板输送机:卡阻是案例的名称
					sourceDTO.setFileId(fileName.substring(0,14));
					String[] split = fileName.split(":");
					String caseName = split[1].substring(0,split[1].indexOf(".") + 1);
					sourceDTO.setFileName(caseName);
					//是否有重复的
					String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
					if (!repeatCheckList.contains(strRepeat)) {
						repeatCheckList.add(strRepeat);
						sourceList.add(sourceDTO);
					}



				}
				// 如果是DEVICE_MANUAL
				if (DifyDbType.DEVICE_MANUAL.getDescription().equals(datasetName)) {
					DifySourceDTO sourceDTO = new DifySourceDTO();
					sourceDTO.setSourceType(DifyDbType.DEVICE_MANUAL.getDescription());
					String fileName = metadata.getString("document_name");
					JSONObject docMetadata = metadata.getJSONObject("doc_metadata");
					if (docMetadata != null) {
						sourceDTO.setFileId(docMetadata.getString("simas_file_id"));
						sourceDTO.setFileLink(metadata.getString("simas_file_link"));
					}
					if(fileName.contains("_")){
						//fileName的格式 是设备名_文件名,获取第一个_后面的内容作为文件名
						fileName = fileName.substring(fileName.indexOf("_") + 1);
					}
					sourceDTO.setFileName(fileName);
					String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
					if (!repeatCheckList.contains(strRepeat)) {
						repeatCheckList.add(strRepeat);
						sourceList.add(sourceDTO);
					}
				}
				// 如果是煤炭设备知识库
				if (DifyDbType.COAL_KNOWLEDGE_DATABASE.getDescription().equals(datasetName)) {
					DifySourceDTO sourceDTO = new DifySourceDTO();
					sourceDTO.setSourceType(DifyDbType.COAL_KNOWLEDGE_DATABASE.getDescription());
					String fileName = metadata.getString("document_name");
					sourceDTO.setFileName(fileName);
					JSONObject docMetadata = metadata.getJSONObject("doc_metadata");
					if (docMetadata != null) {
						sourceDTO.setFileLink(docMetadata.getString("attach_link"));
						log.info("煤炭设备知识库文件链接: {}", sourceDTO.getFileLink());
					}


					String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
					if (!repeatCheckList.contains(strRepeat)) {
						repeatCheckList.add(strRepeat);
						sourceList.add(sourceDTO);
					}
				}
			}
			sourceObject.put("data", sourceList);
			emitter.send(SseEmitter.event()
				.data(StringEscapeUtils.unescapeJava(sourceObject.toJSONString())));
		}
	}

	/**
	 * 停止对话消息响应流
	 * @param inputStream
	 * @param emitter
	 */
	private void stopChatMessages(InputStream inputStream, SseEmitter emitter) {
		String line;
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
			while ((line = reader.readLine()) != null) {
				if (Func.isBlank(line)) {
					continue;
				}
				log.warn("dify智能助手返回结果：==============={}\n", StringEscapeUtils.unescapeJava(line));

				JSONObject jsonObject = JSON.parseObject(line);
				emitter.send(SseEmitter.event().data(jsonObject.toJSONString()));
			}
			emitter.complete();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
