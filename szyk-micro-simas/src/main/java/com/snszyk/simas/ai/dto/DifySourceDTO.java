package com.snszyk.simas.ai.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 问答的来源
 */
@Data
@ApiModel(value = "DifySourceDTO", description = "DifySourceDTO")
public class DifySourceDTO {
//
//	@ApiModelProperty(value = "来源类别")
//	private String sourceTypeName;

	@ApiModelProperty(value = "来源类别")
	private String sourceType;

	@ApiModelProperty(value = "来源文件名")
	private String fileName;

	@ApiModelProperty(value = "来源文件id或者案例的单号")
	private String fileId;
	@ApiModelProperty(value = "来源文件link")
	private String fileLink;


}
