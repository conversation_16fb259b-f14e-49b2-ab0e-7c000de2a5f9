package com.snszyk.simas.ai.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 维修的建议
 */
@Data
@ApiModel(value = "RepairSuggestGenVO", description = "RepairSuggestGenVO")
public class RepairSuggestGenVO {
	@ApiModelProperty(value = "设备名称", required = true)
	private String deviceName;

	@ApiModelProperty(value = "异常描述", required = true)
	private String defectDesc;



}
