package com.snszyk.simas.ai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.equipment.cache.CommonCache;
import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.common.equipment.feign.ICommonClient;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.equipment.vo.DeviceMonitorVO;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.ai.DifyClient;
import com.snszyk.simas.ai.dto.OperateStandardsMonitorGenDTO;
import com.snszyk.simas.ai.dto.OperateStandardsMonitorGenResultDTO;
import com.snszyk.simas.ai.dto.OperateStandardsMonitorGenSourceDTO;
import com.snszyk.simas.ai.dto.RepairSuggestGenDTO;
import com.snszyk.simas.ai.enums.DifyDbType;
import com.snszyk.simas.ai.vo.OperateStandardsGenVO;
import com.snszyk.simas.ai.vo.OperateStandardsMonitorGenVO;
import com.snszyk.simas.ai.vo.RepairSuggestGenVO;
import com.snszyk.simas.fault.dto.FaultDefectCaseDTO;
import com.snszyk.simas.fault.entity.FaultDefectCase;
import com.snszyk.simas.fault.service.IFaultDefectCaseService;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.enums.SystemCodeEnum;
import com.snszyk.system.feign.ISysClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI工具服务类
 * 提供AI相关的功能，包括文件上传、运维标准生成、故障案例同步和维修建议生成等功能
 *
 * <AUTHOR>
 * @since 2025/4/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiToolsService {

	private static final String STATUS_SUCCEEDED = "succeeded";
	private static final String ERROR_WORKFLOW_FAILED = "工作流执行失败";
	private static final String ERROR_REPAIR_SUGGEST_FAILED = "生成维修建议失败";

	private final IDeviceAccountClient deviceAccountClient;
	private final IFaultDefectCaseService faultDefectCaseService;
	private final DifyClient difyClient;
	private final ISysClient sysClient;

	private final ICommonClient commonClient;

	@Value("${dify.operation_standards_api_key}")
	private String operationStandardsApiKey;

	@Value("${dify.repair_suggest_api_key}")
	private String repairSuggestApiKey;


	/**
	 * 删除AI服务中的文档
	 *
	 * @param fileId 文件ID
	 * @return 删除结果
	 */
	public String deleteDocument(String fileId) {
		return difyClient.deleteDocument(fileId);
	}

	/**
	 * 运行AI工作流
	 *
	 * @param query         查询内容
	 * @param orderType     订单类型
	 * @param simasDeviceId 设备ID
	 * @return 工作流执行结果
	 */
	public JSONObject runWorkflow(String query, String orderType, String simasDeviceId) {
		Map<String, Object> params = new HashMap<>();
		params.put("query", query);
		params.put("orderType", orderType);
		params.put("simasDeviceId", simasDeviceId);
		return difyClient.runWorkflow(query, params, operationStandardsApiKey);
	}

	/**
	 * 生成操作标准
	 *
	 * @param operateStandardsGenVO 操作标准生成请求参数
	 * @return 操作标准生成结果
	 * @throws ServiceException 如果生成过程中发生错误
	 */
	public OperateStandardsMonitorGenResultDTO generateOperateStandards(OperateStandardsGenVO operateStandardsGenVO) {
		OperateStandardsMonitorGenResultDTO resultDTO = new OperateStandardsMonitorGenResultDTO();

		// 获取设备信息
		DeviceAccountVO accountVO = getDeviceAccount(operateStandardsGenVO.getEquipmentId());
		if (accountVO == null) {
			return resultDTO;
		}

		// 构建监控点信息
		List<OperateStandardsMonitorGenVO> monitorList = operateStandardsGenVO.getMonitorList();
		Map<String, Long> monitorMap = createMonitorMap(operateStandardsGenVO.getEquipmentId());
		String monitorDesc = buildMonitorDescription(monitorList);

		// 生成优化的提示词
		String prompt = buildComprehensivePrompt(accountVO, monitorDesc, operateStandardsGenVO.getOrderTypeName());

		// 执行工作流并处理结果
		JSONObject result = executeWorkflowAndGetResult(prompt, operateStandardsGenVO);

		// 解析结果
		parseWorkflowResult(result, monitorMap, resultDTO);

		return resultDTO;
	}

	/**
	 * 同步所有故障案例到AI系统
	 */
	public void defectCaseSync() {
		List<FaultDefectCase> cases = faultDefectCaseService.list();
		for (FaultDefectCase defectCase : cases) {
			try {
				handleOneDefectSync(defectCase.getNo());
			} catch (Exception e) {
				log.error("故障案例同步失败, 案例编号: {}", defectCase.getNo(), e);
			}
		}
	}

	/**
	 * 处理单个故障案例的同步
	 *
	 * @param caseNo 案例编号
	 */
	public void handleOneDefectSync(String caseNo) {
		try {
			log.info("开始同步故障案例到Dify平台，案例编号: {}", caseNo);
			FaultDefectCaseDTO caseData = faultDefectCaseService.view(caseNo);
			String markdown = generateMarkdown(caseData);
			String documentName = caseNo + caseData.getEquipmentName() + ":" + caseData.getFaultName() + ".md";
			String result = difyClient.createDocumentByText(documentName, markdown);
			log.info("故障案例同步完成，案例编号: {}, 结果: {}", caseNo, result);
		} catch (Exception e) {
			log.error("故障案例同步失败, 案例编号: {}", caseNo, e);
			throw new ServiceException("故障案例同步失败: " + e.getMessage());
		}
	}

	/**
	 * 生成维修建议
	 *
	 * @param vo 维修建议生成请求参数
	 * @return 维修建议生成结果
	 * @throws ServiceException 如果生成过程中发生错误
	 */
	public RepairSuggestGenDTO generateRepairSuggest(RepairSuggestGenVO vo) {
		try {
			log.info("开始生成维修建议，设备名称: {}, 故障描述: {}", vo.getDeviceName(), vo.getDefectDesc());
			String prompt = String.format("设备名称:%s,故障描述:%s", vo.getDeviceName(), vo.getDefectDesc());

			// 构建参数，添加external_tenant_id
			Map<String, Object> params = new HashMap<>();
			String externalTenantId = getExternalTenantId();
			params.put("external_tenant_id", externalTenantId);
			log.debug("添加external_tenant_id参数: {}", externalTenantId);

			JSONObject result = difyClient.runWorkflow(prompt, params, repairSuggestApiKey);

			RepairSuggestGenDTO repairSuggest = processRepairSuggestResult(result);
			log.info("维修建议生成完成，设备名称: {}", vo.getDeviceName());
			return repairSuggest;
		} catch (Exception e) {
			log.error("生成维修建议失败，设备名称: {}, 故障描述: {}", vo.getDeviceName(), vo.getDefectDesc(), e);
			throw new ServiceException(ERROR_REPAIR_SUGGEST_FAILED + ": " + e.getMessage());
		}
	}

	/**
	 * 流式生成维修建议
	 *
	 * @param vo 维修建议生成请求参数
	 * @return SSE响应
	 */
	public ResponseEntity<SseEmitter> generateRepairSuggestStream(RepairSuggestGenVO vo) {
		try {
			log.info("开始流式生成维修建议，设备名称: {}, 故障描述: {}", vo.getDeviceName(), vo.getDefectDesc());
			String prompt = String.format("设备名称:%s,故障描述:%s", vo.getDeviceName(), vo.getDefectDesc());

			// 构建参数，添加external_tenant_id
			Map<String, Object> params = new HashMap<>();
			String externalTenantId = getExternalTenantId();
			params.put("external_tenant_id", externalTenantId);
			log.debug("添加external_tenant_id参数: {}", externalTenantId);

			return difyClient.runWorkflowStream(prompt, params, repairSuggestApiKey);
		} catch (Exception e) {
			log.error("流式生成维修建议失败，设备名称: {}, 故障描述: {}", vo.getDeviceName(), vo.getDefectDesc(), e);
			throw new ServiceException(ERROR_REPAIR_SUGGEST_FAILED + ": " + e.getMessage());
		}
	}

	// 私有辅助方法...
	private DeviceAccountVO getDeviceAccount(Long equipmentId) {
		R<DeviceAccountVO> response = deviceAccountClient.deviceInfoById(equipmentId);
		if (!response.isSuccess() || response.getData() == null) {
			return null;
		}
		return response.getData();
	}

	/**
	 * 获取当前租户的external_tenant_id
	 *
	 * @return external_tenant_id，如果获取失败返回默认值"000000"
	 */
	private String getExternalTenantId() {
		try {
			R<String> r = sysClient.getExternalTenantId(AuthUtil.getTenantId(), SystemCodeEnum.CMKS.name());
			if (r.isSuccess() && Func.isNotEmpty(r.getData())) {
				return r.getData();
			}

		} catch (Exception e) {
			log.error("获取external_tenant_id失败，使用默认值: 000000", e);
		}
		return "000000";
	}

	private Map<String, Long> createMonitorMap(Long equipmentId) {
		Map<String, Long> resultMap = new HashMap<>();
		DeviceMonitorVO monitorVO = new DeviceMonitorVO();
		monitorVO.setDeviceId(equipmentId);
		R<List<DeviceMonitor>> listR = commonClient.deviceMonitorByParams(monitorVO);
		if (listR.isSuccess() && listR.getData() != null) {
			for (DeviceMonitor monitor : listR.getData()) {
				resultMap.put(monitor.getName(), monitor.getId());
			}
		}

		return resultMap;

	}

	private String buildMonitorDescription(List<OperateStandardsMonitorGenVO> monitorList) {
		return monitorList.stream()
			.map(e -> "- " + e.getMonitorName() + "(" + e.getMonitorTypeName() + ")")
			.collect(Collectors.joining("\n"));
	}

	/**
	 * 构建旧版提示词（保留作为备份）
	 *
	 * @param accountVO     设备账户信息
	 * @param monitorDesc   监控点描述
	 * @param orderTypeName 工单类型名称
	 * @return 旧版提示词
	 */
	private String buildPrompt(DeviceAccountVO accountVO, String monitorDesc, String orderTypeName) {
		return String.format(
			"%s设备类型为（%s），该设备当前的部位有:\n%s，查询当前设备所有的部位和每个部位的%s标准和%s方法。",
			accountVO.getName(),
			accountVO.getCategoryName() == null ? "未知" : accountVO.getCategoryName(),
			monitorDesc,
			orderTypeName,
			orderTypeName);
	}

	/**
	 * 构建优化的提示词，用于全面获取设备维护信息
	 *
	 * @param accountVO       设备账户信息
	 * @param knownComponents 已知部位描述
	 * @param maintenanceType 维护类型
	 * @return 优化的提示词
	 */
	private String buildComprehensivePrompt(DeviceAccountVO accountVO, String knownComponents, String maintenanceType) {
		return String.format(
			"我需要获取以下设备的完整维护信息，具体要求如下：\n\n"
				+ "1. 设备基础信息：\n"
				+ "   - 设备类型：%s（型号：%s）\n"
				+ "   - 已知部位示例：%s（仅供参考）\n\n"
				+ "2. 核心查询要求：\n"
				+ "   - 列出该%s所有可能的组成部位（包括但不限于已知部位）\n"
				+ "   - 对每个部位提供：\n"
				+ "     * 标准名称（专业术语）\n"
				+ "     * %s标准（包括周期、参数阈值、检测方法等）\n"
				+ "     * %s方法（分步骤说明，含工具/材料要求等）\n"
				+ "     * 适用技术规范（行业标准/厂商手册/使用说明书等）\n\n"
				+ "3. 动态适配要求：\n"
				+ "   - 自动适配不同变体（如防护等级IPXX、功率规格等）\n"
				+ "   - 包含常规部位和特殊选装部件\n"
				+ " ",
			accountVO.getCategoryName() == null ? "未知" : accountVO.getCategoryName(),
			accountVO.getModel() == null ? "未知" : accountVO.getModel(),
			knownComponents,
			accountVO.getCategoryName() == null ? "未知" : accountVO.getCategoryName(),
			maintenanceType,
			maintenanceType);
	}

	private JSONObject executeWorkflowAndGetResult(String prompt, OperateStandardsGenVO operateStandardsGenVO) {
		Map<String, Object> params = new HashMap<>();
		params.put("order_type", operateStandardsGenVO.getOrderTypeName());
		params.put("simas_device_id", operateStandardsGenVO.getEquipmentId().toString());

		// 添加external_tenant_id参数
		String externalTenantId = getExternalTenantId();
		params.put("external_tenant_id", externalTenantId);
		log.debug("添加external_tenant_id参数: {}", externalTenantId);

		JSONObject result = difyClient.runWorkflow(prompt, params, operationStandardsApiKey);
		validateWorkflowResult(result);
		return result;
	}

	private void validateWorkflowResult(JSONObject result) {
		JSONObject data = result.getJSONObject("data");
		String status = data.getString("status");
		if (!STATUS_SUCCEEDED.equals(status)) {
			log.error("工作流执行失败，状态：{}", status);
			throw new ServiceException(ERROR_WORKFLOW_FAILED);
		}
	}

	private void parseWorkflowResult(JSONObject result, Map<String, Long> monitorMap,
									 OperateStandardsMonitorGenResultDTO resultDTO) {
		// 解析输出结果
		JSONObject outputs = result.getJSONObject("data").getJSONObject("outputs");
		if (outputs == null) {
			throw new ServiceException("工作流输出为空");
		}

		// 处理标准和方法
		String standardsResult = cleanJsonString(outputs.getString("result"));
		JSONArray standardsArray = JSON.parseArray(standardsResult);
		processStandardsResult(standardsArray, monitorMap, resultDTO);

		// 处理来源信息
		JSONArray sourceArray = outputs.getJSONArray("content");
		if (sourceArray != null) {
			processSourceContent(sourceArray, resultDTO);
		}
	}

	private String cleanJsonString(String json) {
		return StringEscapeUtils.unescapeJava(json)
			.replace("\\n", "")
			.replace("\\t", "")
			.replace("```json", "")
			.replace("```", "");
	}

	/**
	 * 处理标准结果数据
	 * 将AI返回的标准数据转换为系统所需的格式，并补充监控点相关信息
	 *
	 * @param array      AI返回的标准数据数组
	 * @param monitorMap 监控点映射表
	 * @param resultDTO  结果数据传输对象
	 */
	private void processStandardsResult(JSONArray array, Map<String, Long> monitorMap,
										OperateStandardsMonitorGenResultDTO resultDTO) {
		List<OperateStandardsMonitorGenDTO> resultList = new ArrayList<>();

		for (Object o : array) {
			JSONObject obj = (JSONObject) o;
			String monitorName = obj.getString("部位名称");
			String standards = obj.getString("标准");
			String method = obj.getString("方法");

			if (monitorName == null || (standards == null && method == null)) {
				continue;
			}

			OperateStandardsMonitorGenDTO dto = new OperateStandardsMonitorGenDTO();
			dto.setMonitorName(monitorName);
			dto.setStandards(standards);
			dto.setMethod(method);

			if (monitorMap.containsKey(monitorName)) {
				Long monitorId = monitorMap.get(monitorName);
				dto.setMonitorId(String.valueOf(monitorId));
				if (monitorId != null) {
					DeviceMonitor monitor = CommonCache.getMonitor(monitorId);
					dto.setMonitorType(monitor.getType());
					if (Func.isNotEmpty(monitor.getType())) {
						String monitorTypeName = DictBizCache.getValue("monitor_type", monitor.getType());
						dto.setMonitorTypeName(monitorTypeName);
					}
				}
			}

			resultList.add(dto);
		}

		resultDTO.setMonitorList(resultList);
	}

	/**
	 * 处理来源内容数据
	 * 解析AI返回的来源数据，提取标题和内容信息
	 *
	 * @param sourceArray AI返回的来源数据数组
	 * @param resultDTO   结果数据传输对象，用于存储处理后的来源信息
	 */
	private void processSourceContent(JSONArray sourceArray, OperateStandardsMonitorGenResultDTO resultDTO) {
		for (Object o : sourceArray) {
			JSONObject obj = (JSONObject) o;
			String title = obj.getString("title");
			String content = obj.getString("content");

			if (title == null || content == null || Func.isEmpty(title) || Func.isEmpty(content)) {
				continue;
			}
			//trim
			title = title.trim();
			content = content.trim();
			if (Func.isEmpty(title) || Func.isEmpty(content)) {
				continue;
			}

			OperateStandardsMonitorGenSourceDTO sourceDTO = new OperateStandardsMonitorGenSourceDTO();
			if (title.contains("_")) {
				title = title.split("_")[1];
			}
			sourceDTO.setTitle(title);
			sourceDTO.setContent(content.replace("\\n", ""));
			resultDTO.getMonitorSourceList().add(sourceDTO);
		}
	}

	private RepairSuggestGenDTO processRepairSuggestResult(JSONObject result) {
		validateWorkflowResult(result);

		RepairSuggestGenDTO dto = new RepairSuggestGenDTO();
		JSONObject outputs = result.getJSONObject("data").getJSONObject("outputs");

		// 设置建议内容
		dto.setSuggest(outputs.getString("result"));

		// 处理来源信息
		JSONArray sourceArray = outputs.getJSONArray("content");
		if (sourceArray != null) {
			processRepairSuggestSources(sourceArray, dto);
		}

		// 去重
		dto.setSourceList(dto.getSourceList().stream().distinct().collect(Collectors.toList()));
		return dto;
	}

	private void processRepairSuggestSources(JSONArray sourceArray, RepairSuggestGenDTO dto) {
		for (Object o : sourceArray) {
			JSONObject obj = (JSONObject) o;
			JSONObject metadata = obj.getJSONObject("metadata");

			if (metadata == null) {
				continue;
			}

			String datasetName = metadata.getString("dataset_name");
			if (DifyDbType.FAULT_CASE_DATABASE.getDescription().equals(datasetName)) {
				dto.getSourceList().add(datasetName);
			} else if (DifyDbType.DEVICE_MANUAL.getDescription().equals(datasetName)) {
				dto.getSourceList().add(metadata.getString("document_name"));
			}
		}
	}

	/**
	 * 生成故障案例的Markdown文档
	 *
	 * @param caseData 故障案例数据
	 * @return 格式化后的Markdown字符串
	 */
	private static String generateMarkdown(FaultDefectCaseDTO caseData) {
		StringBuilder markdown = new StringBuilder();

		// 设备基础信息
		markdown.append("# 设备名称: ").append(caseData.getEquipmentName()).append("\n\n");
		markdown.append("## 设备部位: ").append(caseData.getMonitorName()).append("\n");
		markdown.append("## 设备型号: ").append(caseData.getEquipmentModel()).append("\n\n");

		// 故障信息区块
		markdown.append("## 故障信息\n");
		markdown.append("- **故障缺陷名称**: ").append(caseData.getFaultName()).append("\n");
		markdown.append("- **异常等级**: ").append(caseData.getFaultLevelName()).append("\n");
		markdown.append("- **故障缺陷类型**: ").append(caseData.getFaultTypeName()).append("\n");
		markdown.append("- **故障描述**: ").append(caseData.getFaultDesc()).append("\n");

		// 处理可能为空的字段
		if (StringUtils.hasText(caseData.getFaultReason())) {
			markdown.append("- **故障原因**: ").append(caseData.getFaultReason()).append("\n");
		}
		if (StringUtils.hasText(caseData.getSolution())) {
			markdown.append("- **解决方案**: ").append(caseData.getSolution()).append("\n");
		}

		// 材料清单表格
		markdown.append("\n## 更换零件清单\n");
		markdown.append("| 序号 | 名称 | 型号 | 数量 |\n");
		markdown.append("|------|------|------|------|\n");

		if (caseData.getMaterialList() != null && !caseData.getMaterialList().isEmpty()) {
			for (ComponentMaterialVO material : caseData.getMaterialList()) {
				markdown.append(String.format("| %4d | %s | %s | %.2f |\n",
					material.getSort(),
					material.getName(),
					material.getModel() != null ? material.getModel() : "未填写",
					material.getCount()));
			}
		}

		return markdown.toString();
	}
}
