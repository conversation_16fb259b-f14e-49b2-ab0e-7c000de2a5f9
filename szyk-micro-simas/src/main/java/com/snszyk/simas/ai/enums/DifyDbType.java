package com.snszyk.simas.ai.enums;

/**
 * <AUTHOR>
 * @Description dify 的知识库的枚举
 * @Date 下午4:58 2025/4/9
 * @Param
 * @return
 **/
public enum DifyDbType {
    DEVICE_MANUAL("设备知识库"),
    FAULT_CASE_DATABASE("故障案例库"),
    COAL_KNOWLEDGE_DATABASE("煤炭设备知识库"),
    LARGE_MODEL("通用大模型");

    private final String description;

    DifyDbType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return this.description;
    }

    public static DifyDbType fromDescription(String description) {
        for (DifyDbType type : DifyDbType.values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的知识库类型描述: " + description);
    }
}
