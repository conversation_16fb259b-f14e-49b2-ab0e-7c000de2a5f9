package com.snszyk.simas.ai.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 运维标准
 */
@Data
@ApiModel(value = "OperateStandardsMonitorGenVO", description = "OperateStandardsMonitorGenVO")
public class OperateStandardsGenVO {
	@ApiModelProperty(value = "工单类型名", required = true)
	private String orderTypeName;

	@ApiModelProperty(value = "设备名称", required = true)
	private String equipmentName;

	@ApiModelProperty(value = "设备id", required = true)
	private Long equipmentId;

	@ApiModelProperty(value = "设备分类id", hidden = true)
	private Long equipmentCategoryId;

	@ApiModelProperty(value = "设备分类名", hidden = true)
	private String equipmentCategoryName;

	@ApiModelProperty(value = "部位list", required = true)
//	@NotEmpty(message = "部位不能为空")
	private List<OperateStandardsMonitorGenVO> monitorList = new ArrayList<>();

//	@ApiModelProperty(value = "资料list", required = true)
//	@NotEmpty(message = "资料不能为空")
//	private List<Long> fileList = new ArrayList<>();

}
