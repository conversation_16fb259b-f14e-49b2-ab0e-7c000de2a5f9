package com.snszyk.simas.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.ai.dto.DifySourceDTO;
import com.snszyk.simas.ai.enums.DifyApiUrl;
import com.snszyk.simas.ai.enums.DifyDbType;
import com.snszyk.simas.ai.enums.DifyMetadataKey;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @Description dify客户端
 * @Date 上午10:46 2025/4/8
 **/
@Slf4j
@Component
@Data
public class DifyClient {

	private static final long SSE_TIMEOUT = 600000L; // 10分钟超时
	private static final String CACHE_PREFIX = "dify";
	private static final String FILE_ID_KEY = "fileId:";

	@Value("${dify.enabled:true}")
	private boolean difyEnabled;

	@Value("${dify.equipment_dataset_id}")
	private String equipmentDatasetId;
	@Value("${dify.defect_dataset_id}")
	private String defectDatasetId;
	@Value("${dify.device_dataset_api_key}")
	private String deviceDatasetApiKey;

	@Value("${dify.server_url}")
	private String serverUrl;

	private final RestTemplate restTemplate;
	private final ExecutorService executorService;
	private final IDeviceAccountClient deviceAccountClient;

	/**
	 * 默认构造函数
	 * 初始化RestTemplate和线程池
	 */
	public DifyClient(IDeviceAccountClient deviceAccountClient) {
		this.restTemplate = new RestTemplateConfig().restTemplate();
		this.executorService = Executors.newCachedThreadPool();
		this.deviceAccountClient = deviceAccountClient;
	}

	/**
	 * 将文件同步到Dify平台
	 *
	 * @param fileId       文件ID
	 * @param deviceId     设备ID
	 * @param tenantId     租户ID
	 * @param fileLink     文件链接
	 * @param originalName 原始文件名
	 * @return 上传响应结果的JSON字符串
	 * @throws IOException IO异常
	 */
	public String uploadFile(String fileId, String deviceId, String tenantId, String fileLink, String originalName)
		throws IOException {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过文件上传操作。fileId: {}, deviceId: {}, tenantId: {}, 文件名: {}",
				fileId, deviceId, tenantId, originalName);
			return "Dify功能未启用";
		}

		File file = new File(fileLink);
		// 根据设备id查询设备
		DeviceAccountVO deviceAccountVO = deviceAccountClient.deviceInfoById(Long.valueOf(deviceId)).getData();
		if (deviceAccountVO == null) {
			throw new ServiceException("设备不存在");
		}
		String deviceName = deviceAccountVO.getName();
		// 2. 构建multipart/form-data请求体
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

		// 添加文件参数
		body.add("file", new MultipartFileResource(file, deviceName + "_" + originalName));
		// 使用Map构建JSON配置参数
		String jsonConfig = genFileUploadConfig();
		// 创建一个 Gson 对象
		Gson gson = new Gson();

		// 创建 Map 对象来存储配置
		Map<String, Object> config = new HashMap<>();
		config.put("indexing_technique", "high_quality");

		// 创建 process_rule Map
		Map<String, Object> processRule = new HashMap<>();

		// 创建 rules Map
		Map<String, Object> rules = new HashMap<>();

		// 创建 pre_processing_rules List
		List<Map<String, Object>> preProcessingRules = new ArrayList<>();

		// 添加 remove_extra_spaces 规则
		Map<String, Object> rule1 = new HashMap<>();
		rule1.put("id", "remove_extra_spaces");
		rule1.put("enabled", true);
		preProcessingRules.add(rule1);

		// 添加 remove_urls_emails 规则
		Map<String, Object> rule2 = new HashMap<>();
		rule2.put("id", "remove_urls_emails");
		rule2.put("enabled", true);
		preProcessingRules.add(rule2);

		// 将 pre_processing_rules 添加到 rules 中
		rules.put("pre_processing_rules", preProcessingRules);

		// 创建 segmentation Map
		Map<String, Object> segmentation = new HashMap<>();
		segmentation.put("separator", "\n\n");
		segmentation.put("max_tokens", 512);

		// 将 segmentation 添加到 rules 中
		rules.put("segmentation", segmentation);

		// 将 rules 添加到 process_rule 中
		processRule.put("rules", rules);

		// 将 mode 添加到 process_rule 中
		processRule.put("mode", "automatic");
		// Map<String, Object> subchunkSegmentation = new HashMap<>();
		// subchunkSegmentation.put("chunk_overlap", "100");
		// rules.put("subchunk_segmentation", subchunkSegmentation);
		// 将 process_rule 添加到 config 中
		config.put("process_rule", processRule);
		// config.put("doc_form", "qa_model");
		// config.put("doc_form", "hierarchical_model");
		// config.put("doc_language", "Chinese");

		// 将 Map 转换为 JSON 字符串
		String jsonString = gson.toJson(config);
		body.add("data", jsonString);
		// 添加JSON配置参数
		// String jsonConfig1 = String.format(
		// "{\"indexing_technique\":\"high_quality\"," +
		// "\"process_rule\":" +
		// "{\"rules\":" +
		// "{\"pre_processing_rules\":" +
		// "[{\"id\":\"remove_extra_spaces\",\"enabled\":true}" +
		// ",{\"id\":\"remove_urls_emails\",\"enabled\":true}]" +
		// ",\"segmentation\":{\"separator\":\"###\"" +
		// ",\"max_tokens\":1024}}" +
		// ",\"mode\":\"custom\"}}"
		// );
		//// body.add("data", jsonConfig1);

		// 3. 设置请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setBearerAuth(deviceDatasetApiKey);
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		// 4. 发送POST请求
		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
		ResponseEntity<String> response = restTemplate.exchange(
			DifyApiUrl.CREATE_DOCUMENT_BY_FILE.getFullUrl(serverUrl, equipmentDatasetId),
			HttpMethod.POST,
			requestEntity,
			String.class);

		// 5. 处理响应
		String responseBody = response.getBody();
		log.info("上传文件响应: {}", responseBody);
		// 转为jsonObject
		JSONObject jsonObject = JSONObject.parseObject(responseBody);
		JSONObject document = jsonObject.getJSONObject("document");
		String documentId = document.getString("id");
		log.info("上传文件成功，documentId:{}, deviceId:{}, tenantId:{}, 文件名:{}",
			documentId, deviceId, tenantId, originalName);
		log.info("上传文件成功，返回的文件id为{}", documentId);
		CacheUtil.put(CACHE_PREFIX, FILE_ID_KEY + fileId, documentId, false);
		// 6. 上传文件元数据
		uploadDocumentMetadata(documentId, deviceId, fileId, tenantId, fileLink);
		return responseBody;
	}

	/**
	 * 生成文件上传的配置参数
	 *
	 * @return 配置参数的JSON字符串
	 * @throws JsonProcessingException JSON处理异常
	 */
	private static String genFileUploadConfig() throws JsonProcessingException {
		// 1.索引方式
		Map<String, Object> jsonConfigMap = new HashMap<>();
		// 高质量
		jsonConfigMap.put("indexing_technique", "high_quality");
		// 2.doc_form 索引内容的形式
		jsonConfigMap.put("doc_form", "text_model");
		// 3.以下的为处理规则 目前使用的是自动的,暂时用不到
		Map<String, Object> processRules = new HashMap<>();
		processRules.put("id", "remove_extra_spaces");
		processRules.put("enabled", false);

		Map<String, Object> processRules2 = new HashMap<>();
		processRules2.put("id", "remove_urls_emails");
		processRules2.put("enabled", true);

		List<Map<String, Object>> preProcessingRules = new ArrayList<>();
		preProcessingRules.add(processRules);
		preProcessingRules.add(processRules2);

		Map<String, Object> rules = new HashMap<>();
		rules.put("pre_processing_rules", preProcessingRules);

		Map<String, Object> segmentation = new HashMap<>();
		segmentation.put("separator", "###");
		segmentation.put("max_tokens", 5000);

		Map<String, Object> processRule = new HashMap<>();
		processRule.put("rules", rules);
		processRule.put("segmentation", segmentation);

		jsonConfigMap.put("process_rule", processRule);
		// 使用自定义的规则
		// jsonConfigMap.put("mode", "custom");
		jsonConfigMap.put("mode", "automatic");
		// jsonConfigMap.put("mode", "string");

		// 将Map转换为JSON字符串
		ObjectMapper objectMapper = new ObjectMapper();
		String jsonConfig = objectMapper.writeValueAsString(jsonConfigMap);
		return jsonConfig;
	}

	/**
	 * 上传文档的元数据信息
	 *
	 * @param difyDocumentId Dify平台的文档ID
	 * @param simasDeviceId  SIMAS系统的设备ID
	 * @param simasFileId    SIMAS系统的文件ID
	 * @param simasTenantId  SIMAS系统的租户ID
	 * @param fileLink       文件链接
	 */
	public void uploadDocumentMetadata(String difyDocumentId, String simasDeviceId, String simasFileId,
									   String simasTenantId, String fileLink) {
		String url = DifyApiUrl.UPDATE_DOCUMENT_METADATA.getFullUrl(serverUrl, equipmentDatasetId);
		Map<String, Object> requestBody = buildMetadataRequestBody(difyDocumentId, simasDeviceId, simasFileId,
			simasTenantId, fileLink);
		executeMetadataUpload(url, requestBody);
	}

	/**
	 * 构建元数据请求体
	 *
	 * @param difyDocumentId Dify文档ID
	 * @param simasDeviceId  SIMAS设备ID
	 * @param simasFileId    SIMAS文件ID
	 * @param simasTenantId  SIMAS租户ID
	 * @param fileLink       文件链接
	 * @return 请求体Map
	 */
	private Map<String, Object> buildMetadataRequestBody(String difyDocumentId, String simasDeviceId,
														 String simasFileId,
														 String simasTenantId, String fileLink) {
		List<Map<String, Object>> metadataList = new ArrayList<>();

		// 添加文件ID元数据
		metadataList.add(createMetadata(DifyMetadataKey.SIMAS_FILE_ID, simasFileId));
		// 添加设备ID元数据
		metadataList.add(createMetadata(DifyMetadataKey.SIMAS_DEVICE_ID, simasDeviceId));
		// 添加租户ID元数据
		metadataList.add(createMetadata(DifyMetadataKey.SIMAS_TENANT_ID, simasTenantId));
		// 添加文件链接元数据
		metadataList.add(createMetadata(DifyMetadataKey.SIMAS_FILE_LINK, fileLink));

		Map<String, Object> operationData = new HashMap<>();
		operationData.put("document_id", difyDocumentId);
		operationData.put("metadata_list", metadataList);

		List<Map<String, Object>> operationDataList = new ArrayList<>();
		operationDataList.add(operationData);

		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("operation_data", operationDataList);

		return requestBody;
	}

	/**
	 * 创建元数据对象
	 *
	 * @param key   元数据键
	 * @param value 元数据值
	 * @return 元数据Map
	 */
	private Map<String, Object> createMetadata(DifyMetadataKey key, String value) {
		Map<String, Object> metadata = new HashMap<>();
		metadata.put("id", key.getId());
		metadata.put("name", key.getName());
		metadata.put("value", value);
		return metadata;
	}

	/**
	 * 构建HTTP请求头
	 *
	 * @param apiKey API密钥
	 * @return HTTP请求头
	 */
	private HttpHeaders buildHeaders(String apiKey) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setBearerAuth(apiKey);
		return headers;
	}

	/**
	 * 执行元数据上传
	 *
	 * @param url         上传URL
	 * @param requestBody 请求体
	 */
	private void executeMetadataUpload(String url, Map<String, Object> requestBody) {
		HttpHeaders headers = buildHeaders(deviceDatasetApiKey);
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

		try {
			ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("Failed to upload document metadata, response status: {}", response.getStatusCode());
			}
		} catch (Exception e) {
			log.error("Error occurred while uploading document metadata: {}", e.getMessage());
		}
	}

	/**
	 * 删除Dify平台上的文档
	 *
	 * @param attachId 要删除的文件ID
	 * @return 删除操作的结果信息
	 */
	public String deleteDocument(String attachId) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过文档删除操作。attachId: {}", attachId);
			return "Dify功能未启用";
		}

		String documentId = CacheUtil.get(CACHE_PREFIX, FILE_ID_KEY, attachId, String.class, false);

		log.info("删除dify文档，dify documentId:{}", documentId);
		// 构建请求URL
		String url = DifyApiUrl.DELETE_DOCUMENT.getFullUrl(serverUrl, equipmentDatasetId, documentId);

		return executeDeleteRequest(url);
	}

	/**
	 * 执行删除请求
	 *
	 * @param url 删除请求URL
	 * @return 删除操作结果
	 */
	private String executeDeleteRequest(String url) {
		HttpHeaders headers = buildHeaders(deviceDatasetApiKey);
		HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

		try {
			ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity,
				String.class);
			if (response.getStatusCode().is2xxSuccessful()) {
				log.info("文档删除成功");
				return "Document deleted successfully";
			} else {
				log.error("Failed to delete document, response status: {}", response.getStatusCode());
				return "Failed to delete document";
			}
		} catch (Exception e) {
			log.error("Error occurred while deleting document: {}", e.getMessage());
			return "Error occurred while deleting document";
		}
	}

	/**
	 * 执行运维标准工作流
	 *
	 * @param query  查询内容
	 * @param inputs 输入参数Map
	 * @param apiKey API密钥
	 * @return 工作流执行结果的JSON对象
	 */
	public JSONObject runWorkflow(String query, Map<String, Object> inputs, String apiKey) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过工作流执行操作。query: {}", query);
			JSONObject result = new JSONObject();
			result.put("message", "Dify功能未启用");
			return result;
		}

		String url = DifyApiUrl.RUN_WORKFLOW.getFullUrl(serverUrl);
		Map<String, Object> requestBody = buildWorkflowRequestBody(query, inputs, "blocking");
		return executeWorkflowRequest(url, requestBody, apiKey);
	}

	/**
	 * 执行工作流请求
	 *
	 * @param url         请求URL
	 * @param requestBody 请求体
	 * @param apiKey      API密钥
	 * @return 工作流执行结果
	 */
	private JSONObject executeWorkflowRequest(String url, Map<String, Object> requestBody, String apiKey) {
		HttpHeaders headers = buildWorkflowHeaders(apiKey);
		HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

		try {
			log.info("dify运维标准工作流请求参数：{}", requestBody);
			ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

			if (response.getStatusCode().is2xxSuccessful()) {
				String unicodeString = response.getBody();
				String decodedString = StringEscapeUtils.unescapeJava(unicodeString);
				log.info("dify运维标准工作流返回结果：{}", decodedString);
				return JSONObject.parseObject(unicodeString);
			} else {
				log.error("Failed to run workflow, response status: {}", response.getStatusCode());
				return new JSONObject();
			}
		} catch (Exception e) {
			log.error("Error occurred while running workflow: {}", e.getMessage());
			return new JSONObject();
		}
	}

	/**
	 * 构建工作流请求体
	 *
	 * @param query        查询内容
	 * @param inputs       输入参数
	 * @param responseMode 响应模式
	 * @return 请求体Map
	 */
	private Map<String, Object> buildWorkflowRequestBody(String query, Map<String, Object> inputs,
														 String responseMode) {
		inputs.put("query", query);
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("inputs", inputs);
		requestBody.put("response_mode", responseMode);
		requestBody.put("user", AuthUtil.getUserId());
		return requestBody;
	}

	/**
	 * 构建工作流请求头
	 *
	 * @param apiKey API密钥
	 * @return HTTP请求头
	 */
	private HttpHeaders buildWorkflowHeaders(String apiKey) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setBearerAuth(apiKey);
		headers.set("Accept", "*/*");
		headers.set("Accept-Encoding", "gzip, deflate, br");
		headers.set("Connection", "keep-alive");
		headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
		return headers;
	}

	/**
	 * 以流式方式执行工作流
	 *
	 * @param query  查询内容
	 * @param inputs 输入参数Map
	 * @param apiKey API密钥
	 * @return SSE事件发射器的响应实体
	 */
	public ResponseEntity<SseEmitter> runWorkflowStream(String query, Map<String, Object> inputs, String apiKey) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过流式工作流执行操作。query: {}", query);
			SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
			// 发送一个消息表示功能未启用，然后完成
			try {
				emitter.send(SseEmitter.event().data("{\"message\":\"Dify功能未启用\"}"));
				emitter.complete();
			} catch (Exception e) {
				emitter.completeWithError(e);
			}
			return ResponseEntity.ok()
				.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE)
				.body(emitter);
		}

		String url = DifyApiUrl.RUN_WORKFLOW.getFullUrl(serverUrl);
		SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);

		// 构建请求体
		Map<String, Object> requestBody = buildWorkflowRequestBody(query, inputs, "streaming");
		// 构建请求头
		HttpHeaders headers = buildStreamingHeaders(apiKey);
		// 构建请求实体
		HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
		log.info("dify运维标准工作流请求参数：{}", requestBody);
		// 发送请求
		executeStreamingRequest(url, requestEntity, emitter);
		// 设置回调
		setupEmitterCallbacks(emitter);
		// 构建响应实体
		return buildStreamingResponse(emitter);
	}

	/**
	 * 构建流式请求头
	 *
	 * @param apiKey API密钥
	 * @return HTTP请求头
	 */
	private HttpHeaders buildStreamingHeaders(String apiKey) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setBearerAuth(apiKey);
		headers.set(HttpHeaders.ACCEPT, MediaType.TEXT_EVENT_STREAM_VALUE);
		headers.set(HttpHeaders.ACCEPT_ENCODING, "identity");
		headers.set(HttpHeaders.CONNECTION, "keep-alive");
		headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
		return headers;
	}

	/**
	 * 执行流式请求
	 *
	 * @param url           请求URL
	 * @param requestEntity 请求实体
	 * @param emitter       SSE事件发射器
	 */
	private void executeStreamingRequest(String url, HttpEntity<Map<String, Object>> requestEntity,
										 SseEmitter emitter) {
		executorService.execute(() -> {
			try {
				restTemplate.execute(url, HttpMethod.POST, request -> {
					request.getHeaders().putAll(requestEntity.getHeaders());
					try (OutputStream os = request.getBody()) {
						byte[] input = JSON.toJSONString(requestEntity.getBody()).getBytes(StandardCharsets.UTF_8);
						os.write(input);
						os.flush();
					}
				}, response -> {
					try (InputStream inputStream = response.getBody()) {
						if (response.getStatusCode() == HttpStatus.OK) {
							// 处理流式响应
							handleStream(inputStream, emitter);
							return null;
						} else {
							throw new IOException(String.format("HTTP error code: %d, message: %s",
								response.getStatusCode().value(), response.getStatusText()));
						}
					}
				});
			} catch (Exception e) {
				handleError(emitter, e);
			}
		});
	}

	/**
	 * 设置SSE事件发射器回调
	 *
	 * @param emitter SSE事件发射器
	 */
	private void setupEmitterCallbacks(SseEmitter emitter) {
		emitter.onCompletion(() -> log.info("Stream completed"));
		emitter.onTimeout(() -> {
			log.warn("Stream timeout after {} seconds", SSE_TIMEOUT / 1000);
			emitter.complete();
		});
		emitter.onError(ex -> {
			log.error("Stream error: {}", ex.getMessage());
			emitter.completeWithError(ex);
		});
	}

	/**
	 * 构建流式响应
	 *
	 * @param emitter SSE事件发射器
	 * @return 响应实体
	 */
	private ResponseEntity<SseEmitter> buildStreamingResponse(SseEmitter emitter) {
		return ResponseEntity.ok()
			.header(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_EVENT_STREAM_VALUE)
			.header(HttpHeaders.CACHE_CONTROL, "no-cache")
			.header(HttpHeaders.CONNECTION, "keep-alive")
			.body(emitter);
	}

	/**
	 * 处理流式响应中的错误
	 *
	 * @param emitter SSE事件发射器
	 * @param e       异常对象
	 */
	private void handleError(SseEmitter emitter, Exception e) {
		String errorMessage = String.format("工作流执行错误: %s", e.getMessage());
		log.error(errorMessage, e);
		try {
			if (e instanceof IOException) {
				emitter.completeWithError(e);
			} else {
				emitter.completeWithError(new ServiceException(errorMessage));
			}
		} catch (Exception ex) {
			log.error("Error while completing emitter with error", ex);
		}
	}

	/**
	 * 处理输入流数据
	 *
	 * @param inputStream 输入流
	 * @param emitter     SSE事件发射器
	 */
	private void handleStream(InputStream inputStream, SseEmitter emitter) {

		// 使用BufferedReader逐行读取响应内容
		try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
			String line;
			while ((line = reader.readLine()) != null) {

				// line = StringEscapeUtils.unescapeJava(line);
				log.info("dify运维标准工作流返回结果：===============start{}\n", StringEscapeUtils.unescapeJava(line));
				log.info("dify运维标准工作流返回结果：===============end\n");
				if (line.startsWith("data: ")) {
					// 如果已经有一个完整的块，则发送并重置构建器
					line = line.substring(6);
					JSONObject jsonObject = JSON.parseObject(line);
					String event = jsonObject.getString("event");
					if ("text_chunk".equals(event)) {
						// 发送
						// 校验line的数据是否是json
						if (!line.startsWith("{") || !line.endsWith("}")) {
							throw new ServiceException("dify运维标准工作流返回结果json格式错误");
						}
						JSONObject data = jsonObject.getJSONObject("data");
						if (data != null) {
							String text = data.getString("text");
							text = text.replaceAll("\"", "");
							text = text.replaceAll("'", "");
							data.put("text", text);
						}
						String escapeLine = StringEscapeUtils.unescapeJava(jsonObject.toJSONString());

						emitter.send(SseEmitter.event().data(escapeLine));
					}
					// node_finished
					else if ("workflow_finished".equals(event)) {
						String status = jsonObject.getJSONObject("data").getString("status");
						if (!"succeeded".equals(status)) {
							log.error("维修建议工作流执行失败，状态：{}", status);
							throw new ServiceException("维修建议工作流执行失败");
						}
						// String result =
						// jsonObject.getJSONObject("data").getJSONObject("outputs").getString("result");
						// 建议
						// 来源
						JSONArray sourceArray = jsonObject.getJSONObject("data").getJSONObject("outputs")
							.getJSONArray("content");
						extractSourceList(emitter, sourceArray);
					} else {
						emitter.send(SseEmitter.event().data(jsonObject.toJSONString()));
					}

				}
			}

		} catch (IOException e) {
			log.error("Error reading response body: {}", e.getMessage());
			emitter.completeWithError(e);
		}
		emitter.complete();
	}

	/**
	 * 提取来源列表
	 *
	 * @param emitter     SSE事件发射器
	 * @param sourceArray 来源数组
	 * @throws IOException IO异常
	 */
	public static void extractSourceList(SseEmitter emitter, JSONArray sourceArray) throws IOException {
		// 来源的文件
		List<DifySourceDTO> sourceList = new ArrayList<>();

		// 来源的文件是否重复
		List<String> repeatCheckList = new ArrayList<>();
		JSONObject sourceObject = new JSONObject();
		sourceObject.put("event", "source_list");

		if (sourceArray != null) {
			for (Object o : sourceArray) {
				JSONObject obj = (JSONObject) o;
				JSONObject metadata = obj.getJSONObject("metadata");
				if (metadata != null) {
					String datasetName = metadata.getString("dataset_name");
					// 如果是故障案例库
					if (DifyDbType.FAULT_CASE_DATABASE.getDescription().equals(datasetName)) {
						DifySourceDTO sourceDTO = new DifySourceDTO();
						sourceDTO.setSourceType(DifyDbType.FAULT_CASE_DATABASE.getDescription());
						String fileName = metadata.getString("document_name");
                   //原始的文件名是 ZC202504200001刮板输送机:卡阻.md ,前14位是单号,刮板输送机:卡阻是案例的名称
						sourceDTO.setFileId(fileName.substring(0,14));
						String[] split = fileName.split(":");
						String caseName = split[1].substring(0,split[1].indexOf(".") + 1);
						sourceDTO.setFileName(caseName);
						String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
						if (!repeatCheckList.contains(strRepeat)) {
							repeatCheckList.add(strRepeat);
							sourceList.add(sourceDTO);
						}

					}
					// 如果是DEVICE_MANUAL
					if (DifyDbType.DEVICE_MANUAL.getDescription().equals(datasetName)) {
						DifySourceDTO sourceDTO = new DifySourceDTO();
						sourceDTO.setSourceType(DifyDbType.DEVICE_MANUAL.getDescription());
						String fileName = metadata.getString("document_name");
						JSONObject docMetadata = metadata.getJSONObject("doc_metadata");
						if (docMetadata != null) {
							sourceDTO.setFileId(docMetadata.getString("simas_file_id"));
							sourceDTO.setFileLink(metadata.getString("simas_file_link"));
						}
						// fileName的格式 是设备名_文件名,获取第一个_后面的内容作为文件名
						if (fileName.contains("_")) {
							fileName = fileName.substring(fileName.indexOf("_") + 1);
						}
						sourceDTO.setFileName(fileName);

						String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
						if (!repeatCheckList.contains(strRepeat)) {
							repeatCheckList.add(strRepeat);
							sourceList.add(sourceDTO);
						} else {
							continue;
						}
					}
					// 如果是煤炭设备知识库
					if (DifyDbType.COAL_KNOWLEDGE_DATABASE.getDescription().equals(datasetName)) {
						DifySourceDTO sourceDTO = new DifySourceDTO();
						sourceDTO.setSourceType(DifyDbType.COAL_KNOWLEDGE_DATABASE.getDescription());
						String fileName = metadata.getString("document_name");
						sourceDTO.setFileName(fileName);
						JSONObject docMetadata = metadata.getJSONObject("doc_metadata");
						if (docMetadata != null) {
							sourceDTO.setFileLink(docMetadata.getString("attach_link"));
							log.info("煤炭设备知识库文件链接: {}", sourceDTO.getFileLink());
						}
						// 从元数据中获取attach_link作为文件链接

						String strRepeat = sourceDTO.getFileName() + sourceDTO.getSourceType();
						if (!repeatCheckList.contains(strRepeat)) {
							repeatCheckList.add(strRepeat);
							sourceList.add(sourceDTO);
						}
					}

				}

			}
			// 如果是空的话来源是通用大模型
			if (Func.isEmpty(sourceList)) {
				DifySourceDTO sourceDTO = new DifySourceDTO();
				sourceDTO.setSourceType(DifyDbType.LARGE_MODEL.getDescription());
				sourceList.add(sourceDTO);
			}
			sourceObject.put("data", sourceList);
			emitter.send(SseEmitter.event()
				.data(StringEscapeUtils.unescapeJava(sourceObject.toJSONString())));
		}
	}

	/**
	 * 通过文本内容创建文档
	 *
	 * @param docName 文档名称
	 * @param content 文档内容
	 * @return 创建结果的JSON字符串
	 */
	public String createDocumentByText(String docName, String content) {
		// 检查Dify功能是否启用
		if (!difyEnabled) {
			log.info("Dify功能未启用，跳过文本文档创建操作。docName: {}", docName);
			return "Dify功能未启用";
		}

		String url = String.format("%s/v1/datasets/%s/document/create-by-text", serverUrl, defectDatasetId);
		Map<String, Object> requestBody = buildTextDocumentRequestBody(docName, content);
		return executeCreateDocumentRequest(url, requestBody);
	}

	/**
	 * 构建文本文档请求体
	 *
	 * @param docName 文档名称
	 * @param content 文档内容
	 * @return 请求体Map
	 */
	private Map<String, Object> buildTextDocumentRequestBody(String docName, String content) {
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("name", docName);
		requestBody.put("text", content);
		requestBody.put("indexing_technique", "high_quality");

		Map<String, String> processRule = new HashMap<>();
		processRule.put("mode", "automatic");
		requestBody.put("process_rule", processRule);

		return requestBody;
	}

	/**
	 * 执行创建文档请求
	 *
	 * @param url         请求URL
	 * @param requestBody 请求体
	 * @return 创建结果的JSON字符串
	 */
	private String executeCreateDocumentRequest(String url, Map<String, Object> requestBody) {
		HttpHeaders headers = buildHeaders(deviceDatasetApiKey);
		HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

		try {
			ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
			if (response.getStatusCode().is2xxSuccessful()) {
				log.info("通过文本创建文档成功，docName: {}", requestBody.get("name"));
				return response.getBody();
			} else {
				log.error("通过文本创建文档失败，响应状态: {}", response.getStatusCode());
				throw new ServiceException("通过文本创建文档失败");
			}
		} catch (Exception e) {
			log.error("通过文本创建文档时发生错误: {}", e.getMessage());
			throw new ServiceException("通过文本创建文档时发生错误");
		}
	}

}
