package com.snszyk.simas.ai.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * PackageName com.snszyk.simas.ai.vo
 * <AUTHOR>
 * @ClassName OperateStandardsMonitorGenVO
 * @Date 2025年04月09日 上午8:56
 * @Description 运维标准生成的结果
 */
@Data
@ApiModel(value = "OperateStandardsGenDTO", description = "OperateStandardsGenDTO")
public class OperateStandardsMonitorGenResultDTO {


	@ApiModelProperty(value = "部位list")
	private List<OperateStandardsMonitorGenDTO> monitorList = new ArrayList<>();

	@ApiModelProperty(value = "来源list")
	private List<OperateStandardsMonitorGenSourceDTO> monitorSourceList = new ArrayList<>();


}
