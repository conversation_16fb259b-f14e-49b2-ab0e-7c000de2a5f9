/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.scrap.enums.ScrapStatusEnum;
import com.snszyk.simas.scrap.vo.ScrapVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备报废单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public class ScrapWrapper extends BaseEntityWrapper<Scrap, ScrapVO> {

	public static ScrapWrapper build() {
		return new ScrapWrapper();
 	}

	@Override
	public ScrapVO entityVO(Scrap scrap) {
		ScrapVO scrapVO = Objects.requireNonNull(BeanUtil.copy(scrap, ScrapVO.class));
		User operateUser = UserCache.getUser(scrap.getOperateUser());
		User createUser = UserCache.getUser(scrap.getCreateUser());
		User updateUser = UserCache.getUser(scrap.getUpdateUser());
		if(Func.isNotEmpty(operateUser)){
			scrapVO.setOperateUserName(operateUser.getRealName());
		}
		if(Func.isNotEmpty(createUser)){
			scrapVO.setCreateUserName(createUser.getRealName());
		}
		if(Func.isNotEmpty(updateUser)){
			scrapVO.setUpdateUserName(updateUser.getRealName());
		}
		scrapVO.setStatusName(ScrapStatusEnum.getByCode(scrap.getStatus()).getName());
		return scrapVO;
	}

}
