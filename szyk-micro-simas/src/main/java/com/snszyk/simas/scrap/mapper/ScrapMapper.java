/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.scrap.vo.ScrapVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备报废单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface ScrapMapper extends BaseMapper<Scrap> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param scrap
	 * @return
	 */
	List<Scrap> page(IPage page, @Param("scrap") ScrapVO scrap);

	/**
	 * 导出列表
	 *
	 * @param scrap
	 * @return
	 */
	List<Scrap> exportList(@Param("scrap") ScrapVO scrap);

}
