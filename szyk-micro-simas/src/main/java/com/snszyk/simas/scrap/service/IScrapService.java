/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.common.excel.ScrapExcel;
import com.snszyk.simas.scrap.vo.ScrapVO;

import java.util.List;

/**
 * 设备报废单表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface IScrapService extends BaseService<Scrap> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param scrap
	 * @return
	 */
	IPage<ScrapVO> page(IPage<ScrapVO> page, ScrapVO scrap);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	ScrapVO detail(Long id);

	/**
	 * 提交
	 *
	 * @param scrap
	 * @return
	 */
	boolean submit(ScrapVO scrap);

	/**
	 * 审核
	 *
	 * @param scrap
	 * @return
	 */
	boolean approve(ScrapVO scrap);

	/**
	 * 导出
	 *
	 * @param scrap
	 * @return
	 */
	List<ScrapExcel> exportScrap(ScrapVO scrap);


}
