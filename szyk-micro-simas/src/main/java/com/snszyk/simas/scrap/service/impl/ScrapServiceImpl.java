/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.simas.common.enums.EquipmentStatusEnum;
import com.snszyk.simas.common.enums.OrderActionEnum;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.common.excel.ScrapExcel;
import com.snszyk.simas.common.processor.OrderLogProcessor;
import com.snszyk.simas.common.processor.RepairLogProcessor;
import com.snszyk.simas.common.processor.ScrapLogProcessor;
import com.snszyk.simas.common.service.IBizLogService;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
import com.snszyk.simas.fault.service.IFaultDefectService;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.service.IInspectOrderService;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.lubricate.service.ILubricateOrderService;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.service.IMaintainOrderService;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.enums.RepairActionEnum;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.service.IOverhaulOrderService;
import com.snszyk.simas.overhaul.service.IRepairService;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.scrap.entity.ScrapDetail;
import com.snszyk.simas.scrap.enums.ScrapActionEnum;
import com.snszyk.simas.scrap.enums.ScrapStatusEnum;
import com.snszyk.simas.scrap.mapper.ScrapMapper;
import com.snszyk.simas.scrap.service.IScrapDetailService;
import com.snszyk.simas.scrap.service.IScrapService;
import com.snszyk.simas.scrap.vo.ScrapVO;
import com.snszyk.simas.scrap.wrapper.ScrapWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 设备报废单表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@AllArgsConstructor
@Service
public class ScrapServiceImpl extends BaseServiceImpl<ScrapMapper, Scrap> implements IScrapService {

	// private final IEquipmentAccountService equipmentAccountService;
	private final IScrapDetailService scrapDetailService;
	private final IInspectOrderService inspectOrderService;
	private final IMaintainOrderService maintainOrderService;
	private final IFaultDefectService faultDefectService;
	private final IRepairService repairService;
	private final ILubricateOrderService lubricateOrderService;
	private final IBizLogService bizLogService;
	private final IAttachClient attachClient;
	private final IOverhaulOrderService overhaulOrderService;
	private final IDeviceAccountClient deviceAccountClient;

	@Override
	public IPage<ScrapVO> page(IPage<ScrapVO> page, ScrapVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		List<Scrap> list = baseMapper.page(page, vo);
		if (Func.isNotEmpty(list)) {
			List<ScrapVO> recordList = ScrapWrapper.build().listVO(list);
			recordList.forEach(record -> {
				record.setEquipmentCount(scrapDetailService.count(Wrappers.<ScrapDetail>query().lambda()
					.eq(ScrapDetail::getScrapId, record.getId())));
			});
			return page.setRecords(recordList);
		}
		return page.setRecords(null);
	}

	@Override
	public ScrapVO detail(Long id) {
		Scrap scrap = this.getById(id);
		if (scrap == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		ScrapVO detail = ScrapWrapper.build().entityVO(scrap);
		List<ScrapDetail> list = scrapDetailService.list(Wrappers.<ScrapDetail>query().lambda()
			.eq(ScrapDetail::getScrapId, id).orderByAsc(ScrapDetail::getSort));
		if (Func.isNotEmpty(list)) {
			detail.setDetailList(list.stream().map(scrapDetail -> {
				DeviceAccountVO deviceAccountVO = JSONUtil.toBean(scrapDetail.getEquipmentInfo(), DeviceAccountVO.class);

				final R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoById(scrapDetail.getEquipmentId());
				if (ObjectUtil.isNotEmpty(deviceAccountVOR.getData())) {
					deviceAccountVO = deviceAccountVOR.getData();
				}
				return deviceAccountVO;
			}).collect(Collectors.toList()));
		}
		if (Func.isNotEmpty(detail.getAttachId())) {
			R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(detail.getAttachId()));
			if (attachListR.isSuccess()) {
				detail.setAttachList(attachListR.getData());
			}
		}
		return detail;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean submit(ScrapVO vo) {
		ScrapActionEnum actionEnum = ScrapActionEnum.RE_SUBMIT;
		Scrap scrap = Objects.requireNonNull(BeanUtil.copy(vo, Scrap.class));
		if (Func.isEmpty(scrap.getId())) {
			scrap.setNo(BizCodeUtil.generate("BF"));
			actionEnum = ScrapActionEnum.INIT;
		}
		if (Func.isEmpty(vo.getAttachId())) {
			scrap.setAttachId(null);
		}
		scrap.setStatus(ScrapStatusEnum.TO_BE_AUDITED.getCode());
		boolean ret = this.saveOrUpdate(scrap);
		// 保存设备报废日志
		ScrapLogProcessor.saveBizLog(actionEnum, scrap.getId(), JSONUtil.toJsonStr(scrap), null);
		if (Func.isNotEmpty(vo.getEquipmentIds())) {
			scrapDetailService.remove(Wrappers.<ScrapDetail>query().lambda()
				.eq(ScrapDetail::getScrapId, scrap.getId()));
			AtomicReference<Integer> sort = new AtomicReference<>(1);
			List<ScrapDetail> list = Func.toLongList(vo.getEquipmentIds()).stream().map(equipmentId -> {
				ScrapDetail scrapDetail = new ScrapDetail();
				scrapDetail.setScrapId(scrap.getId()).setEquipmentId(equipmentId)
					.setCreateTime(DateUtil.now()).setSort(sort.getAndSet(sort.get() + 1));

				final R<DeviceAccountVO> deviceAccountVOR = deviceAccountClient.deviceInfoById(scrapDetail.getEquipmentId());
				if (ObjectUtil.isNotEmpty(deviceAccountVOR.getData())) {
					final DeviceAccountVO deviceAccountVO = deviceAccountVOR.getData();
					scrapDetail.setEquipmentInfo(JSONUtil.toJsonStr(deviceAccountVO));
				}
				return scrapDetail;
			}).collect(Collectors.toList());
			scrapDetailService.saveBatch(list);
		}
		return ret;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean approve(ScrapVO vo) {
		Scrap scrap = this.getById(vo.getId());
		if (scrap == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		scrap.setStatus(vo.getStatus());
		boolean ret = this.updateById(scrap);
		ScrapActionEnum actionEnum = ScrapActionEnum.AUDIT_FAIL;
		// 审核通过，更新设备状态为报废
		if (ScrapStatusEnum.IS_COMPLETED == ScrapStatusEnum.getByCode(vo.getStatus())) {
			actionEnum = ScrapActionEnum.AUDIT_PASS;
			List<ScrapDetail> list = scrapDetailService.list(Wrappers.<ScrapDetail>query().lambda()
				.eq(ScrapDetail::getScrapId, scrap.getId()));
			if (Func.isNotEmpty(list)) {
				List<Long> equipmentIds = list.stream()
					.map(ScrapDetail::getEquipmentId)
					.collect(Collectors.toList());

				final List<DeviceAccountVO> accountVOList = equipmentIds.stream()
					.map(equipmentId -> {
						final DeviceAccountVO accountVO = new DeviceAccountVO();
						accountVO.setId(equipmentId);
						accountVO.setScrapId(scrap.getId());
						accountVO.setStatus(EquipmentStatusEnum.SCRAPPED.getCode());
						final LocalDate scrapLocalDate = scrap.getScrapDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
						accountVO.setScrapDate(scrapLocalDate);
						return accountVO;
					}).collect(Collectors.toList());

				if (Func.isNotEmpty(accountVOList)) {
					deviceAccountClient.updateBatchDeviceAccount(accountVOList);
				}
				// 设备下的所有的工单（维修、保养、点巡检、故障缺陷单、润滑）全部终止，状态变为已关闭
				this.closeInspectOrder(equipmentIds);
				this.closeMaintainOrder(equipmentIds);
				this.closeRepair(equipmentIds);
				this.closeFaultDefect(equipmentIds);
				this.closeLubricate(equipmentIds);
				this.closeOverhaul(equipmentIds);
			}
		}
		// 保存设备报废日志
		ScrapLogProcessor.saveBizLog(actionEnum, scrap.getId(), JSONUtil.toJsonStr(vo), null);
		return ret;
	}

	private void closeOverhaul(List<Long> equipmentIds) {
		List<OverhaulOrder> list = overhaulOrderService.list(Wrappers.<OverhaulOrder>query().lambda()
			.in(OverhaulOrder::getEquipmentId, equipmentIds)
			.and(wrapper -> wrapper.eq(OverhaulOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
				.or()
				.eq(OverhaulOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())));
		if (Func.isNotEmpty(list)) {
			overhaulOrderService.update(Wrappers.<OverhaulOrder>update().lambda()
				.set(OverhaulOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				.in(OverhaulOrder::getId, list.stream().map(OverhaulOrder::getId).collect(Collectors.toList())));
		}
		// 业务日志1.2.1
		for (OverhaulOrder order : list) {
			OrderLogProcessor.saveBizLog(SystemModuleEnum.OVERHAUL_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.CANCEL
			);
		}
	}

	private void closeInspectOrder(List<Long> equipmentIds) {
		List<InspectOrder> list = inspectOrderService.list(Wrappers.<InspectOrder>query().lambda()
			.in(InspectOrder::getEquipmentId, equipmentIds)
			.and(wrapper -> wrapper.eq(InspectOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
				.or()
				.eq(InspectOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())));
		if (Func.isNotEmpty(list)) {
			inspectOrderService.update(Wrappers.<InspectOrder>update().lambda()
				.set(InspectOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				.in(InspectOrder::getId, list.stream().map(InspectOrder::getId).collect(Collectors.toList())));
			// 业务日志1.2.1
			for (InspectOrder inspectOrder : list) {
				OrderLogProcessor.saveBizLog(SystemModuleEnum.INSPECT_ORDER, JSON.parseObject(JSON.toJSONString(inspectOrder)), OrderActionEnum.CANCEL
				);
			}
		}
	}

	private void closeMaintainOrder(List<Long> equipmentIds) {
		List<MaintainOrder> list = maintainOrderService.list(Wrappers.<MaintainOrder>query().lambda()
			.in(MaintainOrder::getEquipmentId, equipmentIds)
			.ne(MaintainOrder::getStatus, OrderStatusEnum.IS_COMPLETED.getCode())
			.ne(MaintainOrder::getStatus, OrderStatusEnum.OVERDUE_COMPLETED.getCode()));
		if (Func.isNotEmpty(list)) {
			maintainOrderService.update(Wrappers.<MaintainOrder>update().lambda()
				.set(MaintainOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				.in(MaintainOrder::getId, list.stream().map(MaintainOrder::getId).collect(Collectors.toList())));
			// 业务日志1.2.1
			for (MaintainOrder order : list) {
				OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.CANCEL
				);
			}
		}
	}

	private void closeRepair(List<Long> equipmentIds) {
		List<Repair> list = repairService.list(Wrappers.<Repair>query().lambda()
			.in(Repair::getEquipmentId, equipmentIds)
			.ne(Repair::getStatus, OrderStatusEnum.IS_COMPLETED.getCode())
			.ne(Repair::getStatus, OrderStatusEnum.OVERDUE_COMPLETED.getCode()));
		if (Func.isNotEmpty(list)) {
			repairService.update(Wrappers.<Repair>update().lambda()
				.set(Repair::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				.in(Repair::getId, list.stream().map(Repair::getId).collect(Collectors.toList())));
			// 业务日志
			final String logContent = RepairLogProcessor.getScrapCloseLogContent();
			list.forEach(repair -> {
				RepairLogProcessor.saveBizLog(RepairActionEnum.SCRAP_CLOSE, RepairBizTypeEnum.getByCode(repair.getBizType()), repair.getId(), JSONUtil.toJsonStr(repair), logContent);
			});

			// bizLogService.submitBatch(list.stream().map(order -> {
			// 	BizLogVO bizLog = new BizLogVO(RepairWrapper.build().entityVO(order));
			// 	bizLog.setBizStatus(OrderStatusEnum.IS_CLOSED.getCode()).setContent("设备报废关闭工单")
			// 		.setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
			// 	return bizLog;
			// }).collect(Collectors.toList()));
		}
	}

	private void closeFaultDefect(List<Long> equipmentIds) {
		List<FaultDefect> list = faultDefectService.list(Wrappers.<FaultDefect>query().lambda()
			.in(FaultDefect::getEquipmentId, equipmentIds)
			.ne(FaultDefect::getStatus, FaultBizStatusEnum.IS_HANDLED.getCode()));
		if (Func.isNotEmpty(list)) {
			faultDefectService.update(Wrappers.<FaultDefect>update().lambda()
				.set(FaultDefect::getStatus, FaultBizStatusEnum.IS_CLOSED.getCode())
				.in(FaultDefect::getId, list.stream().map(FaultDefect::getId).collect(Collectors.toList())));
			// 业务日志
			bizLogService.submitBatch(list.stream().map(faultDefect -> {
				BizLogVO bizLog = new BizLogVO(faultDefect);
				bizLog.setBizStatus(OrderStatusEnum.IS_CLOSED.getCode()).setContent("设备报废关闭故障")
					.setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
				return bizLog;
			}).collect(Collectors.toList()));
		}
	}

	private void closeLubricate(List<Long> equipmentIds) {
		List<LubricateOrderVO> list = lubricateOrderService.selectByEquipmentIds(equipmentIds);
		if (Func.isNotEmpty(list)) {
			lubricateOrderService.update(Wrappers.<LubricateOrder>update().lambda()
				.set(LubricateOrder::getStatus, OrderStatusEnum.IS_CLOSED.getCode())
				.in(LubricateOrder::getId, list.stream().map(LubricateOrder::getId).collect(Collectors.toList())));
			// 业务日志1.2.1
			for (LubricateOrderVO order : list) {
				LubricateOrder entity = BeanUtil.copy(order, LubricateOrder.class);
				OrderLogProcessor.saveBizLog(SystemModuleEnum.LUBRICATE_ORDER, JSON.parseObject(JSON.toJSONString(entity)), OrderActionEnum.CANCEL
				);
			}
		}
	}

	@Override
	public List<ScrapExcel> exportScrap(ScrapVO vo) {
		if (Func.isNotEmpty(vo.getStartDate())) {
			vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		}
		if (Func.isNotEmpty(vo.getEndDate())) {
			vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		}
		List<Scrap> list = baseMapper.exportList(vo);
		if (Func.isNotEmpty(list)) {
			List<ScrapVO> scrapList = ScrapWrapper.build().listVO(list);
			AtomicReference<Integer> sn = new AtomicReference<>(1);
			return scrapList.stream().map(scrap -> {
				ScrapExcel scrapExcel = Objects.requireNonNull(BeanUtil.copy(scrap, ScrapExcel.class));
				scrapExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				scrapExcel.setEquipmentCount(Func.toStr(scrapDetailService.count(Wrappers.<ScrapDetail>query().lambda()
					.eq(ScrapDetail::getScrapId, scrap.getId()))));
				if (Func.isNotEmpty(scrap.getScrapDate())) {
					scrapExcel.setScrapDateStr(DateUtil.format(scrap.getScrapDate(), "yyyy-MM-dd"));
				}
				return scrapExcel;
			}).collect(Collectors.toList());
		}
		return null;
	}

}
