/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.excel.ScrapExcel;
import com.snszyk.simas.common.processor.ScrapLogProcessor;
import com.snszyk.simas.scrap.entity.Scrap;
import com.snszyk.simas.scrap.enums.ScrapActionEnum;
import com.snszyk.simas.scrap.enums.ScrapStatusEnum;
import com.snszyk.simas.scrap.service.IScrapService;
import com.snszyk.simas.scrap.vo.ScrapVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 设备报废单表 控制器
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/scrap")
@Api(value = "设备报废单表", tags = "设备报废单表接口")
public class ScrapController extends SzykController {

	private final IScrapService scrapService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入scrap")
	public R<ScrapVO> detail(Long id) {
		return R.data(scrapService.detail(id));
	}

	/**
	 * 自定义分页 设备报废单表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "no", value = "报废单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "报废单名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "报废单状态（1：待审核，2：已完成，3：已驳回，4：已撤销）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "equipmentKeywords", value = "设备名称或编码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入scrap")
	public R<IPage<ScrapVO>> page(@ApiIgnore ScrapVO scrap, Query query) {
		IPage<ScrapVO> pages = scrapService.page(Condition.getPage(query), scrap);
		return R.data(pages);
	}

	/**
	 * 新增或修改 设备报废单表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入scrap")
	public R submit(@Valid @RequestBody ScrapVO scrap) {
		return R.status(scrapService.submit(scrap));
	}


	/**
	 * 删除 设备报废单表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(scrapService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 审核 设备报废单表
	 */
	@PostMapping("/approve")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "审核", notes = "传入scrap")
	public R approve(@Valid @RequestBody ScrapVO scrap) {
		return R.status(scrapService.approve(scrap));
	}

	/**
	 * 撤销 设备报废单表
	 */
	@PostMapping("/cancel")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "撤销", notes = "传入scrap")
	public R cancel(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		Scrap scrap = scrapService.getById(id);
		if (scrap == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		scrap.setStatus(ScrapStatusEnum.IS_CANCELLED.getCode());
		// 保存设备报废日志
		ScrapLogProcessor.saveBizLog(ScrapActionEnum.CANCEL, id, JSONUtil.toJsonStr(id), null);
		return R.status(scrapService.updateById(scrap));
	}

	/**
	 * 导出 设备保养计划表
	 */
	@GetMapping("/export-scrap")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "no", value = "报废单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "报废单名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "报废单状态（1：待审核，2：已完成，3：已驳回，4：已撤销）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束时间", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "equipmentKeywords", value = "设备名称或编码", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "导出", notes = "传入maintainPlan")
	public void exportScrap(@ApiIgnore ScrapVO scrap, HttpServletResponse response) {
		List<ScrapExcel> list = scrapService.exportScrap(scrap);
		ExcelUtil.export(response, "报废单列表" + DateUtil.time(), "报废单", list, ScrapExcel.class);
	}


}
