<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.scrap.mapper.ScrapMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="scrapResultMap" type="com.snszyk.simas.scrap.entity.Scrap">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="operate_user" property="operateUser"/>
        <result column="scrap_date" property="scrapDate"/>
        <result column="attach_id" property="attachId"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <select id="page" resultMap="scrapResultMap">
        SELECT
            *
        FROM
            simas_scrap
        WHERE is_deleted = 0
        <if test="scrap.no != null and scrap.no != ''">
            AND `no` LIKE CONCAT('%', #{scrap.no}, '%')
        </if>
        <if test="scrap.name != null and scrap.name != ''">
            AND `name` LIKE CONCAT('%', #{scrap.name}, '%')
        </if>
        <if test="scrap.status != null">
            AND status = #{scrap.status}
        </if>
        <if test="scrap.startDate != null and scrap.startDate != ''">
            and scrap_date <![CDATA[ >= ]]> #{scrap.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="scrap.endDate != null and scrap.endDate != ''">
            and scrap_date <![CDATA[ <= ]]> #{scrap.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="scrap.equipmentKeywords != null and scrap.equipmentKeywords != ''">
            AND id IN (SELECT DISTINCT
            sd.scrap_id
            FROM
            simas_scrap_detail sd
            LEFT JOIN device_account ea ON ea.id = sd.equipment_id
            WHERE 1 = 1
            AND (ea.`sn` like concat('%',#{scrap.equipmentKeywords},'%') or ea.`name` like
            concat('%',#{scrap.equipmentKeywords},'%')))
        </if>
        order by create_time desc
    </select>

    <select id="exportList" resultMap="scrapResultMap">
        SELECT
        *
        FROM
        simas_scrap
        WHERE is_deleted = 0
        <if test="scrap.no != null and scrap.no != ''">
            AND `no` LIKE CONCAT('%', #{scrap.no}, '%')
        </if>
        <if test="scrap.name != null and scrap.name != ''">
            AND `name` LIKE CONCAT('%', #{scrap.name}, '%')
        </if>
        <if test="scrap.status != null">
            AND status = #{scrap.status}
        </if>
        <if test="scrap.startDate != null and scrap.startDate != ''">
            and scrap_date <![CDATA[ >= ]]> #{scrap.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="scrap.endDate != null and scrap.endDate != ''">
            and scrap_date <![CDATA[ <= ]]> #{scrap.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="scrap.equipmentKeywords != null and scrap.equipmentKeywords != ''">
            AND id IN (SELECT DISTINCT
            sd.scrap_id
            FROM
            simas_scrap_detail sd
            LEFT JOIN simas_equipment_account ea ON ea.id = sd.equipment_id
            WHERE 1 = 1
            AND (ea.`code` like concat('%',#{scrap.equipmentKeywords},'%') or ea.`name` like
            concat('%',#{scrap.equipmentKeywords},'%')))
        </if>
        order by create_time desc
    </select>

</mapper>
