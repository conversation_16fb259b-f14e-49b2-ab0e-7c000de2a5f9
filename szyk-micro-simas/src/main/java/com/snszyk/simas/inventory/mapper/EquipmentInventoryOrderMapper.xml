<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inventory.mapper.EquipmentInventoryOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentInventoryOrderResultMap" type="com.snszyk.simas.inventory.entity.EquipmentInventoryOrder">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="no" property="no"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_end_date" property="planEndDate"/>
        <result column="plan_create_user_id" property="planCreateUserId"/>
        <result column="plan_create_user_name" property="planCreateUserName"/>
        <result column="device_dept_id" property="deviceDeptId"/>
        <result column="device_dept_name" property="deviceDeptName"/>
        <result column="device_user_id" property="deviceUserId"/>
        <result column="device_user_name" property="deviceUserName"/>
        <result column="gen_source_type" property="genSourceType"/>
        <result column="complete_time" property="completeTime"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="draft_status" property="draftStatus"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectEquipmentInventoryOrderPage" resultMap="equipmentInventoryOrderResultMap">
        select *
        from simas_equipment_inventory_order
        where is_deleted = 0
    </select>
    <select id="pageList" resultType="com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO">
        SELECT
        o.*
        FROM
        simas_equipment_inventory_order o
        <where>
            o.delete_time = 0
            <if test="v.neStatus!=null">
                and o.status != #{v.neStatus}
            </if>
            <if test="v.status != null">
                and o.status = #{v.status}
            </if>
            <if test="v.planName != null and v.planName != ''">
                and o.plan_name like concat('%',#{v.planName},'%')
            </if>
            <if test="v.planStartDate !=null and v.planEndDate!=null">
                and o.plan_start_date between #{v.planStartDate} and #{v.planEndDate}
            </if>
        </where>
        order by o.create_time desc

    </select>

</mapper>
