/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;

import java.util.List;

/**
 * 备品备件盘点工单 服务类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface IEquipmentInventoryOrderService extends IBaseCrudService<EquipmentInventoryOrderDTO, EquipmentInventoryOrderVO> {

	List<EquipmentInventoryOrderDTO> saveOrUpdateBatch(List<EquipmentInventoryOrderVO> orderVOList);

	List<EquipmentInventoryOrderDTO> listByPlanId(Long planId);

	IPage<EquipmentInventoryOrderDTO> pageList(EquipmentInventoryOrderPageVO v);

	Boolean deleteByPlanId(Long planId);

	List<EquipmentInventoryOrderDTO> listBy(Long planId, List<Integer> statusList, Integer neStatus);

	Boolean updateBatch(List<EquipmentInventoryOrderVO> orderVOList);
}
