/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemVO;

import java.util.List;

/**
 * 备品备件盘点明细 服务类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface IEquipmentInventoryItemService extends IBaseCrudService<EquipmentInventoryItemDTO, EquipmentInventoryItemVO> {

	List<EquipmentInventoryItemDTO> listByPlanIds(List<Long> planIds);

	Boolean saveOrUpdateBatch(List<EquipmentInventoryItemVO> itemVOList);

	IPage<EquipmentInventoryItemDTO> pageList(EquipmentInventoryItemPageVO v);

	List<EquipmentInventoryItemDTO> listByOrderIds(List<Long> orderIds);

	Boolean deleteByPlanId(Long planId);

	/**
	 * 统计指定工单下指定状态的明细数量
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 通用的数据查询方法，可复用于各种业务场景
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @param result 盘点结果状态（可为null，表示查询所有状态）
	 * @return 指定状态的明细数量
	 */
	Integer countItemsByOrderIdAndResult(Long inventoryOrderId, Integer result);

	/**
	 * 统一的分页查询方法
	 * 支持所有查询条件，包括设备名称的模糊查询
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 *
	 * @param pageVO 查询条件（支持deviceName、inventoryOrderId等所有条件）
	 * @return 分页结果
	 */
	IPage<EquipmentInventoryItemDTO> pageWithConditions(EquipmentInventoryItemPageVO pageVO);
}
