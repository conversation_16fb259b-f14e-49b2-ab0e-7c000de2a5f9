/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryPlanBeanMapper;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryPlan;
import com.snszyk.simas.inventory.mapper.EquipmentInventoryPlanMapper;
import com.snszyk.simas.inventory.service.IEquipmentInventoryPlanService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 盘点计划表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
public class EquipmentInventoryPlanServiceImpl extends BaseCrudServiceImpl<EquipmentInventoryPlanMapper, EquipmentInventoryPlan, EquipmentInventoryPlanDTO, EquipmentInventoryPlanVO> implements IEquipmentInventoryPlanService {


	@Override
	public EquipmentInventoryPlanDTO onlySave(EquipmentInventoryPlanVO vo) {
		final EquipmentInventoryPlan entity = EquipmentInventoryPlanBeanMapper.INSTANCE.toEntity(vo);
		super.save(entity);

		return EquipmentInventoryPlanBeanMapper.INSTANCE.toDTO(entity);
	}

	@Override
	public List<EquipmentInventoryPlanDTO> listBy(Long likeInventoryDeptId, Integer neStatus) {
		final List<EquipmentInventoryPlan> list = this.lambdaQuery()
			.like(ObjectUtil.isNotEmpty(likeInventoryDeptId), EquipmentInventoryPlan::getInventoryDeptId, likeInventoryDeptId)
			.ne(ObjectUtil.isNotEmpty(neStatus), EquipmentInventoryPlan::getStatus, neStatus)
			.list();

		return ObjectUtil.isEmpty(list) ? null : EquipmentInventoryPlanBeanMapper.INSTANCE.toDTOList(list);
	}

	@Override
	public IPage<EquipmentInventoryPlanDTO> pageList(EquipmentInventoryPlanPageVO v) {
		final Page<EquipmentInventoryPlan> page = this.lambdaQuery()
			.like(StringUtil.isNotBlank(v.getName()), EquipmentInventoryPlan::getName, v.getName())
			.eq(ObjectUtil.isNotEmpty(v.getStatus()), EquipmentInventoryPlan::getStatus, v.getStatus())
			.between(ObjectUtil.isNotEmpty(v.getStartDate()), EquipmentInventoryPlan::getStartDate, v.getStartDate(), v.getEndDate())
			.orderByDesc(EquipmentInventoryPlan::getId)
			.page(new Page<>(v.getCurrent(), v.getSize()));
		return page.convert(e -> BeanUtil.copy(e, EquipmentInventoryPlanDTO.class));
	}

	@Override
	public List<EquipmentInventoryPlanDTO> listBy(LocalDate leStartDate, Integer status) {
		final List<EquipmentInventoryPlan> planList = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(status), EquipmentInventoryPlan::getStatus, status)
			.le(ObjectUtil.isNotEmpty(leStartDate), EquipmentInventoryPlan::getStartDate, leStartDate)
			.list();
		return ObjectUtil.isEmpty(planList) ? Collections.emptyList() : BeanUtil.copy(planList, EquipmentInventoryPlanDTO.class);
	}
}
