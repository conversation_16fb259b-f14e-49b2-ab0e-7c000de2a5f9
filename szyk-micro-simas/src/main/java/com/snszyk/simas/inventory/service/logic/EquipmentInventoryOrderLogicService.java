/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.enums.EquipmentInventoryItemResultEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderStatusEnum;
import com.snszyk.simas.inventory.service.IEquipmentInventoryItemService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryOrderService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryPlanService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemDraftOrSubmitVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 备品备件盘点工单 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
public class EquipmentInventoryOrderLogicService extends BaseCrudLogicService<EquipmentInventoryOrderDTO, EquipmentInventoryOrderVO> {

	private final IEquipmentInventoryOrderService orderService;
	private final IEquipmentInventoryItemService itemService;
	private final IEquipmentInventoryPlanService planService;
	private final EquipmentInventoryItemLogicService itemLogicService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.orderService;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<EquipmentInventoryOrderDTO> page(EquipmentInventoryOrderPageVO v) {
		// 不查询未启动的工单
		v.setNeStatus(EquipmentInventoryOrderStatusEnum.NOT_START.getCode());
		// 分页查询
		IPage<EquipmentInventoryOrderDTO> page = orderService.pageList(v);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		// 查询已盘工单总数Map
		final Map<Long, Long> hasInventoryQuantityMap = this.getHasInventoryQuantityMap(ListUtil.map(page.getRecords(), EquipmentInventoryOrderDTO::getId));

		page.getRecords().forEach(dto -> populateDto(dto, hasInventoryQuantityMap));

		return page;
	}

	/**
	 * 查询已盘点map
	 * key-盘点工单id
	 * value-数量
	 *
	 * @param orderIds
	 * @return
	 */
	private Map<Long, Long> getHasInventoryQuantityMap(List<Long> orderIds) {
		if (ObjectUtil.isEmpty(orderIds)) {
			return MapUtil.empty();
		}
		// 根据工单id查询盘点明细
		List<EquipmentInventoryItemDTO> orderDTOList = itemService.listByOrderIds(orderIds);
		if (ObjectUtil.isEmpty(orderDTOList)) {
			return MapUtil.empty();
		}
		return orderDTOList.stream()
			.filter(item -> !EquipmentInventoryItemResultEnum.NOT_START.getCode().equals(item.getResult()))
			.collect(Collectors.groupingBy(EquipmentInventoryItemDTO::getInventoryOrderId, Collectors.counting()));
	}

	/**
	 * 填充dto
	 *
	 * @param dto
	 */
	private void populateDto(EquipmentInventoryOrderDTO dto, Map<Long, Long> hasInventoryQuantityMap) {
		// 设置盘点状态名称
		dto.setStatusName(EquipmentInventoryOrderStatusEnum.getByCode(dto.getStatus()).getName());
		// 已盘点数量
		Optional.ofNullable(hasInventoryQuantityMap.getOrDefault(dto.getId(), 0L))
			.ifPresent(hasInventoryQuantity -> dto.setHasInventoryQuantity(Math.toIntExact(hasInventoryQuantity)));
		// 创建人姓名
		Optional.ofNullable(dto.getCreateUser())
			.map(UserCache::getUser)
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(dto.getUpdateUser())
			.map(UserCache::getUser)
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));
		// 盘点人姓名
		Optional.ofNullable(dto.getDeviceUserId())
			.map(UserCache::getUser)
			.ifPresent(user -> dto.setDeviceUserName(user.getRealName()));
		// 盘点部门
		Optional.ofNullable(dto.getDeviceDeptId())
			.map(SysCache::getDept)
			.ifPresent(dept -> dto.setDeviceDeptName(dept.getDeptName()));
	}

	/**
	 * 根据id查询
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public EquipmentInventoryOrderDTO getById(Long id) {
		// 根据id查询
		final EquipmentInventoryOrderDTO dto = orderService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			return dto;
		}
		// 查询已盘点数量
		final Map<Long, Long> hasInventoryQuantityMap = this.getHasInventoryQuantityMap(Lists.newArrayList(dto.getId()));
		populateDto(dto, hasInventoryQuantityMap);
		return dto;
	}

	/**
	 * 主动完成盘点
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean complete(Long id) {
		// 完成盘点校验
		final EquipmentInventoryOrderDTO orderDTO = this.completeValid(id);
		// 根据盘点工单id查询盘点明细
		final List<EquipmentInventoryItemDTO> itemList = itemService.listByOrderIds(Lists.newArrayList(id));
		if (ObjectUtil.isEmpty(itemList)) {
			throw new BusinessException("盘点工单不存在盘点明细");
		}
		// 转为vo
		final List<EquipmentInventoryItemDraftOrSubmitVO> itemVOList = itemList.stream()
			.map(itemDTO -> {
				final EquipmentInventoryItemDraftOrSubmitVO orderVO = new EquipmentInventoryItemDraftOrSubmitVO();
				orderVO.setId(itemDTO.getId());
				// 直接完结盘点，盘点数量等于库存数量
				orderVO.setResult(EquipmentInventoryItemResultEnum.NORMAL.getCode());
				orderVO.setInventoryOrderId(itemDTO.getInventoryOrderId());
				orderVO.setDeviceId(itemDTO.getDeviceId());

				return orderVO;
			}).collect(Collectors.toList());
		// 调用盘点接口
		return itemLogicService.submitInventory(orderDTO.getPlanId(), itemVOList);

	}

	/**
	 * 主动完成工单校验
	 *
	 * @param id
	 * @return
	 */
	private EquipmentInventoryOrderDTO completeValid(Long id) {
		// 根据工单id查询
		final EquipmentInventoryOrderDTO orderDTO = orderService.fetchById(id);
		if (ObjectUtil.isEmpty(orderDTO)) {
			throw new BusinessException("保存失败！未查询到盘点工单");
		}
		// 只有"盘点中"状态的工单才可以保存
		if (!EquipmentInventoryOrderStatusEnum.PROCESS.getCode().equals(orderDTO.getStatus())) {
			throw new BusinessException("保存失败！盘点工单状态不正确");
		}
		return orderDTO;
	}
}
