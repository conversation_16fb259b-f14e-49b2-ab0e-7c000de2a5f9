<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inventory.mapper.EquipmentInventoryPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentInventoryPlanResultMap" type="com.snszyk.simas.inventory.entity.EquipmentInventoryPlan">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="total_quantity" property="totalQuantity"/>
        <result column="remark" property="remark"/>
        <result column="inventory_user_id" property="inventoryUserId"/>
        <result column="inventory_user_name" property="inventoryUserName"/>
        <result column="complete_time" property="completeTime"/>
        <result column="inventory_dept_id" property="inventoryDeptId"/>
        <result column="is_inventoried_undept" property="isInventoriedUndept"/>
    </resultMap>


    <select id="selectEquipmentInventoryPlanPage" resultMap="equipmentInventoryPlanResultMap">
        select * from simas_equipment_inventory_plan where is_deleted = 0
    </select>

</mapper>
