/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.service.logic.EquipmentInventoryOrderLogicService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 备品备件盘点工单 控制器
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipmentinventoryorder")
@Api(value = "设备盘点工单", tags = "设备盘点工单接口")
@ApiSupport(order = 61, author = "zhangzhenpu")
@Validated
public class EquipmentInventoryOrderController extends BaseCrudController {

	private final EquipmentInventoryOrderLogicService equipmentInventoryOrderLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return equipmentInventoryOrderLogicService;
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页", notes = "EquipmentInventoryOrderVo")
	public R<IPage<EquipmentInventoryOrderDTO>> page(EquipmentInventoryOrderPageVO v) {
		return R.data(equipmentInventoryOrderLogicService.page(v));
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<EquipmentInventoryOrderDTO> getById(@NotNull @PathVariable("id") Long id) {
		return R.data(equipmentInventoryOrderLogicService.getById(id));
	}

	/**
	 * 完成
	 */
	@PutMapping("/complete/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "完成盘点", notes = "完成盘点")
	public R<Boolean> complete(@PathVariable("id") @NotNull @ApiParam(value = "盘点工单id", required = true) Long id) {
		return R.data(equipmentInventoryOrderLogicService.complete(id));
	}

}
