/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.logic;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.constant.SimasConstant;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.core.tool.utils.*;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.simas.common.service.logic.GeneralLogicService;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryItemBeanMapper;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryOrderBeanMapper;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryPlanBeanMapper;
import com.snszyk.simas.inventory.dto.DeviceInventoryDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.enums.EquipmentInventoryItemResultEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderGenSourceTypeEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderStatusEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryPlanEnum;
import com.snszyk.simas.inventory.service.IEquipmentInventoryItemService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryOrderService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryPlanService;
import com.snszyk.simas.inventory.vo.*;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 盘点计划表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
public class EquipmentInventoryPlanLogicService extends BaseCrudLogicService<EquipmentInventoryPlanDTO, EquipmentInventoryPlanVO> {

	private final IEquipmentInventoryPlanService planService;
	private final IEquipmentInventoryItemService itemService;
	private final IDeviceAccountClient deviceAccountClient;
	private final IEquipmentInventoryOrderService orderService;
	private final SzykRedis szykRedis;
	private final GeneralLogicService generalLogicService;
	private static final String NO_PREFIX = "equipment_inventory_no:";

	/**
	 * 生成单号
	 *
	 * @param planNo
	 * @return
	 */
	private String generateNo(String planNo) {
		String key = NO_PREFIX + planNo;
		Long increment = szykRedis.incr(key);
		return planNo + String.format("%03d", increment);
	}

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.planService;
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<EquipmentInventoryPlanDTO> page(EquipmentInventoryPlanPageVO v) {
		// 分页查询盘点单
		IPage<EquipmentInventoryPlanDTO> page = planService.pageList(v);
		if (ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}
		// 盘点ids
		final List<Long> planIds = ListUtil.map(page.getRecords(), EquipmentInventoryPlanDTO::getId);
		// 查询已盘数量
		final Map<Long, Long> hasInventoryQuantityMap = this.getHasInventoryQuantityMap(planIds);
		page.getRecords().forEach(DTO -> {
			// 填充dto
			populateDTO(DTO, hasInventoryQuantityMap);
		});
		return page;
	}

	/**
	 * 查询已盘工单数量
	 *
	 * @param planIds
	 * @return
	 */
	private Map<Long, Long> getHasInventoryQuantityMap(List<Long> planIds) {
		if (ObjectUtil.isEmpty(planIds)) {
			return MapUtil.empty();
		}
		// 根据计划ids查询盘点明细
		final List<EquipmentInventoryItemDTO> itemDTOList = itemService.listByPlanIds(planIds);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			return MapUtil.empty();
		}
		// 统计已盘点数量
		return itemDTOList.stream()
			.filter(item -> !EquipmentInventoryItemResultEnum.NOT_START.getCode().equals(item.getResult()))
			.collect(Collectors.groupingBy(EquipmentInventoryItemDTO::getPlanId, Collectors.counting()));
	}

	/**
	 * 修改人姓名
	 *
	 * @param dto
	 */
	private void populateDTO(EquipmentInventoryPlanDTO dto, Map<Long, Long> hasInventoryQuantityMap) {
		// 创建人姓名
		Optional.ofNullable(UserCache.getUser(dto.getCreateUser()))
			.ifPresent(user -> dto.setCreateUserName(user.getRealName()));
		// 修改人姓名
		Optional.ofNullable(UserCache.getUser(dto.getUpdateUser()))
			.ifPresent(user -> dto.setUpdateUserName(user.getRealName()));
		// 盘点工单状态名称
		Optional.ofNullable(dto.getStatus())
			.map(status -> EquipmentInventoryPlanEnum.getByCode(status))
			.ifPresent(statusEnum -> dto.setStatusName(statusEnum.getName()));
		// 已经盘点数量
		Optional.ofNullable(hasInventoryQuantityMap.getOrDefault(dto.getId(), 0L))
			.ifPresent(hasInventoryQuantity -> dto.setInventory(Math.toIntExact(hasInventoryQuantity)));
	}

	/**
	 * 保存
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean save(EquipmentInventoryPlanSaveVO v) {
		// 保存校验
		this.saveValid(v);
		final EquipmentInventoryPlanVO vo = EquipmentInventoryPlanBeanMapper.INSTANCE.toVO(v);
		vo.setNo(BizCodeUtil.generate(SimasConstant.BizCode.EQUIPMENT_INVENTORY_PLAN));
		// 保存盘点单
		final EquipmentInventoryPlanDTO planDTO = planService.save(vo);
		// 初始化工单和明细
		final Map<Long, List<DeviceInventoryDTO>> itemMap = initOrderAndItem(planDTO);
		// 修改盘点计划总数量
		updatePlan(planDTO, itemMap);

		return ObjectUtil.isNotEmpty(planDTO);
	}

	/**
	 * 修改盘点计划总数量
	 *
	 * @param planDTO
	 * @param itemMap
	 * @return
	 */
	private Boolean updatePlan(EquipmentInventoryPlanDTO planDTO, Map<Long, List<DeviceInventoryDTO>> itemMap) {
		final EquipmentInventoryPlanVO planVO = EquipmentInventoryPlanBeanMapper.INSTANCE.toVO(planDTO);
		// 计算盘点明细总数量
		final Long totalQuantity = itemMap.values()
			.stream()
			.flatMap(Collection::stream)
			.collect(Collectors.counting());
		planVO.setTotalQuantity(Math.toIntExact(totalQuantity));
		// 盘点人,转为List保证有序
		final List<Long> userIdSet = Lists.newArrayList(itemMap.keySet());
		// 盘点人userId
		final String userIds = userIdSet.stream()
			.map(String::valueOf)
			.collect(Collectors.joining(StringPool.COMMA));
		planVO.setInventoryUserId(userIds);
		// 盘点人姓名
		final String userNames = userIdSet.stream()
			.map(UserCache::getUser)
			.map(User::getRealName)
			.collect(Collectors.joining(StringPool.COMMA));
		planVO.setInventoryUserName(userNames);
		//
		return ObjectUtil.isNotEmpty(planService.save(planVO));
	}

	/**
	 * 保存校验
	 *
	 * @param v
	 */
	private void saveValid(EquipmentInventoryPlanSaveVO v) {
		if (Func.isEmpty(v.getInventoryDeptId())) {
			throw new ServiceException("请选择盘点部门");
		}
		// 校验盘点部门是否存在已盘点工单
		List<Long> deptIds = Func.toLongList(v.getInventoryDeptId());
		// 未完成盘点的部门
		List<String> unCompleteDeptNames = new ArrayList<>();
		// 没有设备的部门
		List<String> noDeviceDeptNames = new ArrayList<>();
		for (Long deptId : deptIds) {
			// 根据使用部门或者归属部门查询设备
			final R<List<DeviceAccountVO>> listR = deviceAccountClient.listByBelongDeptIdsOrUseDeptIds(String.valueOf(deptId));
			if (ObjectUtil.isEmpty(listR.getData())) {

				final List<DeviceAccountVO> accountVOList = listR.getData()
					.stream()
					.filter(deviceAccountVO -> SzykConstant.DB_NOT_DELETED == deviceAccountVO.getIsLease())
					.collect(Collectors.toList());
				if (ObjectUtil.isEmpty(accountVOList)) {
					Dept dept = SysCache.getDept(deptId);
					noDeviceDeptNames.add(dept.getDeptName());
				}
			}

			final List<EquipmentInventoryPlanDTO> planDTOList = planService.listBy(deptId, EquipmentInventoryPlanEnum.COMPLETE.getCode());
			if (ObjectUtil.isNotEmpty(planDTOList)) {
				Dept dept = SysCache.getDept(deptId);
				if (Func.isNotEmpty(dept)) {
					unCompleteDeptNames.add(dept.getDeptName());
				}
			}

		}
		if (ObjectUtil.isNotEmpty(unCompleteDeptNames)) {
			throw new ServiceException(StringUtil.format("【{}】存在未完成的盘点，请重新选择！", StringUtil.join(unCompleteDeptNames, ",")));
		}
		if (ObjectUtil.isNotEmpty(noDeviceDeptNames)) {
			throw new ServiceException(StringUtil.format("【{}】没有设备，请重新选择！", StringUtil.join(noDeviceDeptNames, ",")));
		}


	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public EquipmentInventoryPlanDTO getById(Long id) {
		final EquipmentInventoryPlanDTO dto = planService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			return dto;
		}
		// 已盘点数量Map
		final Map<Long, Long> hasInventoryQuantityMap = getHasInventoryQuantityMap(Lists.newArrayList(dto.getId()));
		// 填充dto
		populateDTO(dto, hasInventoryQuantityMap);
		return dto;
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean removeById(Long id) {
		// 删除校验
		this.removeValid(id);
		// 删除盘点计划
		final Boolean flag = this.planService.deleteById(id);
		// 删除盘点工单
		this.orderService.deleteByPlanId(id);
		// 删除盘点明细
		this.itemService.deleteByPlanId(id);

		return flag;

	}

	private void removeValid(Long id) {
		final EquipmentInventoryPlanDTO dto = this.planService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("删除失败！盘点单不存在");
		}
		// 只有未盘点状态才可以删除
		if (!EquipmentInventoryPlanEnum.NOT_START.getCode().equals(dto.getStatus())) {
			throw new BusinessException("删除失败！盘点单状态不正确");
		}

	}

	/**
	 * 计划启动
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean start(Long id) {
		// 启动校验
		final EquipmentInventoryPlanDTO dto = this.startValid(id);
		// 更新盘点计划状态
		final Boolean flag = this.updatePlanStatus(dto);
		// 更新盘点工单状态为盘点中
		final List<EquipmentInventoryOrderDTO> orderDTOList = this.updateOrderStatus(dto);
		// 发送盘点消息给相关用户
		final Set<Long> userIds = orderDTOList.stream()
			.map(EquipmentInventoryOrderDTO::getDeviceUserId)
			.collect(Collectors.toSet());
		this.sendMessage(userIds, dto);
		return flag;
	}

	/**
	 * 更新盘点工单状态
	 *
	 * @param dto
	 * @return
	 */
	private List<EquipmentInventoryOrderDTO> updateOrderStatus(EquipmentInventoryPlanDTO dto) {
		// 根据盘点计划查询盘点工单
		final List<EquipmentInventoryOrderDTO> orderDTOList = orderService.listByPlanId(dto.getId());
		if (ObjectUtil.isEmpty(orderDTOList)) {
			throw new BusinessException("启动失败！未查询到盘点工单");
		}
		final List<EquipmentInventoryOrderVO> orderVOList = orderDTOList.stream()
			.map(DTO -> {
				final EquipmentInventoryOrderVO orderVO = EquipmentInventoryOrderBeanMapper.INSTANCE.toVO(DTO);
				orderVO.setStatus(EquipmentInventoryOrderStatusEnum.PROCESS.getCode());
				return orderVO;
			}).collect(Collectors.toList());
		return orderService.saveOrUpdateBatch(orderVOList);

	}

	/**
	 * 更新盘点计划状态
	 *
	 * @param dto
	 */
	private Boolean updatePlanStatus(EquipmentInventoryPlanDTO dto) {
		final EquipmentInventoryPlanVO planVO = EquipmentInventoryPlanBeanMapper.INSTANCE.toVO(dto);
		planVO.setStatus(EquipmentInventoryPlanEnum.PROCESS.getCode());
		final EquipmentInventoryPlanDTO planDTO = this.planService.save(planVO);
		return ObjectUtil.isNotEmpty(planDTO);
	}

	/**
	 * 发送消息
	 *
	 * @param userIds
	 */
	private void sendMessage(Set<Long> userIds, EquipmentInventoryPlanDTO planDTO) {
		if (ObjectUtil.isEmpty(userIds)) {
			return;
		}
		generalLogicService.sendMessage(planDTO.getNo(), JsonUtil.toJson(planDTO), new ArrayList<>(userIds), MessageBizTypeEnum.SIMAS_EQUIPMENT_INVENTORY_ADD);
	}

	/**
	 * 初始化工单和明细
	 *
	 * @param dto
	 */
	private Map<Long, List<DeviceInventoryDTO>> initOrderAndItem(EquipmentInventoryPlanDTO dto) {
		// 查询使用部门或归属部门的设备
		final R<List<DeviceAccountVO>> listR = deviceAccountClient.listByBelongDeptIdsOrUseDeptIds(dto.getInventoryDeptId());
		if (ObjectUtil.isEmpty(listR.getData())) {
			throw new BusinessException("初始化失败！未查询到设备");
		}
		// 按照用户id分组； 用户id->设备列表
		final Map<Long, List<DeviceInventoryDTO>> userIdToDeviceMap = listR.getData()
			.stream()
			.filter(deviceAccountVO -> ObjectUtil.isNotEmpty(deviceAccountVO.getIsLeaseBack()) && SzykConstant.DB_NOT_DELETED == deviceAccountVO.getIsLeaseBack())
			.map(deviceAccountVO -> {
				final DeviceInventoryDTO deviceDTO = BeanUtil.copy(deviceAccountVO, DeviceInventoryDTO.class);
				// 使用人不为空
				if (ObjectUtil.isNotEmpty(deviceDTO.getUserId())) {
					deviceDTO.setUseUserIdOrResponsibleId(deviceDTO.getUserId());
					deviceDTO.setGenSourceTypeEnum(EquipmentInventoryOrderGenSourceTypeEnum.USER);
				} else {
					deviceDTO.setUseUserIdOrResponsibleId(deviceDTO.getResponsiblePerson());
					deviceDTO.setGenSourceTypeEnum(EquipmentInventoryOrderGenSourceTypeEnum.RESPONSIBLE);
				}

				// 使用部门不为空
				if (ObjectUtil.isNotEmpty(deviceDTO.getUseDept())) {
					deviceDTO.setUseDeptIdOrBelongDeptId(deviceDTO.getUseDept());
				} else {
					deviceDTO.setUseDeptIdOrBelongDeptId(deviceDTO.getBelongDept());
				}
				return deviceDTO;
			}).collect(Collectors.groupingBy(DeviceInventoryDTO::getUseUserIdOrResponsibleId, Collectors.toList()));

		if (ObjectUtil.isEmpty(userIdToDeviceMap)) {
			throw new BusinessException("初始化失败！未查询到设备");
		}

		// 生成工单集合
		final List<EquipmentInventoryOrderVO> orderVOList = userIdToDeviceMap.entrySet()
			.stream()
			.map(entry -> {
				final Long userId = entry.getKey();
				final List<DeviceInventoryDTO> deviceList = entry.getValue();
				// 取出第一个
				final DeviceInventoryDTO deviceInventoryDTO = deviceList.get(0);
				// 生成工单
				final EquipmentInventoryOrderVO orderVO = EquipmentInventoryOrderBeanMapper.INSTANCE.toVO(dto, userId);
				// 手动创建盘点工单id
				orderVO.setId(IdWorker.getId());
				// 生成单号
				orderVO.setNo(generateNo(dto.getNo()));
				// 来源
				orderVO.setGenSourceType(deviceInventoryDTO.getGenSourceTypeEnum().getCode());

				// 设备人姓名
				Optional.ofNullable(orderVO.getDeviceUserId())
					.map(UserCache::getUser)
					.map(User::getRealName)
					.ifPresent(orderVO::setDeviceUserName);
				// 设备部门id
				orderVO.setDeviceDeptId(deviceInventoryDTO.getUseDeptIdOrBelongDeptId());
				// 设备部门名称
				Optional.ofNullable(orderVO.getDeviceDeptId())
					.map(SysCache::getDept)
					.map(Dept::getDeptName)
					.ifPresent(orderVO::setDeviceDeptName);
				// 计划创建人姓名
				Optional.ofNullable(orderVO.getPlanCreateUserId())
					.map(UserCache::getUser)
					.map(User::getRealName)
					.ifPresent(orderVO::setPlanCreateUserName);
				// 生成明细
				final List<EquipmentInventoryItemVO> itemVOList = deviceList.stream()
					.map(device -> {
						EquipmentInventoryItemVO itemVO = EquipmentInventoryItemBeanMapper.INSTANCE.toVO(dto, orderVO, device);
						itemVO.setTenantId(dto.getTenantId());
						return itemVO;
					}).collect(Collectors.toList());
				// 保存明细
				itemService.saveOrUpdateBatch(itemVOList);
				// 总数
				orderVO.setTotalQuantity(itemVOList.size());
				return orderVO;
			}).collect(Collectors.toList());
		// 批量保存盘点工单
		orderService.saveOrUpdateBatch(orderVOList);

		return userIdToDeviceMap;
	}

	/**
	 * 启动校验
	 *
	 * @param id
	 */
	private EquipmentInventoryPlanDTO startValid(Long id) {
		final EquipmentInventoryPlanDTO dto = this.planService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new BusinessException("启动失败！盘点单不存在");
		}
		// 只有未盘点状态才可以启动
		if (!EquipmentInventoryPlanEnum.NOT_START.getCode().equals(dto.getStatus())) {
			throw new BusinessException("启动失败！盘点单状态不正确");
		}

		return dto;
	}

	/**
	 * 停止
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean stop(Long id) {
		// 停止校验
		EquipmentInventoryPlanDTO planDTO = this.stopPlanValid(id);

		final LocalDateTime now = LocalDateTime.now();
		// 查询所有未完结的工单
		final List<EquipmentInventoryOrderDTO> unCompleteOrderList = orderService.listBy(planDTO.getId(), null, EquipmentInventoryPlanEnum.COMPLETE.getCode());
		if (ObjectUtil.isNotEmpty(unCompleteOrderList)) {
			// 获取未完结的工单id集合
			final List<Long> unCompleteOrderIds = ListUtil.distinctMap(unCompleteOrderList, EquipmentInventoryOrderDTO::getId);
			// 停止计划修改明细状态
			this.stopPlanUpdateBatchItemStatus(unCompleteOrderIds);
			// 停止计划批量修改未完成的工单状态
			this.stopPlanUpdateBatchOrder(unCompleteOrderIds, now);
		}

		// 更新计划状态为完结
		final EquipmentInventoryPlanVO planVO = EquipmentInventoryPlanBeanMapper.INSTANCE.toVO(planDTO);
		planVO.setCompleteTime(now);
		planVO.setStatus(EquipmentInventoryPlanEnum.COMPLETE.getCode());
		planDTO = planService.save(planVO);
		return ObjectUtil.isNotEmpty(planDTO);
	}

	/**
	 * 停止计划批量更新工单
	 *
	 * @param unCompleteOrderIds
	 */
	private void stopPlanUpdateBatchOrder(List<Long> unCompleteOrderIds, LocalDateTime completeTime) {
		if (ObjectUtil.isEmpty(unCompleteOrderIds)) {
			return;
		}
		final List<EquipmentInventoryOrderVO> orderVOList = unCompleteOrderIds.stream()
			.map(id -> {
				EquipmentInventoryOrderVO orderVO = new EquipmentInventoryOrderVO();
				orderVO.setId(id);
				orderVO.setStatus(EquipmentInventoryOrderStatusEnum.COMPLETE.getCode());
				orderVO.setCompleteTime(completeTime);
				return orderVO;
			}).collect(Collectors.toList());

		orderService.updateBatch(orderVOList);
	}

	/**
	 * 停止计划修改明细状态
	 *
	 * @param unCompleteOrderIds
	 */
	private void stopPlanUpdateBatchItemStatus(List<Long> unCompleteOrderIds) {
		// 查询明细
		final List<EquipmentInventoryItemDTO> itemDTOList = itemService.listByOrderIds(unCompleteOrderIds);
		if (ObjectUtil.isEmpty(itemDTOList)) {
			throw new BusinessException("未查询到盘点明细");
		}
		final List<EquipmentInventoryItemVO> itemVOList = itemDTOList.stream()
			.map(itemDTO -> {
				final EquipmentInventoryItemVO itemVO = EquipmentInventoryItemBeanMapper.INSTANCE.toVO(itemDTO);
				// 停止计划视为正常
				itemVO.setResult(EquipmentInventoryItemResultEnum.NORMAL.getCode());
				return itemVO;
			}).collect(Collectors.toList());

		itemService.saveOrUpdateBatch(itemVOList);
	}

	/**
	 * 计划停止校验
	 *
	 * @param id
	 * @return
	 */
	private EquipmentInventoryPlanDTO stopPlanValid(Long id) {
		final EquipmentInventoryPlanDTO planDTO = planService.fetchById(id);
		if (ObjectUtil.isEmpty(planDTO)) {
			throw new BusinessException("未查询到盘点计划");
		}
		// 只有盘点中状态才可以停止
		if (!EquipmentInventoryPlanEnum.PROCESS.getCode().equals(planDTO.getStatus())) {
			throw new BusinessException("停止失败！盘点单状态不正确");
		}
		return planDTO;
	}
}
