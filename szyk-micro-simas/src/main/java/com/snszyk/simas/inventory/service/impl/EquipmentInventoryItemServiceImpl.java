/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryItemBeanMapper;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryItem;
import com.snszyk.simas.inventory.mapper.EquipmentInventoryItemMapper;
import com.snszyk.simas.inventory.service.IEquipmentInventoryItemService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件盘点明细 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
public class EquipmentInventoryItemServiceImpl extends BaseCrudServiceImpl<EquipmentInventoryItemMapper, EquipmentInventoryItem, EquipmentInventoryItemDTO, EquipmentInventoryItemVO> implements IEquipmentInventoryItemService {


	@Override
	public List<EquipmentInventoryItemDTO> listByPlanIds(List<Long> planIds) {
		final List<EquipmentInventoryItem> list = this.lambdaQuery()
			.in(EquipmentInventoryItem::getPlanId, planIds)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : EquipmentInventoryItemBeanMapper.INSTANCE.toDTOList(list);
	}

	@Override
	public Boolean saveOrUpdateBatch(List<EquipmentInventoryItemVO> itemVOList) {
		return super.saveOrUpdateBatch(EquipmentInventoryItemBeanMapper.INSTANCE.toEntityList(itemVOList));
	}

	@Override
	public IPage<EquipmentInventoryItemDTO> pageList(EquipmentInventoryItemPageVO v) {
		// 使用统一的查询方法
		return this.pageWithConditions(v);
	}

	@Override
	public IPage<EquipmentInventoryItemDTO> pageWithConditions(EquipmentInventoryItemPageVO pageVO) {
		// ServiceImpl层只负责数据查询，不包含业务逻辑判断
		if (ObjectUtil.isEmpty(pageVO)) {
			return new Page<>();
		}

		// 创建分页对象
		IPage<EquipmentInventoryItem> page = new Page<>(pageVO.getCurrent(), pageVO.getSize());

		// 统一使用自定义SQL查询，支持所有查询条件
		// 通过动态SQL自动处理是否需要关联设备表
		List<EquipmentInventoryItem> records = baseMapper.selectPageWithDeviceName(page, pageVO);
		page.setRecords(records);

		// 转换为DTO
		return page.convert(EquipmentInventoryItemBeanMapper.INSTANCE::toDTO);
	}

	@Override
	public List<EquipmentInventoryItemDTO> listByOrderIds(List<Long> orderIds) {
		final List<EquipmentInventoryItem> list = this.lambdaQuery()
			.in(EquipmentInventoryItem::getInventoryOrderId, orderIds)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : EquipmentInventoryItemBeanMapper.INSTANCE.toDTOList(list);
	}

	@Override
	public Boolean deleteByPlanId(Long planId) {
		return this.lambdaUpdate()
			.eq(EquipmentInventoryItem::getPlanId, planId)
			.remove();
	}

	@Override
	public Integer countItemsByOrderIdAndResult(Long inventoryOrderId, Integer result) {
		// 通用的数据查询方法，可复用于各种业务场景
		return this.lambdaQuery()
			.eq(EquipmentInventoryItem::getInventoryOrderId, inventoryOrderId)
			.eq(ObjectUtil.isNotEmpty(result), EquipmentInventoryItem::getResult, result)
			.count();
	}
}
