/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.service.logic.EquipmentInventoryPlanLogicService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;


/**
 * 盘点计划表 控制器
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipmentinventoryplan")
@Api(value = "设备盘点计划表", tags = "设备盘点计划表接口")
@ApiSupport(order = 60, author = "zhangzhenpu")
@Validated
public class EquipmentInventoryPlanController extends BaseCrudController {

	private final EquipmentInventoryPlanLogicService equipmentInventoryPlanLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return equipmentInventoryPlanLogicService;
	}

	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存", notes = "EquipmentInventoryPlanVo")
	public R<Boolean> save(@RequestBody @Validated EquipmentInventoryPlanSaveVO v) {
		return R.data(equipmentInventoryPlanLogicService.save(v));
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "EquipmentInventoryPlanVo")
	public R<IPage<EquipmentInventoryPlanDTO>> page(EquipmentInventoryPlanPageVO v) {
		return R.data(equipmentInventoryPlanLogicService.page(v));
	}


	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "根据ID获取数据", notes = "id")
	public R<EquipmentInventoryPlanDTO> getById(@PathVariable("id") @NotNull Long id) {
		return R.data(equipmentInventoryPlanLogicService.getById(id));
	}

	/**
	 * 删除
	 */

	@DeleteMapping("/removeById/{id}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "删除", notes = "id")
	public R<Boolean> removeById(@NotNull @PathVariable("id") Long id) {
		return R.data(equipmentInventoryPlanLogicService.removeById(id));
	}

	/**
	 * 启动
	 */
	@PostMapping("/start/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "启动", notes = "id")
	public R<Boolean> start(@NotNull @PathVariable("id") Long id) {
		return R.data(equipmentInventoryPlanLogicService.start(id));
	}

	/**
	 * 停止
	 */
	@PutMapping("/stop/{id}")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "停止", notes = "id")
	public R<Boolean> stop(@NotNull @PathVariable("id") Long id) {
		return R.data(equipmentInventoryPlanLogicService.stop(id));
	}

}
