/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 盘点计划表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface IEquipmentInventoryPlanService extends IBaseCrudService<EquipmentInventoryPlanDTO, EquipmentInventoryPlanVO> {

	EquipmentInventoryPlanDTO onlySave(EquipmentInventoryPlanVO vo);

	List<EquipmentInventoryPlanDTO> listBy(Long likeInventoryDeptId, Integer neStatus);

	IPage<EquipmentInventoryPlanDTO> pageList(EquipmentInventoryPlanPageVO v);

	List<EquipmentInventoryPlanDTO> listBy(LocalDate leStartDate, Integer status);
}
