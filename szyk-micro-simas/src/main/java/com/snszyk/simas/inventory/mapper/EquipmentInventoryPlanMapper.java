/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.mapper;

import com.snszyk.simas.inventory.entity.EquipmentInventoryPlan;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 盘点计划表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface EquipmentInventoryPlanMapper extends BaseMapper<EquipmentInventoryPlan> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param equipmentInventoryPlan
	 * @return
	 */
	List<EquipmentInventoryPlanVO> selectEquipmentInventoryPlanPage(IPage page, EquipmentInventoryPlanVO equipmentInventoryPlan);

}
