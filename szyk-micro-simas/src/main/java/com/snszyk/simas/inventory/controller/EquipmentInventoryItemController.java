/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.crud.controller.BaseCrudController;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemStatisticsDTO;
import com.snszyk.simas.inventory.service.logic.EquipmentInventoryItemLogicService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemDraftOrSubmitVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 备品备件盘点明细 控制器
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipmentinventoryitem")
@Api(value = "设备盘点明细", tags = "设备盘点明细接口")
@ApiSupport(order = 62, author = "zhangzhenpu")
@Validated
public class EquipmentInventoryItemController extends BaseCrudController {

	private final EquipmentInventoryItemLogicService equipmentInventoryItemLogicService;

	@Override
	protected BaseCrudLogicService fetchBaseLogicService() {
		return equipmentInventoryItemLogicService;
	}


	/**
	 * 分页查询设备盘点明细
	 * 支持按盘点计划ID、工单ID、盘点结果、设备名称进行查询
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "分页查询设备盘点明细", notes = "支持设备名称模糊搜索")
	public R<IPage<EquipmentInventoryItemDTO>> page(EquipmentInventoryItemPageVO v) {
		return R.data(equipmentInventoryItemLogicService.page(v));
	}

	@GetMapping("/statistics")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "统计", notes = "SparePartsInventoryItemVO")
	public R<EquipmentInventoryItemStatisticsDTO> getStatistics(@Validated EquipmentInventoryItemPageVO v) {
		return R.data(equipmentInventoryItemLogicService.getStatistics(v));
	}

	/**
	 * 暂存盘点草稿
	 *
	 * @param voList
	 * @return
	 */
	@PutMapping("/draft")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "暂存盘点草稿", notes = "临时保存盘点数据")
	public R<Boolean> saveDraft(@Valid @RequestBody @NotEmpty List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		return R.data(equipmentInventoryItemLogicService.saveDraft(voList));
	}

	/**
	 * 盘点
	 */
	@PutMapping("/submit/{planId}")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "提交盘点结果", notes = "正式提交盘点数据")
	public R<Boolean> submitInventory(@NotNull @PathVariable("planId") Long planId, @Valid @NotEmpty @RequestBody List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		return R.data(equipmentInventoryItemLogicService.submitInventory(planId, voList));
	}

	/**
	 * 盘点明细详情查询
	 */
	@GetMapping("/detail/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "盘点明细详情", notes = "根据ID查询单条盘点明细详情")
	public R<EquipmentInventoryItemDTO> detail(@ApiParam(value = "盘点明细ID", required = true) @NotNull @PathVariable("id") Long id) {
		return R.data(equipmentInventoryItemLogicService.getById(id));
	}

	/**
	 * APP端单条设备盘点提交
	 */
	@PutMapping("/app/submit-single")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "APP端单条设备盘点提交", notes = "支持APP端对单个设备盘点明细进行独立盘点和提交")
	public R<Boolean> submitSingleInventory(@Valid @RequestBody EquipmentInventoryItemDraftOrSubmitVO vo) {
		return R.data(equipmentInventoryItemLogicService.submitSingleInventory(vo));
	}
}
