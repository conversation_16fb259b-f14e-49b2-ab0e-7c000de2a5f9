/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryOrder;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 备品备件盘点工单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface EquipmentInventoryOrderMapper extends BaseMapper<EquipmentInventoryOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param equipmentInventoryOrder
	 * @return
	 */
	List<EquipmentInventoryOrderVO> selectEquipmentInventoryOrderPage(IPage page, EquipmentInventoryOrderVO equipmentInventoryOrder);

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	IPage<EquipmentInventoryOrderDTO> pageList(@Param("v") EquipmentInventoryOrderPageVO v);
}
