/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.logic;


import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountUseOrBelongVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.core.crud.exception.BusinessException;
import com.snszyk.core.crud.service.IBaseCrudService;
import com.snszyk.core.crud.service.logic.BaseCrudLogicService;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.EquipmentStatusEnum;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryItemBeanMapper;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryOrderBeanMapper;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryPlanBeanMapper;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemStatisticsDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.enums.EquipmentInventoryItemResultEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderStatusEnum;
import com.snszyk.simas.inventory.enums.EquipmentInventoryPlanEnum;
import com.snszyk.simas.inventory.service.IEquipmentInventoryItemService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryOrderService;
import com.snszyk.simas.inventory.service.IEquipmentInventoryPlanService;
import com.snszyk.simas.inventory.vo.*;
import com.snszyk.system.cache.SysCache;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 备品备件盘点明细 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
@Slf4j
public class EquipmentInventoryItemLogicService extends BaseCrudLogicService<EquipmentInventoryItemDTO, EquipmentInventoryItemVO> {

	private final IEquipmentInventoryItemService itemService;
	private final IDeviceAccountClient deviceAccountClient;
	private final IEquipmentInventoryOrderService orderService;
	private final IEquipmentInventoryPlanService planService;

	@Override
	protected IBaseCrudService fetchBaseService() {
		return this.itemService;
	}

	/**
	 * 分页查询
	 *
	 * @param v 查询条件
	 * @return 分页结果
	 */
	@Transactional(readOnly = true)
	public IPage<EquipmentInventoryItemDTO> page(EquipmentInventoryItemPageVO v) {
		// 使用统一的查询方法，支持所有查询条件（包括deviceName）
		final IPage<EquipmentInventoryItemDTO> page = itemService.pageWithConditions(v);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return new Page<>(v.getCurrent(), v.getSize());
		}

		// 填充盘点明细数据
		this.populateEquipmentInventoryItemData(page.getRecords());
		return page;
	}

	/**
	 * 获取设备map
	 *
	 * @param deviceIds
	 * @return
	 */
	private Map<Long, DeviceAccountVO> getDeviceMap(List<Long> deviceIds) {
		if (ObjectUtil.isEmpty(deviceIds)) {
			return MapUtil.empty();
		}
		final DeviceAccountVO accountVO = new DeviceAccountVO();
		accountVO.setDeviceIds(deviceIds);
		final R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(accountVO);
		if (ObjectUtil.isEmpty(listR.getData())) {
			return MapUtil.empty();
		}
		return ListUtil.toMap(listR.getData(), DeviceAccountVO::getId, Function.identity());
	}

	/**
	 * 数量统计
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public EquipmentInventoryItemStatisticsDTO getStatistics(EquipmentInventoryItemPageVO v) {
		// 初始化
		final EquipmentInventoryItemStatisticsDTO dto = new EquipmentInventoryItemStatisticsDTO().init();
		v.setSize(-1L);
		// 分页查询
		final IPage<EquipmentInventoryItemDTO> page = itemService.pageList(v);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return dto;
		}
		final List<EquipmentInventoryItemDTO> itemDTOList = page.getRecords();
		// 全部数量
		dto.setTotal((long) itemDTOList.size());
		// 按照盘点结果分组
		final Map<Integer, Long> resultMap = itemDTOList.stream()
			.collect(Collectors.groupingBy(EquipmentInventoryItemDTO::getResult, Collectors.counting()));
		// 未开始数量
		dto.setNotStart(resultMap.getOrDefault(EquipmentInventoryItemResultEnum.NOT_START.getCode(), 0L));
		// 正常数量
		dto.setNormal(resultMap.getOrDefault(EquipmentInventoryItemResultEnum.NORMAL.getCode(), 0L));
		// 缺失数量
		dto.setLoss(resultMap.getOrDefault(EquipmentInventoryItemResultEnum.LOSS.getCode(), 0L));
		return dto;
	}

	/**
	 * 保存草稿（临时保存）
	 *
	 * @param voList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveDraft(List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		// 保存草稿或提交校验
		this.saveDraftOrSubmitValid(voList);
		// 批量修改明细盘点记录
		return this.updateBatchItem(voList);
	}

	/**
	 * 提交盘点
	 *
	 * @param planId
	 * @param voList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean submitInventory(Long planId, List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		// 保存草稿或提交校验
		this.saveDraftOrSubmitValid(voList);
		// 批量修改明细盘点记录
		final Boolean flag = this.updateBatchItem(voList);
		// 当前时间
		final LocalDateTime currentTime = LocalDateTime.now();
		// 修改盘点工单状态为已盘点
		this.updateOrderStatus(voList.get(0).getInventoryOrderId(), EquipmentInventoryOrderStatusEnum.COMPLETE.getCode(), currentTime);
		// 更新设备台账归属部门、负责人、使用部门、使用人字段
		this.updateDeviceAccount(voList);
		// 如果计划下所有工单都提交成功，则更新计划状态为已完成
		this.updatePlanStatusIfAllOrderFinish(planId, currentTime);
		return flag;
	}

	/**
	 * APP端单条设备盘点提交
	 *
	 * @param vo 单条盘点明细VO
	 * @return 提交结果
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean submitSingleInventory(EquipmentInventoryItemDraftOrSubmitVO vo) {
		if (ObjectUtil.isEmpty(vo)) {
			throw new ServiceException("提交失败！未获取到数据");
		}

		// 查询盘点明细获取计划ID
		EquipmentInventoryItemDTO itemDTO = itemService.fetchById(vo.getId());
		if (ObjectUtil.isEmpty(itemDTO)) {
			throw new ServiceException("提交失败！盘点明细不存在");
		}

		// 单条提交校验和处理
		List<EquipmentInventoryItemDraftOrSubmitVO> voList = Lists.newArrayList(vo);
		this.saveDraftOrSubmitValid(voList);

		// 修改单条明细盘点记录
		final Boolean flag = this.updateBatchItem(voList);

		// 更新设备台账归属部门、负责人、使用部门、使用人字段
		this.updateDeviceAccount(voList);

		// 注意：APP端单条提交不自动完成工单，需要所有明细都提交后才能完成工单
		// 检查该工单下是否所有明细都已盘点完成
		this.checkAndUpdateOrderStatusIfAllItemsCompleted(vo.getInventoryOrderId(), itemDTO.getPlanId());

		return flag;
	}

	/**
	 * 根据ID查询盘点明细详情
	 *
	 * @param id 盘点明细ID
	 * @return 盘点明细详情
	 */
	@Transactional(readOnly = true)
	public EquipmentInventoryItemDTO getById(Long id) {
		if (ObjectUtil.isEmpty(id)) {
			throw new ServiceException("查询失败！ID不能为空");
		}

		// 查询盘点明细
		EquipmentInventoryItemDTO dto = itemService.fetchById(id);
		if (ObjectUtil.isEmpty(dto)) {
			throw new ServiceException("盘点明细不存在");
		}

		// 填充盘点明细数据
		this.populateEquipmentInventoryItemData(Lists.newArrayList(dto));

		return dto;
	}

	/**
	 * 如果计划下所有工单都提交成功，则更新计划状态为已完成
	 *
	 * @param planId
	 */
	private void updatePlanStatusIfAllOrderFinish(Long planId, LocalDateTime currentTime) {
		// 根据盘点计划id查询所有盘点工单
		final List<EquipmentInventoryOrderDTO> orderDTOList = orderService.listByPlanId(planId);
		if (ObjectUtil.isEmpty(orderDTOList)) {
			throw new BusinessException("更新计划状态失败！不应该出现的异常");
		}
		// 所有工单都完成
		final boolean allOrderFinish = orderDTOList.stream()
			.allMatch(order -> EquipmentInventoryOrderStatusEnum.COMPLETE.getCode().equals(order.getStatus()));
		if (allOrderFinish) {
			// 更新计划状态为已完成
			final EquipmentInventoryPlanDTO planDTO = planService.fetchById(planId);
			final EquipmentInventoryPlanVO planVO = EquipmentInventoryPlanBeanMapper.INSTANCE.toVO(planDTO);
			planVO.setStatus(EquipmentInventoryPlanEnum.COMPLETE.getCode());
			planVO.setCompleteTime(currentTime);
			planService.save(planVO);
		}
	}

	/**
	 * 更新设备台账信息
	 *
	 * @param voList
	 */
	private void updateDeviceAccount(List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		if (ObjectUtil.isEmpty(voList)) {
			throw new BusinessException("更新设备台账信息失败！未获取到数据");
		}

		final Map<Long, EquipmentInventoryItemDraftOrSubmitVO> webVOMap = ListUtil.toMap(voList, EquipmentInventoryItemDraftOrSubmitVO::getDeviceId, Function.identity());
		// 根据ids获取设备信息
		final DeviceAccountVO accountVO = new DeviceAccountVO();
		accountVO.setDeviceIds(Lists.newArrayList(webVOMap.keySet()));
		final List<DeviceAccountVO> accountVOList = deviceAccountClient.deviceListByParams(accountVO).getData();
		if (ObjectUtil.isEmpty(accountVOList)) {
			throw new BusinessException("更新设备台账信息失败！未获取到台账数据");
		}
		// 循环处理
		for (DeviceAccountVO dbDeviceAccount : accountVOList) {
			// 前端传来的VO
			final EquipmentInventoryItemDraftOrSubmitVO webVO = webVOMap.get(dbDeviceAccount.getId());

			// 如果使用人、使用部门、责任人、归属部门全都相同无需更新
			if (ObjectUtil.nullSafeEquals(dbDeviceAccount.getUseDept(), webVO.getUseDeptId())
				&& ObjectUtil.nullSafeEquals(dbDeviceAccount.getUserId(), webVO.getUseUserId())
				&& ObjectUtil.nullSafeEquals(dbDeviceAccount.getBelongDept(), webVO.getBelongDeptId())
				&& ObjectUtil.nullSafeEquals(dbDeviceAccount.getResponsiblePerson(), webVO.getResponsibleUserId())) {
				continue;
			}

			final DeviceAccountUseOrBelongVO vo = new DeviceAccountUseOrBelongVO();
			vo.setId(dbDeviceAccount.getId());

			// 归属部门不一致且新值不为空
			if (webVO.getBelongDeptId() != null && !ObjectUtil.nullSafeEquals(dbDeviceAccount.getBelongDept(), webVO.getBelongDeptId())) {
				vo.setBelongDept(webVO.getBelongDeptId());
			}
			// 责任人不一致且新值不为空
			if (webVO.getResponsibleUserId() != null && !ObjectUtil.nullSafeEquals(dbDeviceAccount.getResponsiblePerson(), webVO.getResponsibleUserId())) {
				vo.setResponsiblePerson(webVO.getResponsibleUserId());
			}

			// 非使用中状态设备才会更新使用人、使用部门
			if (!ObjectUtil.nullSafeEquals(EquipmentStatusEnum.IN_USE.getCode(), dbDeviceAccount.getStatus())) {
				// 使用人不一致且新值不为空
				if (webVO.getUseUserId() != null && !ObjectUtil.nullSafeEquals(dbDeviceAccount.getUserId(), webVO.getUseUserId())) {
					vo.setUserId(webVO.getUseUserId());
					vo.setStatus(String.valueOf(EquipmentStatusEnum.IN_USE.getCode()));
				}
				// 使用部门不一致且新值不为空
				if (webVO.getUseDeptId() != null && !ObjectUtil.nullSafeEquals(dbDeviceAccount.getUseDept(), webVO.getUseDeptId())) {
					vo.setUseDept(webVO.getUseDeptId());
				}
			}

			// 检查是否有字段需要更新
			if (vo.getBelongDept() != null || vo.getResponsiblePerson() != null ||
				vo.getUseDept() != null || vo.getUserId() != null || vo.getStatus() != null) {
				final R<Boolean> booleanR = deviceAccountClient.updateUseOrBelong(vo);
				if (!booleanR.isSuccess()) {
					throw new BusinessException("更新设备台账信息失败！");
				}
			}
		}
	}

	/**
	 * 修改工单表状态
	 *
	 * @param inventoryOrderId 盘点工单id
	 * @param orderStatus      盘点工单状态
	 */
	private void updateOrderStatus(Long inventoryOrderId, Integer orderStatus, LocalDateTime currentTime) {
		final EquipmentInventoryOrderDTO orderDTO = orderService.fetchById(inventoryOrderId);
		final EquipmentInventoryOrderVO vo = EquipmentInventoryOrderBeanMapper.INSTANCE.toVO(orderDTO);
		vo.setStatus(orderStatus);
		vo.setCompleteTime(currentTime);
		orderService.save(vo);
	}

	/**
	 * 保存草稿或提交校验
	 *
	 * @param voList
	 */
	private void saveDraftOrSubmitValid(List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		if (ObjectUtil.isEmpty(voList)) {
			throw new BusinessException("保存失败！未获取到数据");
		}
		// 获取盘点工单id
		final Long inventoryOrderId = voList.get(0).getInventoryOrderId();
		// 查询盘点工单
		final EquipmentInventoryOrderDTO orderDTO = orderService.fetchById(inventoryOrderId);
		if (ObjectUtil.isEmpty(orderDTO)) {
			throw new BusinessException("保存失败！未查询到盘点工单");
		}
		// 只有盘点中的状态可以盘点
		if (!EquipmentInventoryOrderStatusEnum.PROCESS.getCode().equals(orderDTO.getStatus())) {
			throw new BusinessException("保存失败！盘点工单状态不正确");
		}
	}

	/**
	 * 批量修改明细盘点记录
	 *
	 * @param voList
	 */
	private Boolean updateBatchItem(List<EquipmentInventoryItemDraftOrSubmitVO> voList) {
		final List<EquipmentInventoryItemVO> itemVOList = voList.stream()
			.map(EquipmentInventoryItemBeanMapper.INSTANCE::toVO)
			.collect(Collectors.toList());
		return itemService.saveOrUpdateBatch(itemVOList);
	}

	/**
	 * 检查工单下所有明细是否都已完成盘点，如果是则更新工单状态
	 * 优化版本：使用数据库层面的聚合查询，避免不必要的数据填充
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @param planId           盘点计划ID
	 */
	private void checkAndUpdateOrderStatusIfAllItemsCompleted(Long inventoryOrderId, Long planId) {
		// 业务逻辑判断：检查是否所有明细都已完成盘点
		boolean allItemsCompleted = this.isAllItemsCompletedByOrderId(inventoryOrderId);

		if (allItemsCompleted) {
			final LocalDateTime currentTime = LocalDateTime.now();
			// 更新工单状态为已完成
			this.updateOrderStatus(inventoryOrderId, EquipmentInventoryOrderStatusEnum.COMPLETE.getCode(), currentTime);
			// 检查计划下所有工单是否都完成
			this.updatePlanStatusIfAllOrderFinish(planId, currentTime);
		}
	}

	/**
	 * 判断指定工单下所有明细是否都已完成盘点
	 * 业务逻辑层：包含参数校验和业务判断逻辑
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return true-所有明细都已完成盘点，false-还有未完成的明细
	 */
	private boolean isAllItemsCompletedByOrderId(Long inventoryOrderId) {
		if (ObjectUtil.isEmpty(inventoryOrderId)) {
			return false;
		}

		// 调用Service层获取未完成的明细数量
		// 业务逻辑：未完成 = 盘点结果为"未盘点"状态
		Integer uncompletedCount = itemService.countItemsByOrderIdAndResult(
			inventoryOrderId,
			EquipmentInventoryItemResultEnum.NOT_START.getCode()
		);

		// 业务逻辑判断：如果未完成的记录数为0，说明所有明细都已完成盘点
		return uncompletedCount != null && uncompletedCount == 0;
	}

	/**
	 * 统计指定工单下未完成盘点的明细数量
	 * 业务语义封装：为上层提供清晰的业务接口
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return 未完成盘点的明细数量
	 */
	public Long countUncompletedItemsByOrderId(Long inventoryOrderId) {
		if (ObjectUtil.isEmpty(inventoryOrderId)) {
			return 0L;
		}

		// 业务逻辑：未完成 = 盘点结果为"未盘点"状态
		Integer count = itemService.countItemsByOrderIdAndResult(
			inventoryOrderId,
			EquipmentInventoryItemResultEnum.NOT_START.getCode()
		);
		return count != null ? count.longValue() : 0L;
	}

	/**
	 * 统计指定工单下正常盘点的明细数量
	 * 业务语义封装：为报表等功能提供便捷接口
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return 正常盘点的明细数量
	 */
	public Long countNormalItemsByOrderId(Long inventoryOrderId) {
		if (ObjectUtil.isEmpty(inventoryOrderId)) {
			return 0L;
		}

		Integer count = itemService.countItemsByOrderIdAndResult(
			inventoryOrderId,
			EquipmentInventoryItemResultEnum.NORMAL.getCode()
		);
		return count != null ? count.longValue() : 0L;
	}

	/**
	 * 统计指定工单下缺失的明细数量
	 * 业务语义封装：为报表等功能提供便捷接口
	 *
	 * @param inventoryOrderId 盘点工单ID
	 * @return 缺失的明细数量
	 */
	public Long countLossItemsByOrderId(Long inventoryOrderId) {
		if (ObjectUtil.isEmpty(inventoryOrderId)) {
			return 0L;
		}

		Integer count = itemService.countItemsByOrderIdAndResult(
			inventoryOrderId,
			EquipmentInventoryItemResultEnum.LOSS.getCode()
		);
		return count != null ? count.longValue() : 0L;
	}

	/**
	 * 批量填充盘点明细数据（设备信息、字典转换、用户信息、部门信息）
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateEquipmentInventoryItemData(List<EquipmentInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 获取设备信息并填充
		this.populateDeviceInfo(itemList);

		// 填充枚举名称（设备状态、盘点结果）
		this.populateEnumNames(itemList);

		// 填充用户姓名（盘点人、负责人、使用人）
		this.populateUserNames(itemList);

		// 填充部门名称（归属部门、使用部门）
		this.populateDeptNames(itemList);
	}

	/**
	 * 填充设备信息
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateDeviceInfo(List<EquipmentInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		// 获取设备ID列表
		final List<Long> deviceIds = ListUtil.map(itemList, EquipmentInventoryItemDTO::getDeviceId);
		// 获取所有设备信息Map
		Map<Long, DeviceAccountVO> accountVOMap = this.getDeviceMap(deviceIds);

		// 填充设备信息
		itemList.forEach(item -> {
			final DeviceAccountVO deviceAccountVO = accountVOMap.get(item.getDeviceId());
			if (ObjectUtil.isNotEmpty(deviceAccountVO)) {
				item.setDeviceName(deviceAccountVO.getName());
				item.setDeviceCode(deviceAccountVO.getCode());
				item.setDeviceModel(deviceAccountVO.getModel());
				item.setDeviceStatus(deviceAccountVO.getStatus());
			}
		});
	}

	/**
	 * 填充枚举名称（设备状态、盘点结果）
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateEnumNames(List<EquipmentInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		itemList.forEach(item -> {
			// 设备状态名称
			Optional.ofNullable(EquipmentStatusEnum.getByCode(item.getDeviceStatus()))
				.ifPresent(statusEnum -> item.setDeviceStatusName(statusEnum.getName()));

			// 盘点结果名称
			Optional.ofNullable(EquipmentInventoryItemResultEnum.getByCode(item.getResult()))
				.ifPresent(resultEnum -> item.setResultName(resultEnum.getName()));
		});
	}

	/**
	 * 填充用户姓名（盘点人、负责人、使用人）
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateUserNames(List<EquipmentInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		itemList.forEach(item -> {
			// 盘点人姓名
			Optional.ofNullable(UserCache.getUser(item.getDeviceUserId()))
				.ifPresent(user -> item.setDeviceUserName(user.getRealName()));

			// 负责人名称
			Optional.ofNullable(UserCache.getUser(item.getResponsibleUserId()))
				.ifPresent(user -> item.setResponsibleUserName(user.getRealName()));

			// 使用人姓名
			Optional.ofNullable(UserCache.getUser(item.getUseUserId()))
				.ifPresent(user -> item.setUseUserName(user.getRealName()));
		});
	}

	/**
	 * 填充部门名称（归属部门、使用部门）
	 *
	 * @param itemList 盘点明细列表
	 */
	private void populateDeptNames(List<EquipmentInventoryItemDTO> itemList) {
		if (ObjectUtil.isEmpty(itemList)) {
			return;
		}

		itemList.forEach(item -> {
			// 归属部门名称
			Optional.ofNullable(item.getBelongDeptId())
				.map(SysCache::getDept)
				.ifPresent(dept -> item.setBelongDeptName(dept.getDeptName()));

			// 使用部门名称
			Optional.ofNullable(item.getUseDeptId())
				.map(SysCache::getDept)
				.ifPresent(dept -> item.setUseDeptName(dept.getDeptName()));
		});
	}


}
