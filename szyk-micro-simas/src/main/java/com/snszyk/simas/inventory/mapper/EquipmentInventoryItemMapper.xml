<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.inventory.mapper.EquipmentInventoryItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentInventoryItemResultMap" type="com.snszyk.simas.inventory.entity.EquipmentInventoryItem">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_start_date" property="planStartDate"/>
        <result column="plan_end_date" property="planEndDate"/>
        <result column="inventory_order_id" property="inventoryOrderId"/>
        <result column="inventory_order_no" property="inventoryOrderNo"/>
        <result column="device_id" property="deviceId"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_model" property="deviceModel"/>
        <result column="result" property="result"/>
        <result column="remark" property="remark"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>


    <select id="selectEquipmentInventoryItemPage" resultMap="equipmentInventoryItemResultMap">
        select * from simas_equipment_inventory_item where is_deleted = 0
    </select>

    <!-- 统一的分页查询方法，支持所有查询条件 -->
    <select id="selectPageWithDeviceName" resultMap="equipmentInventoryItemResultMap">
        SELECT ei.*
        FROM simas_equipment_inventory_item ei
        <if test="pageVO.deviceName != null and pageVO.deviceName != ''">
            LEFT JOIN device_account da ON ei.device_id = da.id AND da.is_deleted = 0
        </if>
        WHERE ei.delete_time = 0
        <if test="pageVO.inventoryOrderId != null">
            AND ei.inventory_order_id = #{pageVO.inventoryOrderId}
        </if>
        <if test="pageVO.planId != null">
            AND ei.plan_id = #{pageVO.planId}
        </if>
        <if test="pageVO.result != null">
            AND ei.result = #{pageVO.result}
        </if>
        <if test="pageVO.deviceName != null and pageVO.deviceName != ''">
            AND da.name LIKE CONCAT('%', #{pageVO.deviceName}, '%')
        </if>
        ORDER BY ei.create_time DESC
    </select>

</mapper>
