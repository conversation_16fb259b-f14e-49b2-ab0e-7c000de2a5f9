package com.snszyk.simas.inventory.schedule;// package com.snszyk.simas.inventory.schedule;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.service.IEquipmentInventoryPlanService;
import com.snszyk.simas.inventory.service.logic.EquipmentInventoryPlanLogicService;
import com.snszyk.simas.spare.enums.SpareInventoryPlanStatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@Component
public class EquipmentInventoryJobHandle {
	private final IEquipmentInventoryPlanService planService;
	private final EquipmentInventoryPlanLogicService planLogicService;

	/**
	 * 盘点计划启动
	 */
	@XxlJob("startEquipmentPlanJobHandler")
	public ReturnT<String> startEquipmentPlanJobHandler(String param) throws ParseException {
		XxlJobLogger.log("################设备盘点计划定时任务-START-################");
		LocalDate timeOnlyDate = LocalDate.now();
		// 查询待开始状态的盘点计
		final List<EquipmentInventoryPlanDTO> planDTOList = planService.listBy(timeOnlyDate, SpareInventoryPlanStatusEnum.NOT_START.getCode());
		if (ObjectUtil.isNotEmpty(planDTOList)) {
			// 开始启动盘点计划
			planDTOList.forEach(plan -> {
				planLogicService.start(plan.getId());
			});
			// 记录今天启动的盘点计划编号
			final String no = planDTOList.stream()
				.map(item -> item.getNo())
				.collect(Collectors.joining(","));
			// 打印日志记录
			XxlJobLogger.log("启动盘点计划编号：{}", no);
		}
		XxlJobLogger.log("################设备盘点计划定时任务-END-################");
		return ReturnT.SUCCESS;
	}
}
