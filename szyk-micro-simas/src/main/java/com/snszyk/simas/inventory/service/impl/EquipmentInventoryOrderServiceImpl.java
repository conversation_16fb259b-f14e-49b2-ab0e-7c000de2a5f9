/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.crud.service.impl.BaseCrudServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.inventory.beanmapper.EquipmentInventoryOrderBeanMapper;
import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryOrder;
import com.snszyk.simas.inventory.mapper.EquipmentInventoryOrderMapper;
import com.snszyk.simas.inventory.service.IEquipmentInventoryOrderService;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 备品备件盘点工单 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@AllArgsConstructor
@Service
public class EquipmentInventoryOrderServiceImpl extends BaseCrudServiceImpl<EquipmentInventoryOrderMapper, EquipmentInventoryOrder, EquipmentInventoryOrderDTO, EquipmentInventoryOrderVO> implements IEquipmentInventoryOrderService {


	@Override
	public List<EquipmentInventoryOrderDTO> saveOrUpdateBatch(List<EquipmentInventoryOrderVO> orderVOList) {
		final List<EquipmentInventoryOrder> entityList = EquipmentInventoryOrderBeanMapper.INSTANCE.toEntityList(orderVOList);
		super.saveOrUpdateBatch(entityList);
		return EquipmentInventoryOrderBeanMapper.INSTANCE.toDTOList(entityList);
	}

	@Override
	public List<EquipmentInventoryOrderDTO> listByPlanId(Long planId) {
		final List<EquipmentInventoryOrder> list = this.lambdaQuery()
			.eq(EquipmentInventoryOrder::getPlanId, planId)
			.orderByDesc(EquipmentInventoryOrder::getCreateTime)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : EquipmentInventoryOrderBeanMapper.INSTANCE.toDTOList(list);

	}

	@Override
	public IPage<EquipmentInventoryOrderDTO> pageList(EquipmentInventoryOrderPageVO v) {
		return this.baseMapper.pageList(v);
	}

	@Override
	public Boolean deleteByPlanId(Long planId) {
		return this.lambdaUpdate()
			.eq(EquipmentInventoryOrder::getPlanId, planId)
			.remove();
	}

	@Override
	public List<EquipmentInventoryOrderDTO> listBy(Long planId, List<Integer> statusList, Integer neStatus) {
		final List<EquipmentInventoryOrder> list = this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(planId), EquipmentInventoryOrder::getPlanId, planId)
			.in(ObjectUtil.isNotEmpty(statusList), EquipmentInventoryOrder::getStatus, statusList)
			.ne(ObjectUtil.isNotEmpty(neStatus), EquipmentInventoryOrder::getStatus, neStatus)
			.list();

		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : EquipmentInventoryOrderBeanMapper.INSTANCE.toDTOList(list);

	}

	@Override
	public Boolean updateBatch(List<EquipmentInventoryOrderVO> orderVOList) {
		final List<EquipmentInventoryOrder> entityList = EquipmentInventoryOrderBeanMapper.INSTANCE.toEntityList(orderVOList);
		return super.updateBatchById(entityList);
	}
}
