/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.mapper;

import com.snszyk.simas.inventory.entity.EquipmentInventoryItem;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemPageVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 设备盘点明细 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
public interface EquipmentInventoryItemMapper extends BaseMapper<EquipmentInventoryItem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param equipmentInventoryItem
	 * @return
	 */
	List<EquipmentInventoryItemVO> selectEquipmentInventoryItemPage(IPage page, EquipmentInventoryItemVO equipmentInventoryItem);

	/**
	 * 统一的分页查询方法
	 * 支持所有查询条件，包括设备名称的模糊查询
	 * 通过动态SQL自动处理是否需要关联设备相关表
	 *
	 * @param page 分页参数
	 * @param pageVO 查询条件（支持deviceName、inventoryOrderId等所有条件）
	 * @return 设备盘点明细列表
	 */
	List<EquipmentInventoryItem> selectPageWithDeviceName(IPage<EquipmentInventoryItem> page, @Param("pageVO") EquipmentInventoryItemPageVO pageVO);

}
