<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.receive.mapper.DeviceReceiveMapper">

    <sql id="selectData">
        select t.*,
               t1.real_name  create_user_name,
               t2.real_name  update_user_name,
               t3.real_name  apply_user_name,
               t4.dept_name  belong_dept_name,
               t5.dict_value status_name,
               t6.path as location_path
        from simas_device_receive t
                 left join szyk_user t1 on t1.id = t.create_user and t1.is_deleted = 0
                 left join szyk_user t2 on t2.id = t.update_user and t2.is_deleted = 0
                 left join szyk_user t3 on t3.id = t.apply_user_id and t3.is_deleted = 0
                 left join szyk_dept t4 on t4.id = t.belong_dept_id and t4.is_deleted = 0
                 left join device_location t6 on t6.id = t.location_id and t6.is_deleted = 0
                 left join szyk_dict_biz t5
                           on t5.code = 'device_receive_status' and t5.dict_key = t.status and t5.is_deleted = 0


    </sql>
    <select id="pageList" resultType="com.snszyk.simas.receive.dto.DeviceReceiveDto">
        <include refid="selectData"/>
        where t.is_deleted = 0
        <if test="v.applyUserName != null and v.applyUserName != ''">
            and t3.real_name like concat('%', #{v.applyUserName}, '%')
        </if>
        <if test="v.status != null and v.status != ''">
            and t.status = #{v.status}
        </if>

        <if test="v.orderNo != null and v.orderNo != ''">
            and t.order_no like concat('%', #{v.orderNo}, '%')
        </if>
        <if test="v.startApplyDate != null">
            and date(t.apply_time) &gt;= #{v.startApplyDate}
        </if>
        <if test="v.endApplyDate != null">
            and date(t.apply_time) &lt;= #{v.endApplyDate}
        </if>
        order by t.create_time desc
    </select>

    <select id="detail" resultType="com.snszyk.simas.receive.dto.DeviceReceiveDto">
        <include refid="selectData"/>
        where t.is_deleted = 0 and t.id=#{id}
    </select>
    <select id="recordPage" resultType="com.snszyk.simas.receive.dto.DeviceReceiveRecordDto"
            parameterType="com.snszyk.simas.receive.vo.DeviceReceivePageVo">

        select t.*,
               t.id as orderId,
        t1.real_name create_user_name,
        t2.real_name update_user_name,
        t3.real_name apply_user_name,
        t4.dept_name belong_dept_name,
        t5.dict_value status_name,
        t7.name as device_name,
        t7.model as device_model,
        t7.code as device_code ,
        t7.id as device_id
        from simas_device_receive t
        left join szyk_user t1 on t1.id = t.create_user and t1.is_deleted = 0
        left join szyk_user t2 on t2.id = t.update_user and t2.is_deleted = 0
        left join szyk_user t3 on t3.id = t.apply_user_id and t3.is_deleted = 0
        left join szyk_dept t4 on t4.id = t.belong_dept_id and t4.is_deleted = 0
        left join szyk_dict_biz t5
        on t5.code = 'device_receive_status' and t5.dict_key = t.status and t5.is_deleted = 0
        left join simas_device_receive_detail t6 on t6.receive_id = t.id and t6.is_deleted = 0
        left join device_account t7 on t7.id=t6.device_id and t7.is_deleted = 0
        <where>
            <if test="v.status != null and v.status != ''">
                and t6.status = #{v.status}
            </if>
            <if test="v.deviceName != null and v.deviceName != ''">
                and t7.name like concat('%', #{v.deviceName}, '%')
            </if>
            <if test="v.deviceCode != null and v.deviceCode != ''">
                and t7.code like concat('%', #{v.deviceCode}, '%')
            </if>
            <if test="v.isExpired != null and v.isExpired == 0">
                and t.receive_end_date &gt;=  CURDATE()

            </if>
            <if test="v.isExpired != null and v.isExpired == 1">
                and t.receive_end_date &lt; CURDATE()
            </if>
            <if test="v.applyUserName != null and v.applyUserName != ''">
                and t3.real_name like concat('%', #{v.applyUserName}, '%')
            </if>
            <if test="v.startBackDate != null">
                and date( t6.back_time) &gt;= #{v.startBackDate}
            </if>
            <if test="v.endBackDate != null">
                and  date(t6.back_time) &lt;= #{v.endBackDate}
            </if>
        and t.status in(2,3)
            order by t.create_time desc
        </where>
    </select>

</mapper>
