/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.mapper;

import com.snszyk.simas.receive.dto.DeviceReceiveRecordDto;
import com.snszyk.simas.receive.entity.DeviceReceive;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import com.snszyk.simas.receive.vo.DeviceReceivePageVo;
import com.snszyk.simas.receive.dto.DeviceReceiveDto;
/**
 * 设备领用 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface DeviceReceiveMapper extends BaseMapper<DeviceReceive> {

    IPage<DeviceReceiveDto> pageList(@Param("v") DeviceReceivePageVo v);

    DeviceReceiveDto detail(Long id);

	IPage<DeviceReceiveRecordDto> recordPage(@Param("v") DeviceReceivePageVo v);
}
