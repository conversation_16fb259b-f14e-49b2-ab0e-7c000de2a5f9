/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.simas.receive.entity.DeviceReceiveDetail;
import com.snszyk.simas.receive.vo.DeviceReceiveDetailPageVo;
import com.snszyk.simas.receive.dto.DeviceReceiveDetailDto;
import com.snszyk.simas.receive.mapper.DeviceReceiveDetailMapper;
import com.snszyk.simas.leaseback.service.IDeviceReceiveDetailService;
import org.springframework.stereotype.Service;
import lombok.AllArgsConstructor;
import com.snszyk.core.mp.base.BaseServiceImpl;

import java.util.List;

/**
 * 设备领用 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceReceiveDetailServiceImpl extends BaseServiceImpl<DeviceReceiveDetailMapper, DeviceReceiveDetail> implements IDeviceReceiveDetailService {




    /**
     * 分页查询
     */
    @Override
    public IPage<DeviceReceiveDetailDto> pageList(DeviceReceiveDetailPageVo v) {
        return baseMapper.pageList(v);
    }

    /**
     * 详情
     */
    @Override
    public DeviceReceiveDetailDto detail(Long id) {
        return baseMapper.detail(id);
    }

    @Override
    public List<DeviceReceiveDetailDto> listByReceiveId(Long receiveId) {
		List<DeviceReceiveDetail> list = this.lambdaQuery().eq(DeviceReceiveDetail::getReceiveId, receiveId).list();
		return com.snszyk.core.tool.utils.BeanUtil.copy(list, DeviceReceiveDetailDto.class);


	}

    @Override
    public DeviceReceiveDetailDto detailByDeviceId(Long equipmentId, Integer status) {
		DeviceReceiveDetail one = this.lambdaQuery()
			.eq(BaseEntity::getStatus,status)
			.eq(DeviceReceiveDetail::getDeviceId, equipmentId).last("limit 1").one();
		if (one == null) {
			return null;
		}
		return detail(one.getId());
	}

}
