/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.receive.dto.DeviceReceiveDto;
import com.snszyk.simas.receive.dto.DeviceReceiveRecordDto;
import com.snszyk.simas.receive.service.logic.DeviceReceiveLogicService;
import com.snszyk.simas.receive.vo.DeviceBackVO;
import com.snszyk.simas.receive.vo.DeviceDistributionVO;
import com.snszyk.simas.receive.vo.DeviceReceiveAVo;
import com.snszyk.simas.receive.vo.DeviceReceivePageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备领用 控制器
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@RestController
@AllArgsConstructor
@RequestMapping("receive/devicereceive")
@Api(value = "设备领用", tags = "设备领用接口")
public class DeviceReceiveController extends SzykController {

	private final DeviceReceiveLogicService deviceReceiveLogicService;


	/**
	 * 保存
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "设备领用保存", notes = "DeviceReceiveVo")
	public R<DeviceReceiveDto> save(@RequestBody DeviceReceiveAVo v) {
		DeviceReceiveDto baseCrudDto = deviceReceiveLogicService.saveOrUpdate(v);
		return R.data(baseCrudDto);
	}

	/**
	 * 分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "设备领用分页", notes = "DeviceReceivePageVo")
	public R<IPage<DeviceReceiveDto>> page(DeviceReceivePageVo v) {
		IPage<DeviceReceiveDto> pageQueryResult = deviceReceiveLogicService.pageList(v);
		return R.data(pageQueryResult);
	}

	/**
	 * <AUTHOR>
	 * @Description 设备领用的台账 分页
	 * @Date 2025/3/19
	 * @Param
	 * @return
	 **/
	@GetMapping("/recordPage")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "设备领用台账分页", notes = "两个tab根据status区分")
	public R<IPage<DeviceReceiveRecordDto>> recordPage(DeviceReceivePageVo v) {
		IPage<DeviceReceiveRecordDto> pageQueryResult = deviceReceiveLogicService.recordPage(v);
		return R.data(pageQueryResult);
	}

	/**
	 * 根据ID获取数据
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "设备领用详情", notes = "id")
	public R<DeviceReceiveDto> detail(Long id) {
		DeviceReceiveDto baseCrudDto = deviceReceiveLogicService.detail(id);
		return R.data(baseCrudDto);
	}

//	/**
//	 * 删除
//	 */
//	@PostMapping("/removeByIds")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "设备领用删除", notes = "id")
//	public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
//		List<CommonDeleteResultDto> result = deviceReceiveLogicService.removeByIds(ids);
//		return R.data(result);
//	}

	/**
	 * 设备的分配
	 */
	@PostMapping("/distribution")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "设备的分配", notes = "DeviceDistributionVo")
	public R<Boolean> distribution(@RequestBody DeviceDistributionVO v) {
		Boolean baseCrudDto = deviceReceiveLogicService.distribution(v);
		return R.data(baseCrudDto);
	}

	/*
	 * <AUTHOR>
	 * @Description 设备的归还
	 * @Date 上午11:08 2025/3/19
	 * @Param
	 * @return
	 **/
	@PostMapping("/back")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "设备的归还", notes = "DeviceReceiveDto")
	public R<Boolean> back(@RequestBody DeviceBackVO v) {
		Boolean baseCrudDto = deviceReceiveLogicService.back(v);
		return R.data(baseCrudDto);
	}
}
