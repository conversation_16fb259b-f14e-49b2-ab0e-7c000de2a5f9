///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.receive.controller;
//
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//
//
//import com.snszyk.core.boot.ctrl.SzykController;
//import com.snszyk.simas.common.dto.CommonDeleteResultDto;
//import com.snszyk.simas.receive.service.logic.DeviceReceiveDetailLogicService;
//import com.snszyk.simas.receive.vo.DeviceReceiveDetailAVo;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//import com.snszyk.simas.receive.vo.DeviceReceiveDetailPageVo;
//import com.snszyk.simas.receive.dto.DeviceReceiveDetailDto;
//
//import java.util.List;
//import com.snszyk.core.tool.api.R;
//
///**
// * 设备领用 控制器
// *
// * <AUTHOR>
// * @since 2025-03-18
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("receive/devicereceivedetail")
//@Api(value = "设备领用", tags = "设备领用接口")
//public class DeviceReceiveDetailController extends SzykController {
//
//    private final DeviceReceiveDetailLogicService deviceReceiveDetailLogicService;
//
//
//
//    /**
//     * 保存
//     */
//    @PostMapping("/save")
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "设备领用保存", notes = "DeviceReceiveDetailVo")
//    public R<DeviceReceiveDetailDto> save(@RequestBody DeviceReceiveDetailAVo v) {
//        DeviceReceiveDetailDto baseCrudDto = deviceReceiveDetailLogicService.saveOrUpdate(v);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 分页
//     */
//    @GetMapping("/page")
//    @ApiOperationSupport(order = 2)
//    @ApiOperation(value = "设备领用分页", notes = "DeviceReceiveDetailPageVo")
//    public R<IPage<DeviceReceiveDetailDto>> page(DeviceReceiveDetailPageVo v) {
//        IPage<DeviceReceiveDetailDto> pageQueryResult = deviceReceiveDetailLogicService.pageList(v);
//        return R.data(pageQueryResult);
//    }
//
//    /**
//     * 根据ID获取数据
//     */
//    @GetMapping("/detail")
//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "设备领用详情", notes = "id")
//    public R<DeviceReceiveDetailDto> detail(Long id) {
//        DeviceReceiveDetailDto baseCrudDto = deviceReceiveDetailLogicService.detail(id);
//        return R.data(baseCrudDto);
//    }
//
//    /**
//     * 删除
//     */
//    @PostMapping("/removeByIds")
//    @ApiOperationSupport(order = 4)
//    @ApiOperation(value = "设备领用删除", notes = "id")
//    public R<List<CommonDeleteResultDto>> removeByIds(@RequestBody List<Long> ids) {
//        List<CommonDeleteResultDto> result = deviceReceiveDetailLogicService.removeByIds(ids);
//        return R.data(result);
//    }
//}
