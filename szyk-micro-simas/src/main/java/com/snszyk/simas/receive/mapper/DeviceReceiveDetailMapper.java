/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.mapper;

import com.snszyk.simas.receive.entity.DeviceReceiveDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import com.snszyk.simas.receive.vo.DeviceReceiveDetailPageVo;
import com.snszyk.simas.receive.dto.DeviceReceiveDetailDto;
/**
 * 设备领用 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface DeviceReceiveDetailMapper extends BaseMapper<DeviceReceiveDetail> {

    IPage<DeviceReceiveDetailDto> pageList(@Param("v") DeviceReceiveDetailPageVo v);

    DeviceReceiveDetailDto detail(Long id);

}
