/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.service.logic;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.equipment.feign.IDeviceAccountClient;
import com.snszyk.common.equipment.vo.DeviceAccountReceiveVO;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.common.dto.CommonDeleteResultDto;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.enums.EquipmentStatusEnum;
import com.snszyk.simas.common.enums.MoveSourceEnum;
import com.snszyk.simas.common.service.IDeviceMoveRecordService;
import com.snszyk.simas.common.vo.DeviceMoveRecordVO;
import com.snszyk.simas.leaseback.service.IDeviceReceiveDetailService;
import com.snszyk.simas.leaseback.service.IDeviceReceiveService;
import com.snszyk.simas.receive.dto.DeviceReceiveDetailDto;
import com.snszyk.simas.receive.dto.DeviceReceiveDeviceInfoDTO;
import com.snszyk.simas.receive.dto.DeviceReceiveDto;
import com.snszyk.simas.receive.dto.DeviceReceiveRecordDto;
import com.snszyk.simas.receive.entity.DeviceReceive;
import com.snszyk.simas.receive.entity.DeviceReceiveDetail;
import com.snszyk.simas.receive.enums.DeviceReceiveExpireEnum;
import com.snszyk.simas.receive.enums.DeviceReceiveStatusEnum;
import com.snszyk.simas.receive.vo.DeviceBackVO;
import com.snszyk.simas.receive.vo.DeviceDistributionVO;
import com.snszyk.simas.receive.vo.DeviceReceiveAVo;
import com.snszyk.simas.receive.vo.DeviceReceivePageVo;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备领用 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceReceiveLogicService {

	private final IDeviceReceiveService deviceReceiveService;

	private final IDeviceReceiveDetailService deviceReceiveDetailService;

	private final IDeviceMoveRecordService deviceMoveRecordService;

	private final IDeviceAccountClient deviceAccountClient;

	@Transactional(rollbackFor = Exception.class)
	public DeviceReceiveDto saveOrUpdate(DeviceReceiveAVo v) {
		DeviceReceive copy = BeanUtil.copy(v, DeviceReceive.class);
		if (copy != null) {
//			copy.setBelongDeptId(Long.valueOf(AuthUtil.getDeptId()));
//			copy.setApplyUserId(AuthUtil.getUserId());
			//领用人的所属部门
			Long applyUserId = v.getApplyUserId();
			User user = UserCache.getUser(applyUserId);
			if (user != null) {
				copy.setBelongDeptId(Long.valueOf(user.getDeptId()));
			}
			copy.setApplyTime(LocalDateTime.now());
			copy.setOrderNo(BizCodeUtil.generate("LY"));

		}
		boolean b = deviceReceiveService.saveOrUpdate(copy);
		if (!b) {
			throw new ServiceException("系统异常,保存失败");
		}
		List<Long> deviceIds = v.getDeviceIds();
		if (deviceIds != null && !deviceIds.isEmpty()) {
			for (Long deviceId : deviceIds) {
				DeviceReceiveDetail deviceReceiveDetail = new DeviceReceiveDetail();
				deviceReceiveDetail.setDeviceId(deviceId);
				deviceReceiveDetail.setReceiveId(copy.getId());
				deviceReceiveDetail.setStatus(DeviceReceiveStatusEnum.WAIT.getCode());
				boolean save = deviceReceiveDetailService.save(deviceReceiveDetail);
				if (!save) {
					throw new ServiceException("系统异常,保存失败");
				}
			}
		}
		return detail(copy.getId());
	}

	public IPage<DeviceReceiveDto> pageList(DeviceReceivePageVo v) {
		IPage<DeviceReceiveDto> page = deviceReceiveService.pageList(v);
		return page;
	}

	/**
	 * 根据ID查询分类详情
	 *
	 * @param id 分类的唯一标识符。
	 * @return 包含分类详细信息的DTO（数据传输对象）。
	 * @throws ServiceException 如果分类不存在，则抛出服务异常。
	 */
	public DeviceReceiveDto detail(Long id) {
		// 通过ID查询数据信息
		DeviceReceiveDto dto = deviceReceiveService.detail(id);
		// 检查查询结果，如果数据不存在，则抛出异常
		if (dto == null) {
			throw new ServiceException("数据不存在");
		}
		List<DeviceReceiveDetail> list = deviceReceiveDetailService.lambdaQuery().eq(DeviceReceiveDetail::getReceiveId, id).list();
		if (list != null && !list.isEmpty()) {
			Map<Long, Integer> statusMap = list.stream().collect(Collectors.toMap(DeviceReceiveDetail::getDeviceId, BaseEntity::getStatus));
			List<Long> deviceIds = list.stream().map(DeviceReceiveDetail::getDeviceId).collect(Collectors.toList());
			DeviceAccountVO vo = new DeviceAccountVO();
			vo.setDeviceIds(deviceIds);
			R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(vo);
			if (!listR.isSuccess()) {
				throw new ServiceException("设备台账查询失败");
			}
			//每个设备的状态
			List<DeviceAccountVO> data = listR.getData();
			List<DeviceReceiveDeviceInfoDTO> copy = BeanUtil.copy(data, DeviceReceiveDeviceInfoDTO.class);
			for (DeviceReceiveDeviceInfoDTO deviceReceiveDeviceInfoDTO : copy) {
				deviceReceiveDeviceInfoDTO.setReceiveStatus(statusMap.get(deviceReceiveDeviceInfoDTO.getId()));
				DeviceReceiveStatusEnum statusEnum = DeviceReceiveStatusEnum.getByCode(deviceReceiveDeviceInfoDTO.getReceiveStatus());
				if (statusEnum != null) {
					deviceReceiveDeviceInfoDTO.setReceiveStatusName(statusEnum.getName());
				}
			}
			dto.setDeviceAccountList(copy);
		}
		return dto;
	}

	/**
	 * 删除 如已被引用则不允许删除
	 *
	 * @param ids 要删除的ID列表
	 * @return 删除结果
	 */
	@Transactional(rollbackFor = Exception.class)
	public List<CommonDeleteResultDto> removeByIds(List<Long> ids) {
		List<CommonDeleteResultDto> result = new ArrayList<>();
		for (Long id : ids) {
			CommonDeleteResultDto deleteResultDto = new CommonDeleteResultDto();
			deleteResultDto.setId(id);
			result.add(deleteResultDto);

			DeviceReceive data = deviceReceiveService.getById(id);
			if (data == null) {
				throw new ServiceException("数据不存在");
			}
//			deleteResultDto.setName(data.getName());

			boolean b = deviceReceiveService.removeById(id);
			if (!b) {
				throw new ServiceException("系统异常,删除失败");
			}
			deleteResultDto.setResult(true);
		}
		return result;
	}


	/**
	 * 设备的分配
	 * 提交后，设备台账中的备用状态更新为在用，更新使用部门以及使用人。 20250319
	 * @param v 设备分配参数
	 * @return 是否分配成功
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean distribution(DeviceDistributionVO v) {
		//校验设备的数据是否有重复的
		long count = v.getEquipmentIds().stream().distinct().count();
		if (count != v.getEquipmentIds().size()) {
			throw new ServiceException("设备列表中存在重复的设备");
		}
		//status
		DeviceReceive deviceReceive = deviceReceiveService.getById(v.getReceiveId());
		if (deviceReceive == null) {
			throw new ServiceException("数据不存在");
		}
		//设备是否都是备用的
		if (!v.getEquipmentIds().isEmpty()) {
			DeviceAccountVO param = new DeviceAccountVO();
			param.setDeviceIds(v.getEquipmentIds());

			R<List<DeviceAccountVO>> listR = deviceAccountClient.deviceListByParams(param);
			if (!listR.isSuccess()) {
				throw new ServiceException("设备台账查询失败");
			}
			List<DeviceAccountVO> data = listR.getData();
			data.stream().filter(e -> !Objects.equals(e.getStatus(), EquipmentStatusEnum.IDLE.getCode())).findFirst().ifPresent(e -> {
				throw new ServiceException("非备用状态的设备不可领用");
			});
		}
		//updateStauts
		//如果分配的设备的数量为0 则状态为已终止
		if (v.getEquipmentIds().isEmpty()) {
			deviceReceive.setStatus(DeviceReceiveStatusEnum.STOP.getCode());
		} else {
			deviceReceive.setStatus(DeviceReceiveStatusEnum.DISTRIBUTE.getCode());
		}
		//update
		deviceReceiveService.updateById(deviceReceive);
		//删除已有的设备
		deviceReceiveDetailService.lambdaUpdate().eq(DeviceReceiveDetail::getReceiveId, v.getReceiveId()).remove();
		//添加新的设备
		List<Long> equipmentIds = v.getEquipmentIds();
		if (equipmentIds != null && !equipmentIds.isEmpty()) {
			for (Long equipmentId : equipmentIds) {
				DeviceReceiveDetail deviceReceiveDetail = new DeviceReceiveDetail();
				deviceReceiveDetail.setDeviceId(equipmentId);
				deviceReceiveDetail.setReceiveId(v.getReceiveId());
				deviceReceiveDetail.setStatus(DeviceReceiveStatusEnum.DISTRIBUTE.getCode());
				boolean save = deviceReceiveDetailService.save(deviceReceiveDetail);
				if (!save) {
					throw new ServiceException("系统异常,保存失败");
				}
				// 设备移动记录
				DeviceMoveRecordVO deviceMoveRecordVO = new DeviceMoveRecordVO(Collections.singletonList(equipmentId),
					null, deviceReceive.getLocationId(),
					deviceReceive.getBelongDeptId(), deviceReceive.getApplyUserId(), MoveSourceEnum.DEVICE_RECEIVE);
				deviceMoveRecordVO.setBizNo(deviceReceive.getOrderNo())
					.setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
				deviceMoveRecordService.saveRecords(deviceMoveRecordVO);
				//提交后，设备台账中的备用状态更新为在用，更新使用部门以及使用人
				//client
				DeviceAccountReceiveVO vo = new DeviceAccountReceiveVO();
				vo.setId(equipmentId);
				vo.setUserId(deviceReceive.getApplyUserId());
				vo.setUseDept(deviceReceive.getBelongDeptId());
				vo.setLocationId(deviceReceive.getLocationId());
				vo.setStatus(String.valueOf(EquipmentStatusEnum.IN_USE.getCode()));
				R<Boolean> booleanR = deviceAccountClient.receiveDevice(vo);
				if (!booleanR.isSuccess()) {
					throw new ServiceException("设备台账更新失败");
				}
			}
		}
		return true;
	}

	/**
	 * <AUTHOR>
	 * @Description 台账
	 * @Date 上午10:28 2025/3/19
	 * @Param [v]
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.simas.receive.dto.DeviceReceiveRecordDto>
	 **/
	public IPage<DeviceReceiveRecordDto> recordPage(DeviceReceivePageVo v) {
		IPage<DeviceReceiveRecordDto> page = deviceReceiveService.recordPage(v);
		LocalDateTime now = LocalDateTime.now();
		//是否已经到期
		Integer isExpired = v.getIsExpired();
		List<DeviceReceiveRecordDto> records = page.getRecords();
		//按查询的参数
		if (isExpired != null) {
			records.forEach(e -> {
				e.setIsExpired(isExpired);
				e.setExpiredName(DeviceReceiveExpireEnum.getByCode(isExpired).getName());
			});
		} else {
			for (DeviceReceiveRecordDto e : records) {
				LocalDate receiveEndDate = e.getReceiveEndDate();
				LocalDateTime receiveEndTime = receiveEndDate.atTime(23, 59, 59);
				//结束的申请日期的对比
				//超期的: 超过当前日期但是没有归还的  or 归还日期>申请结束日期的
				boolean isOverdue = (e.getBackTime() == null && receiveEndTime.isBefore(now)) || (e.getBackTime() != null
					&& e.getBackTime().isAfter(receiveEndTime));
				if (isOverdue) {
					e.setIsExpired(DeviceReceiveExpireEnum.EXPIRED.getCode());
					e.setExpiredName(DeviceReceiveExpireEnum.EXPIRED.getName());
				} else {
					e.setIsExpired(DeviceReceiveExpireEnum.UNEXPIRED.getCode());
					e.setExpiredName(DeviceReceiveExpireEnum.UNEXPIRED.getName());
				}
			}

		}
		return page;
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean back(DeviceBackVO v) {
		List<Long> receiveIds = v.getReceiveIds();
		List<Long> equipmentIds = v.getEquipmentIds();
		//如果是按order归还的
		if (receiveIds != null && !receiveIds.isEmpty()) {
			for (Long receiveId : receiveIds) {
				DeviceReceive deviceReceive = deviceReceiveService.getById(receiveId);
				if (deviceReceive != null) {
					// 设备位置还原
					DeviceMoveRecord deviceMoveRecord = deviceMoveRecordService.getOne(Wrappers.<DeviceMoveRecord>query().lambda()
						.eq(DeviceMoveRecord::getSource, MoveSourceEnum.DEVICE_RECEIVE.getCode())
						.eq(DeviceMoveRecord::getBizNo, deviceReceive.getOrderNo())
						.orderByAsc(DeviceMoveRecord::getOperateTime).last("limit 1"));
					List<DeviceReceiveDetailDto> deviceReceiveDetailDtos = deviceReceiveDetailService.listByReceiveId(receiveId);
					for (DeviceReceiveDetailDto deviceReceiveDetailDto : deviceReceiveDetailDtos) {
						deviceReceiveDetailDto.setLocationId(deviceMoveRecord.getOriginalLocation())
							.setOrderNo(deviceReceive.getOrderNo());
						//处理设备的详情
						SpringUtil.getBean(this.getClass()).backHandleDetail(deviceReceiveDetailDto);
					}
					SpringUtil.getBean(this.getClass()).changeOrderBackStatus(receiveId);
				}
			}
		}
		//如果是按设备归还的
		if (equipmentIds != null && !equipmentIds.isEmpty()) {
			//对应的orderid
			Set<Long> orderIds = new HashSet<>();
			for (Long equipmentId : equipmentIds) {
				DeviceReceiveDetailDto detail = deviceReceiveDetailService.detailByDeviceId(equipmentId, DeviceReceiveStatusEnum.DISTRIBUTE.getCode());
				if (detail == null) {
					throw new ServiceException("领用记录不存在");
				}
				Long receiveId = detail.getReceiveId();
				DeviceReceive deviceReceive = deviceReceiveService.getById(receiveId);
				orderIds.add(receiveId);
				// 设备位置还原
				DeviceMoveRecord deviceMoveRecord = deviceMoveRecordService.getOne(Wrappers.<DeviceMoveRecord>query().lambda()
					.eq(DeviceMoveRecord::getDeviceId, equipmentId)
					.eq(DeviceMoveRecord::getBizNo, deviceReceive.getOrderNo())
					.eq(DeviceMoveRecord::getSource, MoveSourceEnum.DEVICE_RECEIVE.getCode()));
				detail.setLocationId(deviceMoveRecord.getOriginalLocation()).setOrderNo(deviceReceive.getOrderNo());
				backHandleDetail(detail);
			}
			for (Long orderId : orderIds) {
				List<DeviceReceiveDetailDto> deviceReceiveDetailDtos = deviceReceiveDetailService.listByReceiveId(orderId);
				//判断是否全部
				if (deviceReceiveDetailDtos.size() == deviceReceiveDetailDtos.stream().filter(e -> Objects.equals(e.getStatus(), DeviceReceiveStatusEnum.BACK.getCode())).count()) {
					//order的变更
					changeOrderBackStatus(orderId);
				}
			}
		}
		return true;
	}

	/**
	 * <AUTHOR>
	 * @Description 归还的order的处理
	 * @Date 上午11:32 2025/3/19
	 * @Param [receiveId]
	 * @return void
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void changeOrderBackStatus(Long receiveId) {
		//order的变更
		boolean update = deviceReceiveService.lambdaUpdate().eq(DeviceReceive::getId, receiveId)
			.set(DeviceReceive::getBackTime, LocalDateTime.now())
			.set(DeviceReceive::getStatus, DeviceReceiveStatusEnum.BACK.getCode()).update();
		if (!update) {
			throw new ServiceException("更新失败");
		}
	}

	/*
	 * <AUTHOR>
	 * @Description 归还的detail的处理
	 * @Date 上午11:28 2025/3/19
	 * @Param [deviceReceiveDetailDto]
	 * @return void
	 **/
	@Transactional(rollbackFor = Exception.class)
	public void backHandleDetail(DeviceReceiveDetailDto deviceReceiveDetailDto) {
		DeviceAccountReceiveVO vo = new DeviceAccountReceiveVO();
		vo.setId(deviceReceiveDetailDto.getDeviceId());
		vo.setStatus(String.valueOf(EquipmentStatusEnum.IDLE.getCode()));
		vo.setUserId(null);
		vo.setUseDept(null);
		// 设备位置还原
		if(Func.isNotEmpty(deviceReceiveDetailDto.getLocationId())){
			vo.setLocationId(deviceReceiveDetailDto.getLocationId());
		} else {
			vo.setLocationId(null);
		}
		// 设备移动记录
		DeviceMoveRecordVO deviceMoveRecordVO
			= new DeviceMoveRecordVO(Collections.singletonList(deviceReceiveDetailDto.getDeviceId()),
			null, deviceReceiveDetailDto.getLocationId(),
			vo.getUseDept(), vo.getUserId(), MoveSourceEnum.DEVICE_BACK);
		deviceMoveRecordVO.setBizNo(deviceReceiveDetailDto.getOrderNo())
			.setOperateUser(AuthUtil.getUserId()).setOperateTime(DateUtil.now());
		deviceMoveRecordService.saveRecords(deviceMoveRecordVO);
		//设备的变更
		R<Boolean> booleanR = deviceAccountClient.receiveDevice(vo);
		if (!booleanR.isSuccess()) {
			throw new ServiceException("设备台账更新失败");
		}
		//detail的变更
		boolean update = deviceReceiveDetailService.lambdaUpdate().eq(DeviceReceiveDetail::getId, deviceReceiveDetailDto.getId())
			.set(DeviceReceiveDetail::getBackTime, LocalDateTime.now())
			.set(DeviceReceiveDetail::getStatus, DeviceReceiveStatusEnum.BACK.getCode()).update();
		if (!update) {
			throw new ServiceException("领用记录更新失败");
		}
	}

}
