/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.simas.leaseback.service.IDeviceReceiveService;
import com.snszyk.simas.receive.dto.DeviceReceiveDto;
import com.snszyk.simas.receive.dto.DeviceReceiveRecordDto;
import com.snszyk.simas.receive.entity.DeviceReceive;
import com.snszyk.simas.receive.mapper.DeviceReceiveMapper;
import com.snszyk.simas.receive.vo.DeviceReceivePageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 设备领用 服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@AllArgsConstructor
@Service
public class DeviceReceiveServiceImpl extends BaseServiceImpl<DeviceReceiveMapper, DeviceReceive> implements IDeviceReceiveService {

	/**
	 * 名称校验
	 */
	@Override
	public void checkName(Long id, String name) {
//        Integer count = lambdaQuery().eq(DeviceReceive::getName, name).ne(id != null, DeviceReceive::getId, id).count();
//        if (count > 0) {
//            throw new ServiceException("名称已存在");
//        }
	}

	/**
	 * 分页查询
	 */
	@Override
	public IPage<DeviceReceiveDto> pageList(DeviceReceivePageVo v) {
		return baseMapper.pageList(v);
	}

	/**
	 * 详情
	 */
	@Override
	public DeviceReceiveDto detail(Long id) {
		return baseMapper.detail(id);
	}

	@Override
	public IPage<DeviceReceiveRecordDto> recordPage(DeviceReceivePageVo v) {
		return baseMapper.recordPage(v);
	}

}
