/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.sealpoint.entity.SealPointStatusRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * 密封点 服务类
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
public interface ISealPointStatusRecordService extends BaseService<SealPointStatusRecord> {

	List<SealPointStatusRecord> list(List<Long> pointId, LocalDate startDate, LocalDate endDate);
}
