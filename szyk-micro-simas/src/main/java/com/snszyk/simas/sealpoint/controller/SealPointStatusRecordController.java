/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.common.dto.KeyValueDTO;
import com.snszyk.simas.common.enums.TimeTypeEnumV2;
import com.snszyk.simas.sealpoint.service.logic.SealPointStatusRecordLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 密封点 控制器
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sealpointstatusrecord")
@Api(value = "密封点状态", tags = "密封点状态接口")
@Validated
public class SealPointStatusRecordController {

	private final SealPointStatusRecordLogicService sealPointStatusRecordLogicService;

	/**
	 * 列表
	 */
	@GetMapping("leakage-percentage")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "泄漏率", notes = "")
	@ApiImplicitParam(name = "timeType", value = "时间类型", paramType = "query", dataType = "TimeTypeEnumV2", required = true)
	public R<List<KeyValueDTO>> listLeakagePercentage(@NotNull TimeTypeEnumV2 timeType) {
		return R.data(sealPointStatusRecordLogicService.listLeakagePercentage(timeType));
	}

}
