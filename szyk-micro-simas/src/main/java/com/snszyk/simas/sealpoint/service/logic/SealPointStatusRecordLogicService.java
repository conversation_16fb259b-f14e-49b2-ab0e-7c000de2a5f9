/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service.logic;

import com.google.common.collect.Lists;
import com.snszyk.common.utils.DateUtils;
import com.snszyk.common.utils.ListUtil;
import com.snszyk.common.utils.StringPool;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.dto.KeyValueDTO;
import com.snszyk.simas.common.enums.TimeTypeEnumV2;
import com.snszyk.simas.sealpoint.entity.SealPoint;
import com.snszyk.simas.sealpoint.entity.SealPointStatusRecord;
import com.snszyk.simas.sealpoint.enums.SealPointStatusEnum;
import com.snszyk.simas.sealpoint.service.ISealPointService;
import com.snszyk.simas.sealpoint.service.ISealPointStatusRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 密封点 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@AllArgsConstructor
@Service
public class SealPointStatusRecordLogicService {

	private final ISealPointStatusRecordService sealPointStatusRecordService;
	private final ISealPointService sealPointService;

	/**
	 * 密封点泄漏率
	 *
	 * @param timeType
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<KeyValueDTO> listLeakagePercentage(TimeTypeEnumV2 timeType) {
		// 初始化返回值
		List<KeyValueDTO> keyValueDTOList = KeyValueDTO.initKeyValueDTOList(timeType);
		// 初始化密封点状态记录
		List<SealPointStatusRecord> statusRecordList = Lists.newArrayList();
		// 查询今日的密封点状态
		final List<SealPoint> toDaySealPointList = sealPointService.list();
		if (ObjectUtil.isEmpty(toDaySealPointList)) {
			return keyValueDTOList;
		}
		// 添加今日的密封点状态
		statusRecordList.addAll(this.convertToStatusRecord(toDaySealPointList));
		// 查询时间范围内今天之外的密封点状态记录
		statusRecordList.addAll(sealPointStatusRecordService.list(ListUtil.map(toDaySealPointList, SealPoint::getId), timeType.computeStartDate(), DateUtils.calculateYesterday(LocalDate.now())));
		if (ObjectUtil.isEmpty(statusRecordList)) {
			return keyValueDTOList;
		}
		// 填充周期字段
		fillCycle(timeType, statusRecordList);
		// 获取周期->百分比Map，key-周期，value-百分比
		Map<String, BigDecimal> cycleToPercentageMap = this.getCycleToPercentageMap(statusRecordList);
		// 循环设置
		keyValueDTOList.forEach(dto -> {
			Optional.ofNullable(cycleToPercentageMap.get(dto.getKey()))
				.ifPresent(dto::setValue);
		});
		return keyValueDTOList;
	}

	/***
	 * 获取周期->百分比Map，key-周期，value-百分比
	 * @param statusRecordList
	 * @return
	 */
	private Map<String, BigDecimal> getCycleToPercentageMap(List<SealPointStatusRecord> statusRecordList) {
		if (ObjectUtil.isEmpty(statusRecordList)) {
			return Collections.emptyMap();
		}
		return statusRecordList.stream()
			.collect(Collectors.groupingBy(
				SealPointStatusRecord::getCycle,
				Collectors.collectingAndThen(
					Collectors.toList(),
					records -> {
						// 总数量
						long total = records.size();
						// 泄漏数量
						final long leakCount = records.stream()
							.filter(record -> SealPointStatusEnum.LEAK.getCode().equals(record.getStatus()))
							.count();
						return new BigDecimal(leakCount)
							.multiply(new BigDecimal(StringPool.HUNDRED))
							.divide(new BigDecimal(total), 2, BigDecimal.ROUND_HALF_UP);
					}
				)
			));
	}

	/**
	 * 填充周期字段
	 *
	 * @param timeType
	 * @param statusRecordList
	 */
	private void fillCycle(TimeTypeEnumV2 timeType, List<SealPointStatusRecord> statusRecordList) {
		if (ObjectUtil.isEmpty(timeType)) {
			throw new IllegalArgumentException("时间类型不能为空");
		}
		if (ObjectUtil.isEmpty(statusRecordList)) {
			return;
		}
		switch (timeType) {
			// 近三十天周期时间等于日期
			case THIRTY_DAYS:
				statusRecordList.forEach(statusRecord -> {
					statusRecord.setCycle(statusRecord.getRecordDate().format(DateTimeFormatter.ofPattern(KeyValueDTO.DAY_FORMAT)));
				});
				break;
			case MONTH:
				statusRecordList.forEach(statusRecord -> {
					statusRecord.setCycle(YearMonth.from(statusRecord.getRecordDate()).format(DateTimeFormatter.ofPattern(KeyValueDTO.MONTH_FORMAT)));
				});
				break;
			case QUARTER:
				statusRecordList.forEach(statusRecord -> {
					// 获取当前日期所属季度
					Integer quarter = DateUtils.getFormattedQuarter(statusRecord.getRecordDate());
					// 当前日期所属年度
					final int year = statusRecord.getRecordDate().getYear();
					final String quarterStr = String.format(KeyValueDTO.QUARTER_FORMAT, year, quarter);
					statusRecord.setCycle(quarterStr);
				});
				break;
			case YEAR:
				statusRecordList.forEach(statusRecord -> {
					statusRecord.setCycle(statusRecord.getRecordDate().format(DateTimeFormatter.ofPattern(KeyValueDTO.YEAR_FORMAT)));
				});
				break;
			default:
				throw new IllegalArgumentException("参数异常");
		}
	}

	/**
	 * 转为状态记录
	 *
	 * @param toDaySealPointList
	 * @return
	 */
	private List<SealPointStatusRecord> convertToStatusRecord(List<SealPoint> toDaySealPointList) {
		if (ObjectUtil.isEmpty(toDaySealPointList)) {
			return Collections.emptyList();
		}
		final LocalDate localDate = LocalDate.now();
		return toDaySealPointList.stream()
			.map(sealPoint -> sealPoint.convertToSealPointStatusRecord(localDate))
			.collect(Collectors.toList());
	}
}
