/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.simas.sealpoint.service.logic.SealPointLogicService;
import com.snszyk.simas.sealpoint.dto.SealPointDto;
import com.snszyk.simas.sealpoint.vo.SealPointBatchUpdateVo;
import com.snszyk.simas.sealpoint.vo.SealPointPageVo;
import com.snszyk.simas.sealpoint.vo.SealPointVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * 密封点 控制器
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@RestController
@AllArgsConstructor
@RequestMapping("sealpoint")
@Api(value = "密封点", tags = "密封点接口")
@ApiSupport(order = 99, author = "zzp")
@Validated
public class SealPointController {

	private final SealPointLogicService sealPointLogicService;

	@PostMapping
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "新增或修改", notes = "")
	public R<Boolean> saveOrUpdate(@RequestBody @Validated SealPointVo v) {
		return R.status(sealPointLogicService.saveOrUpdate(v));
	}

	/**
	 * 分页查询
	 */
	@GetMapping(value = "/page")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "")
	public R<IPage<SealPointDto>> page(SealPointPageVo v) {
		return R.data(sealPointLogicService.page(v));
	}

	/**
	 * 删除
	 */
	@DeleteMapping(value = "/{id}")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "删除", notes = "")
	public R<Boolean> remove(@PathVariable("id") @NotNull Long id) {
		return R.status(sealPointLogicService.remove(id));
	}

	/**
	 * 批量更新
	 */
	@PutMapping("/batch")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "批量更新", notes = "")
	public R<Boolean> updateBatch(@RequestBody @NotEmpty @Validated List<SealPointBatchUpdateVo> vos) {
		return R.status(sealPointLogicService.updateBatch(vos));
	}

	/**
	 * 详情
	 */
	@GetMapping("/{id}")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "详情", notes = "")
	public R<SealPointDto> detail(@PathVariable("id") @NotNull Long id) {
		return R.data(sealPointLogicService.getById(id));
	}

	/**
	 * 导出
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "导出", notes = "")
	public void export(SealPointPageVo v, HttpServletResponse response) throws UnsupportedEncodingException {
		sealPointLogicService.export(v, response);
	}

	/**
	 * 密封点泄漏率
	 */
	@GetMapping("/leakage-percentage")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "泄漏率", notes = "")
	public R<String> leakagePercentage() {
		return R.data(sealPointLogicService.getLeakagePercentage());
	}

}
