/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service.impl;

import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.sealpoint.entity.SealPointStatusRecord;
import com.snszyk.simas.sealpoint.mapper.SealPointStatusRecordMapper;
import com.snszyk.simas.sealpoint.service.ISealPointStatusRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 密封点 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@AllArgsConstructor
@Service
public class SealPointStatusRecordServiceImpl extends BaseServiceImpl<SealPointStatusRecordMapper, SealPointStatusRecord> implements ISealPointStatusRecordService {

	@Override
	public List<SealPointStatusRecord> list(List<Long> pointIds, LocalDate startDate, LocalDate endDate) {
		return this.lambdaQuery()
			.in(ObjectUtil.isNotEmpty(pointIds), SealPointStatusRecord::getPointId, pointIds)
			.between(ObjectUtil.isNotEmpty(startDate) && ObjectUtil.isNotEmpty(endDate), SealPointStatusRecord::getRecordDate, startDate, endDate)
			.list();
	}
}
