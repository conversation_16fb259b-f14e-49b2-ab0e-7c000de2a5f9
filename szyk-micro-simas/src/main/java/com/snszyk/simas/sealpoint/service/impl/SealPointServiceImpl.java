/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.sealpoint.entity.SealPoint;
import com.snszyk.simas.sealpoint.mapper.SealPointMapper;
import com.snszyk.simas.sealpoint.service.ISealPointService;
import com.snszyk.simas.sealpoint.vo.SealPointPageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 密封点 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@AllArgsConstructor
@Service
public class SealPointServiceImpl extends BaseServiceImpl<SealPointMapper, SealPoint> implements ISealPointService {


	@Override
	public IPage<SealPoint> pageList(SealPointPageVo v) {
		return this.lambdaQuery()
			.like(ObjectUtil.isNotEmpty(v.getName()), SealPoint::getName, v.getName())
			.eq(ObjectUtil.isNotEmpty(v.getType()), SealPoint::getType, v.getType())
			.orderByDesc(SealPoint::getCreateTime)
			.page(new Page<>(v.getCurrent(), v.getSize()));
	}
}
