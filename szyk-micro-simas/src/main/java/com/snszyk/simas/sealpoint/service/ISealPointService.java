/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.sealpoint.entity.SealPoint;
import com.snszyk.simas.sealpoint.vo.SealPointPageVo;

/**
 * 密封点 服务类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
public interface ISealPointService extends BaseService<SealPoint> {

	IPage<SealPoint> pageList(SealPointPageVo v);
}
