package com.snszyk.simas.sealpoint.schedule;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.tenant.annotation.TenantIgnore;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.sealpoint.entity.SealPoint;
import com.snszyk.simas.sealpoint.entity.SealPointStatusRecord;
import com.snszyk.simas.sealpoint.mapper.SealPointMapper;
import com.snszyk.simas.sealpoint.service.ISealPointStatusRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName: EquipmentStatusRecordSchedule
 * Package: com.snszyk.simas.schedule
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/2/11 10:26
 */
@Slf4j
@AllArgsConstructor
@Component
public class SealPointStatusRecordJobHandler {

	private final ISealPointStatusRecordService sealPointStatusRecordService;
	private final SealPointMapper sealPointMapper;

	/**
	 * 每天23:35
	 * 保存设备状态记录
	 */
	@TenantIgnore
	@XxlJob("saveSealPointStatusRecordJobHandler")
	public ReturnT<String> saveRecord(String param) {
		XxlJobLogger.log("################开始保存密封点状态记录-START-################");
		final LocalDate localDate = LocalDate.now();

		final int pageSize = 500;
		int current = 1;

		while (true) {
			// 分页查询设备列表
			Page<SealPoint> sealPointPage = sealPointMapper.selectPage(new Page<>(current, pageSize),
				Wrappers.lambdaQuery(SealPoint.class));

			List<SealPoint> sealPointList = sealPointPage.getRecords();
			if (ObjectUtil.isEmpty(sealPointList)) {
				break;
			}
			// 转换并保存当前页数据
			List<SealPointStatusRecord> recordList = sealPointList.stream()
				.map(sealPoint -> sealPoint.convertToSealPointStatusRecord(localDate))
				.collect(Collectors.toList());

			if (ObjectUtil.isNotEmpty(recordList)) {
				sealPointStatusRecordService.saveBatch(recordList);
			}
			// 判断是否还有下一页
			if (sealPointPage.hasNext()) {
				current++;
			} else {
				break;
			}
		}
		XxlJobLogger.log("################保存密封点记录完成################");
		return ReturnT.SUCCESS;
	}
}
