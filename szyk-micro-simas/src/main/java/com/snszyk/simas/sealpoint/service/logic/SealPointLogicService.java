/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.service.logic;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.utils.CustomExcelUtil;
import com.snszyk.common.utils.StringPool;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.sealpoint.dto.SealPointDto;
import com.snszyk.simas.sealpoint.entity.SealPoint;
import com.snszyk.simas.sealpoint.enums.SealPointStatusEnum;
import com.snszyk.simas.sealpoint.enums.SealPointTypeEnum;
import com.snszyk.simas.common.excel.SealPointExcel;
import com.snszyk.simas.sealpoint.service.ISealPointService;
import com.snszyk.simas.sealpoint.vo.SealPointBatchUpdateVo;
import com.snszyk.simas.sealpoint.vo.SealPointPageVo;
import com.snszyk.simas.sealpoint.vo.SealPointVo;
import com.snszyk.user.cache.UserCache;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 密封点 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@AllArgsConstructor
@Service
public class SealPointLogicService {

	private final ISealPointService sealPointService;

	/**
	 * 保存或修改
	 *
	 * @param v
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean saveOrUpdate(SealPointVo v) {
		return sealPointService.saveOrUpdate(BeanUtil.copy(v, SealPoint.class));
	}

	/**
	 * 分页查询
	 *
	 * @param v
	 * @return
	 */
	@Transactional(readOnly = true)
	public IPage<SealPointDto> page(SealPointPageVo v) {
		IPage<SealPoint> page = sealPointService.pageList(v);
		if (ObjectUtil.isEmpty(page) || ObjectUtil.isEmpty(page.getRecords())) {
			return v;
		}
		return page.convert(e -> {
			SealPointDto dto = BeanUtil.copy(e, SealPointDto.class);
			dto.setTypeName(SealPointTypeEnum.getMessage(dto.getType()));
			dto.setStatusName(SealPointStatusEnum.getMessage(dto.getStatus()));
			Optional.ofNullable(UserCache.getUser(e.getUpdateUser()))
				.ifPresent(user -> dto.setUpdateUserName(user.getName()));
			return dto;
		});
	}

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean remove(Long id) {
		return sealPointService.removeById(id);
	}

	/**
	 * 批量更新
	 *
	 * @param voList
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public Boolean updateBatch(List<SealPointBatchUpdateVo> voList) {
		return sealPointService.updateBatchById(BeanUtil.copy(voList, SealPoint.class));
	}

	/**
	 * 导出
	 *
	 * @param v
	 * @param response
	 */
	public void export(SealPointPageVo v, HttpServletResponse response) throws UnsupportedEncodingException {
		CustomExcelUtil.setResponse("密封点泄漏率" + com.snszyk.core.tool.utils.DateUtil.time(), response);
		// 查询密封点泄漏率
		String leakagePercentage = this.getLeakagePercentage();
		v.setSize(-1L);
		final IPage<SealPointDto> page = this.page(v);
		List<SealPointExcel> list = new ArrayList<>();
		if (ObjectUtil.isNotEmpty(page) && ObjectUtil.isNotEmpty(page.getRecords())) {
			list = page.getRecords()
				.stream()
				.map(d -> BeanUtil.copy(d, SealPointExcel.class))
				.collect(Collectors.toList());
		}
		ClassPathResource pathResource = new ClassPathResource("/template/leakagePercentage.xlsx");
		try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(pathResource.getInputStream()).build()) {
			WriteSheet writeSheet = EasyExcel.writerSheet().build();

			FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
			Map<String, Object> map = MapUtils.newHashMap();
			map.put("leakagePercentage", leakagePercentage);
			excelWriter.fill(map, writeSheet);
			excelWriter.fill(list, fillConfig, writeSheet);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		// ExcelUtil.export(response, "密封点列表" + com.snszyk.core.tool.utils.DateUtil.time(), "密封点", list, SealPointExcel.class);
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	@Transactional(readOnly = true)
	public SealPointDto getById(Long id) {
		return Optional.ofNullable(sealPointService.getById(id))
			.map(e -> {
				SealPointDto pointDto = BeanUtil.copy(e, SealPointDto.class);
				pointDto.setStatusName(SealPointStatusEnum.getMessage(e.getStatus()));
				return pointDto;
			}).orElseThrow(() -> new RuntimeException("数据不存在"));
	}

	/**
	 * 泄漏率
	 *
	 * @return
	 */
	public String getLeakagePercentage() {
		// 获取所有的密封点
		List<SealPoint> sealPointList = sealPointService.list();
		if (ObjectUtil.isEmpty(sealPointList)) {
			return StringPool.ZERO_PERCENT;
		}
		// 密封点总数
		BigDecimal total = new BigDecimal(sealPointList.size());
		// 查询泄漏点数量
		BigDecimal leakageCount = new BigDecimal(sealPointList.stream()
			.filter(e -> e.getStatus()
				.equals(SealPointStatusEnum.LEAK.getCode()))
			.count());
		return leakageCount.divide(total, 4, RoundingMode.HALF_UP)
			.multiply(new BigDecimal(StringPool.HUNDRED))
			.setScale(2, RoundingMode.HALF_UP).toPlainString() + StringPool.PERCENT;
	}
}
