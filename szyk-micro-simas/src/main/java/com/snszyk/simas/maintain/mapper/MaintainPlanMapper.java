/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
import com.snszyk.simas.maintain.entity.MaintainPlan;
import com.snszyk.simas.maintain.vo.MaintainPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备保养计划表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MaintainPlanMapper extends BaseMapper<MaintainPlan> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainPlan
	 * @return
	 */
	List<MaintainPlan> page(IPage page, @Param("maintainPlan") MaintainPlanVO maintainPlan);

	/**
	 * 查询当天点检计划
	 *
	 * @param currentDate yyyy-MM-dd
	 * @return
	 */
	List<MaintainPlanDTO> getTheDayPlans(@Param("currentDate") Date currentDate);

}
