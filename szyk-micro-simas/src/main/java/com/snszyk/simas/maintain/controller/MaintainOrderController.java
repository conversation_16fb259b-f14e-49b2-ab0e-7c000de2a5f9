 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.secure.SzykUser;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.MaintainOrderExcel;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.service.ITimeoutRemindSetService;
 import com.snszyk.simas.common.vo.EquipmentMaintainVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.common.vo.TimeoutRemindSetVO;
 import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
 import com.snszyk.simas.maintain.service.IMaintainOrderService;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;
 import java.util.Objects;

 /**
  * 设备保养工单表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/maintain-order")
 @Api(value = "设备保养工单表", tags = "设备保养工单表接口")
 public class MaintainOrderController extends SzykController {

	 private final IMaintainOrderService maintainOrderService;
	 private final ITimeoutRemindSetService timeoutRemindSetService;
	 private final IOperateLogService operateLogService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入no")
	 public R<MaintainOrderDTO> detail(String no) {
		 MaintainOrderDTO detail = maintainOrderService.detail(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.MAINTAIN_ORDER, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 自定义分页 设备保养工单表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "keywords", value = "关键字", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "orderName", value = "名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "equipmentCode", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "equipmentSn", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "onlyQueryExecuteUser", value = "执行人id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "分页", notes = "传入maintainOrder")
	 public R<IPage<MaintainOrderDTO>> page(@ApiIgnore MaintainOrderVO maintainOrder, Query query) {
		 OperateLogVO operateLog = new OperateLogVO(null, SystemModuleEnum.MAINTAIN_ORDER, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(maintainOrderService.page(Condition.getPage(query), maintainOrder));
	 }

	 /**
	  * 提交保养 设备保养工单表
	  */
	 @PostMapping("/maintain")
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "提交保养", notes = "传入equipmentMaintain")
	 public R maintain(@Valid @RequestBody EquipmentMaintainVO equipmentMaintain) {
		 return R.status(maintainOrderService.maintain(equipmentMaintain));
	 }

	 /**
	  * 审核确认 设备保养工单表
	  */
	 @PostMapping("/confirm")
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "审核确认", notes = "MaintainOrderVO，驳回：status=6")
	 public R confirm(@RequestBody MaintainOrderVO maintainOrder) {
		 return R.status(maintainOrderService.confirm(maintainOrder));
	 }

	 /**
	  * 导出 设备保养计划表
	  */
	 @GetMapping("/export-order")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "no", value = "编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "orderName", value = "名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "startDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "endDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "导出", notes = "传入maintainPlan")
	 public void exportOrder(@ApiIgnore MaintainOrderVO maintainOrder, HttpServletResponse response) {
		 List<MaintainOrderExcel> list = maintainOrderService.exportOrder(maintainOrder);
		 ExcelUtil.export(response, "保养工单列表" + DateUtil.time(), "保养工单", list, MaintainOrderExcel.class);
	 }

	 /**
	  * 超时提醒时间详情
	  */
	 @GetMapping("/timeoutRemindDetail")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "超时提醒时间详情", notes = "传入bizType")
	 public R<TimeoutRemindSetVO> timeoutRemindDetail(@ApiParam(value = "业务类型", required = true) @RequestParam String bizType
		 , SzykUser szykUser) {
		 TimeoutRemindSet set = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getBizType, bizType).eq(TimeoutRemindSet::getUserId, szykUser.getUserId()));
		 if (Func.isNotEmpty(set)) {
			 return R.data(Objects.requireNonNull(BeanUtil.copy(set, TimeoutRemindSetVO.class)));
		 }
		 return R.data(null);
	 }

	 /**
	  * 设置超时提醒时间
	  */
	 @PostMapping("/setTimeoutRemind")
	 @ApiOperationSupport(order = 7)
	 @ApiOperation(value = "设置超时提醒时间", notes = "传入equipment")
	 public R submit(@Valid @RequestBody TimeoutRemindSetVO timeoutRemindSet) {
		 return R.status(timeoutRemindSetService.submit(timeoutRemindSet));
	 }

	 /**
	  * 即将超时分页 设备保养工单表
	  */
	 @GetMapping("/timeoutPage")
	 @ApiOperationSupport(order = 8)
	 @ApiOperation(value = "即将超时分页", notes = "传入maintainOrder")
	 public R<IPage<MaintainOrderDTO>> timeoutPage(@ApiIgnore MaintainOrderVO maintainOrder, Query query) {
		 return R.data(maintainOrderService.timeoutPage(Condition.getPage(query), maintainOrder));
	 }

	 /**
	  * 超时说明 设备保养工单表
	  */
	 @PostMapping("/timeoutExplain")
	 @ApiOperationSupport(order = 9)
	 @ApiOperation(value = "超时说明", notes = "传入maintainOrder")
	 public R timeoutExplain(@Valid @RequestBody MaintainOrderVO maintainOrder) {
		 return R.status(maintainOrderService.timeoutExplain(maintainOrder));
	 }

	 /**
	  * 批量审核确认 设备保养工单表
	  */
	 @PostMapping("/confirmBatch")
	 @ApiOperationSupport(order = 10)
	 @ApiOperation(value = "审核确认", notes = "MaintainOrderVO，驳回：status=6")
	 public R confirmBatch(@RequestBody MaintainOrderVO vo) {
		 return R.status(maintainOrderService.confirmBatch(vo));
	 }


 }
