 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.service.impl;

 import cn.hutool.core.map.MapUtil;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
 import com.snszyk.common.equipment.enums.EquipmentDataScopeEnum;
 import com.snszyk.common.equipment.feign.FeignPage;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountPageVO;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.ObjectUtil;
 import com.snszyk.simas.common.enums.PlanStatusEnum;
 import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
 import com.snszyk.simas.common.vo.EquipmentStandardVO;
 import com.snszyk.simas.inspect.service.IInspectStandardService;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
 import com.snszyk.simas.maintain.entity.MaintainStandard;
 import com.snszyk.simas.maintain.mapper.MaintainPlanEquipmentMapper;
 import com.snszyk.simas.maintain.mapper.MaintainPlanMapper;
 import com.snszyk.simas.maintain.mapper.MaintainStandardMapper;
 import com.snszyk.simas.maintain.service.IMaintainStandardService;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import com.snszyk.simas.maintain.wrapper.MaintainStandardWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.ArrayList;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备保养标准表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @AllArgsConstructor
 @Service
 public class MaintainStandardServiceImpl extends BaseServiceImpl<MaintainStandardMapper, MaintainStandard> implements IMaintainStandardService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final MaintainPlanMapper maintainPlanMapper;
	 private final MaintainPlanEquipmentMapper maintainPlanEquipmentMapper;
	 private final IInspectStandardService stdService;

	 @Override
	 public IPage<DeviceAccountVO> page(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount) {
		 DeviceAccountPageVO vo = BeanUtil.copy(deviceAccount, DeviceAccountPageVO.class);
		 R<FeignPage<DeviceAccountVO>> deviceResult = deviceAccountClient.devicePageListScope(vo,
			 Func.toInt(page.getCurrent()), Func.toInt(page.getSize()), EquipmentDataScopeEnum.MAINTAIN_STANDARD.getCode());
		 if (!deviceResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isNotEmpty(deviceResult.getData())&&Func.isNotEmpty(deviceResult.getData().getRecords())) {
			 deviceResult.getData().getRecords().forEach(data -> {
				 // 保养标准数量
				 data.setStandardCount(this.count(Wrappers.<MaintainStandard>query().lambda()
					 .eq(MaintainStandard::getEquipmentId, data.getId())));
			 });
		 }
		 IPage<DeviceAccountVO> result= new Page<>();
		 result.setTotal(deviceResult.getData().getTotal());
		 result.setRecords(deviceResult.getData().getRecords());
		 return result;
	 }

	 @Override
	 public IPage<MaintainStandardVO> selectPage(IPage<MaintainStandardVO> page, MaintainStandardVO vo) {
		 //DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		 //deviceAccountVO.setUseDept(vo.getUseDept()).setName(vo.getEquipmentName()).setCode(vo.getEquipmentCode());
		 //R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		 //if (equipmentListResult.isSuccess() && Func.isNotEmpty(equipmentListResult.getData())) {
			// vo.setEquipmentIdList(equipmentListResult.getData().stream()
			//	 .map(DeviceAccountVO::getId).collect(Collectors.toList()));
		 //}
		 List<MaintainStandard> list = baseMapper.page(page, vo);
		 List<MaintainStandardVO> resultList = null;
		 if(Func.isNotEmpty(list)){
			 resultList = MaintainStandardWrapper.build().listVO(list);
			 //List<Long> equipmentIdList = list.stream().distinct().map(s -> s.getEquipmentId()).collect(Collectors.toList());
			 //// 查询设备Map
			 //final Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(equipmentIdList);
			 //resultList.forEach(standard -> {
				// DeviceAccountVO equipmentAccount = equipmentVOMap.get(standard.getEquipmentId());
				// if(Func.isNotEmpty(equipmentAccount)){
				//	 standard.setEquipmentName(equipmentAccount.getName()).setEquipmentCode(equipmentAccount.getCode());
				// }
			 //});
		 }
		 return page.setRecords(resultList);
	 }

	 /**
	  * 获取设备Map
	  *
	  * @param equipmentIdList
	  * @return java.util.Map<java.lang.Long,com.snszyk.common.equipment.vo.DeviceAccountVO>
	  * <AUTHOR>
	  * @date 2025/3/20 11:02
	  */
	 private Map<Long, DeviceAccountVO> getEquipmentVOMap(List<Long> equipmentIdList) {
		 if (ObjectUtil.isEmpty(equipmentIdList)) {
			 return MapUtil.empty();
		 }
		 // 根据设备ids查询设备信息
		 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		 deviceAccountVO.setDeviceIds(equipmentIdList);
		 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		 if(!equipmentListResult.isSuccess()){
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if(Func.isEmpty(equipmentListResult.getData())){
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(equipmentListResult.getData(), DeviceAccountVO::getId, e
			 -> Objects.requireNonNull(BeanUtil.copy(e, DeviceAccountVO.class)));
	 }

	 @Override
	 public EquipmentStandardVO detail(Long equipmentId) {
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(equipmentId);
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 EquipmentStandardVO detail = new EquipmentStandardVO(equipmentId);
		 detail.setEquipmentAccount(deviceAccountResult.getData());
		 List<MaintainStandard> standardList = this.list(Wrappers.<MaintainStandard>query().lambda()
			 .eq(MaintainStandard::getEquipmentId, equipmentId).orderByAsc(MaintainStandard::getSort));
		 if (Func.isNotEmpty(standardList)) {
			 detail.setMaintainStandardList(MaintainStandardWrapper.build().listVO(standardList));
		 }
		 return detail;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean submit(EquipmentStandardVO vo) {
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(vo.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 Long equipmentId = vo.getEquipmentId();
//		 Map<String, String> monitorMap = vo.getMaintainStandardList().stream().filter(e -> e.getMonitorId() == null).collect(Collectors.toMap(e -> e.getMonitorName(), e -> e.getMonitorType()));
//		 stdService.genMonitor(equipmentId, monitorMap);
		 boolean ret;
		 // 1. 判断是否已经存在该标准，如果存在则更新，不存在则新增
		 if (Func.isNotEmpty(vo.getMaintainStandardList())) {
			 List<Long> updateIds = vo.getMaintainStandardList().stream()
				 .filter(monitorStandard -> Func.isNotEmpty(monitorStandard.getId()))
				 .map(monitorStandard -> monitorStandard.getId()).collect(Collectors.toList());
			 // 删除标准
			 List<MaintainStandard> removeStandardList = new ArrayList<>();
			 if (Func.isNotEmpty(updateIds)) {
				 //List<MaintainStandard> allList = this.list(Wrappers.<MaintainStandard>query().lambda()
					// .eq(MaintainStandard::getEquipmentId, vo.getEquipmentId()));
				 //allList.forEach(s -> {
					// if(!updateIds.contains(s.getId())){
					//	 // 删除的标准
					//	 removeStandardList.add(s);
					// }
				 //});
				 //// 校验点检计划
				 //List<String> planNoList = new ArrayList<>();
				 //if(Func.isNotEmpty(removeStandardList)){
					// List<MaintainPlanEquipment> list = maintainPlanEquipmentMapper.selectList(Wrappers.<MaintainPlanEquipment>query().lambda()
					//	 .in(MaintainPlanEquipment::getStandardId, removeStandardList.stream().map(MaintainStandard::getId).collect(Collectors.toList())));
					// if(Func.isNotEmpty(list)){
					//	 list.forEach(planEquipment -> {
					//		 MaintainPlan maintainPlan = maintainPlanMapper.selectById(planEquipment.getPlanId());
					//		 if(PlanStatusEnum.IS_COMPLETED != PlanStatusEnum.getByCode(maintainPlan.getStatus())
					//			 || PlanStatusEnum.IS_TERMINATED != PlanStatusEnum.getByCode(maintainPlan.getStatus())){
					//			 planNoList.add(maintainPlan.getNo());
					//		 }
					//	 });
					//	 if(Func.isNotEmpty(planNoList)){
					//		 throw new ServiceException(String.format("保养计划%s中包含删除的标准，不能操作！",
					//			 planNoList.stream().collect(Collectors.joining(StringPool.COMMA))));
					//	 }
					// }
				 //}
				 this.remove(Wrappers.<MaintainStandard>query().lambda()
					 .eq(MaintainStandard::getEquipmentId, vo.getEquipmentId()).notIn(MaintainStandard::getId, updateIds));
				 // 删除保养计划中选择的标准
				 maintainPlanEquipmentMapper.delete(Wrappers.<MaintainPlanEquipment>query().lambda()
					 .eq(MaintainPlanEquipment::getEquipmentId, vo.getEquipmentId()).notIn(MaintainPlanEquipment::getStandardId, updateIds));
				 //// 如果计划中的标准都被删完了，则计划自动停止
				 //this.checkPlanStandard();
			 }else{
				 this.remove(Wrappers.<MaintainStandard>query().lambda()
					 .eq(MaintainStandard::getEquipmentId, vo.getEquipmentId()));
				 // 删除保养计划中选择的标准
				 maintainPlanEquipmentMapper.delete(Wrappers.<MaintainPlanEquipment>query().lambda()
					 .eq(MaintainPlanEquipment::getEquipmentId, vo.getEquipmentId()));

			 }
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<MaintainStandard> list = vo.getMaintainStandardList().stream().map(standardVO -> {
				 MaintainStandard maintainStandard = Objects.requireNonNull(BeanUtil.copy(standardVO, MaintainStandard.class));
				 if (Func.isEmpty(standardVO.getId())) {
					 maintainStandard.setEquipmentId(vo.getEquipmentId());
					 //如果是新增的
					 if(standardVO.getMonitorId()==null) {
						 Long monitorId = stdService.genMonitor(vo.getEquipmentId(), standardVO.getMonitorName(), standardVO.getMonitorType());
						 maintainStandard.setMonitorId(monitorId);
					 }
				 } else {
				 }
				 maintainStandard.setUpdateTime(DateUtil.now());
				 maintainStandard.setSort(sort.getAndSet(sort.get() + 1));
				 return maintainStandard;
			 }).collect(Collectors.toList());
			 // 2. 保存数据
			 ret = this.saveOrUpdateBatch(list);
		 } else {
			 ret = this.clear(vo.getEquipmentId());
		 }
		 // 3. 返回结果
		 return ret;
	 }

	 /**
	  * 校验未执行和执行中计划里的标准是否为空，为空则计划状态变为已终止
	  *
	  * @return void
	  * <AUTHOR>
	  * @date 2024/10/29 10:16
	  */
	 private void checkPlanStandard() {
		 List<MaintainPlan> list = maintainPlanMapper.selectList(Wrappers.<MaintainPlan>query().lambda()
			 .eq(MaintainPlan::getStatus, PlanStatusEnum.NO_START.getCode()).or().eq(MaintainPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode()));
		 if (Func.isNotEmpty(list)) {
			 list.forEach(plan -> {
				 Integer cnt = maintainPlanEquipmentMapper.selectCount(Wrappers.<MaintainPlanEquipment>query().lambda()
					 .eq(MaintainPlanEquipment::getPlanId, plan.getId()));
				 if (cnt == 0) {
					 plan.setStatus(PlanStatusEnum.IS_TERMINATED.getCode());
					 maintainPlanMapper.updateById(plan);
				 }
			 });
		 }
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean clear(Long equipmentId) {
		 // 清空标准，同步删除设备关联的工单
		 //boolean ret = maintainOrderMapper.delete(Wrappers.<MaintainOrder>query().lambda()
			// .eq(MaintainOrder::getEquipmentId, equipmentId)
			// .and(wrapper ->
			//	 wrapper.eq(MaintainOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
			//		 .or()
			//		 .eq(MaintainOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
			//		 .or()
			//		 .eq(MaintainOrder::getStatus, OrderStatusEnum.WAIT_CONFIRM.getCode())
			//		 .or()
			//		 .eq(MaintainOrder::getStatus, OrderStatusEnum.IS_REJECTED.getCode()))) >= 0;
		 // 清空标准
		 boolean ret = this.remove(Wrappers.<MaintainStandard>query().lambda()
			 .eq(MaintainStandard::getEquipmentId, equipmentId));
		 return ret;
	 }

	 @Override
	 public List<BaseStandardTemplate> generateExcelData(String deptId) {
		 List<Long> deptIds = null;
		 if (Func.isNotEmpty(deptId)) {
			 deptIds = Func.toLongList(deptId);
		 }
		 return this.baseMapper.generateExcelData(deptIds);
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean saveImportData(List<MaintainStandard> list) {
		 updateSort(list);
		 return this.saveOrUpdateBatch(list);
	 }

	 /**
	  * 添加排序
	  *
	  * @param list
	  */
	 private void updateSort(List<MaintainStandard> list) {
		 // 获取设备id分组
		 Map<Long, List<MaintainStandard>> map = list.stream().collect(Collectors.groupingBy(MaintainStandard::getEquipmentId));
		 map.keySet().forEach(equipmentId -> {
			 MaintainStandard standard = this.getOne(Wrappers.<MaintainStandard>query().lambda().eq(MaintainStandard::getEquipmentId, equipmentId).orderByDesc(MaintainStandard::getSort).last("LIMIT 1"));
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 if (Func.isNotEmpty(standard) && !Func.isNull(standard.getSort())) {
				 sort.set(standard.getSort());
			 }
			 map.get(equipmentId).forEach(item -> {
				 MaintainStandard entity = this.getOne(Wrappers.<MaintainStandard>query().lambda().eq(MaintainStandard::getEquipmentId, item.getEquipmentId()).eq(MaintainStandard::getMonitorId, item.getMonitorId()));
				 if (Func.isNotEmpty(entity)) {
					 //	item.setNeedConfirm(entity.getNeedConfirm());
					 item.setRemark(entity.getRemark());
					 item.setUpdateTime(DateUtil.now());
					 item.setSort(entity.getSort());
					 item.setId(entity.getId());
					 item.setStatus(entity.getStatus());
				 } else {
					 item.setSort(sort.getAndSet(sort.get() + 1));
					 //	.setNeedConfirm(1);
				 }
			 });
		 });
	 }


 }
