/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
import com.snszyk.simas.maintain.vo.MaintainPlanEquipmentVO;

/**
 * 设备保养计划关联表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface IMaintainPlanEquipmentService extends IService<MaintainPlanEquipment> {


	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainPlanEquipment
	 * @return
	 */
	IPage<MaintainPlanEquipmentVO> page(IPage<MaintainPlanEquipmentVO> page, MaintainPlanEquipmentVO maintainPlanEquipment);


}
