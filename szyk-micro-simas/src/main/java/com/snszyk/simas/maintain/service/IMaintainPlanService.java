 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.simas.common.excel.MaintainPlanExcel;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.vo.MaintainPlanVO;

 import java.util.Date;
 import java.util.List;

 /**
  * 设备保养计划表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 public interface IMaintainPlanService extends BaseService<MaintainPlan> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param maintainPlan
	  * @return
	  */
	 IPage<MaintainPlanDTO> page(IPage<MaintainPlanDTO> page, MaintainPlanVO maintainPlan);

	 /**
	  * 详情
	  *
	  * @param no
	  * @return
	  */
	 MaintainPlanDTO detail(String no);

	 /**
	  * 查看
	  *
	  * @param no
	  * @return
	  */
	 MaintainPlanDTO view(String no);

	 /**
	  * 新增
	  *
	  * @param maintainPlan
	  * @return
	  */
	 boolean add(MaintainPlanVO maintainPlan);

	 /**
	  * 修改
	  *
	  * @param maintainPlan
	  * @return
	  */
	 boolean modify(MaintainPlanVO maintainPlan);

	 /**
	  * 查询当天点检计划
	  *
	  * @param currentDate
	  * @return
	  */
	 List<MaintainPlanDTO> getTheDayPlans(Date currentDate);

	 /**
	  * 导出
	  *
	  * @param maintainPlan
	  * @return
	  */
	 List<MaintainPlanExcel> exportPlan(MaintainPlanVO maintainPlan);

	 /**
	  * 重启
	  *
	  * @param ids
	  * @return
	  */
	 boolean restart(List<Long> ids);


 }
