/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.maintain.entity.MaintainRecord;
import com.snszyk.simas.fault.enums.DefectLevelEnum;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.Objects;

/**
 * 设备保养记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class MaintainRecordWrapper extends BaseEntityWrapper<MaintainRecord, MaintainRecordVO> {

	public static MaintainRecordWrapper build() {
		return new MaintainRecordWrapper();
 	}

	@Override
	public MaintainRecordVO entityVO(MaintainRecord maintainRecord) {
		MaintainRecordVO maintainRecordVO = Objects.requireNonNull(BeanUtil.copy(maintainRecord, MaintainRecordVO.class));
		if(Func.isNotEmpty(maintainRecord.getMaintainUser())){
			User maintainUser = UserCache.getUser(maintainRecord.getMaintainUser());
			if(Func.isNotEmpty(maintainUser)){
				maintainRecordVO.setMaintainUserName(maintainUser.getRealName());
			}
		}
		if(Func.isNotEmpty(maintainRecord.getAbnormalLevel())){
			maintainRecordVO.setAbnormalLevelName(DefectLevelEnum.getByCode(maintainRecord.getAbnormalLevel()).getName());
		}
		if(Func.isNotEmpty(maintainRecord.getIsHandled())){
			maintainRecordVO.setIsHandledName(maintainRecord.getIsHandled() == 1 ? "是" : "否");
		}
		if(Func.isNotEmpty(maintainRecord.getIsAbnormal())){
			maintainRecordVO.setAbnormalStatus(maintainRecord.getIsAbnormal() == 0 ? "正常" : "异常");
		}
		return maintainRecordVO;
	}

}
