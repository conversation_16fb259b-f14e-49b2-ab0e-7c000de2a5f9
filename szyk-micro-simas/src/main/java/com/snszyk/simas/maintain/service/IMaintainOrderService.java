 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.service;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.mp.base.BaseService;
 import com.snszyk.core.tool.support.Kv;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.excel.MaintainOrderExcel;
 import com.snszyk.simas.common.vo.EquipmentMaintainVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
 import com.snszyk.simas.maintain.entity.MaintainOrder;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;

 import java.time.LocalDateTime;
 import java.util.List;
 import java.util.Map;

 /**
  * 设备保养工单表 服务类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 public interface IMaintainOrderService extends BaseService<MaintainOrder> {

	 /**
	  * 自定义分页
	  *
	  * @param page
	  * @param maintainOrder
	  * @return
	  */
	 IPage<MaintainOrderDTO> page(IPage<MaintainOrderDTO> page, MaintainOrderVO maintainOrder);

	 /**
	  * 详情
	  *
	  * @param no
	  * @return
	  */
	 MaintainOrderDTO detail(String no);

	 /**
	  * 设备点巡检
	  *
	  * @param equipmentMaintain
	  * @return
	  */
	 boolean maintain(EquipmentMaintainVO equipmentMaintain);

	 /**
	  * 工单确认
	  *
	  * @param maintainOrder
	  * @return
	  */
	 boolean confirm(MaintainOrderVO maintainOrder);

	 /**
	  * 批量工单确认
	  *
	  * @param maintainOrder
	  * @return
	  */
	 boolean confirmBatch(MaintainOrderVO maintainOrder);

	 /**
	  * 导出
	  *
	  * @param maintainOrder
	  * @return
	  */
	 List<MaintainOrderExcel> exportOrder(MaintainOrderVO maintainOrder);

	 /**
	  * 即将超期分页
	  *
	  * @param page
	  * @param maintainOrder
	  * @return
	  */
	 IPage<MaintainOrderDTO> timeoutPage(IPage<MaintainOrderDTO> page, MaintainOrderVO maintainOrder);

	 /**
	  * 统计-工单完成情况
	  *
	  * @param queryDate
	  * @return
	  */
	 List<MaintainOrderDTO> maintainOrderStatistics(Integer queryDate);

	 /**
	  * 即将超期工单数量
	  *
	  * @return
	  */
	 Integer expireSoonCount();

	 /**
	  * 发送消息提醒
	  *
	  * @param list
	  * @param messageBizType
	  */
	 void sendMessage(List<MaintainOrder> list, MessageBizTypeEnum messageBizType);

	 /**
	  * 统计报表-点巡检统计
	  *
	  * @param page
	  * @param search
	  * @return
	  */
	 IPage<MaintainOrderDTO> statisticalReport(IPage<MaintainOrderDTO> page, StatisticSearchVO search);

	 /**
	  * 保养工单列表
	  *
	  * @param maintainOrder
	  * @return
	  */
	 List<MaintainOrderDTO> queryList(MaintainOrderVO maintainOrder);

	 /**
	  * 超时说明
	  *
	  * @param maintainOrder
	  * @return
	  */
	 boolean timeoutExplain(MaintainOrderVO maintainOrder);

	 /**
	  * 大屏超期
	  *
	  * @return
	  */
	 List<BigScreenMessageDTO> overdueList(String tenantId);

	 List<Kv> maintenance30day();

	 List<MaintainOrderDTO> coverList30day(String tenantId);

	 Integer handleMaintainCount(MaintainOrderVO maintainOrder);

	 List<MaintainOrder> listBy(Long executeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus);

	 /**
	  * 保养设备统计
	  *
	  * @param vo
	  * @param deviceMap
	  * @return
	  */
	 List<EquipmentStatisticsDTO> maintainStatistics(StatisticSearchVO vo, Map<Long, DeviceAccountVO> deviceMap);


 }
