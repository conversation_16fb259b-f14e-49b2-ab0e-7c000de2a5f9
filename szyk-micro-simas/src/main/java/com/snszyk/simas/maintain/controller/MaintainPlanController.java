 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.controller;

 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.excel.util.ExcelUtil;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.PlanStatusEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.MaintainPlanExcel;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.vo.DeviceInfoVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
 import com.snszyk.simas.maintain.service.IMaintainPlanService;
 import com.snszyk.simas.maintain.service.IMaintainStandardService;
 import com.snszyk.simas.maintain.vo.MaintainPlanEquipmentVO;
 import com.snszyk.simas.maintain.vo.MaintainPlanVO;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.util.List;

 /**
  * 设备保养计划表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/maintain-plan")
 @Api(value = "设备保养计划表", tags = "设备保养计划表接口")
 public class MaintainPlanController extends SzykController {

	 private final IMaintainPlanService maintainPlanService;
	 private final IMaintainStandardService maintainStandardService;
	 private final IMaintainPlanEquipmentService planEquipmentService;
	 private final IOperateLogService operateLogService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入id")
	 public R<MaintainPlanDTO> detail(String no) {
		 MaintainPlanDTO detail = maintainPlanService.detail(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 查看
	  */
	 @GetMapping("/view")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "查看", notes = "传入no")
	 public R<MaintainPlanDTO> view(String no) {
		 MaintainPlanDTO detail = maintainPlanService.view(no);
		 OperateLogVO operateLog = new OperateLogVO(detail.getId(), SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(detail);
	 }

	 /**
	  * 自定义分页 设备保养计划表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "分页", notes = "传入maintainPlan")
	 public R<IPage<MaintainPlanDTO>> page(@ApiIgnore MaintainPlanVO maintainPlan, Query query) {
		 OperateLogVO operateLog = new OperateLogVO(null, SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(maintainPlanService.page(Condition.getPage(query), maintainPlan));
	 }

	 /**
	  * 计划选择标准分页 设备保养标准表
	  */
	 @GetMapping("/selectPage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "useDept", value = "部门id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "equipmentCode", value = "设备编号", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "equipmentName", value = "设备名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "monitorId", value = "部位id", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "standard", value = "标准", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "计划选择标准分页", notes = "传入maintainPlan")
	 public R<IPage<MaintainStandardVO>> selectPage(@ApiIgnore MaintainStandardVO maintainStandard, Query query) {
		 return R.data(maintainStandardService.selectPage(Condition.getPage(query), maintainStandard));
	 }

	 /**
	  * 计划详情设备分页 设备保养计划表
	  */
	 @GetMapping("/planDevicePage")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "planId", value = "计划id", paramType = "query", dataType = "long")
	 })
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "计划设备分页", notes = "传入maintainPlan")
	 public R<IPage<MaintainPlanEquipmentVO>> maintainPlanPage(@ApiIgnore MaintainPlanEquipmentVO maintainPlanEquipment, Query query) {
		 return R.data(planEquipmentService.page(Condition.getPage(query), maintainPlanEquipment));
	 }

	 /**
	  * 新增 设备保养计划表
	  */
	 @PostMapping("/save")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "新增", notes = "传入maintainPlan")
	 public R save(@Valid @RequestBody MaintainPlanVO maintainPlan) {
		 boolean ret = maintainPlanService.add(maintainPlan);
		 OperateLogVO operateLog = new OperateLogVO(maintainPlan.getId(), SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.CREATE);
		 operateLogService.submit(operateLog);
		 return R.status(ret);
	 }

	 /**
	  * 修改 设备保养计划表
	  */
	 @PostMapping("/update")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "修改", notes = "传入maintainPlan")
	 public R update(@Valid @RequestBody MaintainPlanVO maintainPlan) {
		 boolean ret = maintainPlanService.modify(maintainPlan);
		 OperateLogVO operateLog = new OperateLogVO(maintainPlan.getId(), SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.UPDATE);
		 operateLogService.submit(operateLog);
		 return R.status(ret);
	 }

	 /**
	  * 删除 设备保养计划表
	  */
	 @PostMapping("/remove")
	 @ApiOperationSupport(order = 7)
	 @ApiOperation(value = "逻辑删除", notes = "传入ids")
	 public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		 List<Long> list = Func.toLongList(ids);
		 list.forEach(id -> {
			 OperateLogVO operateLog = new OperateLogVO(id, SystemModuleEnum.MAINTAIN_PLAN, OperateTypeEnum.DELETE);
			 operateLogService.submit(operateLog);
		 });
		 return R.status(maintainPlanService.deleteLogic(list));
	 }

	 /**
	  * 手动开始 设备保养计划表
	  */
	 @PostMapping("/manual-start")
	 @ApiOperationSupport(order = 8)
	 @ApiOperation(value = "手动开始", notes = "传入id")
	 public R manualStart(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		 return R.status(maintainPlanService.update(Wrappers.<MaintainPlan>update().lambda()
			 .set(MaintainPlan::getStartDate, DateUtil.now())
			 .set(MaintainPlan::getStatus, PlanStatusEnum.IN_PROGRESS.getCode())
			 .eq(MaintainPlan::getId, id)));
	 }

	 /**
	  * 手动停止 设备保养计划表
	  */
	 @PostMapping("/manual-stop")
	 @ApiOperationSupport(order = 9)
	 @ApiOperation(value = "手动停止", notes = "传入id")
	 public R manualStop(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		 return R.status(maintainPlanService.update(Wrappers.<MaintainPlan>update().lambda()
			 .set(MaintainPlan::getEndDate, DateUtil.now())
			 .set(MaintainPlan::getStatus, PlanStatusEnum.IS_TERMINATED.getCode())
			 .eq(MaintainPlan::getId, id)));
	 }

	 /**
	  * 导出 设备保养计划表
	  */
	 @GetMapping("/export-plan")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "name", value = "计划名称", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "cycleType", value = "计划周期（字典：）", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "executeDept", value = "执行部门", paramType = "query", dataType = "long"),
		 @ApiImplicitParam(name = "status", value = "状态", paramType = "query", dataType = "Integer"),
		 @ApiImplicitParam(name = "queryStartDate", value = "查询-开始日期", paramType = "query", dataType = "string"),
		 @ApiImplicitParam(name = "queryEndDate", value = "查询-结束日期", paramType = "query", dataType = "string")
	 })
	 @ApiOperationSupport(order = 10)
	 @ApiOperation(value = "导出", notes = "传入maintainPlan")
	 public void exportPlan(@ApiIgnore MaintainPlanVO maintainPlan, HttpServletResponse response) {
		 List<MaintainPlanExcel> list = maintainPlanService.exportPlan(maintainPlan);
		 ExcelUtil.export(response, "保养计划列表" + DateUtil.time(), "保养计划", list, MaintainPlanExcel.class);
	 }


 }
