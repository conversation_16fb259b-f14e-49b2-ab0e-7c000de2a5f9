/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.entity.MaintainPlan;
import com.snszyk.simas.maintain.vo.MaintainOrderVO;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备保养工单表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class MaintainOrderWrapper extends BaseEntityWrapper<MaintainOrder, MaintainOrderVO> {

	public static MaintainOrderWrapper build() {
		return new MaintainOrderWrapper();
 	}

	@Override
	public MaintainOrderVO entityVO(MaintainOrder maintainOrder) {
		MaintainOrderVO maintainOrderVO = Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrderVO.class));

		//User createUser = UserCache.getUser(maintainOrder.getCreateUser());
		//User updateUser = UserCache.getUser(maintainOrder.getUpdateUser());
		//maintainOrderVO.setCreateUserName(createUser.getName());
		//maintainOrderVO.setUpdateUserName(updateUser.getName());

		return maintainOrderVO;
	}

	public MaintainOrderDTO entityDTO(MaintainOrder maintainOrder) {
		MaintainOrderDTO maintainOrderDTO = Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrderDTO.class));
		// 执行部门
		if(Func.isNotEmpty(maintainOrder.getExecuteDept())){
			Dept dept = SysCache.getDept(maintainOrder.getExecuteDept());
			if(Func.isNotEmpty(dept)){
				maintainOrderDTO.setExecuteDeptName(dept.getDeptName());
			}
		}
		// 执行人
		if(Func.isNotEmpty(maintainOrder.getExecuteUser())){
			User user = UserCache.getUser(maintainOrder.getExecuteUser());
			if(Func.isNotEmpty(user)){
				maintainOrderDTO.setExecuteUserName(user.getRealName());
			}
		}
		// 审批人
		if(Func.isNotEmpty(maintainOrder.getApprovalUser())){
			User user = UserCache.getUser(maintainOrder.getApprovalUser());
			if(Func.isNotEmpty(user)){
				maintainOrderDTO.setApprovalUserName(user.getRealName());
			}
		}
		// 状态
		maintainOrderDTO.setStatusName(OrderStatusEnum.getByCode(maintainOrder.getStatus()).getName());
		if(Func.isNotEmpty(maintainOrder.getPlanInfo())){
			MaintainPlan plan = JSONUtil.toBean(maintainOrder.getPlanInfo(), MaintainPlan.class);
			// 计划名称
			maintainOrderDTO.setOrderName(plan.getName());
			// 计划周期
			maintainOrderDTO.setCycleTypeName(PlanCycleEnum.getByCode(plan.getCycleType()).getName());
			switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
				case DAY:
					maintainOrderDTO.setStartTimeStr(DateUtil.format(maintainOrder.getStartTime(), "yyyy-MM-dd HH:mm"))
						.setEndTimeStr(DateUtil.format(maintainOrder.getEndTime(), "yyyy-MM-dd HH:mm"));
					break;
				case WEEK:
				case MONTH:
					maintainOrderDTO.setStartTimeStr(DateUtil.format(maintainOrder.getStartTime(), "yyyy-MM-dd"))
						.setEndTimeStr(DateUtil.format(maintainOrder.getEndTime(), "yyyy-MM-dd"));
					break;
				default:
			}
		}
		return maintainOrderDTO;
	}

	public List<MaintainOrderDTO> listDTO(List<MaintainOrder> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
