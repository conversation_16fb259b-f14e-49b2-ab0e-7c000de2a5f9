/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.service.impl;

import com.snszyk.simas.maintain.entity.MaintainRecord;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;
import com.snszyk.simas.maintain.mapper.MaintainRecordMapper;
import com.snszyk.simas.maintain.service.IMaintainRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 设备保养记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Service
public class MaintainRecordServiceImpl extends ServiceImpl<MaintainRecordMapper, MaintainRecord> implements IMaintainRecordService {

	@Override
	public IPage<MaintainRecordVO> selectMaintainRecordPage(IPage<MaintainRecordVO> page, MaintainRecordVO maintainRecord) {
		return page.setRecords(baseMapper.selectMaintainRecordPage(page, maintainRecord));
	}

}
