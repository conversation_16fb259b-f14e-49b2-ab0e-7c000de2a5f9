/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
import com.snszyk.simas.maintain.entity.MaintainStandard;
import com.snszyk.simas.maintain.vo.MaintainStandardVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备保养标准表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MaintainStandardMapper extends BaseMapper<MaintainStandard> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainStandard
	 * @return
	 */
	List<MaintainStandard> page(IPage page, @Param("maintainStandard") MaintainStandardVO maintainStandard);

	/**
	 * 导入模板数据
	 *
	 * @param deptIds
	 * @return
	 */
	List<BaseStandardTemplate> generateExcelData(@Param("deptIds") List<Long> deptIds);


}
