/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.common.vo.DeviceInfoVO;
import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
import com.snszyk.simas.maintain.vo.MaintainPlanEquipmentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备保养计划关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MaintainPlanEquipmentMapper extends BaseMapper<MaintainPlanEquipment> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainPlanEquipment
	 * @return
	 */
	List<MaintainPlanEquipmentVO> page(IPage page, @Param("maintainPlanEquipment") MaintainPlanEquipmentVO maintainPlanEquipment);


}
