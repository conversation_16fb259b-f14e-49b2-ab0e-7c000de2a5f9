/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.simas.common.dto.BigScreenMessageDTO;
import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.vo.MaintainOrderVO;
import com.snszyk.simas.common.vo.StatisticSearchVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备保养工单表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MaintainOrderMapper extends BaseMapper<MaintainOrder> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainOrder
	 * @return
	 */
	List<MaintainOrder> page(IPage page, @Param("maintainOrder") MaintainOrderVO maintainOrder);

	/**
	 * 即将超时分页
	 *
	 * @param page
	 * @param maintainOrder
	 * @return
	 */
	List<MaintainOrder> timeoutPage(IPage page, @Param("maintainOrder") MaintainOrderVO maintainOrder);

	/**
	 * 即将超时工单数量
	 *
	 * @param maintainOrder
	 * @return
	 */
	Integer expireSoonCount(@Param("maintainOrder") MaintainOrderVO maintainOrder);

	/**
	 * 统计-工单完成情况
	 *
	 * @param queryDate
	 * @return
	 */
	List<MaintainOrder> maintainOrderStatistics(@Param("queryDate")Integer queryDate);

	/**
	 * 统计报表-保养统计
	 *
	 * @param page
	 * @param search
	 * @return
	 */
	List<MaintainOrder> statisticalReport(IPage page, @Param("search") StatisticSearchVO search);

	/**
	 * 保养工单列表
	 *
	 * @param maintainOrder
	 * @return
	 */
	List<MaintainOrderDTO> queryList(@Param("maintainOrder") MaintainOrderVO maintainOrder);

	/**
	 * 统计报表-保养按设备统计
	 *
	 * @param search
	 * @return
	 */
	List<EquipmentStatisticsDTO> statisticsByEquipment(@Param("search") StatisticSearchVO search);

	List<BigScreenMessageDTO> overdueList(String tenantId);

	List<Kv> maintenance30day();

	List<MaintainOrderDTO> coverList30day(String tenantId);

    Integer handleMaintainCount(@Param("maintainOrder") MaintainOrderVO maintainOrder);

	/**
	 * 工单统计
	 *
	 * @param search
	 * @return
	 */
	List<MaintainOrder> equipmentStatisticsOfOrder(@Param("search") StatisticSearchVO search);


}
