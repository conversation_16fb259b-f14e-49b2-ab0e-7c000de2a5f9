 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.wrapper;

 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.entity.DeviceMonitor;
 import com.snszyk.core.mp.support.BaseEntityWrapper;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.maintain.entity.MaintainStandard;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import com.snszyk.system.cache.DictBizCache;
 import com.snszyk.user.cache.UserCache;
 import com.snszyk.user.entity.User;

 import java.util.Objects;

 /**
  * 设备保养标准表包装类,返回视图层所需的字段
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 public class MaintainStandardWrapper extends BaseEntityWrapper<MaintainStandard, MaintainStandardVO> {

 	public static MaintainStandardWrapper build() {
 		return new MaintainStandardWrapper();
  	}

 	@Override
 	public MaintainStandardVO entityVO(MaintainStandard maintainStandard) {
 		MaintainStandardVO maintainStandardVO = Objects.requireNonNull(BeanUtil.copy(maintainStandard, MaintainStandardVO.class));
 		DeviceMonitor equipmentMonitor = CommonCache.getMonitor(maintainStandard.getMonitorId());
 		if(Func.isNotEmpty(equipmentMonitor)){
 			maintainStandardVO.setMonitorName(equipmentMonitor.getName());
			maintainStandardVO.setMonitorType(equipmentMonitor.getType());
				if (Func.isNotEmpty(equipmentMonitor.getType())) {
					String monitorTypeName = DictBizCache.getValue("monitor_type", equipmentMonitor.getType());
					maintainStandardVO.setMonitorTypeName(monitorTypeName);
			}
 		}
 		//if(Func.isNotEmpty(maintainStandard.getNeedConfirm())){
 		//	maintainStandardVO.setNeedConfirmName(maintainStandard.getNeedConfirm() == 0 ? "否" : "是");
 		//}
 		User createUser = UserCache.getUser(maintainStandard.getCreateUser());
 		if(Func.isNotEmpty(createUser)){
 			maintainStandardVO.setCreateUserName(createUser.getName());
 		}
 		return maintainStandardVO;
 	}


 }
