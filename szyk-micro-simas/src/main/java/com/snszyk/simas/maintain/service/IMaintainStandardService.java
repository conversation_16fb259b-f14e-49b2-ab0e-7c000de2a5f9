/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
import com.snszyk.simas.common.vo.EquipmentStandardVO;
import com.snszyk.simas.maintain.entity.MaintainStandard;
import com.snszyk.simas.maintain.vo.MaintainStandardVO;

import java.util.List;

/**
 * 设备保养标准表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface IMaintainStandardService extends BaseService<MaintainStandard> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param deviceAccount
	 * @return
	 */
	IPage<DeviceAccountVO> page(IPage<DeviceAccountVO> page, DeviceAccountVO deviceAccount);

	/**
	 * 计划选择标准分页
	 *
	 * @param page
	 * @param maintainStandard
	 * @return
	 */
	IPage<MaintainStandardVO> selectPage(IPage<MaintainStandardVO> page, MaintainStandardVO maintainStandard);

	/**
	 * 详情
	 *
	 * @param equipmentId
	 * @return
	 */
	EquipmentStandardVO detail(Long equipmentId);

	/**
	 * 保存
	 *
	 * @param equipmentStandardVO
	 * @return
	 */
	boolean submit(EquipmentStandardVO equipmentStandardVO);

	/**
	 * 清空标准
	 *
	 * @param equipmentId
	 * @return
	 */
	boolean clear(Long equipmentId);

	/**
	 * 生成excel数据
	 *
	 * @param deptId
	 * @return
	 */
	List<BaseStandardTemplate> generateExcelData(String deptId);

	/**
	 * 保存导入数据
	 * @param list
	 * @return
	 */
    boolean saveImportData(List<MaintainStandard> list);


}
