/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.mapper;

import com.snszyk.simas.maintain.entity.MaintainRecord;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 设备保养记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface MaintainRecordMapper extends BaseMapper<MaintainRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainRecord
	 * @return
	 */
	List<MaintainRecordVO> selectMaintainRecordPage(IPage page, MaintainRecordVO maintainRecord);

}
