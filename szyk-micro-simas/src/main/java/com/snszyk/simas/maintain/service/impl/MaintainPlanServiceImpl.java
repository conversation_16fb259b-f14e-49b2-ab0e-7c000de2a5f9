 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.service.impl;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.json.JSONUtil;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.BizCodeUtil;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.ObjectUtil;
 import com.snszyk.simas.common.enums.PlanCycleEnum;
 import com.snszyk.simas.common.enums.PlanStatusEnum;
 import com.snszyk.simas.common.excel.MaintainPlanExcel;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.entity.MaintainOrder;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
 import com.snszyk.simas.maintain.entity.MaintainStandard;
 import com.snszyk.simas.maintain.mapper.MaintainPlanMapper;
 import com.snszyk.simas.maintain.mapper.MaintainStandardMapper;
 import com.snszyk.simas.maintain.service.IMaintainOrderService;
 import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
 import com.snszyk.simas.maintain.service.IMaintainPlanService;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;
 import com.snszyk.simas.maintain.vo.MaintainPlanVO;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import com.snszyk.simas.maintain.wrapper.MaintainOrderWrapper;
 import com.snszyk.simas.maintain.wrapper.MaintainPlanWrapper;
 import com.snszyk.simas.maintain.wrapper.MaintainStandardWrapper;
 import lombok.AllArgsConstructor;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.util.Date;
 import java.util.List;
 import java.util.Map;
 import java.util.Objects;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备保养计划表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @AllArgsConstructor
 @Service
 public class MaintainPlanServiceImpl extends BaseServiceImpl<MaintainPlanMapper, MaintainPlan> implements IMaintainPlanService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final IMaintainPlanEquipmentService planEquipmentService;
	 private final IMaintainOrderService maintainOrderService;
	 private final MaintainStandardMapper maintainStandardMapper;

	 @Override
	 public IPage<MaintainPlanDTO> page(IPage<MaintainPlanDTO> page, MaintainPlanVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<MaintainPlan> list = baseMapper.page(page, vo);
		 if (Func.isNotEmpty(list)) {
			 List<MaintainPlanDTO> resultList = list.stream().map(maintainPlan -> {
				 MaintainPlanDTO dto = MaintainPlanWrapper.build().entityDTO(maintainPlan);
				 List<MaintainPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<MaintainPlanEquipment>query().lambda()
					 .eq(MaintainPlanEquipment::getPlanId, dto.getId()));
				 dto.setEquipmentCount((int) planEquipmentList.stream()
					 .map(MaintainPlanEquipment::getEquipmentId).distinct().count());
				 return dto;
			 }).collect(Collectors.toList());
			 return page.setRecords(resultList);
		 }
		 return page.setRecords(null);
	 }

	 @Override
	 public MaintainPlanDTO detail(String no) {
		 MaintainPlan plan = this.getOne(Wrappers.<MaintainPlan>query().lambda().eq(MaintainPlan::getNo, no));
		 if(plan == null){
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 MaintainPlanDTO detail = MaintainPlanWrapper.build().entityDTO(plan);
		 detail.setEquipmentCount(planEquipmentService.count(Wrappers.<MaintainPlanEquipment>query().lambda()
			 .eq(MaintainPlanEquipment::getPlanId, detail.getId())));
		 // 所选标准
		 List<MaintainPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<MaintainPlanEquipment>query().lambda()
			 .eq(MaintainPlanEquipment::getPlanId, plan.getId()));
		 if(Func.isNotEmpty(planEquipmentList)){
			 detail.setEquipmentIds(planEquipmentList.stream()
				 .map(MaintainPlanEquipment::getEquipmentId).collect(Collectors.toList()));
			 // 查询设备Map
			 final Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(detail.getEquipmentIds());
			 detail.setMaintainStandardList(planEquipmentList.stream().map(planEquipment -> {
				 MaintainStandard maintainStandard = maintainStandardMapper.selectById(planEquipment.getStandardId());
				 MaintainStandardVO vo = MaintainStandardWrapper.build().entityVO(maintainStandard);
				 DeviceAccountVO equipmentAccount = equipmentVOMap.get(planEquipment.getEquipmentId());
				 if(Func.isNotEmpty(equipmentAccount)){
					 vo.setEquipmentName(equipmentAccount.getName()).setEquipmentCode(equipmentAccount.getCode())
						 .setEquipmentSn(equipmentAccount.getSn());
				 }
				 return vo;
			 }).collect(Collectors.toList()));
		 }
		 return detail;
	 }

	 @Override
	 public MaintainPlanDTO view(String no) {
		 MaintainPlan plan = this.getOne(Wrappers.<MaintainPlan>query().lambda().eq(MaintainPlan::getNo, no));
		 if(plan == null){
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 return MaintainPlanWrapper.build().entityDTO(plan);
	 }

	 /**
	  * 查询保养工单map
	  *
	  * @param equipmentIdList
	  * @param planIds
	  * @return
	  */
	 private Map<Long, MaintainOrderVO> getMaintainOrderMap(List<Long> equipmentIdList, List<Long> planIds) {
		 if (ObjectUtil.isEmpty(equipmentIdList) || ObjectUtil.isEmpty(planIds)) {
			 return MapUtil.empty();
		 }
		 List<MaintainOrder> maintainOrders = maintainOrderService.list(Wrappers.<MaintainOrder>query().lambda()
			 .in(MaintainOrder::getEquipmentId, equipmentIdList)
			 .in(MaintainOrder::getPlanId, planIds));
		 if (ObjectUtil.isEmpty(maintainOrders)) {
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(maintainOrders, MaintainOrder::getEquipmentId,
			 e -> MaintainOrderWrapper.build().entityVO(e));
	 }

	 /**
	  * 获取设备Map
	  */
	 private Map<Long, DeviceAccountVO> getEquipmentVOMap(List<Long> equipmentIdList) {
		 if (ObjectUtil.isEmpty(equipmentIdList)) {
			 return MapUtil.empty();
		 }
		 // 根据设备ids查询设备信息
		 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		 deviceAccountVO.setDeviceIds(equipmentIdList);
		 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		 if(!equipmentListResult.isSuccess()){
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if(Func.isEmpty(equipmentListResult.getData())){
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(equipmentListResult.getData(), DeviceAccountVO::getId,
			 e -> Objects.requireNonNull(BeanUtil.copy(e, DeviceAccountVO.class)));
	 }

	 /**
	  * 获取标准Map
	  *
	  * @param planEquipmentList
	  * @return
	  */
	 private Map<Long, MaintainStandardVO> getMaintainStandardMap(List<MaintainPlanEquipment> planEquipmentList) {
		 if (ObjectUtil.isEmpty(planEquipmentList)) {
			 return MapUtil.empty();
		 }
		 // 获取标准ids
		 final List<Long> standardIdList = ListUtil.map(planEquipmentList, MaintainPlanEquipment::getStandardId);
		 if (ObjectUtil.isEmpty(standardIdList)) {
			 return MapUtil.empty();
		 }
		 // 根据标准ids查询标准信息
		 List<MaintainStandard> maintainStandardList = maintainStandardMapper.selectList(Wrappers.<MaintainStandard>query().lambda()
			 .in(MaintainStandard::getId, standardIdList));
		 if (ObjectUtil.isEmpty(standardIdList)) {
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(maintainStandardList, MaintainStandard::getId,
			 e -> MaintainStandardWrapper.build().entityVO(e));
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean add(MaintainPlanVO vo) {
		 MaintainPlan plan = Objects.requireNonNull(BeanUtil.copy(vo, MaintainPlan.class));
		 plan.setNo(BizCodeUtil.generate("MP")).setStatus(PlanStatusEnum.NO_START.getCode());
		 // 时间设置
		 switch (Objects.requireNonNull(PlanCycleEnum.getByCode(plan.getCycleType()))){
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(String.join(",", vo.getByWeekSet()));
				 break;
			 case MONTH:
				 plan.setExecuteTime(String.join(",", vo.getByMonthSet()));
				 break;
			 default:
		 }
		 boolean ret = this.save(plan);
		 // 所选标准
		 if(Func.isNotEmpty(vo.getStandardIds())){
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<MaintainPlanEquipment> planEquipmentList =
				 Func.toLongList(vo.getStandardIds()).stream().map(standardId -> {
				 MaintainPlanEquipment planEquipment = new MaintainPlanEquipment();
				 MaintainStandard maintainStandard = maintainStandardMapper.selectById(standardId);
				 if(maintainStandard == null){
					 throw new ServiceException("所选标准不存在！");
				 }
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(maintainStandard.getEquipmentId())
					 .setMonitorId(maintainStandard.getMonitorId()).setStandardId(standardId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 return ret;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean modify(MaintainPlanVO vo) {
		 MaintainPlan plan = this.getById(vo.getId());
		 if(plan == null){
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 BeanUtil.copy(vo, plan);
		 // 时间设置
		 switch (Objects.requireNonNull(PlanCycleEnum.getByCode(vo.getCycleType()))){
			 case DAY:
				 plan.setExecuteTime(JSONUtil.toJsonStr(vo.getByDaySet()));
				 break;
			 case WEEK:
				 plan.setExecuteTime(String.join(",", vo.getByWeekSet()));
				 break;
			 case MONTH:
				 plan.setExecuteTime(String.join(",", vo.getByMonthSet()));
				 break;
			 default:
		 }
		 planEquipmentService.remove(Wrappers.<MaintainPlanEquipment>query().lambda()
			 .eq(MaintainPlanEquipment::getPlanId, plan.getId()));
		 // 所选标准
		 if(Func.isNotEmpty(vo.getStandardIds())){
			 AtomicReference<Integer> sort = new AtomicReference<>(1);
			 List<MaintainPlanEquipment> planEquipmentList =
				 Func.toLongList(vo.getStandardIds()).stream().map(standardId -> {
				 MaintainPlanEquipment planEquipment = new MaintainPlanEquipment();
				 MaintainStandard maintainStandard = maintainStandardMapper.selectById(standardId);
				 if(maintainStandard == null){
					 throw new ServiceException("所选标准不存在！");
				 }
				 planEquipment.setPlanId(plan.getId()).setEquipmentId(maintainStandard.getEquipmentId())
					 .setMonitorId(maintainStandard.getMonitorId()).setStandardId(standardId)
					 .setSort(sort.getAndSet(sort.get() + 1));
				 return planEquipment;
			 }).collect(Collectors.toList());
			 planEquipmentService.saveBatch(planEquipmentList);
		 }
		 if(Func.isEmpty(vo.getExecuteUser())){
			 plan.setExecuteUser(null);
		 }
		 return this.updateById(plan);
	 }

	 @Override
	 public List<MaintainPlanDTO> getTheDayPlans(Date currentDate) {
		 return baseMapper.getTheDayPlans(currentDate);
	 }

	 @Override
	 public List<MaintainPlanExcel> exportPlan(MaintainPlanVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setQueryStartDate(vo.getQueryStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setQueryEndDate(vo.getQueryEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<MaintainPlan> list = baseMapper.selectList(Wrappers.<MaintainPlan>query().lambda()
			 .eq(Func.isNotEmpty(vo.getCycleType()), MaintainPlan::getCycleType, vo.getCycleType())
			 .eq(Func.isNotEmpty(vo.getExecuteDept()), MaintainPlan::getExecuteDept, vo.getExecuteDept())
			 .like(Func.isNotEmpty(vo.getName()), MaintainPlan::getName, vo.getName())
			 .ge(Func.isNotEmpty(vo.getQueryStartDate()), MaintainPlan::getStartDate, vo.getQueryStartDate())
			 .le(Func.isNotEmpty(vo.getQueryEndDate()), MaintainPlan::getStartDate, vo.getQueryEndDate())
			 .orderByDesc(MaintainPlan::getCreateTime));
		 if (Func.isNotEmpty(list)) {
			 List<MaintainPlanDTO> planList = MaintainPlanWrapper.build().listDTO(list);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return planList.stream().map(plan -> {
				 MaintainPlanExcel planExcel = Objects.requireNonNull(BeanUtil.copy(plan, MaintainPlanExcel.class));
				 if (Func.isNotEmpty(plan.getStartDate())) {
					 planExcel.setStartDateStr(DateUtil.formatDate(plan.getStartDate()));
				 }
				 if (Func.isNotEmpty(plan.getEndDate())) {
					 planExcel.setEndDateStr(DateUtil.formatDate(plan.getEndDate()));
				 }
				 planExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 planExcel.setEquipmentCount(Func.toStr(planEquipmentService.count(Wrappers.<MaintainPlanEquipment>query().lambda()
					 .eq(MaintainPlanEquipment::getPlanId, plan.getId()))));
				 return planExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean restart(List<Long> ids) {
		 ids.forEach(id -> {
			 MaintainPlan plan = this.getById(id);
			 if(plan == null){
				 throw new ServiceException(ResultCode.FAILURE);
			 }
			 plan.setStatus(PlanStatusEnum.IN_PROGRESS.getCode());
			 // 判断重启时间是否超过计划截止日期
			 Date currentDate =
				 DateUtil.parse(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE), DateUtil.PATTERN_DATE);
			 if(currentDate.after(plan.getEndDate())){
				 plan.setStatus(PlanStatusEnum.IS_TERMINATED.getCode());
			 }
			 this.updateById(plan);
		 });
		 return true;
	 }


 }
