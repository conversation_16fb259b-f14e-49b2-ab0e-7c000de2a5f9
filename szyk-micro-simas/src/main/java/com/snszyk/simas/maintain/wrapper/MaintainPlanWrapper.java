/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.wrapper;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
import com.snszyk.simas.maintain.entity.MaintainPlan;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.common.enums.PlanStatusEnum;
import com.snszyk.simas.common.enums.WeekDateEnum;
import com.snszyk.simas.common.vo.ByDaySetVO;
import com.snszyk.simas.maintain.vo.MaintainPlanVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.enums.DictBizEnum;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备保养计划表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public class MaintainPlanWrapper extends BaseEntityWrapper<MaintainPlan, MaintainPlanVO> {

	public static MaintainPlanWrapper build() {
		return new MaintainPlanWrapper();
 	}

	@Override
	public MaintainPlanVO entityVO(MaintainPlan maintainPlan) {
		MaintainPlanVO maintainPlanVO = Objects.requireNonNull(BeanUtil.copy(maintainPlan, MaintainPlanVO.class));

		//User createUser = UserCache.getUser(maintainPlan.getCreateUser());
		//User updateUser = UserCache.getUser(maintainPlan.getUpdateUser());
		//maintainPlanVO.setCreateUserName(createUser.getName());
		//maintainPlanVO.setUpdateUserName(updateUser.getName());

		return maintainPlanVO;
	}

	public MaintainPlanDTO entityDTO(MaintainPlan maintainPlan) {
		MaintainPlanDTO maintainPlanDTO = Objects.requireNonNull(BeanUtil.copy(maintainPlan, MaintainPlanDTO.class));
		maintainPlanDTO.setCycleTypeName(DictBizCache.getValue(DictBizEnum.PLAN_CYCLE, maintainPlan.getCycleType()))
			.setStatusName(PlanStatusEnum.getByCode(maintainPlan.getStatus()).getName());
		if(Func.isNotEmpty(maintainPlan.getExecuteDept())){
			Dept executeDept = SysCache.getDept(maintainPlan.getExecuteDept());
			if(Func.isNotEmpty(executeDept)){
				maintainPlanDTO.setExecuteDeptName(executeDept.getDeptName());
			}
		}
		if(Func.isNotEmpty(maintainPlan.getExecuteUser())){
			User executeUser = UserCache.getUser(maintainPlan.getExecuteUser());
			if(Func.isNotEmpty(executeUser)){
				maintainPlanDTO.setExecuteUserName(executeUser.getRealName());
			}
		}
		// 时间设置
		switch (PlanCycleEnum.getByCode(maintainPlan.getCycleType())){
			case DAY:
				maintainPlanDTO.setByDaySet(JSONUtil.toList(maintainPlan.getExecuteTime(), ByDaySetVO.class));
				break;
			case WEEK:
				maintainPlanDTO.setByWeekSet(Func.toStrList(maintainPlan.getExecuteTime()));
				maintainPlanDTO.setExecuteTimeStr(Func.toStrList(maintainPlan.getExecuteTime()).stream()
					.map(date -> WeekDateEnum.getByCode(date).getName()).collect(Collectors.joining(",")));
				break;
			case MONTH:
				maintainPlanDTO.setByMonthSet(Func.toStrList(maintainPlan.getExecuteTime()))
					.setExecuteTimeStr(maintainPlan.getExecuteTime());
				break;
			default:
		}
		User createUser = UserCache.getUser(maintainPlan.getCreateUser());
		if(Func.isNotEmpty(createUser)){
			maintainPlanDTO.setCreateUserName(createUser.getRealName());
		}
		User updateUser = UserCache.getUser(maintainPlan.getUpdateUser());
		if(Func.isNotEmpty(updateUser)){
			maintainPlanDTO.setUpdateUserName(updateUser.getRealName());
		}
		return maintainPlanDTO;
	}

	public List<MaintainPlanDTO> listDTO(List<MaintainPlan> list) {
		return list.stream().map(this::entityDTO).collect(Collectors.toList());
	}

}
