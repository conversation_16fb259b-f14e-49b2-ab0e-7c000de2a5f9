/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.simas.maintain.entity.MaintainRecord;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;

/**
 * 设备保养记录表 服务类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
public interface IMaintainRecordService extends IService<MaintainRecord> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param maintainRecord
	 * @return
	 */
	IPage<MaintainRecordVO> selectMaintainRecordPage(IPage<MaintainRecordVO> page, MaintainRecordVO maintainRecord);

}
