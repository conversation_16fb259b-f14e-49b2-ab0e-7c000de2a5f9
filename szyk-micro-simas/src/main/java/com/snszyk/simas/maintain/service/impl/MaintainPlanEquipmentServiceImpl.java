/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
import com.snszyk.simas.maintain.mapper.MaintainPlanEquipmentMapper;
import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
import com.snszyk.simas.maintain.vo.MaintainPlanEquipmentVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备保养计划关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Service
public class MaintainPlanEquipmentServiceImpl extends ServiceImpl<MaintainPlanEquipmentMapper, MaintainPlanEquipment> implements IMaintainPlanEquipmentService {

	@Override
	public IPage<MaintainPlanEquipmentVO> page(IPage<MaintainPlanEquipmentVO> page, MaintainPlanEquipmentVO vo) {
		List<MaintainPlanEquipmentVO> list = baseMapper.page(page, vo);
		if(Func.isNotEmpty(list)){
			list.forEach(record -> {
				if(Func.isNotEmpty(record.getNeedConfirm())){
					record.setNeedConfirmName(record.getNeedConfirm() == 0 ? "否" : "是");
				}
			});
		}
		return page.setRecords(list);
	}


}
