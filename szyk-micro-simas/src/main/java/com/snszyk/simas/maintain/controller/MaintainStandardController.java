 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.controller;

 import com.alibaba.excel.EasyExcel;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.core.boot.ctrl.SzykController;
 import com.snszyk.core.mp.support.Condition;
 import com.snszyk.core.mp.support.Query;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.simas.common.enums.OperateTypeEnum;
 import com.snszyk.simas.common.enums.SystemModuleEnum;
 import com.snszyk.simas.common.excel.listener.MaintainStandardListener;
 import com.snszyk.simas.common.excel.support.EasyExcelUtil;
 import com.snszyk.simas.common.excel.template.BaseStandardTemplate;
 import com.snszyk.simas.common.excel.template.MaintainStandardExcelTemplate;
 import com.snszyk.simas.common.service.IOperateLogService;
 import com.snszyk.simas.common.service.logic.ImportDataValidLogicService;
 import com.snszyk.simas.common.vo.EquipmentStandardVO;
 import com.snszyk.simas.common.vo.OperateLogVO;
 import com.snszyk.simas.maintain.service.IMaintainStandardService;
 import io.swagger.annotations.*;
 import lombok.AllArgsConstructor;
 import org.springframework.web.bind.annotation.*;
 import org.springframework.web.multipart.MultipartFile;
 import springfox.documentation.annotations.ApiIgnore;

 import javax.servlet.http.HttpServletResponse;
 import javax.validation.Valid;
 import java.io.IOException;
 import java.util.ArrayList;
 import java.util.LinkedHashMap;
 import java.util.List;
 import java.util.Map;
 import java.util.stream.Collectors;

 /**
  * 设备保养标准表 控制器
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @RestController
 @AllArgsConstructor
 @RequestMapping("/maintain-standard")
 @Api(value = "设备保养标准表", tags = "设备保养标准表接口")
 public class MaintainStandardController extends SzykController {

	 private final IMaintainStandardService maintainStandardService;
	 private final IOperateLogService operateLogService;
	 private ImportDataValidLogicService importDataValidLogicService;

	 /**
	  * 详情
	  */
	 @GetMapping("/detail")
	 @ApiOperationSupport(order = 1)
	 @ApiOperation(value = "详情", notes = "传入equipmentId")
	 public R<EquipmentStandardVO> detail(Long equipmentId) {
		 OperateLogVO operateLog = new OperateLogVO(equipmentId, SystemModuleEnum.MAINTAIN_STANDARD, OperateTypeEnum.RETRIEVE);
		 operateLogService.submit(operateLog);
		 return R.data(maintainStandardService.detail(equipmentId));
	 }

	 /**
	  * 分页 设备保养标准表
	  */
	 @GetMapping("/page")
	 @ApiImplicitParams({
		 @ApiImplicitParam(name = "useDept", value = "使用部门id", required = true, paramType = "query", dataType = "long")
	 })
	 @ApiOperationSupport(order = 2)
	 @ApiOperation(value = "分页", notes = "传入useDept")
	 public R<IPage<DeviceAccountVO>> page(@ApiIgnore DeviceAccountVO deviceAccount, Query query) {
		 return R.data(maintainStandardService.page(Condition.getPage(query), deviceAccount));
	 }

	 /**
	  * 新增或修改 设备保养标准表
	  */
	 @PostMapping("/submit")
	 @ApiOperationSupport(order = 3)
	 @ApiOperation(value = "新增或修改", notes = "传入equipmentStandard")
	 public R submit(@Valid @RequestBody EquipmentStandardVO equipmentStandard) {
		 return R.status(maintainStandardService.submit(equipmentStandard));
	 }

	 /**
	  * 清空标准 设备保养标准表
	  */
	 @PostMapping("/clear")
	 @ApiOperationSupport(order = 4)
	 @ApiOperation(value = "清空标准", notes = "传入equipmentId")
	 public R clear(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId) {
		 return R.status(maintainStandardService.clear(equipmentId));
	 }

	 @GetMapping("/export-template")
	 @ApiOperationSupport(order = 5)
	 @ApiOperation(value = "下载导入模版")
	 public void exportTemplate(HttpServletResponse response, @RequestParam(required = false) String deptId) {
		 List<BaseStandardTemplate> list = maintainStandardService.generateExcelData(deptId);
		 Map<String, Long> map = list.stream().collect(Collectors.groupingBy(BaseStandardTemplate::getCode, Collectors.counting()));
		 LinkedHashMap<String, Integer> mergeMap = new LinkedHashMap<>();
		 List<MaintainStandardExcelTemplate> templateList = new ArrayList<>();
		 for (BaseStandardTemplate account : list) {
			 templateList.add(BeanUtil.copyProperties(account, MaintainStandardExcelTemplate.class));
			 if (!mergeMap.containsKey(account.getCode())) {
				 mergeMap.put(account.getCode(), map.get(account.getCode()).intValue());
			 }
		 }
		 EasyExcelUtil.writerTwo(response, "保养标准导入模板", "保养标准", templateList, MaintainStandardExcelTemplate.class, mergeMap, new int[]{0, 1, 2}, 2);
	 }

	 @PostMapping("/import-data")
	 @ApiOperationSupport(order = 6)
	 @ApiOperation(value = "导入标准", notes = "传入excel")
	 public R importData(MultipartFile file) {
		 MaintainStandardListener listener = new MaintainStandardListener(2, maintainStandardService, importDataValidLogicService);
		 try {
			 //监听器读取内容
			 EasyExcel.read(file.getInputStream(), MaintainStandardExcelTemplate.class, listener)
				 .sheet()
				 // 忽略表头行数
				 .headRowNumber(2)
				 .doRead();
		 } catch (IOException e) {
			 e.printStackTrace();
		 }
		 List<String> errorMsg = listener.getFailReasonList();
		 if (Func.isNotEmpty(errorMsg)) {
			 return R.data(errorMsg, "导入异常！");
		 }
		 return R.success("导入成功");
	 }


 }
