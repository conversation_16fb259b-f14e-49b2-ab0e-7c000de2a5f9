<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.maintain.mapper.MaintainOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="maintainOrderResultMap" type="com.snszyk.simas.maintain.entity.MaintainOrder">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="standard_info" property="standardInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="material" property="material"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="submit_time" property="submitTime"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="is_need_approval" property="isNeedApproval"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="maintainOrderDTOResultMap" type="com.snszyk.simas.maintain.dto.MaintainOrderDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="plan_id" property="planId"/>
        <result column="plan_info" property="planInfo"/>
        <result column="standard_info" property="standardInfo"/>
        <result column="is_abnormal" property="isAbnormal"/>
        <result column="is_expired" property="isExpired"/>
        <result column="material" property="material"/>
        <result column="remark" property="remark"/>
        <result column="submit_time" property="submitTime"/>
        <result column="approval_user" property="approvalUser"/>
        <result column="reject_reason" property="rejectReason"/>
        <result column="is_need_approval" property="isNeedApproval"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="equipment_category" property="equipmentCategory"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_sn" property="equipmentSn"/>
    </resultMap>


    <select id="page" resultType="com.snszyk.simas.maintain.dto.MaintainOrderDTO">
        select o.*,e.`name` as equipment_name,e.`sn` as equipment_sn
        from simas_maintain_order o left join device_account e on o.equipment_id = e.id
        left join simas_maintain_plan p on o.plan_id = p.id and p.is_deleted = 0
        where e.is_deleted = 0
        <if test="maintainOrder.onlyQueryExecuteUser!=null">
            <choose>
                <when test="maintainOrder.onlyQueryExecuteUser == 0">
                    and o.execute_user is null
                </when>
                <otherwise>
                    and o.execute_user = #{maintainOrder.onlyQueryExecuteUser}
                </otherwise>
            </choose>
        </if>
        <if test="maintainOrder.executeDept != null">
            <choose>
                <when test="maintainOrder.executeDept==0">
                    and o.execute_dept is null
                </when>
                <otherwise>
                    and o.execute_dept = #{maintainOrder.executeDept}
                </otherwise>
            </choose>
        </if>
        <if test="maintainOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{maintainOrder.executeUser})
        </if>
        <if test="maintainOrder.no != null and maintainOrder.no != ''">
            and o.`no` like concat('%',#{maintainOrder.no},'%')
        </if>
        <if test="maintainOrder.orderName != null and maintainOrder.orderName != ''">
            and p.name like concat('%',#{maintainOrder.orderName},'%')
        </if>
        <if test="maintainOrder.planId != null">
            and o.plan_id=#{maintainOrder.planId}
        </if>
        <if test="maintainOrder.equipmentId != null">
            and o.equipment_id = #{maintainOrder.equipmentId}
        </if>
        <if test="maintainOrder.equipmentCode != null and maintainOrder.equipmentCode != ''">
            and o.equipment_code = #{maintainOrder.equipmentCode}
        </if>
        <if test="maintainOrder.status != null">
            and o.status = #{maintainOrder.status}
        </if>
        <if test="maintainOrder.keywords != null and maintainOrder.keywords != ''">
            and (o.`no` like concat('%',#{maintainOrder.keywords},'%') or
            e.`sn` like concat('%',#{maintainOrder.keywords},'%') or e.`code` = #{maintainOrder.keywords})
        </if>
        <if test="maintainOrder.startDate != null and maintainOrder.startDate != ''">
            and o.start_time <![CDATA[ >= ]]> #{maintainOrder.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="maintainOrder.endDate != null and maintainOrder.endDate != ''">
            and o.start_time <![CDATA[ <= ]]> #{maintainOrder.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="maintainOrder.neStatus != null">
            and o.status <![CDATA[ <> ]]> #{maintainOrder.neStatus}
        </if>
        <if test="maintainOrder.statusList != null and maintainOrder.statusList.size() > 0">
            and o.status in
            <foreach collection="maintainOrder.statusList" item="ids" index="index" open="(" close=")"
                     separator=",">
                #{ids}
            </foreach>
        </if>
        order by o.id desc
    </select>

    <select id="timeoutPage" resultMap="maintainOrderResultMap">
        select *
        from simas_maintain_order
        where execute_dept = #{maintainOrder.executeDept}
          and (execute_user is null or execute_user = #{maintainOrder.executeUser})
          and (status = 1 or status = 6)
        <![CDATA[ AND NOW() >= DATE_SUB(end_time, INTERVAL #{maintainOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < end_time ]]>
        order by id desc
    </select>

    <select id="expireSoonCount" resultType="java.lang.Integer">
        select count(*)
        from simas_maintain_order o
        LEFT JOIN device_account e ON o.equipment_id = e.id
        where e.is_deleted = 0
        <if test="maintainOrder.executeDept != null">
            and o.execute_dept = #{maintainOrder.executeDept}
        </if>
        <if test="maintainOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{maintainOrder.executeUser})
        </if>
        and (o.status = 1 or o.status = 6)
        <![CDATA[ AND NOW() >= DATE_SUB(o.end_time, INTERVAL #{maintainOrder.timeInterval} HOUR) ]]>
        <![CDATA[ AND NOW() < o.end_time
        ]]>
    </select>

    <select id="maintainOrderStatistics" resultMap="maintainOrderResultMap">
        SELECT * FROM simas_maintain_order WHERE 1=1
        <if test="queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
    </select>

    <select id="statisticalReport" resultMap="maintainOrderResultMap">
        SELECT * FROM simas_maintain_order WHERE 1=1
        <if test="search.queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.deptIdList != null">
            and execute_dept in
            <foreach collection="search.deptIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <if test="search.status!=null">
            and status = #{search.status}
        </if>
        order by id desc
    </select>

    <select id="queryList" resultMap="maintainOrderDTOResultMap">
        select o.*, e.name as equipment_name, e.category_id as equipment_category from simas_maintain_order o
        left join device_account e on o.equipment_id = e.id
        where e.is_deleted = 0
        <if test="maintainOrder.executeDept != null">
            and o.execute_dept = #{maintainOrder.executeDept}
        </if>
        <if test="maintainOrder.executeUser != null">
            and (o.execute_user is null or o.execute_user = #{maintainOrder.executeUser})
        </if>
        <if test="maintainOrder.no != null and maintainOrder.no != ''">
            and o.`no` like concat('%',#{maintainOrder.no},'%')
        </if>
        <if test="maintainOrder.orderName != null and maintainOrder.orderName != ''">
            and o.plan_info like concat('%',#{maintainOrder.orderName},'%')
        </if>
        <if test="maintainOrder.equipmentId != null">
            and o.equipment_id = #{maintainOrder.equipmentId}
        </if>
        <if test="maintainOrder.equipmentCode != null and maintainOrder.equipmentCode != ''">
            and o.equipment_code = #{maintainOrder.equipmentCode}
        </if>
        <if test="maintainOrder.status != null">
            and o.status = #{maintainOrder.status}
        </if>
    </select>

    <select id="statisticsByEquipment" resultType="com.snszyk.simas.common.dto.EquipmentStatisticsDTO">
        SELECT equipment_id as id, status, is_abnormal FROM `simas_maintain_order` where 1=1
        <if test="search.queryDate == 0">
            AND create_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND create_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND create_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(create_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        <!--        SELECT equipment_id as id, count(*) as count FROM `simas_maintain_order` where 1=1-->
        <!--        <if test="search.queryDate == 1">-->
        <!--            AND start_time >= CURDATE() - INTERVAL 30 DAY-->
        <!--        </if>-->
        <!--        <if test="search.queryDate == 2">-->
        <!--            AND start_time >= CURDATE() - INTERVAL 7 DAY-->
        <!--        </if>-->
        <!--        <if test="search.queryDate == 3">-->
        <!--            AND TO_DAYS(start_time) = TO_DAYS(NOW())-->
        <!--        </if>-->
        <!--        <if test="search.startDate != null and search.startDate != ''">-->
        <!--            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}-->
        <!--        </if>-->
        <!--        <if test="search.endDate != null and search.endDate != ''">-->
        <!--            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}-->
        <!--        </if>-->
        <!--        <if test="search.equipmentIds != null">-->
        <!--            and equipment_id in-->
        <!--            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">-->
        <!--                #{ids}-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        <if test="search.status != null">-->
        <!--            AND status = #{search.status}-->
        <!--        </if>-->
        <!--        group by equipment_id-->
    </select>
    <select id="overdueList" resultType="com.snszyk.simas.common.dto.BigScreenMessageDTO">
        SELECT t.no as orderNo, e.name as equipmentName, t.equipment_id
        from simas_maintain_order t
        left join device_account e on t.equipment_id = e.id and e.is_deleted = 0
        where t.status = 3
        <if test="tenantId != null and tenantId !=''">
            and t.tenant_id = #{tenantId}
        </if>
        order by t.create_time desc

    </select>
    <select id="maintenance30day" resultType="com.snszyk.core.tool.support.Kv">
        select count(1) as count,t.status as status
        from simas_maintain_order t
        where t.create_time >= CURDATE() - INTERVAL 30 DAY
    </select>

    <select id="coverList30day" resultType="com.snszyk.simas.maintain.dto.MaintainOrderDTO">

        select t3.id,
        t3.tenant_id,
        t3.no,
        t3.equipment_id,
        t3.status,
        t3.create_time,
        t3.execute_dept,
        t.id as analysis_dept_id
        from szyk_dept t
        left join szyk_dept t2 on find_in_set(t.id, t2.ancestors) or t.id = t2.id and t2.is_deleted = 0
        left join simas_maintain_order t3
        on t2.id = t3.execute_dept and to_days(t3.create_time) >= to_days(now()) - 30
        where t.is_deleted = 0
        and t3.equipment_id is not null
        and t.parent_id!=0
        <if test="tenantId != null and tenantId !=''">
            and t.tenant_id = #{tenantId}
        </if>

    </select>
    <select id="handleMaintainCount" resultType="java.lang.Integer"
            parameterType="com.snszyk.simas.maintain.vo.MaintainOrderVO">
        select count(1) from simas_maintain_order o
        <where>
            <if test="maintainOrder.queryAuthRole != null and maintainOrder.queryAuthRole == 1">
                and (o.execute_user = #{maintainOrder.executeUser} or ( o.execute_dept = #{maintainOrder.executeDept}
                and o.execute_user is null))
            </if>
            <if test="maintainOrder.queryAuthRole != null and maintainOrder.queryAuthRole == 2">
                and o.execute_dept = #{maintainOrder.executeDept} and o.execute_user is null
            </if>
            <if test="maintainOrder.statusList != null and maintainOrder.statusList.size() > 0">
                and o.status in
                <foreach collection="maintainOrder.statusList" item="ids" index="index" open="(" close=")"
                         separator=",">
                    #{ids}
                </foreach>
            </if>
            <if test="maintainOrder.tenantId != null and maintainOrder.tenantId !=''">
                and o.tenant_id = #{maintainOrder.tenantId}
            </if>
            <if test="maintainOrder.startDate != null and maintainOrder.startDate != ''">
                and o.create_time <![CDATA[ >= ]]> #{maintainOrder.startDate, jdbcType=TIMESTAMP}
            </if>
            <if test="maintainOrder.endDate != null and maintainOrder.endDate != ''">
                and o.create_time <![CDATA[ <= ]]> #{maintainOrder.endDate, jdbcType=TIMESTAMP}
            </if>


        </where>
    </select>

    <select id="equipmentStatisticsOfOrder" resultMap="maintainOrderResultMap">
        SELECT * FROM simas_maintain_order where 1=1
        <if test="search.queryDate == 0">
            AND start_time >= CURDATE() - INTERVAL 1 YEAR
        </if>
        <if test="search.queryDate == 1">
            AND start_time >= CURDATE() - INTERVAL 30 DAY
        </if>
        <if test="search.queryDate == 2">
            AND start_time >= CURDATE() - INTERVAL 7 DAY
        </if>
        <if test="search.queryDate == 3">
            AND TO_DAYS(start_time) = TO_DAYS(NOW())
        </if>
        <if test="search.startDate != null and search.startDate != ''">
            and start_time <![CDATA[ >= ]]> #{search.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.endDate != null and search.endDate != ''">
            and start_time <![CDATA[ <= ]]> #{search.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="search.equipmentIds != null">
            and equipment_id in
            <foreach collection="search.equipmentIds" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
    </select>

</mapper>
