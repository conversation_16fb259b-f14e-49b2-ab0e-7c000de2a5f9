<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.maintain.mapper.MaintainStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="maintainStandardResultMap" type="com.snszyk.simas.maintain.entity.MaintainStandard">
        <id column="id" property="id"/>
        <id column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="standard" property="standard"/>
        <result column="method" property="method"/>
        <result column="need_confirm" property="needConfirm"/>
        <result column="remark" property="remark"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="equipment_sn" property="equipmentSn"/>
    </resultMap>

    <select id="page" resultMap="maintainStandardResultMap">
        SELECT s.*, d.`name` as equipment_name, d.`code` as equipment_code, d.sn as equipment_sn
        FROM simas_maintain_standard s
        LEFT JOIN device_account d ON s.equipment_id = d.id
        WHERE d.is_deleted = 0 AND s.is_deleted = 0 AND s.`status` = 1
        <if test="maintainStandard.equipmentId != null">
            and s.equipment_id = #{maintainStandard.equipmentId}
        </if>
        <if test="maintainStandard.monitorId != null">
            and s.monitor_id = #{maintainStandard.monitorId}
        </if>
        <if test="maintainStandard.standard != null and maintainStandard.standard != ''">
            and s.`standard` like concat('%', #{maintainStandard.standard}, '%')
        </if>
        <if test="maintainStandard.equipmentName != null and maintainStandard.equipmentName != ''">
            and d.`name` like concat('%', #{maintainStandard.equipmentName}, '%')
        </if>
        <if test="maintainStandard.equipmentCode != null and maintainStandard.equipmentCode != ''">
            and d.`code` like concat('%', #{maintainStandard.equipmentCode}, '%')
        </if>
        <if test="maintainStandard.equipmentSn != null and maintainStandard.equipmentSn != ''">
            and d.sn like concat('%', #{maintainStandard.equipmentSn}, '%')
        </if>
        <if test="maintainStandard.equipmentIdList != null">
            and equipment_id in
            <foreach collection="maintainStandard.equipmentIdList" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
        </if>
        order by create_time desc
    </select>

    <select id="generateExcelData" resultType="com.snszyk.simas.common.excel.template.BaseStandardTemplate">
        select d.`code` as code,d.`name` as name,d.`sn` as sn,m.`name` as monitorName
        from device_monitor m
        left join device_account d on m.device_id = d.id
        <where>
            d.is_deleted = 0
            <if test="deptIds != null">
                and d.use_dept in
                <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
        order by d.`code`, m.sort
    </select>


</mapper>
