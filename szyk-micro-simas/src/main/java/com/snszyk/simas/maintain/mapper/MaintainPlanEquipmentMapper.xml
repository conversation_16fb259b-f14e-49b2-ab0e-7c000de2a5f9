<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.maintain.mapper.MaintainPlanEquipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="maintainPlanEquipmentResultMap" type="com.snszyk.simas.maintain.entity.MaintainPlanEquipment">
        <id column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="standard_id" property="standardId"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="maintainPlanEquipmentVOResultMap" type="com.snszyk.simas.maintain.vo.MaintainPlanEquipmentVO">
        <id column="id" property="id"/>
        <result column="plan_id" property="planId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="standard_id" property="standardId"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="equipment_code" property="equipmentCode"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="standard" property="standard"/>
        <result column="need_confirm" property="needConfirm"/>
    </resultMap>


    <select id="page" resultMap="maintainPlanEquipmentVOResultMap">
        SELECT pe.*,e.`name` as equipment_name,e.`code` as equipment_code,m.`name` as monitor_name,s.method,s.standard,s.need_confirm
        FROM simas_maintain_plan_equipment pe
        LEFT JOIN device_account e ON pe.equipment_id = e.id
        LEFT JOIN device_monitor m ON pe.monitor_id = m.id
        LEFT JOIN simas_maintain_standard s ON pe.standard_id = s.id
        WHERE 1 = 1
        <if test="maintainPlanEquipment.planId != null">
            and plan_id = #{maintainPlanEquipment.planId}
        </if>
        <if test="maintainPlanEquipment.equipmentId != null">
            and equipment_id = #{maintainPlanEquipment.equipmentId}
        </if>
        <if test="maintainPlanEquipment.monitorId != null">
            and monitor_id = #{maintainPlanEquipment.monitorId}
        </if>
        <if test="maintainPlanEquipment.standardId != null">
            and standard_id = #{maintainPlanEquipment.standardId}
        </if>
    </select>

</mapper>
