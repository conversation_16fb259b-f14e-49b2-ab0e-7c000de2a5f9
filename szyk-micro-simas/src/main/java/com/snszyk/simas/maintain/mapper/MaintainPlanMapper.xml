<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.simas.maintain.mapper.MaintainPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="maintainPlanResultMap" type="com.snszyk.simas.maintain.entity.MaintainPlan">
        <result column="id" property="id"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>


    <resultMap id="maintainPlanDTOResultMap" type="com.snszyk.simas.maintain.dto.MaintainPlanDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="no" property="no"/>
        <result column="name" property="name"/>
        <result column="cycle_type" property="cycleType"/>
        <result column="cycle_interval" property="cycleInterval"/>
        <result column="execute_dept" property="executeDept"/>
        <result column="execute_user" property="executeUser"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="execute_time" property="executeTime"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="create_dept" property="createDept"/>
    </resultMap>


    <select id="page" resultMap="maintainPlanDTOResultMap">
        select * from simas_maintain_plan where is_deleted = 0
        <if test="maintainPlan.name != null and maintainPlan.name != ''">
            and `name` like concat('%', #{maintainPlan.name}, '%')
        </if>
        <if test="maintainPlan.keywords!=null and maintainPlan.keywords != ''">
            AND (`no` like concat('%',#{maintainPlan.keywords},'%') or `name` like
            concat('%',#{maintainPlan.keywords},'%'))
        </if>
        <if test="maintainPlan.cycleType != null and maintainPlan.cycleType != ''">
            and cycle_type = #{maintainPlan.cycleType}
        </if>
        <if test="maintainPlan.executeDept != null and maintainPlan.executeDept != ''">
            and execute_dept = #{maintainPlan.executeDept}
        </if>
        <if test="maintainPlan.status != null">
            and status = #{maintainPlan.status}
        </if>
        <if test="maintainPlan.queryStartDate != null and maintainPlan.queryStartDate != ''">
            and start_date <![CDATA[ >= ]]> #{maintainPlan.queryStartDate, jdbcType=TIMESTAMP}
        </if>
        <if test="maintainPlan.queryEndDate != null and maintainPlan.queryEndDate != ''">
            and start_date <![CDATA[ <= ]]> #{maintainPlan.queryEndDate, jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </select>

    <!--查询当天点检计划-->
    <select id="getTheDayPlans" resultMap="maintainPlanDTOResultMap">
        SELECT
            p.id,
            p.tenant_id,
            p.`name`,
            p.`no`,
            p.cycle_type,
            p.cycle_interval,
            p.execute_dept,
            p.execute_user,
            p.start_date,
            p.end_date,
            p.execute_time,
            p.remark,
            p.`status`
        FROM
            simas_maintain_plan p
        WHERE p.is_deleted = 0
          AND(p.`status` = 0 OR p.`status` = 1)
          AND p.start_date <![CDATA[ <= ]]> #{currentDate, jdbcType=DATE}
          AND p.end_date <![CDATA[ >= ]]> #{currentDate, jdbcType=DATE}
    </select>

</mapper>
