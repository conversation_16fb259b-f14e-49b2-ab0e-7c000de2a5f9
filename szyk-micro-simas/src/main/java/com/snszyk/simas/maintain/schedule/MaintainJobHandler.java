 package com.snszyk.simas.maintain.schedule;

 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.utils.BeanUtil;
 import com.snszyk.core.tool.utils.DateUtil;
 import com.snszyk.core.tool.utils.Func;
 import com.snszyk.core.tool.utils.StringPool;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.service.ITimeoutRemindSetService;
 import com.snszyk.simas.common.util.ApprovalUtil;
 import com.snszyk.simas.common.vo.ByDaySetVO;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.entity.MaintainOrder;
 import com.snszyk.simas.maintain.entity.MaintainPlan;
 import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
 import com.snszyk.simas.maintain.entity.MaintainStandard;
 import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
 import com.snszyk.simas.maintain.mapper.MaintainPlanMapper;
 import com.snszyk.simas.maintain.mapper.MaintainStandardMapper;
 import com.snszyk.simas.maintain.service.IMaintainOrderService;
 import com.snszyk.simas.maintain.service.IMaintainPlanEquipmentService;
 import com.snszyk.simas.maintain.service.IMaintainPlanService;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import com.snszyk.simas.maintain.wrapper.MaintainStandardWrapper;
 import com.snszyk.simas.spare.schedule.SimasPlanSchedule;
 import com.xxl.job.core.biz.model.ReturnT;
 import com.xxl.job.core.handler.annotation.XxlJob;
 import com.xxl.job.core.log.XxlJobLogger;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Component;

 import java.math.BigDecimal;
 import java.util.*;
 import java.util.stream.Collectors;

 /**
  * XxlJob开发示例（Bean模式）
  * <p>
  * 开发步骤：
  * 1、在Spring Bean实例中，开发Job方法，方式格式要求为 "public ReturnT<String> execute(String param)"
  * 2、为Job方法添加注解 "@XxlJob(value="自定义jobhandler名称", init = "JobHandler初始化方法", destroy = "JobHandler销毁方法")"，注解value值对应的是调度中心新建任务的JobHandler属性的值。
  * 3、执行日志：需要通过 "XxlJobLogger.log" 打印执行日志；
  *
  * <AUTHOR>
  */
 @Slf4j
 @AllArgsConstructor
 @Component
 public class MaintainJobHandler {

	 private final IMaintainPlanService planService;
	 private final IMaintainOrderService orderService;
	 private final ITimeoutRemindSetService timeoutRemindSetService;
	 private final IDeviceAccountClient deviceAccountClient;
	 private final MaintainPlanMapper planMapper;
	 private final IMaintainPlanEquipmentService planEquipmentService;
	 private final MaintainStandardMapper maintainStandardMapper;
	 private final MaintainOrderMapper maintainOrderMapper;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	 private static final int DAY_30 = 30;
	 private static final int MONTH_2 = 2;
	 private static final int FEBRUARY_DAY = 28;

	 /**
	  * 日检、周检、月检（每日07:00进行）
	  *
	  * @return void
	  * <AUTHOR>
	  * @date 2025/03/30 09:16
	  */
	 @XxlJob("generateMaintainOrdersJobHandler")
	 public ReturnT<String> generateOrdersJobHandler(String param)  {
		 XxlJobLogger.log("################保养任务（日检、周检、月检）生成-START-################");
		 List<MaintainPlanDTO> planList = planService.getTheDayPlans(DateUtil.now());
		 List<MaintainPlanDTO> executePlanList = new ArrayList<>();
		 if (Func.isNotEmpty(planList)) {
			 for (MaintainPlanDTO plan : planList) {
				 // 生成点检任务时间判断
				 String cycleType = plan.getCycleType();
				 Integer cycleInterval = plan.getCycleInterval();
				 String cycleExecuteTimeStr = plan.getExecuteTime();
				 boolean isExecuted;
				 // 确认当前计划第一次生成工单的日期
				 MaintainOrder order = orderService.getOne(Wrappers.<MaintainOrder>query().lambda()
					 .eq(MaintainOrder::getPlanId, plan.getId()).orderByAsc(MaintainOrder::getCreateTime).last("limit 1"));
				 long actualStartDate = plan.getStartDate().getTime();
				 if(Func.isNotEmpty(order)){
					 actualStartDate = order.getCreateTime().getTime();
				 }
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(cycleType)) {
					 isExecuted = SimasPlanSchedule.executeDay(actualStartDate, plan.getEndDate().getTime(), cycleInterval);
					 if (isExecuted) {
						 log.info("保养任务——日检工单生成：{}", cycleExecuteTimeStr);
						 executePlanList.add(plan);
					 }
				 }
				 if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(cycleType)) {
					 List<String> list = Func.toStrList(cycleExecuteTimeStr);
					 for (String cycleExecuteTime : list) {
						 isExecuted = SimasPlanSchedule.executeWeek(plan.getStartDate().getTime(),
							 plan.getEndDate().getTime(), cycleInterval, cycleExecuteTime);
						 if (isExecuted) {
							 log.info("保养任务——周检工单生成：{}", cycleExecuteTimeStr);
							 executePlanList.add(plan);
						 }
					 }
				 }
				 if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(cycleType)) {
					 List<String> list = Func.toStrList(cycleExecuteTimeStr);
					 for (String cycleExecuteTime : list) {
						 isExecuted = SimasPlanSchedule.executeMonth(actualStartDate, plan.getEndDate().getTime(),
							 cycleInterval, cycleExecuteTime);
						 if (isExecuted) {
							 log.info("保养任务——月检工单生成：{}", cycleExecuteTimeStr);
							 executePlanList.add(plan);
						 }
					 }
				 }
			 }
		 }
		 if (Func.isNotEmpty(executePlanList)) {
			 log.info("需要执行的计划：{}", executePlanList.stream().map(dto ->
				 dto.getId() + StringPool.COLON + dto.getNo()).collect(Collectors.toList()));
			 this.generateOrders(executePlanList);
		 }
		 XxlJobLogger.log("################保养任务（日检、周检、月检）生成-END-################");
		 return ReturnT.SUCCESS;
	 }

	 /**
	  * 生成保养工单
	  *
	  * @param planList
	  * @return void
	  * <AUTHOR>
	  * @date 2025/3/21 09:16
	  */
	 public boolean generateOrders(List<MaintainPlanDTO> planList) {
		 boolean ret = Boolean.FALSE;
		 List<MaintainOrder> orderList = new ArrayList<>();
		 for (MaintainPlan plan : planList) {
			 List<MaintainPlanEquipment> planEquipmentList = planEquipmentService.list(Wrappers.<MaintainPlanEquipment>query().lambda()
				 .eq(MaintainPlanEquipment::getPlanId, plan.getId()).orderByAsc(MaintainPlanEquipment::getSort));
			 List<MaintainPlanEquipment> equipmentList = new ArrayList<>();
			 // 正在维修的设备不再生成工单
			 if (Func.isNotEmpty(planEquipmentList)) {
				 planEquipmentList.forEach(planEquipment -> {
					 // 正在维修的设备不再生成工单
					 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(planEquipment.getEquipmentId());
					 if (!deviceAccountResult.isSuccess()) {
						 log.error(String.format("查询设备信息%s失败", planEquipment.getEquipmentId()));
					 }
					 if (Func.isNotEmpty(deviceAccountResult.getData())) {
						 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
						 if (EquipmentStatusEnum.IN_USE == EquipmentStatusEnum.getByCode(equipmentAccount.getStatus())) {
							 equipmentList.add(planEquipment);
						 }
					 }
				 });
			 }
			 Boolean needApproval = ApprovalUtil.isNeedApproval(OrderTypeEnum.MAINTAIN_ORDER.name(), plan.getTenantId());
			 // 查询当前计划下选择的保养标准信息
			 if (Func.isNotEmpty(equipmentList)) {
				 Map<Long, List<MaintainPlanEquipment>> dataMap = equipmentList.stream()
					 .collect(Collectors.groupingBy(MaintainPlanEquipment::getEquipmentId));
				 dataMap.forEach((k, v) -> {
					 List<MaintainStandardVO> standardList = new ArrayList<>();
					 v.forEach(planEquipment -> {
						 MaintainStandard maintainStandard = maintainStandardMapper.selectById(planEquipment.getStandardId());
						 if (Func.isNotEmpty(maintainStandard)) {
							 standardList.add(MaintainStandardWrapper.build().entityVO(maintainStandard));
						 }
					 });
					 if (Func.isNotEmpty(standardList)) {
						 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
							 List<ByDaySetVO> list = JSONUtil.toList(plan.getExecuteTime(), ByDaySetVO.class);
							 for (ByDaySetVO daySet : list) {
								 MaintainOrderVO maintainOrder = new MaintainOrderVO(plan, k, plan.getExecuteDept(), plan.getExecuteUser());
								 R<DeviceAccountVO> deviceAccountR = deviceAccountClient.deviceInfoById(k);
								 if (deviceAccountR.isSuccess() && Func.isNotEmpty(deviceAccountR.getData())) {
									 maintainOrder.setEquipmentCode(deviceAccountR.getData().getCode());
								 }
								 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + daySet.getStartTime() + ":00";
								 String endTime;
								 String[] startTimeItems = daySet.getStartTime().split(StringPool.COLON);
								 String[] endTimeItems = daySet.getEndTime().split(StringPool.COLON);
								 if (Func.toInt(startTimeItems[0]) <= Func.toInt(endTimeItems[0])) {
									 // 未跨天
									 endTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " " + daySet.getEndTime() + ":00";
								 } else {
									 // 跨天
									 endTime = DateUtil.format(DateUtil.plusDays(DateUtil.now(), 1), DateUtil.PATTERN_DATE) + " " + daySet.getEndTime() + ":00";
								 }
								 maintainOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
								 maintainOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
								 maintainOrder.setStandardInfo(JSONUtil.toJsonStr(standardList));
								 maintainOrder.setIsNeedApproval(needApproval);
								 orderList.add(Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrder.class)));
							 }
						 } else {
							 MaintainOrderVO maintainOrder = new MaintainOrderVO(plan, k, plan.getExecuteDept(), plan.getExecuteUser());
							 R<DeviceAccountVO> deviceAccountR = deviceAccountClient.deviceInfoById(k);
							 if (deviceAccountR.isSuccess() && Func.isNotEmpty(deviceAccountR.getData())) {
								 maintainOrder.setEquipmentCode(deviceAccountR.getData().getCode());
							 }
							 maintainOrder.setPlanInfo(JSONUtil.toJsonStr(plan));
							 String startTime = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE) + " 00:00:00";
							 Date endDate = DateUtil.now();
							 if (PlanCycleEnum.WEEK == PlanCycleEnum.getByCode(plan.getCycleType())) {
								 endDate = DateUtils.getSunDay(DateUtil.now());
							 }
							 if (PlanCycleEnum.MONTH == PlanCycleEnum.getByCode(plan.getCycleType())) {
								 // 判断2月
								 String executeTime = plan.getExecuteTime();
								 int nowMonth = DateUtils.nowMonth();
								 int today = DateUtils.nowDayOfMonth();
								 if (MONTH_2 == nowMonth && executeTime.contains(Func.toStr(DAY_30)) && FEBRUARY_DAY == today) {
									 startTime = DateUtil.format(DateUtil.parse(DateUtils.nowYear()
										 + "-02-" + FEBRUARY_DAY, DateUtil.PATTERN_DATE), DateUtil.PATTERN_DATE);
								 }
								 endDate = DateUtils.getLastDayOfMonth(DateUtil.now());
							 }
							 String endTime = DateUtil.format(endDate, DateUtil.PATTERN_DATE) + " 23:59:59";
							 maintainOrder.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
							 maintainOrder.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
							 maintainOrder.setStandardInfo(JSONUtil.toJsonStr(standardList));
							 maintainOrder.setIsNeedApproval(needApproval);
							 orderList.add(Objects.requireNonNull(BeanUtil.copy(maintainOrder, MaintainOrder.class)));
						 }
					 }
				 });
			 }
			 MaintainPlan p = planMapper.selectById(plan.getId());
			 // 更新计划状态（未开始 —> 执行中）
			 if (PlanStatusEnum.NO_START == PlanStatusEnum.getByCode(p.getStatus())) {
				 p.setStatus(PlanStatusEnum.IN_PROGRESS.getCode());
				 p.setUpdateTime(DateUtil.now());
				 planMapper.updateById(p);
			 }
			 // 更新计划状态（执行中 —> 已完成）
			 if (PlanStatusEnum.IN_PROGRESS == PlanStatusEnum.getByCode(p.getStatus())
				 && Func.equals(DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE),
				 DateUtil.format(plan.getEndDate(), DateUtil.PATTERN_DATE))) {
				 p.setStatus(PlanStatusEnum.IS_COMPLETED.getCode());
				 p.setUpdateTime(DateUtil.now());
				 planMapper.updateById(p);
			 }
		 }
		 log.info("保养计划：{}，需要生成的工单：{}", orderList);
		 if (Func.isNotEmpty(orderList)) {
			 ret = orderService.saveBatch(orderList);
			 // 业务日志
			 orderList.forEach(order -> {
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.GEN);
			 });
			 // 发送消息提醒
			 orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_MAINTAIN_ADD);
		 }
		 return ret;
	 }

	 /**
	  * 工单超时(每分钟执行一次)
	  *
	  * @return void
	  * <AUTHOR>
	  * @date 2025/3/20 09:16
	  */
	 @XxlJob("maintainOrderOverdueJobHandler")
	 public ReturnT<String> orderOverdueJobHandler(String param) {
		 XxlJobLogger.log("################保养工单超期定时任务-START-################");
		 List<MaintainOrder> list = orderService.list(Wrappers.<MaintainOrder>query().lambda()
			 .eq(MaintainOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode()));
		 List<MaintainOrder> orderList = new ArrayList<>();
		 if (Func.isNotEmpty(list)) {
			 for (MaintainOrder order : list) {
				 MaintainPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), MaintainPlanDTO.class);
				 if (PlanCycleEnum.DAY == PlanCycleEnum.getByCode(plan.getCycleType())) {
					 String currentTime = DateUtil.format(DateUtil.now(), "yyyy-MM-dd HH:mm");
					 String endTime = DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm");
					 Date date1 = DateUtil.parse(currentTime, "yyyy-MM-dd HH:mm");
					 Date date2 = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm");
					 if (date1.after(date2)) {
						 orderList.add(order);
					 }
				 } else {
					 String endDate = DateUtil.format(order.getEndTime(), DateUtil.PATTERN_DATE);
					 Date endTime = DateUtil.parse(endDate + " 23:59:59", DateUtil.PATTERN_DATETIME);
					 if(DateUtil.now().after(endTime)){
						 orderList.add(order);
					 }
				 }
			 }
		 }
		 if (Func.isNotEmpty(orderList)) {
			 orderService.update(Wrappers.<MaintainOrder>update().lambda()
				 .set(MaintainOrder::getStatus, OrderStatusEnum.IS_OVERDUE.getCode())
				 .in(MaintainOrder::getId, orderList.stream().map(MaintainOrder::getId).collect(Collectors.toList())));
			 // 业务日志
			 orderList.forEach(order -> {
				 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER, JSON.parseObject(JSON.toJSONString(order)), OrderActionEnum.OVERDUE);
			 });
			 // 发送消息提醒
			 orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_MAINTAIN_OVERDUE);
		 }
		 XxlJobLogger.log("################保养工单超期定时任务-END-################");
		 return ReturnT.SUCCESS;
	 }

	 /**
	  * 即将超时-发送消息
	  *
	  * @return void
	  * <AUTHOR>
	  * @date 2025/03/20 09:16
	  */
	 @XxlJob("maintainOrderExpireSoonJobHandler")
	 public ReturnT<String> orderExpireSoonJobHandler(String param) {
		 XxlJobLogger.log("################保养工单即将超期发送消息-START-################");
		 List<MaintainOrder> list = orderService.list(Wrappers.<MaintainOrder>query().lambda()
			 .eq(MaintainOrder::getIsExpired, 0)
			 .and(wrapper -> wrapper.eq(MaintainOrder::getStatus, OrderStatusEnum.IN_PROCESS.getCode())
				 .or()
				 .eq(MaintainOrder::getStatus, OrderStatusEnum.IS_REJECTED.getCode())));
		 List<MaintainOrder> orderList = new ArrayList<>();
		 BigDecimal timeInterval = new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL);
		 if (Func.isNotEmpty(list)) {
			 for (MaintainOrder order : list) {
				 if(Func.isNotEmpty(order.getExecuteUser())){
					 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetService.getOne(Wrappers.<TimeoutRemindSet>query().lambda()
						 .eq(TimeoutRemindSet::getUserId, order.getExecuteUser())
						 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.MAINTAIN.getCode()));
					 if(Func.isNotEmpty(timeoutRemindSet)){
						 timeInterval = timeoutRemindSet.getTimeInterval();
					 }
				 }
				 Long seconds = order.getEndTime().getTime() -  System.currentTimeMillis();
				 if (seconds <= timeInterval.multiply(new BigDecimal(3600)).multiply(new BigDecimal(1000)).longValue()) {
					 order.setIsExpired(1);
					 orderService.updateById(order);
					 orderList.add(order);
				 }
			 }
		 }
		 // 发送消息提醒
		 if(Func.isNotEmpty(orderList)){
			 orderService.sendMessage(orderList, MessageBizTypeEnum.SIMAS_MAINTAIN_EXPIRE);
		 }
		 XxlJobLogger.log("################保养工单即将超期发送消息-END-################");
		 return ReturnT.SUCCESS;
	 }


 }
