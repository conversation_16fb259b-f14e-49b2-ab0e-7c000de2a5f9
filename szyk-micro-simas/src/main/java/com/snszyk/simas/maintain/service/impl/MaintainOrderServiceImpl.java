 /*
  *      Copyright (c) 2018-2028
  */
 package com.snszyk.simas.maintain.service.impl;

 import cn.hutool.core.map.MapUtil;
 import cn.hutool.json.JSONUtil;
 import com.alibaba.fastjson.JSON;
 import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
 import com.baomidou.mybatisplus.core.metadata.IPage;
 import com.baomidou.mybatisplus.core.toolkit.Wrappers;
 import com.snszyk.common.constant.SimasConstant;
 import com.snszyk.common.equipment.cache.CommonCache;
 import com.snszyk.common.equipment.feign.IDeviceAccountClient;
 import com.snszyk.common.equipment.vo.DeviceAccountVO;
 import com.snszyk.common.utils.DateUtils;
 import com.snszyk.common.utils.ListUtil;
 import com.snszyk.core.log.exception.ServiceException;
 import com.snszyk.core.mp.base.BaseServiceImpl;
 import com.snszyk.core.secure.utils.AuthUtil;
 import com.snszyk.core.tool.api.R;
 import com.snszyk.core.tool.api.ResultCode;
 import com.snszyk.core.tool.support.Kv;
 import com.snszyk.core.tool.utils.*;
 import com.snszyk.message.enums.MessageBizTypeEnum;
 import com.snszyk.message.enums.MessageTypeEnum;
 import com.snszyk.message.enums.ReceiverTypeEnum;
 import com.snszyk.message.enums.YesNoEnum;
 import com.snszyk.message.feign.IMessageClient;
 import com.snszyk.message.vo.MessageVo;
 import com.snszyk.message.vo.ReceiverInfoVo;
 import com.snszyk.resource.entity.Attach;
 import com.snszyk.resource.feign.IAttachClient;
 import com.snszyk.simas.common.dto.BigScreenMessageDTO;
 import com.snszyk.simas.common.dto.EquipmentStatisticsDTO;
 import com.snszyk.simas.common.entity.BizLog;
 import com.snszyk.simas.common.entity.TimeoutRemindSet;
 import com.snszyk.simas.common.enums.*;
 import com.snszyk.simas.common.excel.MaintainOrderExcel;
 import com.snszyk.simas.common.mapper.TimeoutRemindSetMapper;
 import com.snszyk.simas.common.processor.OrderLogProcessor;
 import com.snszyk.simas.common.service.IBizLogService;
 import com.snszyk.simas.common.service.IComponentMaterialService;
 import com.snszyk.simas.common.vo.EquipmentMaintainVO;
 import com.snszyk.simas.common.vo.StatisticSearchVO;
 import com.snszyk.simas.common.wrapper.ComponentMaterialWrapper;
 import com.snszyk.simas.fault.service.IFaultDefectService;
 import com.snszyk.simas.fault.vo.FaultDefectVO;
 import com.snszyk.simas.fault.wrapper.FaultDefectWrapper;
 import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
 import com.snszyk.simas.maintain.dto.MaintainPlanDTO;
 import com.snszyk.simas.maintain.dto.MaintainStandardDTO;
 import com.snszyk.simas.maintain.entity.MaintainOrder;
 import com.snszyk.simas.maintain.entity.MaintainRecord;
 import com.snszyk.simas.maintain.entity.MaintainStandard;
 import com.snszyk.simas.maintain.mapper.MaintainOrderMapper;
 import com.snszyk.simas.maintain.service.IMaintainOrderService;
 import com.snszyk.simas.maintain.service.IMaintainRecordService;
 import com.snszyk.simas.maintain.vo.MaintainOrderVO;
 import com.snszyk.simas.maintain.vo.MaintainRecordVO;
 import com.snszyk.simas.maintain.vo.MaintainStandardVO;
 import com.snszyk.simas.maintain.wrapper.MaintainOrderWrapper;
 import com.snszyk.simas.maintain.wrapper.MaintainRecordWrapper;
 import com.snszyk.simas.spare.entity.ComponentMaterial;
 import com.snszyk.simas.spare.vo.ComponentMaterialVO;
 import com.snszyk.system.cache.SysCache;
 import com.snszyk.system.feign.ISysClient;
 import com.snszyk.system.vo.RoleVO;
 import com.snszyk.user.entity.User;
 import com.snszyk.user.feign.IUserClient;
 import lombok.AllArgsConstructor;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.stereotype.Service;
 import org.springframework.transaction.annotation.Transactional;

 import java.math.BigDecimal;
 import java.math.RoundingMode;
 import java.time.LocalDateTime;
 import java.util.*;
 import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;

 /**
  * 设备保养工单表 服务实现类
  *
  * <AUTHOR>
  * @since 2024-08-23
  */
 @Slf4j
 @AllArgsConstructor
 @Service
 public class MaintainOrderServiceImpl extends BaseServiceImpl<MaintainOrderMapper, MaintainOrder> implements IMaintainOrderService {

	 private final IDeviceAccountClient deviceAccountClient;
	 private final IComponentMaterialService componentMaterialService;
	 private final IMaintainRecordService maintainRecordService;
	 private final MaintainOrderMapper maintainOrderMapper;
	 private final IFaultDefectService faultDefectService;
	 private final TimeoutRemindSetMapper timeoutRemindSetMapper;
	 private final IBizLogService bizLogService;
	 private final ISysClient sysClient;
	 private final IUserClient userClient;
	 private final IAttachClient attachClient;
	 private final IMessageClient messageClient;

	 @Override
	 public IPage<MaintainOrderDTO> page(IPage<MaintainOrderDTO> page, MaintainOrderVO maintainOrder) {
		 if (Func.isNotEmpty(maintainOrder.getStartDate())) {
			 maintainOrder.setStartDate(maintainOrder.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(maintainOrder.getEndDate())) {
			 maintainOrder.setEndDate(maintainOrder.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 List<MaintainOrder> list = baseMapper.page(page, maintainOrder);
		 List<MaintainOrderDTO> resultList = null;
		 if (Func.isNotEmpty(list)) {
			 resultList = MaintainOrderWrapper.build().listDTO(list);
			 resultList.forEach(dto -> {
				 // 保养结果
				 List<MaintainRecord> recordList = maintainRecordService.list(Wrappers.<MaintainRecord>query().lambda()
					 .eq(MaintainRecord::getOrderId, dto.getId()));
				 if (Func.isNotEmpty(recordList)) {
					 List<Integer> abnormalList = recordList.stream().map(MaintainRecord::getIsAbnormal).collect(Collectors.toList());
					 if (abnormalList.contains(Func.toInt(StringPool.ONE))) {
						 dto.setMaintainResult("异常");
					 } else {
						 dto.setMaintainResult("正常");
					 }
				 }
			 });
		 }
		 return page.setRecords(resultList);
	 }

	 /**
	  * 获取设备Map
	  *
	  * @param equipmentIdList
	  * @return java.util.Map<java.lang.Long, com.snszyk.common.equipment.vo.DeviceAccountVO>
	  * <AUTHOR>
	  * @date 2025/3/20 11:36
	  */
	 private Map<Long, DeviceAccountVO> getEquipmentVOMap(List<Long> equipmentIdList) {
		 if (ObjectUtil.isEmpty(equipmentIdList)) {
			 return MapUtil.empty();
		 }
		 // 根据设备ids查询设备信息
		 DeviceAccountVO deviceAccountVO = new DeviceAccountVO();
		 deviceAccountVO.setDeviceIds(equipmentIdList);
		 R<List<DeviceAccountVO>> equipmentListResult = deviceAccountClient.deviceListByParams(deviceAccountVO);
		 if (!equipmentListResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(equipmentListResult.getData())) {
			 return MapUtil.empty();
		 }
		 return ListUtil.toMap(equipmentListResult.getData(), DeviceAccountVO::getId, e -> Objects.requireNonNull(BeanUtil.copy(e, DeviceAccountVO.class)));
	 }

	 @Override
	 public MaintainOrderDTO detail(String no) {
		 MaintainOrder maintainOrder = this.getOne(Wrappers.<MaintainOrder>query().lambda().eq(MaintainOrder::getNo, no));
		 if (maintainOrder == null) {
			 throw new ServiceException("当前工单不存在");
		 }
		 MaintainOrderDTO detail = MaintainOrderWrapper.build().entityDTO(maintainOrder);
		 R<DeviceAccountVO> deviceAccountResult = deviceAccountClient.deviceInfoById(maintainOrder.getEquipmentId());
		 if (!deviceAccountResult.isSuccess()) {
			 throw new ServiceException("查询设备台账信息失败！");
		 }
		 if (Func.isEmpty(deviceAccountResult.getData())) {
			 throw new ServiceException("当前设备台账不存在，请刷新后再试！");
		 }
		 DeviceAccountVO equipmentAccount = deviceAccountResult.getData();
		 detail.setEquipmentAccount(equipmentAccount).setMaintainPlan(JSONUtil.toBean(detail.getPlanInfo(), MaintainPlanDTO.class));
		 // 未完成的工单，点检明细查询standard表
		 if (OrderStatusEnum.IS_COMPLETED != OrderStatusEnum.getByCode(maintainOrder.getStatus())
			 && OrderStatusEnum.OVERDUE_COMPLETED != OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
			 // 保养标准列表
			 if (Func.isNotEmpty(detail.getStandardInfo())) {
				 List<MaintainStandardVO> maintainStandardList = JSONUtil.toList(detail.getStandardInfo(), MaintainStandardVO.class);
				 detail.setStandardList(maintainStandardList.stream().map(mainainStandard -> {
					 MaintainStandardDTO dto = Objects.requireNonNull(BeanUtil.copy(mainainStandard, MaintainStandardDTO.class));
					 // 部位名称
					 dto.setMonitorName(CommonCache.getMonitor(mainainStandard.getMonitorId()).getName());
					 MaintainRecord maintainRecord = maintainRecordService.getOne(Wrappers.<MaintainRecord>query().lambda()
						 .eq(MaintainRecord::getOrderId, maintainOrder.getId())
						 .eq(MaintainRecord::getMonitorId, dto.getMonitorId())
						 .eq(MaintainRecord::getStandardId, dto.getId()));
					 if (Func.isNotEmpty(maintainRecord)) {
						 MaintainRecordVO recordVO = MaintainRecordWrapper.build().entityVO(maintainRecord);
						 dto.setIsAbnormal(maintainRecord.getIsAbnormal())
							 .setAbnormalStatus(maintainRecord.getIsAbnormal() == 0 ? "正常" : "异常");
						 // 保养结果
						 dto.setMaintainRecord(recordVO);
						 // 异常图片
						 if (Func.isNotEmpty(recordVO.getAbnormalImage())) {
							 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(recordVO.getAbnormalImage()));
							 if (attachListR.isSuccess()) {
								 recordVO.setAbnormalImageList(attachListR.getData());
							 }
						 }
					 }
					 return dto;
				 }).collect(Collectors.toList()));
			 }
			 // 驳回原因
			 // if (OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
			 // BizLog bizLog = bizLogService.getOne(Wrappers.<BizLog>query().lambda()
			 //	 .eq(BizLog::getBizNo, no).eq(BizLog::getBizStatus, OrderStatusEnum.IS_REJECTED.getCode())
			 //	 .orderByDesc(BizLog::getOperateTime).last(" limit 1"));
			 // if (Func.isNotEmpty(bizLog)) {
			 //	 MaintainOrderVO order = JSONUtil.toBean(bizLog.getBizInfo(), MaintainOrderVO.class);
			 //	 detail.setRejectReason(order.getRejectReason());
			 //	 User checkUser = UserCache.getUser(bizLog.getOperateUser());
			 //	 if (Func.isNotEmpty(checkUser)) {
			 //		 detail.setCheckUserName(checkUser.getRealName());
			 //	 }
			 // }
			 //}
		 } else {
			 // 完成的工单，点检明细查询record表
			 List<MaintainRecord> recordList = maintainRecordService.list(Wrappers.<MaintainRecord>query().lambda()
				 .eq(MaintainRecord::getOrderId, maintainOrder.getId()));
			 if (Func.isNotEmpty(recordList)) {
				 detail.setStandardList(recordList.stream().map(record -> {
					 MaintainRecordVO recordVO = MaintainRecordWrapper.build().entityVO(record);
					 MaintainStandard standard = JSONUtil.toBean(record.getStandardInfo(), MaintainStandard.class);
					 MaintainStandardDTO standDTO = Objects.requireNonNull(BeanUtil.copy(standard, MaintainStandardDTO.class));
					 // 保养结果
					 standDTO.setMonitorName(recordVO.getMonitorName()).setIsAbnormal(recordVO.getIsAbnormal())
						 .setAbnormalStatus(recordVO.getAbnormalStatus()).setMaintainRecord(recordVO);
					 //.setNeedConfirmName(standard.getNeedConfirm() == 1 ? "是" : "否");
					 // 异常图片
					 if (Func.isNotEmpty(recordVO.getAbnormalImage())) {
						 R<List<Attach>> attachListR = attachClient.listByIds(Func.toLongList(recordVO.getAbnormalImage()));
						 if (attachListR.isSuccess()) {
							 recordVO.setAbnormalImageList(attachListR.getData());
						 }
					 }
					 return standDTO;
				 }).collect(Collectors.toList()));
			 }
		 }
		 // 保养耗材
		 if (Func.isNotEmpty(detail.getMaterial())) {
			 detail.setMaterialList(JSONUtil.toList(detail.getMaterial(), ComponentMaterialVO.class));
		 }
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(detail.getStatus())) {
			 List<ComponentMaterial> materialList = componentMaterialService.list(Wrappers.<ComponentMaterial>query().lambda()
				 .eq(ComponentMaterial::getBizNo, detail.getNo()).orderByAsc(ComponentMaterial::getSort));
			 if (Func.isNotEmpty(materialList)) {
				 detail.setMaterialList(ComponentMaterialWrapper.build().listVO(materialList));
			 }
		 }
		 return detail;
	 }

	 /**
	  * 提交保养
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean maintain(EquipmentMaintainVO vo) {
		 MaintainOrder maintainOrder = maintainOrderMapper.selectById(vo.getOrderId());
		 Integer oldStatus = maintainOrder.getStatus();
		 if (OrderStatusEnum.getByCode(oldStatus) == OrderStatusEnum.IS_COMPLETED
			 || OrderStatusEnum.getByCode(oldStatus) == OrderStatusEnum.OVERDUE_COMPLETED) {
			 throw new ServiceException("当前工单已完成，不能提交！");
		 }
		 // 保养记录
		 AtomicReference<Integer> isAbnormal = new AtomicReference<>(0);
		 List<MaintainRecord> toFaultList = new ArrayList<>();
		 Date now = DateUtil.now();
		 maintainRecordService.remove(Wrappers.<MaintainRecord>query().lambda()
			 .eq(MaintainRecord::getOrderId, maintainOrder.getId()));
		 List<MaintainRecord> recordList = null;
		 // 保养执行标准
		 List<MaintainStandard> maintainStandardList = JSONUtil.toList(maintainOrder.getStandardInfo(), MaintainStandard.class);
		 Map<Long, MaintainStandard> maintainStandardMap = ListUtil.toMap(maintainStandardList, MaintainStandard::getId,
			 e -> Objects.requireNonNull(BeanUtil.copy(e, MaintainStandard.class)));
		 if (Func.isNotEmpty(vo.getMaintainRecordList())) {
			 recordList = vo.getMaintainRecordList().stream().map(maintainRecordVO -> {
				 MaintainRecord maintainRecord = Objects.requireNonNull(BeanUtil.copy(maintainRecordVO, MaintainRecord.class));
				 maintainRecord.setOrderId(maintainOrder.getId()).setEquipmentId(vo.getEquipmentId())
					 .setMaintainUser(AuthUtil.getUserId()).setMaintainTime(now)
					 .setMonitorName(CommonCache.getMonitor(maintainRecord.getMonitorId()).getName())
					 .setStandardInfo(JSONUtil.toJsonStr(maintainStandardMap.get(maintainRecord.getStandardId())));
				 if (maintainRecord.getIsAbnormal() == 1) {
					 isAbnormal.set(1);
					 toFaultList.add(maintainRecord);
				 }
				 return maintainRecord;
			 }).collect(Collectors.toList());
			 maintainRecordService.saveBatch(recordList);
		 }
		 maintainOrder.setIsAbnormal(isAbnormal.get());
		 maintainOrder.setRemark(vo.getRemark());
		 // 工单需审核的情况
		 Boolean needApproval = maintainOrder.getIsNeedApproval();
		 if (needApproval) {
			 maintainOrder.setStatus(OrderStatusEnum.WAIT_CONFIRM.getCode());
		 } else {
			 // 工单无需确认，生成故障缺陷(按标准生成)
			 if (Func.isNotEmpty(toFaultList)) {
				 List<MaintainRecordVO> list = MaintainRecordWrapper.build().listVO(toFaultList);
				 List<FaultDefectVO> faultDefectList = list.stream().map(record -> {
					 record.setOrderNo(maintainOrder.getNo());
					 return FaultDefectWrapper.build().maintainEntityVO(record);
				 }).collect(Collectors.toList());
				 faultDefectService.submit(faultDefectList);
			 }
			 if (OrderStatusEnum.IN_PROCESS == OrderStatusEnum.getByCode(oldStatus)) {
				 maintainOrder.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
			 }
			 if (OrderStatusEnum.IS_OVERDUE == OrderStatusEnum.getByCode(oldStatus)) {
				 maintainOrder.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
			 }
		 }
		 // 保养耗材
		 if (Func.isNotEmpty(vo.getMaterialList())) {
			 maintainOrder.setMaterial(JSONUtil.toJsonStr(vo.getMaterialList()));
		 } else {
			 maintainOrder.setMaterial(null);
		 }
		 // 实际执行人
		 maintainOrder.setExecuteUser(AuthUtil.getUserId());
		 maintainOrder.setSubmitTime(now);
		 boolean ret = this.updateById(maintainOrder);
		 // 如果之前的状态是驳回,则为再次提交
		 if (OrderStatusEnum.IS_REJECTED.getCode().equals(oldStatus)) {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER,
				 JSON.parseObject(JSON.toJSONString(maintainOrder)), OrderActionEnum.RE_SUBMIT);
		 } else {
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER,
				 JSON.parseObject(JSON.toJSONString(maintainOrder)), OrderActionEnum.INIT);
		 }
		 return ret;
	 }

	 /**
	  * 审核
	  *
	  * @param vo
	  * @return
	  */
	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirm(MaintainOrderVO vo) {
		 MaintainOrder maintainOrder = this.getById(vo.getId());
		 if (maintainOrder == null) {
			 throw new ServiceException(ResultCode.FAILURE);
		 }
		 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(maintainOrder.getStatus())
			 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(maintainOrder.getStatus())) {
			 throw new ServiceException("当前保养工单已确认！");
		 }
		 // 审核拒绝v1.2.1
		 if (OrderStatusEnum.IS_REJECTED == OrderStatusEnum.getByCode(vo.getStatus())) {
			 maintainOrder.setStatus(OrderStatusEnum.IS_REJECTED.getCode());
			 maintainOrder.setRejectReason(vo.getRejectReason());
			 maintainOrder.setApprovalUser(AuthUtil.getUserId());
			 boolean ret = this.updateById(maintainOrder);
			 if (!ret) {
				 throw new ServiceException("工单更新失败");
			 }
			 // 发送消息
			 this.sendMessage(Collections.singletonList(maintainOrder), MessageBizTypeEnum.SIMAS_MAINTAIN_REJECT);
			 // 业务日志
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER, JSON.parseObject(JSON.toJSONString(maintainOrder)),
				 OrderActionEnum.AUDIT_FAIL, vo.getRejectReason());
		 } else {
			 // 审核通过
			 List<MaintainRecord> recordList = maintainRecordService.lambdaQuery().eq(MaintainRecord::getOrderId, vo.getId()).list();
			 if (Func.isNotEmpty(recordList)) {
				 recordList = recordList.stream().filter(inspectRecord -> inspectRecord.getIsAbnormal() == 1).collect(Collectors.toList());
			 }
			 SpringUtil.getBean(MaintainOrderServiceImpl.class).approvalPass(vo.getId(), recordList);
			 OrderLogProcessor.saveBizLog(SystemModuleEnum.MAINTAIN_ORDER, JSON.parseObject(JSON.toJSONString(maintainOrder)), OrderActionEnum.AUDIT_PASS);
		 }
		 return true;
	 }

	 @Override
	 @Transactional(rollbackFor = Exception.class)
	 public boolean confirmBatch(MaintainOrderVO vo) {
		 if (Func.isNotEmpty(vo.getOrderIds())) {
			 vo.getOrderIds().forEach(id -> {
				 vo.setId(id);
				 this.confirm(vo);
			 });
		 }
		 return true;
	 }

	 /**
	  * 审核通过
	  *
	  * @param id
	  * @param toFaultList
	  */
	 @Transactional(rollbackFor = Exception.class)
	 public void approvalPass(Long id, List<MaintainRecord> toFaultList) {
		 MaintainOrder maintainOrder = this.getById(id);
		 BizLog bizLog = bizLogService.getOne(Wrappers.<BizLog>query().lambda()
			 .eq(BizLog::getBizId, id)
			 .eq(BizLog::getBizStatus, OrderStatusEnum.IS_OVERDUE.getCode()));
		 if (Func.isNotEmpty(bizLog)) {
			 maintainOrder.setStatus(OrderStatusEnum.OVERDUE_COMPLETED.getCode());
		 } else {
			 maintainOrder.setStatus(OrderStatusEnum.IS_COMPLETED.getCode());
		 }
		 // 生成故障缺陷(按标准生成)
		 if (Func.isNotEmpty(toFaultList)) {
			 List<MaintainRecordVO> list = MaintainRecordWrapper.build().listVO(toFaultList);
			 List<FaultDefectVO> faultDefectList = list.stream().map(record -> {
				 record.setOrderNo(maintainOrder.getNo());
				 return FaultDefectWrapper.build().maintainEntityVO(record);
			 }).collect(Collectors.toList());
			 faultDefectService.submit(faultDefectList);
		 }
		 // 保养耗材入库
		 if (Func.isNotEmpty(maintainOrder.getMaterial())) {
			 List<ComponentMaterialVO> materials = JSONUtil.toList(maintainOrder.getMaterial(), ComponentMaterialVO.class);
			 componentMaterialService.submitBatch(maintainOrder.getNo(), SystemModuleEnum.MAINTAIN_ORDER.getCode(), materials);
		 }
		 maintainOrder.setApprovalUser(AuthUtil.getUserId());
		 maintainOrder.setCompleteTime(new Date());
		 this.updateById(maintainOrder);
	 }

	 @Override
	 public List<MaintainOrderExcel> exportOrder(MaintainOrderVO vo) {
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 vo.setStartDate(vo.getStartDate() + DateUtils.DAY_START_TIME);
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 vo.setEndDate(vo.getEndDate() + DateUtils.DAY_END_TIME);
		 }
		 QueryWrapper<MaintainOrder> queryWrapper = Wrappers.query();
		 if (Func.isNotEmpty(vo.getExecuteDept())) {
			 queryWrapper.lambda().eq(MaintainOrder::getExecuteDept, Func.toLongList(AuthUtil.getDeptId()).get(0));
		 }
		 if (Func.isNotEmpty(vo.getExecuteUser())) {
			 queryWrapper.lambda().and(wrapper -> {
				 wrapper.isNull(MaintainOrder::getExecuteUser).or().eq(MaintainOrder::getExecuteUser, AuthUtil.getUserId());
			 });
		 }
		 if (Func.isNotEmpty(vo.getStatus())) {
			 queryWrapper.lambda().eq(MaintainOrder::getStatus, vo.getStatus());
		 }
		 if (Func.isNotEmpty(vo.getNo())) {
			 queryWrapper.lambda().like(MaintainOrder::getNo, vo.getNo());
		 }
		 if (Func.isNotEmpty(vo.getOrderName())) {
			 queryWrapper.lambda().like(MaintainOrder::getPlanInfo, vo.getOrderName());
		 }
		 if (Func.isNotEmpty(vo.getStartDate())) {
			 queryWrapper.lambda().ge(MaintainOrder::getStartTime, vo.getStartDate());
		 }
		 if (Func.isNotEmpty(vo.getEndDate())) {
			 queryWrapper.lambda().le(MaintainOrder::getStartTime, vo.getEndDate());
		 }
		 queryWrapper.lambda().orderByDesc(MaintainOrder::getCreateTime);
		 List<MaintainOrder> list = baseMapper.selectList(queryWrapper);
		 if (Func.isNotEmpty(list)) {
			 List<MaintainOrderDTO> orderList = MaintainOrderWrapper.build().listDTO(list);
			 AtomicReference<Integer> sn = new AtomicReference<>(1);
			 return orderList.stream().map(order -> {
				 MaintainOrderExcel orderExcel = Objects.requireNonNull(BeanUtil.copy(order, MaintainOrderExcel.class));
				 orderExcel.setSn(Func.toStr(sn.getAndSet(sn.get() + 1)));
				 List<MaintainRecord> recordList = maintainRecordService.list(Wrappers.<MaintainRecord>query().lambda()
					 .eq(MaintainRecord::getOrderId, order.getId()));
				 if (Func.isNotEmpty(recordList)) {
					 List<Integer> abnormalList = recordList.stream().map(MaintainRecord::getIsAbnormal).collect(Collectors.toList());
					 if (abnormalList.contains(Func.toInt(StringPool.ONE))) {
						 orderExcel.setMaintainResult("异常");
					 } else {
						 orderExcel.setMaintainResult("正常");
					 }
				 }
				 MaintainPlanDTO plan = JSONUtil.toBean(order.getPlanInfo(), MaintainPlanDTO.class);
				 switch (Objects.requireNonNull(PlanCycleEnum.getByCode(plan.getCycleType()))) {
					 case DAY:
						 orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm"));
						 orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm"));
						 break;
					 case WEEK:
					 case MONTH:
						 orderExcel.setStartTimeStr(DateUtil.format(order.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
						 orderExcel.setEndTimeStr(DateUtil.format(order.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
						 break;
					 default:
				 }
				 return orderExcel;
			 }).collect(Collectors.toList());
		 }
		 return null;
	 }

	 @Override
	 public IPage<MaintainOrderDTO> timeoutPage(IPage<MaintainOrderDTO> page, MaintainOrderVO maintainOrder) {
		 maintainOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.MAINTAIN.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 maintainOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 maintainOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			 .setExecuteUser(AuthUtil.getUserId());
		 List<MaintainOrder> list = baseMapper.timeoutPage(page, maintainOrder);
		 List<MaintainOrderDTO> resultList = null;
		 if (Func.isNotEmpty(list)) {
			 resultList = MaintainOrderWrapper.build().listDTO(list);
			 List<Long> equipmentIdList = list.stream().map(o -> o.getEquipmentId()).collect(Collectors.toList());
			 // 查询设备Map
			 final Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(equipmentIdList);
			 resultList.forEach(dto -> {
				 DeviceAccountVO equipmentAccount = equipmentVOMap.get(dto.getEquipmentId());
				 if (Func.isNotEmpty(equipmentAccount)) {
					 dto.setEquipmentName(equipmentAccount.getName()).setEquipmentCode(equipmentAccount.getCode());
				 }
			 });
		 }
		 return page.setRecords(resultList);
	 }

	 @Override
	 public List<MaintainOrderDTO> maintainOrderStatistics(Integer queryDate) {
		 return MaintainOrderWrapper.build().listDTO(baseMapper.maintainOrderStatistics(queryDate));
	 }

	 @Override
	 public Integer expireSoonCount() {
		 MaintainOrderVO maintainOrder = new MaintainOrderVO();
		 maintainOrder.setTimeInterval(new BigDecimal(SimasConstant.DEFAULT_TIME_INTERVAL));
		 TimeoutRemindSet timeoutRemindSet = timeoutRemindSetMapper.selectOne(Wrappers.<TimeoutRemindSet>query().lambda()
			 .eq(TimeoutRemindSet::getUserId, AuthUtil.getUserId())
			 .eq(TimeoutRemindSet::getBizType, BizTypeEnum.MAINTAIN.getCode()));
		 if (Func.isNotEmpty(timeoutRemindSet)) {
			 maintainOrder.setTimeInterval(timeoutRemindSet.getTimeInterval());
		 }
		 // 当前登录人和所属部门
		 maintainOrder.setExecuteDept(Func.toLongList(AuthUtil.getDeptId()).get(0))
			 .setExecuteUser(AuthUtil.getUserId());
		 return baseMapper.expireSoonCount(maintainOrder);
	 }

	 @Override
	 public void sendMessage(List<MaintainOrder> list, MessageBizTypeEnum messageBizType) {
		 log.info("=================== 发送{}消息- START- ===================", messageBizType.getMessage());
		 list.forEach(order -> {
			 // 指定执行人的，给指定人发消息，未指定执行人的，给当前部门所有人发消息
			 ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			 MessageVo messageVo = new MessageVo();
			 messageVo.setAppKey("SIMAS");
			 messageVo.setSender("SIMAS");
			 messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			 messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			 messageVo.setTitle(messageBizType.getMessage());
			 messageVo.setBizType(messageBizType.getCode());
			 messageVo.setBizId(order.getNo());
			 messageVo.setContent(JSONUtil.toJsonStr(order));
			 messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			 if (Func.isNotEmpty(order.getExecuteUser())) {
				 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				 userVo.setId(order.getExecuteUser());
				 receiverInfoVo.setUserList(Collections.singletonList(userVo));
			 } else {
				 // 部门下具有保养员角色的人
				 R<RoleVO> roleResult = sysClient.getRoleByAlias(order.getTenantId(), SimasConstant.SimasRole.MAINTAIN_USER);
				 if (roleResult.isSuccess() && Func.isNotEmpty(roleResult.getData())) {
					 R<List<User>> userListResult = userClient.userListByDeptRole(order.getExecuteDept(),
						 roleResult.getData().getId());
					 if (userListResult.isSuccess() && Func.isNotEmpty(userListResult.getData())) {
						 receiverInfoVo.setUserList(userListResult.getData().stream().map(user -> {
							 ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
							 userVo.setId(user.getId());
							 return userVo;
						 }).collect(Collectors.toList()));
					 }
				 }
			 }
			 messageVo.setReceiverInfoVo(receiverInfoVo);
			 messageClient.pushMessage(messageVo);
		 });
		 log.info("=================== 发送{}消息- END- ===================", messageBizType.getMessage());
	 }

	 @Override
	 public IPage<MaintainOrderDTO> statisticalReport(IPage<MaintainOrderDTO> page, StatisticSearchVO search) {
		 List<MaintainOrder> list = baseMapper.statisticalReport(page, search);
		 List<MaintainOrderDTO> orderList = null;
		 if (Func.isNotEmpty(list)) {
			 orderList = MaintainOrderWrapper.build().listDTO(list);
			 List<Long> equipmentIdList = list.stream().distinct().map(MaintainOrder::getEquipmentId).collect(Collectors.toList());
			 Map<Long, DeviceAccountVO> equipmentVOMap = this.getEquipmentVOMap(equipmentIdList);
			 orderList.forEach(dto -> {
				 DeviceAccountVO equipmentAccount = equipmentVOMap.get(dto.getEquipmentId());
				 if (Func.isNotEmpty(equipmentAccount)) {
					 dto.setEquipmentName(equipmentAccount.getName()).setEquipmentCode(equipmentAccount.getCode())
						 .setEquipmentCategoryName(CommonCache.getEquipmentCategory(equipmentAccount.getCategoryId()).getCategoryName());
				 }
				 if (Func.isNotEmpty(dto.getIsAbnormal())) {
					 if (Func.equals(Func.toInt(StringPool.ONE), dto.getIsAbnormal())) {
						 dto.setMaintainResult("异常");
					 } else {
						 dto.setMaintainResult("正常");
					 }
				 }
			 });
		 }
		 return page.setRecords(orderList);
	 }

	 @Override
	 public List<MaintainOrderDTO> queryList(MaintainOrderVO vo) {
		 return baseMapper.queryList(vo);
	 }

	 @Override
	 public boolean timeoutExplain(MaintainOrderVO vo) {
		 MaintainOrder maintainOrder = this.getOne(Wrappers.<MaintainOrder>query().lambda().eq(MaintainOrder::getNo, vo.getNo()));
		 if (maintainOrder == null) {
			 throw new ServiceException("当前工单不存在");
		 }
		 maintainOrder.setRemark(vo.getRemark());
		 return this.updateById(maintainOrder);
	 }

	 @Override
	 public List<BigScreenMessageDTO> overdueList(String tenantId) {
		 return baseMapper.overdueList(tenantId);

	 }

	 @Override
	 public List<Kv> maintenance30day() {
		 return baseMapper.maintenance30day();

	 }

	 @Override
	 public List<MaintainOrderDTO> coverList30day(String tenantId) {
		 return baseMapper.coverList30day(tenantId);

	 }

	 @Override
	 public Integer handleMaintainCount(MaintainOrderVO maintainOrder) {
		 return baseMapper.handleMaintainCount(maintainOrder);
	 }

	 @Override
	 public List<MaintainOrder> listBy(Long executeDeptId, List<Long> executeUserIds, List<Long> equipmentIds, LocalDateTime startDateTime, LocalDateTime endDateTime, Integer neStatus) {
		 return this.lambdaQuery()
			 .eq(ObjectUtil.isNotEmpty(executeDeptId), MaintainOrder::getExecuteDept, executeDeptId)
			 .in(ObjectUtil.isNotEmpty(executeUserIds), MaintainOrder::getExecuteUser, executeUserIds)
			 .in(ObjectUtil.isNotEmpty(equipmentIds), MaintainOrder::getEquipmentId, equipmentIds)
			 .ge(ObjectUtil.isNotEmpty(startDateTime), MaintainOrder::getStartTime, startDateTime)
			 .lt(ObjectUtil.isNotEmpty(endDateTime), MaintainOrder::getStartTime, endDateTime)
			 .ne(ObjectUtil.isNotEmpty(neStatus), MaintainOrder::getStatus, neStatus)
			 .list();
	 }

	 @Override
	 public List<EquipmentStatisticsDTO> maintainStatistics(StatisticSearchVO search, Map<Long, DeviceAccountVO> deviceMap) {
		 List<EquipmentStatisticsDTO> list = maintainOrderMapper.statisticsByEquipment(search);
		 if (Func.isNotEmpty(list)) {
			 list.forEach(dto -> {
				 DeviceAccountVO equipment = deviceMap.get(dto.getId());
				 dto.setName(equipment.getName()).setSn(equipment.getSn())
					 .setCategoryName(CommonCache.getEquipmentCategory(equipment.getCategoryId()).getCategoryName());
				 if (Func.isNotEmpty(equipment.getUseDept())) {
					 dto.setUseDeptName(SysCache.getDept(equipment.getUseDept()).getDeptName());
				 }
				 dto.setCompleteCount(0).setUnfinishedCount(0).setAbnormalCount(0)
					 .setCompleteRate(BigDecimal.ZERO).setAbnormalRate(BigDecimal.ZERO);
				 StatisticSearchVO searchVO = new StatisticSearchVO(search.getQueryDate(), search.getStartDate(), search.getEndDate(),
					 Func.toLongList(Func.toStr(dto.getId())));
				 List<MaintainOrder> orderList = maintainOrderMapper.equipmentStatisticsOfOrder(searchVO);
				 for (MaintainOrder order : orderList) {
					 if (OrderStatusEnum.IS_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())
						 || OrderStatusEnum.OVERDUE_COMPLETED == OrderStatusEnum.getByCode(order.getStatus())) {
						 dto.setCompleteCount(dto.getCompleteCount() + 1);
					 }
					 if (Func.isNotEmpty(order.getIsAbnormal()) && order.getIsAbnormal() == 1) {
						 dto.setAbnormalCount(dto.getAbnormalCount() + 1);
					 }
				 }
				 dto.setUnfinishedCount(dto.getCount() - dto.getCompleteCount());
				 // 完成率
				 if (dto.getCount() != 0) {
					 dto.setCompleteRate(new BigDecimal(dto.getCompleteCount())
						 .divide(new BigDecimal(dto.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
				 }
				 // 异常率
				 if (dto.getCompleteCount() != 0) {
					 dto.setAbnormalRate(new BigDecimal(dto.getAbnormalCount())
						 .divide(new BigDecimal(dto.getCompleteCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
				 }
			 });
		 }
		 return list;
	 }


 }
