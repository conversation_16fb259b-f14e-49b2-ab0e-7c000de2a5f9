# Dify平台功能开关配置说明

## 概述

本文档说明如何在Nacos配置中心配置Dify平台功能开关，以控制是否启用Dify相关功能。

## 配置项说明

### 主要配置项

在Nacos配置中心的对应配置文件中添加以下配置项：

```yaml
# Dify平台功能开关配置
dify:
  # 是否启用Dify平台功能，默认为false
  enabled: true
  
  # Dify服务器地址
  server_url: http://your-dify-server:port
  
  # 设备数据集ID
  equipment_dataset_id: your-equipment-dataset-id
  
  # 缺陷数据集ID  
  defect_dataset_id: your-defect-dataset-id
  
  # 设备数据集API密钥
  device_dataset_api_key: your-device-dataset-api-key
  
  # 运维标准API密钥
  operation_standards_api_key: your-operation-standards-api-key
  
  # 维修建议API密钥
  repair_suggest_api_key: your-repair-suggest-api-key
  
  # 助手API密钥
  assistant_api_key: your-assistant-api-key
  
  # 元数据配置
  metadata:
    simas-device-id:
      id: your-simas-device-id
      name: SIMAS设备ID
    simas-tenant-id:
      id: your-simas-tenant-id  
      name: SIMAS租户ID
    simas-file-link:
      id: your-simas-file-link-id
      name: SIMAS文件链接
```

## 功能影响范围

当 `dify.enabled` 设置为 `false` 时，以下功能将被跳过：

### 1. 文件上传功能
- **影响模块**: `EquipmentFileServiceImpl`
- **影响方法**: `syncDify()`, `aiToolsAddPreFile()`
- **行为**: 跳过文件同步到Dify平台的操作，不会抛出连接异常

### 2. 文件删除功能  
- **影响模块**: `EquipmentFileServiceImpl`
- **影响方法**: `syncRemoveDify()`, `aiToolsRemovePreFile()`
- **行为**: 跳过从Dify平台删除文档的操作

### 3. 故障案例同步功能
- **影响模块**: `AiToolsService`, `FaultDefectCaseServiceImpl`
- **影响方法**: `handleOneDefectSync()`, `submit()`
- **行为**: 跳过故障案例同步到Dify平台的操作

### 4. 维修建议生成功能
- **影响模块**: `AiToolsService`
- **影响方法**: `generateRepairSuggest()`, `generateRepairSuggestStream()`
- **行为**: 跳过调用Dify工作流生成维修建议

### 5. 运维标准生成功能
- **影响模块**: `AiToolsService`
- **影响方法**: `runWorkflow()`
- **行为**: 跳过调用Dify工作流生成运维标准

## 配置步骤

### 1. 在Nacos配置中心添加配置

1. 登录Nacos管理界面
2. 找到对应的配置文件（如：`szyk-simas-dev.yml`）
3. 添加上述Dify配置项
4. 发布配置

### 2. 重启应用

配置修改后需要重启应用使配置生效。

### 3. 验证配置

查看应用日志，当Dify功能被禁用时，会看到类似以下日志：

```
INFO  - Dify功能未启用，跳过文件上传操作。fileId: xxx, deviceId: xxx, tenantId: xxx, 文件名: xxx
INFO  - Dify功能未启用，跳过文档删除操作。attachId: xxx
INFO  - Dify功能未启用，跳过工作流执行操作。query: xxx
INFO  - Dify功能未启用，跳过故障案例同步操作。docName: xxx
```

## 注意事项

1. **默认值**: `dify.enabled` 的默认值为 `false`，确保在未配置时不会尝试连接Dify平台
2. **向后兼容**: 现有的Dify配置项保持不变，只需添加 `enabled` 开关
3. **优雅降级**: 当Dify功能关闭时，主业务流程不受影响，只是跳过AI相关功能
4. **日志记录**: 所有跳过的操作都会记录相应的日志，便于问题排查

## 环境配置示例

### 开发环境（启用Dify）
```yaml
dify:
  enabled: true
  server_url: http://dev-dify-server:8080
  # ... 其他配置
```

### 生产环境（禁用Dify）
```yaml
dify:
  enabled: false
  # 其他配置可以保留或注释掉
```

### 测试环境（按需启用）
```yaml
dify:
  enabled: ${DIFY_ENABLED:false}  # 通过环境变量控制
  # ... 其他配置
```

## 代码修改说明

### 修改的文件列表

1. **DifyClient.java** - 主要的Dify客户端类
   - 添加了 `@Value("${dify.enabled:false}")` 配置项
   - 在所有主要方法中添加了开关判断逻辑
   - 修改的方法：`uploadFile()`, `deleteDocument()`, `runWorkflow()`, `runWorkflowStream()`, `createDocumentByText()`

2. **DifyChatFlowClient.java** - Dify聊天流客户端类
   - 添加了 `@Value("${dify.enabled:false}")` 配置项
   - 在所有主要方法中添加了开关判断逻辑
   - 修改的方法：`chatMessagesByStreaming()`, `stopChatStreaming()`, `getConversations()`, `deleteConversation()`, `getConversationMessages()`

3. **EquipmentFileServiceImpl.java** - 设备文件服务实现类
   - 在文件同步相关方法中添加了详细的日志记录
   - 修改的方法：`syncDify()`, `syncRemoveDify()`, `aiToolsAddPreFile()`

4. **AiToolsService.java** - AI工具服务类
   - 在AI相关方法中添加了详细的日志记录
   - 修改的方法：`handleOneDefectSync()`, `generateRepairSuggest()`, `generateRepairSuggestStream()`

### 开关判断逻辑

当 `dify.enabled = false` 时：
- 所有Dify相关操作都会被跳过
- 返回适当的默认值或提示信息
- 记录相应的日志信息
- 不会抛出连接异常，确保主业务流程正常运行

## 故障排查

如果遇到Dify相关问题，可以：

1. 首先将 `dify.enabled` 设置为 `false` 来快速恢复业务
2. 检查Dify服务器连接状态
3. 验证API密钥是否正确
4. 查看应用日志中的详细错误信息
5. 问题解决后再将 `dify.enabled` 设置为 `true`

## 测试验证

### 验证开关生效

1. 设置 `dify.enabled: false`
2. 重启应用
3. 执行以下操作并观察日志：
   - 上传设备文件
   - 生成故障案例
   - 使用智能助手对话
   - 生成维修建议

应该看到类似以下的日志输出：
```
INFO  - Dify功能未启用，跳过文件上传操作。fileId: xxx, deviceId: xxx, tenantId: xxx, 文件名: xxx
INFO  - Dify功能未启用，跳过故障案例同步操作。docName: xxx
INFO  - Dify功能未启用，跳过智能助手对话操作。query: xxx
```

### 验证功能正常

1. 设置 `dify.enabled: true`
2. 配置正确的Dify服务器地址和API密钥
3. 重启应用
4. 执行相同操作，应该能正常调用Dify平台功能
