package com.snszyk.simas.spare.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel(value = "SparePartsWarehouseVO对象", description = "备品备件仓库")
public class SparePartsWarehouseVO extends SparePartsWarehouse {
	private static final long serialVersionUID = 1L;

	@NotBlank(message = "仓库名称不能为空")
	@ApiModelProperty(value = "仓库名称", required = true)
	private String name;

	@NotNull(message = "管理员不能为空")
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "管理员", required = true)
	private Long manager;

	@NotBlank(message = "联系方式不能为空")
	@ApiModelProperty(value = "联系方式", required = true)
	private String tel;

}
