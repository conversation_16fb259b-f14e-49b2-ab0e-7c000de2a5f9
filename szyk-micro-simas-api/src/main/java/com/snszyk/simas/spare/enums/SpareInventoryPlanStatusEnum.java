package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpareInventoryPlanStatusEnum {

	NOT_START(0, "未盘点"),
	PROCESS(1, "盘点中"),
	COMPLETE(2, "已完成"),
	;

	final Integer code;
	final String name;

	public static SpareInventoryPlanStatusEnum getByCode(Integer code){
		for (SpareInventoryPlanStatusEnum value : SpareInventoryPlanStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}
}
