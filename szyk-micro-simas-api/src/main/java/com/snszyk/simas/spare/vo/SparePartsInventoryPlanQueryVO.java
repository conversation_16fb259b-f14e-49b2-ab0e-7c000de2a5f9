package com.snszyk.simas.spare.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "SparePartsInventoryPlanQueryVO对象", description = "盘点计划表")
public class SparePartsInventoryPlanQueryVO {

	@ApiModelProperty(value = "计划名称")
	private String name;

	@ApiModelProperty(value = "开始日期")
	private String startDate;

	@ApiModelProperty(value = "结束日期")
	private String endDate;

	@ApiModelProperty(value = "仓库id")
	private String warehouseId;

	@ApiModelProperty(value = "状态")
	private Integer status;

}
