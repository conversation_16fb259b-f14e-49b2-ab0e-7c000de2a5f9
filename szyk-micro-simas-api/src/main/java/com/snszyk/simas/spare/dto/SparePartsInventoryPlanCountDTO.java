package com.snszyk.simas.spare.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SparePartsInventoryPlanCountDTO {

	@ApiModelProperty(value = "全部")
	private int total;
	@ApiModelProperty(value = "已盘点数量")
	private int finish;
	@ApiModelProperty(value = "未盘点数量")
	private int unComplete;
	@ApiModelProperty(value = "盘点中数量")
	private int process;
}
