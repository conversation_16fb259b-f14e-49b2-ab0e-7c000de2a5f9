/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * 备品备件库存实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundOrderVo对象", description = "备品备件入库单")
public class SparePartsInboundOrderSaveVO extends BaseCrudSlimVo {

	/**
	 * 入库类型,字典
	 */
	@NotBlank
	@ApiModelProperty(value = "入库类型,字典", required = true)
	private String inboundType;
	/**
	 * 库房id
	 */
	@NotNull
	@ApiModelProperty(value = "库房id", required = true)
	private Long warehouseId;
	/**
	 * 入库日期
	 */
	@NotNull
	@ApiModelProperty(value = "入库日期,yyyy-MM-dd", required = true)
	private LocalDate inboundDate;
	/**
	 * 入库人id
	 */
	@NotNull
	@ApiModelProperty(value = "入库人id", required = true)
	private Long inboundUserId;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 入库明细
	 */
	@Valid
	@ApiModelProperty(value = "入库明细")
	@NotEmpty
	@Size(min = 1)
	private List<SparePartsInboundItemVO> itemList;

}
