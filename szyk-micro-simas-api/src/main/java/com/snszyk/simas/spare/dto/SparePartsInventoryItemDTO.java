/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInventoryItemDto对象", description = "备品备件盘点记录表")
public class SparePartsInventoryItemDTO extends BaseCrudDto {

	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	@ApiModelProperty(value = "盘点计划单号")
	private String planNo;
	/**
	 * 盘点计划名称
	 */
	@ApiModelProperty(value = "盘点计划名称")
	private String planName;
	/**
	 * 盘点计划开始日期
	 */
	@ApiModelProperty(value = "盘点计划开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点计划结束日期
	 */
	@ApiModelProperty(value = "盘点计划结束日期")
	private LocalDate planEndDate;
	/**
	 * 盘点工单id
	 */
	@ApiModelProperty(value = "盘点工单id")
	private Long inventoryOrderId;
	/**
	 * 盘点工单号
	 */
	@ApiModelProperty(value = "盘点工单号")
	private String inventoryOrderNo;
	/**
	 * 仓库id
	 */
	@ApiModelProperty(value = "仓库id")
	private Long warehouseId;
	/**
	 * 仓库名称
	 */
	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;
	/**
	 * 库存id
	 */
	@ApiModelProperty(value = "库存id")
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	@ApiModelProperty(value = "备品备件字典id")
	private Long dictId;
	/**
	 * 盘点前系统库存（支持小数）
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "盘点前系统库存（支持小数）")
	private BigDecimal beforeSystemStock;
	/**
	 * 盘点后实际数量（支持小数）
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "盘点后实际数量（支持小数）")
	private BigDecimal afterCountedStock;
	/**
	 * 盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）
	 */
	@ApiModelProperty(value = "盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）")
	private Integer result;
	/**
	 * 盘点结果名称
	 */
	@ApiModelProperty(value = "盘点结果名称")
	private String resultName;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private Long inventoryUserId;
	/**
	 * 盘点人姓名
	 */
	@ApiModelProperty(value = "盘点人姓名")
	private String inventoryUserName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 备品备件字典名称
	 */
	@ApiModelProperty(value = "备品备件字典名称")
	private String dictName;
	/**
	 * 备品备件字典编号
	 */
	@ApiModelProperty(value = "备品备件字典编号")
	private String dictNo;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;

}
