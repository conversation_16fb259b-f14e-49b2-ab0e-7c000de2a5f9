/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsStockVo对象", description = "备品备件字典")
public class SparePartsStockVO extends BaseCrudSlimVo {

	/**
	 * 备品备件字典id
	 */
	@ApiModelProperty(value = "备品备件字典id")
	private Long dictId;
	/**
	 * 库房
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 当前数量
	 */
	@ApiModelProperty(value = "当前数量")
	private BigDecimal currentQuantity;


}
