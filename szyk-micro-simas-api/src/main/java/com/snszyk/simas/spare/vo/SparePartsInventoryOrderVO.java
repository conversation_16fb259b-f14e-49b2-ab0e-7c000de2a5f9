/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInventoryOrderVo对象", description = "备品备件盘点记录表")
public class SparePartsInventoryOrderVO extends BaseCrudVo {

	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 盘点单号
	 */
	@ApiModelProperty(value = "盘点单号")
	private String no;
	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	@ApiModelProperty(value = "盘点计划单号")
	private String planNo;
	/**
	 * 盘点名称
	 */
	@ApiModelProperty(value = "盘点名称")
	private String planName;
	/**
	 * 盘点开始日期
	 */
	@ApiModelProperty(value = "盘点开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	@ApiModelProperty(value = "盘点结束日期")
	private LocalDate planEndDate;
	/**
	 * 仓库id
	 */
	@ApiModelProperty(value = "仓库id")
	private Long warehouseId;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private Long inventoryUserId;
	/**
	 * 盘点人员名称
	 */
	@ApiModelProperty(value = "盘点人员名称")
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	@ApiModelProperty(value = "完成时间")
	private LocalDateTime completeTime;
	/**
	 * 总数
	 */
	@ApiModelProperty(value = "总数")
	private Integer totalQuantity;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;


}
