package com.snszyk.simas.spare.beanmapper;

import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface SparePartsInboundItemBeanMapper {
	SparePartsInboundItemBeanMapper INSTANCE = Mappers.getMapper(SparePartsInboundItemBeanMapper.class);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "warehouseId")
	@Mapping(target = "dictId", source = "inboundItemVO.dictId")
	@Mapping(target = "currentQuantity", source = "inboundItemVO.inboundQuantity")
	SparePartsStockVO toStockVO(Long warehouseId, SparePartsInboundItemVO inboundItemVO);

}
