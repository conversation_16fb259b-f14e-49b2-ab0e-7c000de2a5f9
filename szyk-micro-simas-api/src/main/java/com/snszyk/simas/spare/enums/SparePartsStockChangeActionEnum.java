package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 备品备件库存变更动作
 * ClassName: SparePartsInventoryEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 17:03
 */
@AllArgsConstructor
@Getter
public enum SparePartsStockChangeActionEnum {
	/**
	 * 入库
	 */
	IN("入库"),
	/**
	 * 出库
	 */
	OUT("出库"),
	/**
	 * 盘点
	 */
	INVENTORY("盘点"),
	/**
	 * 临时盘点
	 */
	INTERIM_INVENTORY("临时盘点"),


	;

	private String desc;
}
