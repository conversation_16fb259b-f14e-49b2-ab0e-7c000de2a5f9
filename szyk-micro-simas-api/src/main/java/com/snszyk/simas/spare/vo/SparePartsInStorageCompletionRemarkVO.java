package com.snszyk.simas.spare.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 入库单完结备注
 * ClassName: SparePartsInStorageCompletionRemark
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/7 15:14
 */
@Data
@AllArgsConstructor
public class SparePartsInStorageCompletionRemarkVO {
	/**
	 * id
	 */
	@NotNull
	@ApiModelProperty(value = "入库单id", required = true)
	private Long id;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String completionRemark;
}
