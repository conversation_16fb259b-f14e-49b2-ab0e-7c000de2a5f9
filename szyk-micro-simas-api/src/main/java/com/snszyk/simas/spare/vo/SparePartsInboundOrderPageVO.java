/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 备品备件入库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundOrderPageVO对象", description = "备品备件入库单")
public class SparePartsInboundOrderPageVO extends BaseCrudSlimVo {
	/**
	 * 入库类型,字典
	 */
	@ApiModelProperty(value = "入库类型,字典")
	private String inboundType;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 开始入库日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "开始入库日期")
	private LocalDate startInboundDate;
	/**
	 * 结束入库日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "结束入库日期")
	private LocalDate endInboundDate;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;

}
