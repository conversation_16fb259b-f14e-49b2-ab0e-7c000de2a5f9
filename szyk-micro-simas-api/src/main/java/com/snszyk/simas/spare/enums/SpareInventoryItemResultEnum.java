package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
@AllArgsConstructor
public enum SpareInventoryItemResultEnum {

	NOT_START(0, "未盘点"),
	NORMAL(1, "正常"),
	PROFIT(2, "盘盈"),
	LOSS(3, "盘亏"),
	;

	final Integer code;
	final String name;

	public static SpareInventoryItemResultEnum getByCode(Integer code){
		for (SpareInventoryItemResultEnum value : SpareInventoryItemResultEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

	/**
	 * 计算盘点结果
	 * 统一的盘点结果计算逻辑，避免代码重复
	 *
	 * @param beforeSystemStock 系统库存（支持小数）
	 * @param afterCountedStock 盘点库存
	 * @return 盘点结果枚举
	 * @throws IllegalArgumentException 当参数为null时抛出异常
	 */
	public static SpareInventoryItemResultEnum calculateResult(BigDecimal beforeSystemStock, BigDecimal afterCountedStock) {
		if (beforeSystemStock == null || afterCountedStock == null) {
			throw new IllegalArgumentException("beforeSystemStock and afterCountedStock can not be null");
		}

		int comparison = beforeSystemStock.compareTo(afterCountedStock);
		if (comparison > 0) {
			// 盘点后数量小于盘点前数量：盘亏
			return LOSS;
		} else if (comparison < 0) {
			// 盘点后数量大于盘点前数量：盘盈
			return PROFIT;
		} else {
			// 盘点后数量等于盘点前数量：正常
			return NORMAL;
		}
	}

	/**
	 * 计算盘点结果并返回状态码
	 * 便捷方法，直接返回状态码而不是枚举对象
	 *
	 * @param beforeSystemStock 系统库存（支持小数）
	 * @param afterCountedStock 盘点库存
	 * @return 盘点结果状态码
	 * @throws IllegalArgumentException 当参数为null时抛出异常
	 */
	public static Integer calculateResultCode(BigDecimal beforeSystemStock, BigDecimal afterCountedStock) {
		return calculateResult(beforeSystemStock, afterCountedStock).getCode();
	}
}
