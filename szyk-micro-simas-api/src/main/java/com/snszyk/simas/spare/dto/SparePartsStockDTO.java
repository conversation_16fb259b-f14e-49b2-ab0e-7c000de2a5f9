/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件库存实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsStockDto对象", description = "备品备件库存")
public class SparePartsStockDTO extends BaseCrudDto {

	/**
	 * 备品备件字典id
	 */
	@ApiModelProperty(value = "备品备件字典id")
	private Long dictId;
	/**
	 * 备品备件编号
	 */
	@ApiModelProperty(value = "备品备件编号")
	private String dictNo;
	/**
	 * 库房
	 */
	@ApiModelProperty(value = "库房")
	private Long warehouseId;
	/**
	 * 库房名称
	 */
	@ApiModelProperty(value = "库房名称")
	private String warehouseName;
	/**
	 * 当前数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "当前数量")
	private BigDecimal currentQuantity;
	/**
	 * 备品备件名称
	 */
	@ApiModelProperty(value = "备品备件名称")
	private String dictName;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;


}
