/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件入库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundItemDto对象", description = "备品备件入库明细")
public class SparePartsInboundItemDTO extends BaseCrudDto {

	/**
	 * 入库单id
	 */
	@ApiModelProperty(value = "入库单id")
	private Long inboundOrderId;
	/**
	 * 备品备件字典id
	 */
	@ApiModelProperty(value = "备品备件字典id")
	private Long dictId;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 入库数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "入库数量")
	private BigDecimal inboundQuantity;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 备品备件编号
	 */
	@ApiModelProperty(value = "备品备件编号")
	private String dictNo;
	/**
	 * 备品备件名称
	 */
	@ApiModelProperty(value = "备品备件名称")
	private String dictName;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;

}
