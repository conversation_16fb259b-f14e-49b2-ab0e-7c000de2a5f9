/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 备品备件出库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsOutboundOrderDto对象", description = "备品备件出库单")
public class SparePartsOutboundOrderDTO extends BaseCrudDto {

	/**
	 * 出库单号
	 */
	@ApiModelProperty(value = "出库单号")
	private String no;
	/**
	 * 出库类型,字典，1-请领出库；2-其他出库
	 */
	@ApiModelProperty(value = "出库类型,字典，1-请领出库；2-其他出库")
	private String outboundType;
	/**
	 * 出库类型名称
	 */
	@ApiModelProperty(value = "出库类型名称")
	private String outboundTypeName;
	/**
	 * 用途,字典
	 */
	@ApiModelProperty(value = "用途,字典")
	private String outboundUse;
	/**
	 * 用途名称
	 */
	@ApiModelProperty(value = "用途名称")
	private String outboundUseName;
	/**
	 * 出库日期
	 */
	@ApiModelProperty(value = "出库日期")
	private LocalDate outboundDate;
	/**
	 * 领用单id
	 */
	@ApiModelProperty(value = "领用单id")
	private Long issuanceOrderId;
	/**
	 * 领用单名称
	 */
	@ApiModelProperty(value = "领用单名称")
	private String issuanceOrderName;
	/**
	 * 领用总数量
	 */
	@ApiModelProperty(value = "领用总数量")
	private BigDecimal totalQuantity;
	/**
	 * 领用部门id
	 */
	@ApiModelProperty(value = "领用部门id")
	private Long receiveDeptId;
	/**
	 * 领用部门名称
	 */
	@ApiModelProperty(value = "领用部门名称")
	private String receiveDeptName;
	/**
	 * 领用人id
	 */
	@ApiModelProperty(value = "领用人id")
	private Long receiveUserId;
	/**
	 * 领用人姓名
	 */
	@ApiModelProperty(value = "领用人姓名")
	private String receiveUserName;
	/**
	 * 出库库房id
	 */
	@ApiModelProperty(value = "出库库房id")
	private Long warehouseId;
	/**
	 * 出库库房名称
	 */
	@ApiModelProperty(value = "出库库房名称")
	private String warehouseName;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;
	/**
	 * 状态名称
	 */
	@ApiModelProperty(value = "状态名称")
	private String statusName;
	/**
	 * 是否允许撤销
	 */
	@ApiModelProperty(value = "是否允许撤销")
	private Boolean allowCancel;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 出库单明细
	 */
	@ApiModelProperty(value = "出库单明细")
	private List<SparePartsOutboundItemDTO> itemList;

}
