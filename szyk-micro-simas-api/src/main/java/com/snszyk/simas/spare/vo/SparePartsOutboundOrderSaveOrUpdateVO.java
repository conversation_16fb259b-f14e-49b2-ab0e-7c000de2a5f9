/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.simas.common.crud.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 备品备件出库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@ApiModel(value = "SparePartsOutboundOrderSaveOrUpdateVO对象", description = "备品备件出库单")
public class SparePartsOutboundOrderSaveOrUpdateVO {

	/**
	 * id
	 */
	@NotNull(groups = Update.class)
	@ApiModelProperty(value = "修改时必传")
	private Long id;
	/**
	 * 出库类型,字典，1-请领出库；2-其他出库
	 */
	@NotBlank
	@ApiModelProperty(value = "出库类型,字典，1-请领出库；2-其他出库", required = true)
	private String outboundType;
	/**
	 * 用途,字典
	 */
	@NotBlank
	@ApiModelProperty(value = "用途,字典", required = true)
	private String outboundUse;
	/**
	 * 出库日期
	 */
	@NotNull
	@ApiModelProperty(value = "出库日期", required = true)
	private LocalDate outboundDate;
	/**
	 * 请领单id
	 */
	@ApiModelProperty(value = "请领单id")
	private Long issuanceOrderId;
	/**
	 * 领用部门id
	 */
	@NotNull
	@ApiModelProperty(value = "领用部门id", required = true)
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	@NotNull
	@ApiModelProperty(value = "领用人id", required = true)
	private Long receiveUserId;
	/**
	 * 出库库房id
	 */
	@ApiModelProperty(value = "出库库房id")
	private Long warehouseId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	@NotEmpty
	@Valid
	@ApiModelProperty(value = "出库明细")
	private List<SparePartsOutboundItemVO> itemList;
}
