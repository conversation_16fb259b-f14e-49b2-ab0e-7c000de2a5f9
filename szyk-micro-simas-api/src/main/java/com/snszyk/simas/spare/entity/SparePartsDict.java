/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@TableName("simas_spare_parts_dict")
@EqualsAndHashCode(callSuper = true)
public class SparePartsDict extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 编号
	 */
	private String no;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 规格型号
	 */
	private String model;
	/**
	 * 计量单位id
	 */
	private Long measureUnitId;
	/**
	 * 默认库房id
	 */
	private Long defaultWarehouseId;
	/**
	 * 安全库存数量
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private BigDecimal safeStockAmount;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;

}
