/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 备品备件出库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsOutboundItemVo对象", description = "备品备件出库明细")
public class SparePartsOutboundItemVO extends BaseCrudVo {

	/**
	 * 出库单id
	 */
	@ApiModelProperty(value = "出库单id", hidden = true)
	private Long outboundOrderId;
	/**
	 * 备品备件库存id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件库存id", required = true)
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件字典id", required = true)
	private Long dictId;
	/**
	 * 备品备件仓库id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件仓库id", required = true)
	private Long warehouseId;
	/**
	 * 出库数量
	 */
	@NotNull
	@ApiModelProperty(value = "出库数量", required = true)
	private BigDecimal outboundQuantity;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除", hidden = true)
	private Long deleteTime;


}
