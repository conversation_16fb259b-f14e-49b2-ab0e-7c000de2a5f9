/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsDictSaveOrUpdateVo对象", description = "备品备件字典")
public class SparePartsDictSaveOrUpdateVo extends BaseCrudVo {
	/**
	 * id
	 */
	@ApiModelProperty(value = "id,修改时必传")
	private Long id;
	/**
	 * 编号
	 */
	@NotBlank
	@ApiModelProperty(value = "编号", required = true)
	private String no;
	/**
	 * 名称
	 */
	@NotBlank
	@ApiModelProperty(value = "名称", required = true)
	private String name;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@NotNull
	@ApiModelProperty(value = "计量单位id", required = true)
	private Long measureUnitId;
	/**
	 * 默认库房id
	 */
	@ApiModelProperty(value = "默认库房id")
	private Long defaultWarehouseId;
	/**
	 * 安全库存数量
	 */
	@ApiModelProperty(value = "安全库存数量")
	private BigDecimal safeStockAmount;


}
