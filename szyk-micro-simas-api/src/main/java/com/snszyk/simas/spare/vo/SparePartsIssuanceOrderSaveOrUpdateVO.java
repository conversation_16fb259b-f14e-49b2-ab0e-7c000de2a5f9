/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.simas.common.crud.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@ApiModel(value = "SparePartsIssuanceOrderVo对象", description = "备品备件领用单新增")
public class SparePartsIssuanceOrderSaveOrUpdateVO {
	/**
	 * id
	 */
	@NotNull(groups = {Update.class})
	@ApiModelProperty(value = "修改时必传")
	private Long id;

	/**
	 * 名称
	 */
	@NotBlank
	@ApiModelProperty(value = "名称", required = true)
	private String name;
	/**
	 * 领用部门id
	 */
	@NotNull
	@ApiModelProperty(value = "领用部门id", required = true)
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	@NotNull
	@ApiModelProperty(value = "领用人id", required = true)
	private Long receiveUserId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 备品备件明细
	 */
	@Valid
	@NotEmpty
	@ApiModelProperty(value = "备品备件明细")
	private List<SparePartsIssuanceItemVO> itemList;
}
