/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件入库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@TableName("simas_spare_parts_inbound_item")
@EqualsAndHashCode(callSuper = true)
public class SparePartsInboundItem extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 入库单id
	 */
	private Long inboundOrderId;
	/**
	 * 备品备件字典id
	 */
	private Long dictId;
	/**
	 * 库房id
	 */
	private Long warehouseId;
	/**
	 * 入库数量
	 */
	private BigDecimal inboundQuantity;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
