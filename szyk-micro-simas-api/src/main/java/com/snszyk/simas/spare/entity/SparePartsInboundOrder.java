/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件库存实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@TableName("simas_spare_parts_inbound_order")
@EqualsAndHashCode(callSuper = true)
public class SparePartsInboundOrder extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 入库单号
	 */
	private String no;
	/**
	 * 入库类型,字典
	 */
	private String inboundType;
	/**
	 * 库房id
	 */
	private Long warehouseId;
	/**
	 * 入库日期
	 */
	private LocalDate inboundDate;
	/**
	 * 入库人id
	 */
	private Long inboundUserId;
	/**
	 * 供应商id
	 */
	private Long supplierId;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 完结备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String completionRemark;
	/**
	 * 备注时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime completionRemarkDateTime;
	/**
	 * 备注人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long completionRemarkUserId;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
