package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IssuanceOrderStatusEnum {
	WAIT(1, "待审核"),
	WAIT_CHECKOUT(2, "已审核待出库"),
	REJECT(3, "驳回"),
	CANCEL(4, "撤销"),
	IS_COMPLETED(5, "已完成");


	final Integer code;
	final String name;

	public static IssuanceOrderStatusEnum getByCode(Integer code){
		for (IssuanceOrderStatusEnum value : IssuanceOrderStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}


}
