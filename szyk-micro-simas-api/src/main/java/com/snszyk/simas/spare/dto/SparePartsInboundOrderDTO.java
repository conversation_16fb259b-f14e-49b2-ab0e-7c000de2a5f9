/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 备品备件入库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundOrderDto对象", description = "备品备件入库单")
public class SparePartsInboundOrderDTO extends BaseCrudDto {

	/**
	 * 入库单号
	 */
	@ApiModelProperty(value = "入库单号")
	private String no;
	/**
	 * 入库类型,字典
	 */
	@ApiModelProperty(value = "入库类型,字典")
	private String inboundType;
	/**
	 * 入库类型名称
	 */
	@ApiModelProperty(value = "入库类型名称")
	private String inboundTypeName;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 库房名称
	 */
	@ApiModelProperty(value = "库房名称")
	private String warehouseName;
	/**
	 * 入库日期
	 */
	@ApiModelProperty(value = "入库日期")
	private LocalDate inboundDate;
	/**
	 * 入库人id
	 */
	@ApiModelProperty(value = "入库人id")
	private Long inboundUserId;
	/**
	 * 入库人姓名
	 */
	@ApiModelProperty(value = "入库人姓名")
	private String inboundUserName;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 供应商名称
	 */
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 更新人姓名
	 */
	@ApiModelProperty(value = "更新人姓名")
	private String updateUserName;
	/**
	 * 完结备注
	 */
	@ApiModelProperty(value = "完结备注")
	private String completionRemark;
	/**
	 * 备注时间
	 */
	@ApiModelProperty(value = "完结备注时间")
	private LocalDateTime completionRemarkDateTime;
	/**
	 * 备注人
	 */
	@ApiModelProperty(value = "完结备注人")
	private Long completionRemarkUserId;
	/**
	 * 备注人
	 */
	@ApiModelProperty(value = "完结备注人名称")
	private String completionRemarkUserName;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 入库单明细
	 */
	@ApiModelProperty(value = "入库单明细")
	private List<SparePartsInboundItemDTO> itemList;


}
