/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件领用单明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@TableName("simas_spare_parts_issuance_item")
@EqualsAndHashCode(callSuper = true)
public class SparePartsIssuanceItem extends BaseCrudEntity {

	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 领用单id
	 */
	private Long issuanceOrderId;
	/**
	 * 备品备件库存id
	 */
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	private Long dictId;
	/**
	 * 仓库id
	 */
	private Long warehouseId;
	/**
	 * 领用数量
	 */
	private BigDecimal issuanceQuantity;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
