/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInventoryItemPageVO对象", description = "备品备件盘点记录表")
public class SparePartsInventoryItemPageVO extends BaseCrudSlimVo {
	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 盘点工单id
	 */
	@ApiModelProperty(value = "盘点工单id")
	private Long inventoryOrderId;
	/**
	 * 盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）
	 */
	@ApiModelProperty(value = "盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）")
	private SpareInventoryItemResultEnum resultEnum;

	/**
	 * 备品备件名称（模糊查询）
	 */
	@ApiModelProperty(value = "备品备件名称（模糊查询）")
	private String dictName;


}
