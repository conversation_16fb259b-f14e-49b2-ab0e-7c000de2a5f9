package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("simas_spare_parts_inventory_plan")
@ApiModel(value = "InventoryPlan对象", description = "备品备件盘点计划表")
public class SparePartsInventoryPlan extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "计划编号")
	private String no;

	@ApiModelProperty(value = "计划名称")
	private String name;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "开始日期")
	private Date startDate;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "结束日期")
	private Date endDate;


	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "仓库id")
	private String warehouseId;

	@ApiModelProperty(value = "盘点人员")
	private String inventoryUser;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "完成时间")
	private Date completeTime;
}
