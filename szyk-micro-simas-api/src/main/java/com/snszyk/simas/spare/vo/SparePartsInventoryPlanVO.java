package com.snszyk.simas.spare.vo;

import com.snszyk.simas.inventory.entity.EquipmentInventoryPlan;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryPlanVO对象", description = "盘点计划表")
public class SparePartsInventoryPlanVO extends SparePartsInventoryPlan {

	@NotBlank(message = "计划名称不能为空")
	@ApiModelProperty(value = "计划名称")
	private String name;

	@NotNull(message = "计划开始日期不能为空")
	@ApiModelProperty(value = "开始日期")
	private Date startDate;

	@NotNull(message = "计划结束日期不能为空")
	@ApiModelProperty(value = "结束日期")
	private Date endDate;

	@ApiModelProperty(value = "仓库id")
	private String warehouseId;
}
