/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.Map;

/**
 * 数量统计结果DTO
 * 优化：使用强类型替代Map<String, Map<Long, Long>>，提高类型安全性
 * 设计理由：相比Guava Table，Map结构更简单直观，无需引入额外依赖，性能差异微小
 * 
 * 优势：
 * 1. 避免重复数据库查询 - 一次查询获取两种统计结果
 * 2. 类型安全 - 强类型比Map<String, Map<Long, Long>>更安全
 * 3. 代码可读性 - 明确的字段名比字符串key更直观
 * 4. 便于扩展 - 后续可以轻松添加新的统计字段
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "数量统计结果", description = "工单已盘点数量和总数量统计")
public class QuantityMapsDTO {

    @ApiModelProperty(value = "已盘点数量Map，key为工单ID，value为已盘点数量")
    private Map<Long, Long> hasInventoryMap = Collections.emptyMap();

    @ApiModelProperty(value = "总数量Map，key为工单ID，value为总数量")
    private Map<Long, Long> totalMap = Collections.emptyMap();

    /**
     * 创建空的统计结果
     */
    public static QuantityMapsDTO empty() {
        return new QuantityMapsDTO();
    }

    /**
     * 创建统计结果
     */
    public static QuantityMapsDTO of(Map<Long, Long> hasInventoryMap, Map<Long, Long> totalMap) {
        QuantityMapsDTO dto = new QuantityMapsDTO();
        dto.setHasInventoryMap(hasInventoryMap != null ? hasInventoryMap : Collections.emptyMap());
        dto.setTotalMap(totalMap != null ? totalMap : Collections.emptyMap());
        return dto;
    }
}
