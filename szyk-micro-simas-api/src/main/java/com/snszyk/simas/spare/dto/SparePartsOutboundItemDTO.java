/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件出库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsOutboundItemDto对象", description = "备品备件出库明细")
public class SparePartsOutboundItemDTO extends BaseCrudDto {

	/**
	 * 出库单id
	 */
	@ApiModelProperty(value = "出库单id")
	private Long outboundOrderId;
	/**
	 * 备品备件库存id
	 */
	@ApiModelProperty(value = "备品备件库存id")
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	@ApiModelProperty(value = "备品备件字典id")
	private Long dictId;
	/**
	 * 备品备件仓库id
	 */
	@ApiModelProperty(value = "备品备件仓库id")
	private Long warehouseId;
	/**
	 * 仓库名称
	 */
	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;
	/**
	 * 出库数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "出库数量")
	private BigDecimal outboundQuantity;
	/**
	 * 备品备件编号
	 */
	@ApiModelProperty(value = "备品备件编号")
	private String dictNo;
	/**
	 * 备品备件名称
	 */
	@ApiModelProperty(value = "备品备件名称")
	private String dictName;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;
	/**
	 * 库存数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "当前库存数量")
	private BigDecimal currentQuantity;


}
