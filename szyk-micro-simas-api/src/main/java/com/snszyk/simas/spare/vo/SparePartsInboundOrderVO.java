/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件入库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundOrderVo对象", description = "备品备件入库单")
public class SparePartsInboundOrderVO extends BaseCrudSlimVo {

	/**
	 * 入库单号
	 */
	@ApiModelProperty(value = "入库单号")
	private String no;
	/**
	 * 入库类型,字典
	 */
	@ApiModelProperty(value = "入库类型,字典")
	private String inboundType;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 入库日期
	 */
	@ApiModelProperty(value = "入库日期")
	private LocalDate inboundDate;
	/**
	 * 入库人id
	 */
	@ApiModelProperty(value = "入库人id")
	private Long inboundUserId;
	/**
	 * 供应商id
	 */
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 完结备注
	 */
	@ApiModelProperty(value = "完结备注")
	private String completionRemark;
	/**
	 * 备注时间
	 */
	@ApiModelProperty(value = "完结备注时间")
	private LocalDateTime completionRemarkDateTime;
	/**
	 * 备注人
	 */
	@ApiModelProperty(value = "完结备注人")
	private Long completionRemarkUserId;


}
