package com.snszyk.simas.spare.dto;

import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryPlanDTO对象", description = "备品备件盘点计划表")
public class SparePartsInventoryPlanDTO extends SparePartsInventoryPlan {

	@ApiModelProperty(value = "仓库")
	private String warehouse;

	@ApiModelProperty(value = "盘点人员")
	private String inventoryUserName;

	@ApiModelProperty(value = "盘点状态（未盘点、盘点中、已完成）")
	private String statusName;

	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "已盘数量")
	private Long inventory;

	@ApiModelProperty(value = "模块")
	private String moduleName;

	@ApiModelProperty(value = "盘点部门")
	private String inventoryDeptName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;

	@ApiModelProperty(value = "总数量")
	private Long totalQuantity;

}
