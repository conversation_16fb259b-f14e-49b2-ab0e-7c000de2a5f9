/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsIssuanceOrderVo对象", description = "备品备件领用单")
public class SparePartsIssuanceOrderPageVO extends BaseCrudSlimVo {

	/**
	 * 单号
	 */
	@ApiModelProperty(value = "单号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 领用单状态
	 */
	@ApiModelProperty(value = "领用单状态")
	private Integer status;
	/**
	 * 申请时间开始日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "申请时间开始日期")
	private LocalDate startCreateDate;
	/**
	 * 申请时间结束日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "申请时间结束日期")
	private LocalDate endCreateDate;
}
