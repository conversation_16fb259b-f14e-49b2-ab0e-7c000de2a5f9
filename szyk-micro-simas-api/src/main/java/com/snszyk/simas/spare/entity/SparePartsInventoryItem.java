/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@TableName("simas_spare_parts_inventory_item")
@EqualsAndHashCode(callSuper = true)
public class SparePartsInventoryItem extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 盘点计划id
	 */
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	private String planNo;
	/**
	 * 盘点计划名称
	 */
	private String planName;
	/**
	 * 盘点计划开始日期
	 */
	private LocalDate planStartDate;
	/**
	 * 盘点计划结束日期
	 */
	private LocalDate planEndDate;
	/**
	 * 盘点工单id
	 */
	private Long inventoryOrderId;
	/**
	 * 盘点工单号
	 */
	private String inventoryOrderNo;
	/**
	 * 仓库id
	 */
	private Long warehouseId;
	/**
	 * 库存id
	 */
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	private Long dictId;
	/**
	 * 盘点前系统库存（支持小数）
	 */
	private BigDecimal beforeSystemStock;
	/**
	 * 盘点后实际数量（支持小数）
	 */
	private BigDecimal afterCountedStock;
	/**
	 * 盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）
	 */
	private Integer result;
	/**
	 * 盘点人员id
	 */
	private Long inventoryUserId;
	/**
	 * 盘点人姓名
	 */
	private String inventoryUserName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
