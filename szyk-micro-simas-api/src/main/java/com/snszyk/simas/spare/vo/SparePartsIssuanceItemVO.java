/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 备品备件领用单明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsIssuanceItemVo对象", description = "备品备件领用单明细")
public class SparePartsIssuanceItemVO extends BaseCrudSlimVo {

	/**
	 * 领用单id
	 */
	@ApiModelProperty(value = "领用单id", hidden = true)
	private Long issuanceOrderId;
	/**
	 * 备品备件库存id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件库存id", required = true)
	private Long stockId;
	/**
	 * 备品备件字典id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件字典id", required = true)
	private Long dictId;
	/**
	 * 仓库id
	 */
	@NotNull
	@ApiModelProperty(value = "仓库id", required = true)
	private Long warehouseId;
	/**
	 * 领用数量
	 */
	@NotNull
	@ApiModelProperty(value = "领用数量", required = true)
	private BigDecimal issuanceQuantity;


}
