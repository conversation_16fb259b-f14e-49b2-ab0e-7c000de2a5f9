package com.snszyk.simas.spare.beanmapper;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsInventoryOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryOrder;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.entity.SparePartsWarehouse;
import com.snszyk.simas.spare.vo.SparePartsInventoryOrderVO;
import com.snszyk.user.cache.UserCache;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface SparePartsInventoryOrderBeanMapper {
	SparePartsInventoryOrderBeanMapper INSTANCE = Mappers.getMapper(SparePartsInventoryOrderBeanMapper.class);

	SparePartsInventoryOrderVO toVO(SparePartsInventoryOrderDTO orderDTO);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "tenantId", source = "plan.tenantId")
	@Mapping(target = "planId", source = "plan.id")
	@Mapping(target = "planNo", source = "plan.no")
	@Mapping(target = "planName", source = "plan.name")
	@Mapping(target = "planStartDate", source = "plan.startDate")
	@Mapping(target = "planEndDate", source = "plan.endDate")
	@Mapping(target = "warehouseId", source = "warehouse.id")
	@Mapping(target = "inventoryUserId", source = "warehouse.manager")
	@Mapping(target = "inventoryUserName", source = "warehouse.manager", qualifiedByName = "getWarehouseManagerName")
	SparePartsInventoryOrderVO toVO(SparePartsWarehouse warehouse, SparePartsInventoryPlan plan);


	@Named("getWarehouseManagerName")
	default String getWarehouseManagerName(Long inventoryUserId) {
		if (ObjectUtil.isEmpty(inventoryUserId)) {
			return null;
		}
		return Optional.ofNullable(UserCache.getUser(inventoryUserId))
			.map(user -> user.getRealName())
			.orElse(null);
	}

	Collection<SparePartsInventoryOrder> toEntity(List<SparePartsInventoryOrderVO> orderVOList);
}
