package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpareInventoryOrderStatusEnum {

	/**
	 * 未启动
	 */
	NOT_START(0, "未启动"),
	PROCESS(1, "盘点中"),
	COMPLETE(2, "已完成"),
	;

	final Integer code;
	final String name;

	public static SpareInventoryOrderStatusEnum getByCode(Integer code) {
		for (SpareInventoryOrderStatusEnum value : SpareInventoryOrderStatusEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}
}
