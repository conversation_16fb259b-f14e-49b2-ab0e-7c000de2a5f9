/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 备件耗材表实体类
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Data
@Accessors(chain = true)
@TableName("simas_component_material")
@ApiModel(value = "ComponentMaterial对象", description = "备件耗材表")
public class ComponentMaterial extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String bizNo;
	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String bizModule;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 测量单位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测量单位id")
	private Long measureUnitId;
	/**
	 * 测量单位名称
	 */
	@ApiModelProperty(value = "测量单位名称")
	private String measureUnitName;
	/**
	 * 数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "数量")
	private BigDecimal count;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;


}
