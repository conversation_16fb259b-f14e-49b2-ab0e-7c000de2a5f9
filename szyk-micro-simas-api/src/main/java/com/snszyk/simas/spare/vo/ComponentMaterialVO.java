/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.simas.common.annotation.DynamicPrecision;
import com.snszyk.simas.spare.entity.ComponentMaterial;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 备件耗材表视图实体类
 *
 * <AUTHOR>
 * @since 2024-09-06
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ComponentMaterialVO对象", description = "备件耗材表")
public class ComponentMaterialVO extends ComponentMaterial {
	private static final long serialVersionUID = 1L;

	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String bizModuleName;
	/**
	 * 数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "数量")
	private BigDecimal count;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;
}
