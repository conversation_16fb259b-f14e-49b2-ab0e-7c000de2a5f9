/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsDictDto对象", description = "备品备件字典")
public class SparePartsDictDTO extends BaseCrudDto {

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;
	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;
	/**
	 * 默认库房id
	 */
	@ApiModelProperty(value = "默认库房id")
	private Long defaultWarehouseId;
	/**
	 * 库房名称
	 */
	@ApiModelProperty(value = "默认库房名称")
	private String defaultWarehouseName;
	/**
	 * 安全库存数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "安全库存数量")
	private BigDecimal safeStockAmount;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 删除时间
	 */
	@ApiModelProperty(value = "删除时间")
	private Long deleteTime;
	/**
	 * 状态名称
	 */
	@ApiModelProperty(value = "状态名称")
	private String statusName;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 更新人姓名
	 */
	@ApiModelProperty(value = "更新人姓名")
	private String updateUserName;


}
