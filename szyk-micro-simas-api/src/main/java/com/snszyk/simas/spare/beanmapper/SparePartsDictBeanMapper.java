package com.snszyk.simas.spare.beanmapper;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.simas.spare.dto.*;
import com.snszyk.simas.spare.entity.SparePartsDict;
import com.snszyk.simas.spare.vo.SparePartsDictPageVo;
import com.snszyk.simas.spare.vo.SparePartsDictVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper(uses = {AuthUtil.class})
public interface SparePartsDictBeanMapper {
	SparePartsDictBeanMapper INSTANCE = Mappers.getMapper(SparePartsDictBeanMapper.class);

	/**
	 * pageVO转VO
	 *
	 * @param v
	 * @return
	 */
	@BeanMapping(ignoreByDefault = true)
	@Mapping(source = "no", target = "likeNo")
	@Mapping(source = "name", target = "likeName")
	@Mapping(source = "model", target = "model")
	@Mapping(source = "current", target = "current")
	@Mapping(source = "size", target = "size")
	@Mapping(source = "status", target = "status")
	SparePartsDictVO toVO(SparePartsDictPageVo v);


	@Mapping(source = "no", target = "inboundItemDTO.dictNo")
	@Mapping(source = "name", target = "inboundItemDTO.dictName")
	@Mapping(source = "model", target = "inboundItemDTO.model")
	@Mapping(source = "measureUnitId", target = "inboundItemDTO.measureUnitId")
	@Mapping(source = "measureUnitName", target = "inboundItemDTO.measureUnitName")
	@Mapping(source = "measureUnitPrecision", target = "inboundItemDTO.measureUnitPrecision")
	@BeanMapping(ignoreByDefault = true)
	void setSparePartsInboundItemDTO(@MappingTarget SparePartsInboundItemDTO inboundItemDTO, SparePartsDictDTO dto);


	@Mapping(source = "no", target = "itemDTO.dictNo")
	@Mapping(source = "name", target = "itemDTO.dictName")
	@Mapping(source = "model", target = "itemDTO.model")
	@Mapping(source = "measureUnitId", target = "itemDTO.measureUnitId")
	@Mapping(source = "measureUnitName", target = "itemDTO.measureUnitName")
	@Mapping(source = "measureUnitPrecision", target = "itemDTO.measureUnitPrecision")
	@BeanMapping(ignoreByDefault = true)
	void setSparePartsIssuanceItemDTO(@MappingTarget SparePartsIssuanceItemDTO itemDTO, SparePartsDictDTO dictDTO);


	@Mapping(source = "no", target = "itemDTO.dictNo")
	@Mapping(source = "name", target = "itemDTO.dictName")
	@Mapping(source = "model", target = "itemDTO.model")
	@Mapping(source = "measureUnitId", target = "itemDTO.measureUnitId")
	@Mapping(source = "measureUnitName", target = "itemDTO.measureUnitName")
	@Mapping(source = "measureUnitPrecision", target = "itemDTO.measureUnitPrecision")
	@BeanMapping(ignoreByDefault = true)
	void setSparePartsOutboundItemDTO(@MappingTarget SparePartsOutboundItemDTO itemDTO, SparePartsDictDTO dictDTO);


	@BeanMapping(ignoreByDefault = true)
	@Mapping(source = "no", target = "itemDTO.dictNo")
	@Mapping(source = "name", target = "itemDTO.dictName")
	@Mapping(source = "model", target = "itemDTO.model")
	@Mapping(source = "measureUnitId", target = "itemDTO.measureUnitId")
	@Mapping(source = "measureUnitName", target = "itemDTO.measureUnitName")
	@Mapping(source = "measureUnitPrecision", target = "itemDTO.measureUnitPrecision")
	void setSparePartsInventoryItemDTO(@MappingTarget SparePartsInventoryItemDTO itemDTO, SparePartsDictDTO dictDTO);

	@Mapping(target = "createUser", ignore = true)
	@Mapping(target = "createDept", ignore = true)
	@Mapping(target = "createTime", ignore = true)
	@Mapping(target = "updateUser", ignore = true)
	@Mapping(target = "updateTime", ignore = true)
	@Mapping(target = "isDeleted", ignore = true)
	@Mapping(target = "deleteTime", ignore = true)
	SparePartsDict toEntity(SparePartsDictVO vo);

	SparePartsDictDTO toDTO(SparePartsDict entity);
}
