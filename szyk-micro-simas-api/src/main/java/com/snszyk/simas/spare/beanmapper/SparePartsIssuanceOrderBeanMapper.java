package com.snszyk.simas.spare.beanmapper;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsIssuanceOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsIssuanceOrder;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsIssuanceOrderVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper(uses = {AuthUtil.class})
public interface SparePartsIssuanceOrderBeanMapper {
	SparePartsIssuanceOrderBeanMapper INSTANCE = Mappers.getMapper(SparePartsIssuanceOrderBeanMapper.class);

	/**
	 * pageVO转VO
	 *
	 * @param v
	 * @return
	 */
	@BeanMapping(ignoreByDefault = true)
	@Mapping(source = "no", target = "likeNo")
	@Mapping(source = "name", target = "likeName")
	@Mapping(source = "status", target = "status")
	@Mapping(source = "startCreateDate", target = "startCreateTime", qualifiedByName = "toStartDateTime")
	@Mapping(source = "endCreateDate", target = "endCreateTime", qualifiedByName = "toEndDateTime")
	SparePartsIssuanceOrderVO toVO(SparePartsIssuanceOrderPageVO v);


	SparePartsIssuanceOrderVO toVO(SparePartsIssuanceOrderDTO dto);


	@Named("toStartDateTime")
	default LocalDateTime toLocalDateTime(LocalDate startCreateDate) {
		return ObjectUtil.isEmpty(startCreateDate) ? null : startCreateDate.atStartOfDay();
	}

	@Named("toEndDateTime")
	default LocalDateTime toEndDateTime(LocalDate endCreateDate) {
		return ObjectUtil.isEmpty(endCreateDate) ? null : endCreateDate.plusDays(SzykConstant.DB_STATUS_NORMAL).atStartOfDay();
	}

	SparePartsIssuanceOrderDTO toDTO(SparePartsIssuanceOrder entity);

	@Mapping(target = "createUser", ignore = true)
	@Mapping(target = "createDept", ignore = true)
	@Mapping(target = "createTime", ignore = true)
	@Mapping(target = "updateUser", ignore = true)
	@Mapping(target = "updateTime", ignore = true)
	@Mapping(target = "isDeleted", ignore = true)
	@Mapping(target = "deleteTime", ignore = true)
	SparePartsIssuanceOrder toEntity(SparePartsIssuanceOrderVO vo);
}
