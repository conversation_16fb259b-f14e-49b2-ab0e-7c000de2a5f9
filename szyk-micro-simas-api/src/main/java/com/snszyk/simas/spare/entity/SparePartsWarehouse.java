package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("simas_spare_parts_warehouse")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsWarehouse对象", description = "备品备件库房")
public class SparePartsWarehouse extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	private String no;

	@ApiModelProperty(value = "仓库名称")
	private String name;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "管理员")
	private Long manager;
	
	@ApiModelProperty(value = "仓库地址")
	private String tel;

	@ApiModelProperty(value = "备注")
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private String remark;

	@ApiModelProperty(value = "锁定库存 1:锁定")
	private Integer lockStock;

}
