package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpareInventoryOrderDraftStatusEnum {
	/**
	 * 草稿
	 */
	DRAFT(1, "草稿"),
	/**
	 * 已提交
	 */
	SUBMITTED(2, "已提交"),
	;

	final Integer code;
	final String name;

	public static SpareInventoryOrderDraftStatusEnum getByCode(Integer code) {
		for (SpareInventoryOrderDraftStatusEnum value : SpareInventoryOrderDraftStatusEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}
}
