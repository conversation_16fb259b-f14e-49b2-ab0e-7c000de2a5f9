package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 备品备件仓库锁定状态
 * ClassName: SparePartsWarehouseLockStatus
 * Package: com.snszyk.simas.spare.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/20 13:52
 */
@Getter
@AllArgsConstructor
public enum SparePartsWarehouseLockStatus {
	/**
	 * 锁定
	 */
	LOCK(1, "锁定"),
	/**
	 * 未锁定
	 */
	UNLOCK(0, "未锁定");;
	private Integer status;
	private String name;
}
