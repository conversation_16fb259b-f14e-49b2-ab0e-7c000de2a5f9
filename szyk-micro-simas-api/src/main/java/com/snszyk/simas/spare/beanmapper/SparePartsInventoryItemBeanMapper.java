package com.snszyk.simas.spare.beanmapper;

import com.snszyk.simas.spare.dto.SparePartsInventoryItemDTO;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.entity.SparePartsInventoryItem;
import com.snszyk.simas.spare.entity.SparePartsInventoryPlan;
import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.vo.*;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface SparePartsInventoryItemBeanMapper {
	SparePartsInventoryItemBeanMapper INSTANCE = Mappers.getMapper(SparePartsInventoryItemBeanMapper.class);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "tenantId", source = "plan.tenantId")
	@Mapping(target = "planId", source = "plan.id")
	@Mapping(target = "planNo", source = "plan.no")
	@Mapping(target = "planName", source = "plan.name")
	@Mapping(target = "planStartDate", source = "plan.startDate")
	@Mapping(target = "planEndDate", source = "plan.endDate")
	@Mapping(target = "inventoryOrderId", source = "orderVo.id")
	@Mapping(target = "inventoryOrderNo", source = "orderVo.no")
	@Mapping(target = "warehouseId", source = "orderVo.warehouseId")
	@Mapping(target = "inventoryUserId", source = "orderVo.inventoryUserId")
	@Mapping(target = "inventoryUserName", source = "orderVo.inventoryUserName")
	@Mapping(target = "stockId", source = "stock.id")
	@Mapping(target = "dictId", source = "stock.dictId")
	@Mapping(target = "beforeSystemStock", source = "stock.currentQuantity")
	SparePartsInventoryItemVO toVO(SparePartsInventoryPlan plan, SparePartsInventoryOrderVO orderVo, SparePartsStockDTO stock);

	List<SparePartsInventoryItem> toEntityList(List<SparePartsInventoryItemVO> itemVOList);

	List<SparePartsInventoryItemDTO> toDTOList(List<SparePartsInventoryItem> list);

	/**
	 * Entity转DTO
	 */
	SparePartsInventoryItemDTO toDTO(SparePartsInventoryItem entity);

	@Mapping(target = "result", source = "resultEnum", qualifiedByName = "convertToResult")
	SparePartsInventoryItemVO toVO(SparePartsInventoryItemPageVO pageVO);

	SparePartsInventoryItemVO toVO(SparePartsInventoryItemDTO DTO);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "id", source = "operationVO.id")
	@Mapping(target = "afterCountedStock", source = "operationVO.afterCountedStock")
	SparePartsInventoryItemVO toVO(SparePartsInventoryItemOperationVO operationVO);

	@Named("convertToResult")
	default Integer convertToResult(SpareInventoryItemResultEnum resultEnum) {
		if (resultEnum == null) {
			return null;
		}
		return resultEnum.getCode();
	}

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "dictId", source = "dictId")
	@Mapping(target = "warehouseId", source = "warehouseId")
	@Mapping(target = "currentQuantity", source = "afterCountedStock")
	SparePartsStockVO toStockVO(SparePartsInventoryItemDTO itemDTO);

	/**
	 * 将OperationVO和模板DTO转换为新的盘点明细VO
	 * 用于APP端动态添加新明细的场景
	 *
	 * @param operationVO 操作VO（包含用户输入的盘点数据）
	 * @param templateDTO 模板DTO（提供工单基础信息）
	 * @return 新的盘点明细VO
	 */
	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "id", ignore = true) // 新明细ID为空
	@Mapping(target = "stockId", ignore = true) // 新明细库存ID为空
	@Mapping(target = "dictId", source = "operationVO.dictId")
	@Mapping(target = "beforeSystemStock", source = "operationVO.beforeSystemStock")
	@Mapping(target = "afterCountedStock", source = "operationVO.afterCountedStock")
	@Mapping(target = "result", source = "operationVO", qualifiedByName = "calculateInventoryResult")
	// 从模板DTO复制工单基础信息
	@Mapping(target = "planId", source = "templateDTO.planId")
	@Mapping(target = "planNo", source = "templateDTO.planNo")
	@Mapping(target = "planName", source = "templateDTO.planName")
	@Mapping(target = "planStartDate", source = "templateDTO.planStartDate")
	@Mapping(target = "planEndDate", source = "templateDTO.planEndDate")
	@Mapping(target = "inventoryOrderId", source = "templateDTO.inventoryOrderId")
	@Mapping(target = "inventoryOrderNo", source = "templateDTO.inventoryOrderNo")
	@Mapping(target = "warehouseId", source = "templateDTO.warehouseId")
	@Mapping(target = "inventoryUserId", source = "templateDTO.inventoryUserId")
	@Mapping(target = "inventoryUserName", source = "templateDTO.inventoryUserName")
	SparePartsInventoryItemVO toNewItemVO(SparePartsInventoryItemOperationVO operationVO, SparePartsInventoryItemDTO templateDTO);

	/**
	 * 计算盘点结果
	 * 使用统一的枚举计算方法
	 */
	@Named("calculateInventoryResult")
	default Integer calculateInventoryResult(SparePartsInventoryItemOperationVO operationVO) {
		if (operationVO == null || operationVO.getBeforeSystemStock() == null || operationVO.getAfterCountedStock() == null) {
			return null;
		}

		// 使用枚举中的统一计算方法
		return SpareInventoryItemResultEnum.calculateResultCode(
			operationVO.getBeforeSystemStock(),
			operationVO.getAfterCountedStock()
		);
	}
}
