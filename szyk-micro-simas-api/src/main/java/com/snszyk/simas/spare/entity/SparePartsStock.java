/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件库存实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@TableName("simas_spare_parts_stock")
@EqualsAndHashCode(callSuper = true)
public class SparePartsStock extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 备品备件字典id
	 */
	private Long dictId;
	/**
	 * 库房
	 */
	private Long warehouseId;
	/**
	 * 当前数量
	 */
	private BigDecimal currentQuantity;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
