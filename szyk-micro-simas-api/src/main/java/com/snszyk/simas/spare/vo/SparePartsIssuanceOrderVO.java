/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsIssuanceOrderVo对象", description = "备品备件领用单")
public class SparePartsIssuanceOrderVO extends BaseCrudVo {

	/**
	 * 单号
	 */
	@ApiModelProperty(value = "单号")
	private String no;
	/**
	 * 模糊搜索单号
	 */
	@ApiModelProperty(value = "模糊搜索单号")
	private String likeNo;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 模糊搜索名称
	 */
	@ApiModelProperty(value = "模糊搜索名称")
	private String likeName;
	/**
	 * 领用部门id
	 */
	@ApiModelProperty(value = "领用部门id")
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	@ApiModelProperty(value = "领用人id")
	private Long receiveUserId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 审核人id
	 */
	@ApiModelProperty(value = "审核人id")
	private Long auditUserId;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private LocalDateTime auditTime;
	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;
	/**
	 * 申请时间开始日期
	 */
	@ApiModelProperty(value = "申请时间开始日期")
	private LocalDateTime startCreateTime;
	/**
	 * 申请时间结束日期
	 */
	@ApiModelProperty(value = "申请时间结束日期")
	private LocalDateTime endCreateTime;
	/**
	 * 领用总数量
	 */
	@ApiModelProperty(value = "领用总数量")
	private BigDecimal totalQuantity;
}
