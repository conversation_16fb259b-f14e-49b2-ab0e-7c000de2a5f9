/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 备品备件出库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@TableName("simas_spare_parts_outbound_item")
@EqualsAndHashCode(callSuper = true)
public class SparePartsOutboundItem extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private Long tenantId;
	/**
	* 出库单id
	*/
	private Long outboundOrderId;
	/**
	* 备品备件库存id
	*/
	private Long stockId;
	/**
	* 备品备件字典id
	*/
	private Long dictId;
	/**
	* 备品备件仓库id
	*/
	private Long warehouseId;
	/**
	* 出库数量
	*/
	private BigDecimal outboundQuantity;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
