/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInventoryOrderVo对象", description = "备品备件盘点记录表")
public class SparePartsInventoryOrderPageVO extends BaseCrudSlimVo {
	/**
	 * 盘点名称
	 */
	@ApiModelProperty(value = "盘点名称")
	private String planName;
	/**
	 * 盘点开始日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "盘点开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "盘点结束日期")
	private LocalDate planEndDate;
	/**
	 * 盘点状态值
	 */
	@ApiModelProperty(hidden = true)
	private Integer status;
	/**
	 * 不等于状态
	 */
	@ApiModelProperty(hidden = true)
	private Integer neStatus;

}
