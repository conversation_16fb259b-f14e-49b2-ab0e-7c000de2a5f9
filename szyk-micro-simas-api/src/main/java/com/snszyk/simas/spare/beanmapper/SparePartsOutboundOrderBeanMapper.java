package com.snszyk.simas.spare.beanmapper;

import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.spare.dto.SparePartsOutboundOrderDTO;
import com.snszyk.simas.spare.entity.SparePartsOutboundOrder;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderPageVO;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderSaveOrUpdateVO;
import com.snszyk.simas.spare.vo.SparePartsOutboundOrderVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface SparePartsOutboundOrderBeanMapper {
	SparePartsOutboundOrderBeanMapper INSTANCE = Mappers.getMapper(SparePartsOutboundOrderBeanMapper.class);


	SparePartsOutboundOrderVO toVO(SparePartsOutboundOrderSaveOrUpdateVO v, Integer status);

	@Mapping(source = "no", target = "likeNo")
	SparePartsOutboundOrderVO toVO(SparePartsOutboundOrderPageVO v);

	@Named("toStartDateTime")
	default LocalDateTime toLocalDateTime(LocalDate startCreateDate) {
		return ObjectUtil.isEmpty(startCreateDate) ? null : startCreateDate.atStartOfDay();
	}

	@Named("toEndDateTime")
	default LocalDateTime toEndDateTime(LocalDate endCreateDate) {
		return ObjectUtil.isEmpty(endCreateDate) ? null : endCreateDate.plusDays(SzykConstant.DB_STATUS_NORMAL).atStartOfDay();
	}

	@Mapping(target = "createUser", ignore = true)
	@Mapping(target = "createDept", ignore = true)
	@Mapping(target = "createTime", ignore = true)
	@Mapping(target = "updateUser", ignore = true)
	@Mapping(target = "updateTime", ignore = true)
	@Mapping(target = "isDeleted", ignore = true)
	@Mapping(target = "deleteTime", ignore = true)
	SparePartsOutboundOrder toEntity(SparePartsOutboundOrderVO vo);

	SparePartsOutboundOrderDTO toDTO(SparePartsOutboundOrder entity);

}
