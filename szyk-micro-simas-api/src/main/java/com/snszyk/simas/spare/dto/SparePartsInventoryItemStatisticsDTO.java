package com.snszyk.simas.spare.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@ApiModel(value = "备品备件盘点明细统计")
@Accessors(chain = true)
@NoArgsConstructor
public class SparePartsInventoryItemStatisticsDTO {

	@ApiModelProperty(value = "全部")
	private Long total;
	@ApiModelProperty(value = "正常")
	private Long normal;
	@ApiModelProperty(value = "未盘点")
	private Long notStart;
	@ApiModelProperty(value = "盘盈")
	private Long profit;
	@ApiModelProperty(value = "盘亏")
	private Long loss;

	public SparePartsInventoryItemStatisticsDTO init() {
		this.total = 0L;
		this.normal = 0L;
		this.notStart = 0L;
		this.profit = 0L;
		this.loss = 0L;
		return this;
	}
}
