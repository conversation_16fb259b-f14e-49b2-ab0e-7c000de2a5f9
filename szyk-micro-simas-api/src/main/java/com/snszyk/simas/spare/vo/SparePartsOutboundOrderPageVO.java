/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 备品备件出库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsOutboundOrderVo对象", description = "备品备件出库单")
public class SparePartsOutboundOrderPageVO extends BaseCrudSlimVo {

	/**
	 * 出库单号
	 */
	@ApiModelProperty(value = "出库单号")
	private String no;
	/**
	 * 出库类型,字典，1-请领出库；2-其他出库
	 */
	@ApiModelProperty(value = "出库类型,字典，1-请领出库；2-其他出库")
	private String outboundType;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "出库开始日期")
	private LocalDate startOutboundDate;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "出库结束日期")
	private LocalDate endOutboundDate;
}
