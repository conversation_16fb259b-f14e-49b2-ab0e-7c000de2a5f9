/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@TableName("simas_spare_parts_inventory_order")
@EqualsAndHashCode(callSuper = true)
public class SparePartsInventoryOrder extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 盘点单号
	 */
	private String no;
	/**
	 * 盘点计划id
	 */
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	private String planNo;
	/**
	 * 盘点名称
	 */
	private String planName;
	/**
	 * 盘点开始日期
	 */
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	private LocalDate planEndDate;
	/**
	 * 仓库id
	 */
	private Long warehouseId;
	/**
	 * 盘点人员id
	 */
	private Long inventoryUserId;
	/**
	 * 盘点人员名称
	 */
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	private LocalDateTime completeTime;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
