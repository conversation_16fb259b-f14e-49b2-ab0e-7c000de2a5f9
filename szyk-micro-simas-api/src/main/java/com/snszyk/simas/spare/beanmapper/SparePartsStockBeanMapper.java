package com.snszyk.simas.spare.beanmapper;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.simas.spare.dto.SparePartsStockDTO;
import com.snszyk.simas.spare.vo.SparePartsInboundItemVO;
import com.snszyk.simas.spare.vo.SparePartsStockVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ClassName: ArchiveBeanMapper
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper(uses = {AuthUtil.class})
public interface SparePartsStockBeanMapper {
	SparePartsStockBeanMapper INSTANCE = Mappers.getMapper(SparePartsStockBeanMapper.class);

	SparePartsStockVO toVO(SparePartsStockDTO dto);
}
