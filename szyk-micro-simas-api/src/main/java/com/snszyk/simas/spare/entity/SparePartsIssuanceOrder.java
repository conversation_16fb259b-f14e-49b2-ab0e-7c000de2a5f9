/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@TableName("simas_spare_parts_issuance_order")
@EqualsAndHashCode(callSuper = true)
public class SparePartsIssuanceOrder extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 单号
	 */
	private String no;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * 领用部门id
	 */
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	private Long receiveUserId;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 审核人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long auditUserId;
	/**
	 * 审核时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private LocalDateTime auditTime;
	/**
	 * 驳回原因
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String rejectReason;
	/**
	 * 领用总数量
	 */
	private BigDecimal totalQuantity;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
