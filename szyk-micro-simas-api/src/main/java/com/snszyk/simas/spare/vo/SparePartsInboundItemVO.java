/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 备品备件入库明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInboundItemVo对象", description = "备品备件入库明细")
public class SparePartsInboundItemVO extends BaseCrudSlimVo {

	/**
	 * 入库单id
	 */
	@ApiModelProperty(value = "入库单id", hidden = true)
	private Long inboundOrderId;
	/**
	 * 备品备件字典id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件字典id", required = true)
	private Long dictId;
	/**
	 * 库房id
	 */
	@ApiModelProperty(value = "库房id")
	private Long warehouseId;
	/**
	 * 入库数量
	 */
	@NotNull
	@ApiModelProperty(value = "入库数量", required = true)
	private BigDecimal inboundQuantity;


}
