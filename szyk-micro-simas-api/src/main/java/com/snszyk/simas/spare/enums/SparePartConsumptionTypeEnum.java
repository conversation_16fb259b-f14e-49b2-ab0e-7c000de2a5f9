package com.snszyk.simas.spare.enums;

import com.snszyk.simas.common.enums.SystemModuleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 备品备件消耗类型
 * ClassName: SparePartConsumptionTypeEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/26 9:39
 */
@AllArgsConstructor
@Getter
public enum SparePartConsumptionTypeEnum {
	/**
	 * 保养工工单
	 */
	MAINTAIN_ORDER(Arrays.asList(SystemModuleEnum.MAINTAIN_ORDER.getCode()), "保养"),
	/**
	 * 润滑工单
	 */
	LUBRICATE_ORDER(Arrays.asList(SystemModuleEnum.LUBRICATE_ORDER.getCode()), "润滑"),
	/**
	 * 维修工单 (内部修工单和外委维修单)
	 */
	REPAIR_ORDER(Arrays.asList(SystemModuleEnum.INTERNAL_REPAIR.getCode(), SystemModuleEnum.EXTERNAL_REPAIR.getCode()), "维修"),
	/**
	 * 检修工单
	 */
	OVERHAUL_ORDER(Arrays.asList(SystemModuleEnum.OVERHAUL_ORDER.getCode()), "检修"),
	;

	private final List<String> bizModuleList;
	private final String desc;


	public static SparePartConsumptionTypeEnum getByBizModule(String bizModule) {
		for (SparePartConsumptionTypeEnum item : values()) {
			if (item.bizModuleList.contains(bizModule)) {
				return item;
			}
		}
		return null;
	}
}
