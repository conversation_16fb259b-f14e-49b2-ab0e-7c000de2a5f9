/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.simas.spare.enums.SpareInventoryItemResultEnum;
import com.snszyk.simas.spare.validation.ExistingItemValidationGroup;
import com.snszyk.simas.spare.validation.NewItemValidationGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@ApiModel(value = "SparePartsInventoryItemOperationVO", description = "盘点明细操作对象")
public class SparePartsInventoryItemOperationVO {
	/**
	 * 明细ID - 已存在明细必须提供，临时新增明细为空
	 */
	@NotNull(groups = ExistingItemValidationGroup.class, message = "已存在明细的ID不能为空")
	@ApiModelProperty(value = "明细ID，已存在明细必须提供，临时新增明细为空")
	private Long id;

	/**
	 * 盘点工单ID - 所有场景都必须提供
	 */
	@NotNull(groups = {NewItemValidationGroup.class, ExistingItemValidationGroup.class}, message = "盘点工单ID不能为空")
	@ApiModelProperty(value = "盘点工单ID", required = true)
	private Long inventoryOrderId;

	/**
	 * 库存ID - 已存在明细通常有库存ID，临时新增明细为空
	 */
	@NotNull(groups = ExistingItemValidationGroup.class, message = "已存在明细的stockId不能为空")
	@ApiModelProperty(value = "库存ID，已存在明细通常有值，临时新增明细为空")
	private Long stockId;

	/**
	 * 备品备件字典ID - 所有场景都必须提供
	 */
	@NotNull(groups = {NewItemValidationGroup.class, ExistingItemValidationGroup.class}, message = "备品备件字典ID不能为空")
	@ApiModelProperty(value = "备品备件字典ID", required = true)
	private Long dictId;

	/**
	 * 盘点前系统库存 - 已存在明细必须提供，临时新增明细可为空（系统会设置为0）
	 */
	@NotNull(groups = ExistingItemValidationGroup.class, message = "已存在明细的盘点前系统库存不能为空")
	@ApiModelProperty(value = "盘点前系统库存，已存在明细必须提供，临时新增明细可为空")
	private BigDecimal beforeSystemStock;

	/**
	 * 盘点后实际数量 - 所有场景都必须提供
	 */
	@NotNull(groups = {NewItemValidationGroup.class, ExistingItemValidationGroup.class}, message = "盘点后实际数量不能为空")
	@ApiModelProperty(value = "盘点后实际数量", required = true)
	private BigDecimal afterCountedStock;
	/**
	 * 盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）
	 */
	@ApiModelProperty(value = "盘点结果（0：未盘点，1：正常，2：盘盈，3：盘亏）", hidden = true)
	private SpareInventoryItemResultEnum resultEnum;


}
