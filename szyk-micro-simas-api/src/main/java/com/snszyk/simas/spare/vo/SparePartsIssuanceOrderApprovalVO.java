/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.simas.common.enums.AuditStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@ApiModel(value = "SparePartsIssuanceOrderApprovalVO对象", description = "领用单审核")
public class SparePartsIssuanceOrderApprovalVO {
	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因,驳回时必传")
	private String rejectReason;
	/**
	 * 状态
	 */
	@NotNull
	@ApiModelProperty(value = "审核状态", required = true)
	private AuditStatusEnum auditStatusEnum;

}
