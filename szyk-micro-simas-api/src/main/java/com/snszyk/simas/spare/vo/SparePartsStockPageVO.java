/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsStockVo对象", description = "备品备件字典")
public class SparePartsStockPageVO extends BaseCrudSlimVo {
	/**
	 * 备品备件编号
	 */
	@ApiModelProperty(value = "备品备件编号")
	private String dictNo;
	/**
	 * 备品备件名称
	 */
	@ApiModelProperty(value = "备品备件名称")
	private String dictName;
	/**
	 * 库房
	 */
	@ApiModelProperty(value = "库房Id")
	private Long warehouseId;
	/**
	 * 库房ids
	 */
	@ApiModelProperty(value = "库房ids")
	private List<Long> warehouseIds;
	/**
	 * 库房名称
	 */
	@ApiModelProperty(value = "库房名称")
	private String warehouseName;


}
