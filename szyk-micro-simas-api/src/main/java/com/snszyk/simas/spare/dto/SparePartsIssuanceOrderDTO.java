/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 备品备件领用单实体类
 *
 * <AUTHOR>
 * @since 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsIssuanceOrderDto对象", description = "备品备件领用单")
public class SparePartsIssuanceOrderDTO extends BaseCrudDto {

	/**
	 * 单号
	 */
	@ApiModelProperty(value = "单号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 领用部门id
	 */
	@ApiModelProperty(value = "领用部门id")
	private Long receiveDeptId;
	/**
	 * 领用部门名称
	 */
	@ApiModelProperty(value = "领用部门名称")
	private String receiveDeptName;
	/**
	 * 领用人id
	 */
	@ApiModelProperty(value = "领用人id")
	private Long receiveUserId;
	/**
	 * 领用人姓名
	 */
	@ApiModelProperty(value = "领用人姓名")
	private String receiveUserName;
	/**
	 * 领用总数量
	 */
	@ApiModelProperty(value = "领用总数量")
	private BigDecimal totalQuantity;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 审核人
	 */
	@ApiModelProperty(value = "审核人id")
	private Long auditUserId;
	/**
	 * 审核人姓名
	 */
	@ApiModelProperty(value = "审核人姓名")
	private String auditUserName;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private LocalDateTime auditTime;
	/**
	 * 状态名称
	 */
	@ApiModelProperty(value = "状态名称")
	private String statusName;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;
	/**
	 * 领用明细
	 */
	@ApiModelProperty(value = "领用明细")
	private List<SparePartsIssuanceItemDTO> itemList;

}
