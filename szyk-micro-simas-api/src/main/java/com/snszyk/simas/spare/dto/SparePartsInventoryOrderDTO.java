/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 备品备件盘点记录表实体类
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsInventoryOrderDto对象", description = "备品备件盘点记录表")
public class SparePartsInventoryOrderDTO extends BaseCrudDto {

	/**
	 * 盘点单号
	 */
	@ApiModelProperty(value = "盘点单号")
	private String no;
	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	@ApiModelProperty(value = "盘点计划单号")
	private String planNo;
	/**
	 * 盘点名称
	 */
	@ApiModelProperty(value = "盘点名称")
	private String planName;
	/**
	 * 盘点开始日期
	 */
	@ApiModelProperty(value = "盘点开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	@ApiModelProperty(value = "盘点结束日期")
	private LocalDate planEndDate;
	/**
	 * 仓库id
	 */
	@ApiModelProperty(value = "仓库id")
	private Long warehouseId;
	/**
	 * 仓库名称
	 */
	@ApiModelProperty(value = "仓库名称")
	private String warehouseName;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private Long inventoryUserId;
	/**
	 * 盘点人员名称
	 */
	@ApiModelProperty(value = "盘点人员名称")
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	@ApiModelProperty(value = "完成时间")
	private LocalDateTime completeTime;
	/**
	 * 总数
	 */
	@ApiModelProperty(value = "总数")
	private Integer totalQuantity;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 已盘点数量
	 */
	@ApiModelProperty(value = "已盘数量")
	private Integer hasInventoryQuantity;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;
	/**
	 * 盘点工单状态名称
	 */
	@ApiModelProperty(value = "盘点工单状态名称")
	private String statusName;
	/**
	 * 盘点明细
	 */
	@ApiModelProperty(value = "盘点明细")
	private List<SparePartsInventoryItemDTO> itemList;

	/**
	 * 计划创建人id
	 */
	@ApiModelProperty(value = "计划创建人id")
	private Long planCreateUserId;

	/**
	 * 计划创建人
	 */
	@ApiModelProperty(value = "计划创建人")
	private String planCreateUserName;

}
