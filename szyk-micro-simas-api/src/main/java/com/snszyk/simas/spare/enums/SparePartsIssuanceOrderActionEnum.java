package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 备品备件领用动作
 * ClassName: SparePartsInventoryEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 17:03
 */
@AllArgsConstructor
@Getter
public enum SparePartsIssuanceOrderActionEnum {
	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00张三提交了申请单。
	 */
	INIT(IssuanceOrderStatusEnum.WAIT.getCode(), "%s%s提交了申请单"),
	/**
	 * 再次提交
	 * 2024/12/12 8：00：00张三再次提交了申请单。
	 */
	RE_SUBMIT(IssuanceOrderStatusEnum.WAIT.getCode(), "%s%s再次提交了申请单"),
	/**
	 * 审核通过
	 * 2024/12/12 8：00：00李四审核通过了申请单。
	 */
	AUDIT_PASS(IssuanceOrderStatusEnum.WAIT_CHECKOUT.getCode(), "%s%s审核通过了申请单"),
	/**
	 * 审核不通过
	 * 2024/12/12 8：00：00李四驳回了申请单，驳回原因“具体原因”。
	 */
	AUDIT_FAIL(IssuanceOrderStatusEnum.REJECT.getCode(), "%s%s驳回了申请单，驳回原因“%s”"),
	/**
	 * 撤销
	 * 2024/12/12 8：00：00张三撤销了申请单。
	 */
	CANCEL(IssuanceOrderStatusEnum.CANCEL.getCode(), "%s%s撤销了申请单"),
	;

	private Integer code;
	private String desc;


}
