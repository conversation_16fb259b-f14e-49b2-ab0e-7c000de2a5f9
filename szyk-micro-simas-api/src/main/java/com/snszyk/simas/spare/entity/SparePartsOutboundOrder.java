/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备品备件出库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@TableName("simas_spare_parts_outbound_order")
@EqualsAndHashCode(callSuper = true)
public class SparePartsOutboundOrder extends BaseCrudEntity {
	/**
	 * 租户id
	 */
	private Long tenantId;
	/**
	 * 出库单号
	 */
	private String no;
	/**
	 * 出库类型,字典，1-请领出库；2-其他出库
	 */
	private String outboundType;
	/**
	 * 用途,字典
	 */
	private String outboundUse;
	/**
	 * 出库日期
	 */
	private LocalDate outboundDate;
	/**
	 * 领用总数量
	 */
	private BigDecimal totalQuantity;
	/**
	 * 请领单id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long issuanceOrderId;
	/**
	 * 领用部门id
	 */
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	private Long receiveUserId;
	/**
	 * 出库库房id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long warehouseId;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;


}
