/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 备品备件字典实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsDictVo对象", description = "备品备件字典")
@NoArgsConstructor
@AllArgsConstructor
public class SparePartsDictVO extends BaseCrudVo {


	private Long neId;
	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;
	/**
	 * 计量单位id
	 */
	@ApiModelProperty(value = "计量单位id")
	private Long measureUnitId;
	/**
	 * 默认库房id
	 */
	@ApiModelProperty(value = "默认库房id")
	private Long defaultWarehouseId;
	/**
	 * 安全库存数量
	 */
	@ApiModelProperty(value = "安全库存数量")
	private BigDecimal safeStockAmount;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 删除时间
	 */
	@ApiModelProperty(value = "删除时间")
	private Long deleteTime;
	/**
	 * 编号
	 */
	@ApiModelProperty(value = "模糊搜索编号条件")
	private String likeNo;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "模糊搜索名称条件")
	private String likeName;

	public SparePartsDictVO(Long neId, String no) {
		this.neId = neId;
		this.no = no;
	}

	public SparePartsDictVO(Long neId, String name, String model) {
		this.neId = neId;
		this.name = name;
		this.model = model;
	}

}
