/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.spare.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备品备件出库单实体类
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SparePartsOutboundOrderVo对象", description = "备品备件出库单")
public class SparePartsOutboundOrderVO extends BaseCrudVo {

	/**
	 * 出库单号
	 */
	@ApiModelProperty(value = "出库单号")
	private String no;
	/**
	 * 出库类型,字典，1-请领出库；2-其他出库
	 */
	@ApiModelProperty(value = "出库类型,字典，1-请领出库；2-其他出库")
	private String outboundType;
	/**
	 * 用途,字典
	 */
	@ApiModelProperty(value = "用途,字典")
	private String outboundUse;
	/**
	 * 出库日期
	 */
	@ApiModelProperty(value = "出库日期")
	private LocalDate outboundDate;
	/**
	 * 出库总数量
	 */
	@ApiModelProperty(value = "出库总数量")
	private BigDecimal totalQuantity;
	/**
	 * 请领单id
	 */
	@ApiModelProperty(value = "请领单id")
	private Long issuanceOrderId;
	/**
	 * 领用部门id
	 */
	@ApiModelProperty(value = "领用部门id")
	private Long receiveDeptId;
	/**
	 * 领用人id
	 */
	@ApiModelProperty(value = "领用人id")
	private Long receiveUserId;
	/**
	 * 出库库房id
	 */
	@ApiModelProperty(value = "出库库房id")
	private Long warehouseId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 模糊搜索单号
	 */
	@ApiModelProperty(value = "模糊搜索单号")
	private String likeNo;
	/**
	 * 出库开始日期
	 */
	@ApiModelProperty(value = "出库开始日期")
	private LocalDate startOutboundDate;
	/**
	 * 出库结束日期
	 */
	@ApiModelProperty(value = "出库结束日期")
	private LocalDate endOutboundDate;

}
