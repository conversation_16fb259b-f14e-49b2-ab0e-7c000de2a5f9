package com.snszyk.simas.spare.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 备品备件临时盘点
 * ClassName: SparePartsInterimInventoryVO
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 9:47
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SparePartsInterimInventoryVO {
	/**
	 * id
	 */
	@NotNull
	@ApiModelProperty(value = "备品备件库存id", required = true)
	private Long stockId;
	/**
	 * 实际库存
	 */
	@Range(min = 0, max = 9999)
	@ApiModelProperty(value = "实际库存", required = true)
	private Integer currentAmount;
	/**
	 * 盘点原因
	 */
	@NotBlank
	@ApiModelProperty(value = "盘点原因", required = true)
	private String remark;
}
