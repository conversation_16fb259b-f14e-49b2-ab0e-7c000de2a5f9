package com.snszyk.simas.spare.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OutboundOrderStatusEnum {

	IN_COMPLETE(1, "完成"),
	CANCEL(2, "已撤销");

	final Integer code;
	final String name;

	public static OutboundOrderStatusEnum getByCode(Integer code){
		for (OutboundOrderStatusEnum value : OutboundOrderStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return OutboundOrderStatusEnum.IN_COMPLETE;
	}
}
