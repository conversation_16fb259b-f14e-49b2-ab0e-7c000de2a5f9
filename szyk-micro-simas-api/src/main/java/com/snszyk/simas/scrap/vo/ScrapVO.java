/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.vo;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.dto.EquipmentAccountDTO;
import com.snszyk.simas.scrap.entity.Scrap;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备报废单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ScrapVO对象", description = "设备报废单表")
public class ScrapVO extends Scrap {
	private static final long serialVersionUID = 1L;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operateUserName;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;
	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;
	/**
	 * 设备id列表
	 */
	@ApiModelProperty(value = "设备id列表")
	private String equipmentIds;
	/**
	 * 查询条件-开始日期
	 */
	@ApiModelProperty(value = "查询条件-开始日期")
	private String startDate;
	/**
	 * 查询条件-结束日期
	 */
	@ApiModelProperty(value = "查询条件-结束日期")
	private String endDate;
	/**
	 * 查询条件-设备名称或编码
	 */
	@ApiModelProperty(value = "查询条件-设备名称或编码")
	private String equipmentKeywords;
	/**
	 * 设备数量
	 */
	@ApiModelProperty(value = "设备数量")
	private Integer equipmentCount;
	/**
	 * 设备列表
	 */
	@ApiModelProperty(value = "设备列表")
	private List<DeviceAccountVO> detailList;



}
