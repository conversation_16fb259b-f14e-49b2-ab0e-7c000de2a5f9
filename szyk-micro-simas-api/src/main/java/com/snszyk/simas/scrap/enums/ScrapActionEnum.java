package com.snszyk.simas.scrap.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备报废动作枚举
 * ClassName: ScrapActionEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/17 10:39
 */
@AllArgsConstructor
@Getter
public enum ScrapActionEnum {
	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00张三提交了申请单。
	 */
	INIT(ScrapStatusEnum.TO_BE_AUDITED.getCode(), "%s%s提交了申请单"),
	/**
	 * 再次提交
	 * 2024/12/12 8：00：00张三再次提交了申请单。
	 */
	RE_SUBMIT(ScrapStatusEnum.TO_BE_AUDITED.getCode(), "%s%s再次提交了申请单"),
	/**
	 * 审核通过
	 * 2024/12/12 8：00：00李四审核通过了申请单。
	 */
	AUDIT_PASS(ScrapStatusEnum.IS_COMPLETED.getCode(), "%s%s审核通过了申请单"),
	/**
	 * 审核不通过
	 * 2024/12/12 8：00：00李四驳回了申请单，驳回原因“具体原因”。
	 */
	AUDIT_FAIL(ScrapStatusEnum.IS_REJECTED.getCode(), "%s%s驳回了申请单，驳回原因“%s”"),
	/**
	 * 撤销
	 * 2024/12/12 8：00：00张三撤销了申请单。
	 */
	CANCEL(ScrapStatusEnum.IS_CANCELLED.getCode(), "%s%s撤销了申请单"),
	;

	private Integer code;
	private String desc;
}
