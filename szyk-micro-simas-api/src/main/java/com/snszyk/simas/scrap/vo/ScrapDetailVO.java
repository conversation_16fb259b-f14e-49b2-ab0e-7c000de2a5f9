/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.vo;

import com.snszyk.simas.scrap.entity.ScrapDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 设备报废单明细表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ScrapDetailVO对象", description = "设备报废单明细表")
public class ScrapDetailVO extends ScrapDetail {
	private static final long serialVersionUID = 1L;

}
