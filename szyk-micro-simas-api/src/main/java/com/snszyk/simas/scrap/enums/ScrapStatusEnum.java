/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.scrap.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报废单状态枚举类
 *
 * <AUTHOR>
 * @date 2024/08/26 13:56
 **/
@Getter
@AllArgsConstructor
public enum ScrapStatusEnum {

	/**
	 * 待审核
	 */
	TO_BE_AUDITED(1, "待审核"),
	/**
	 * 已完成
	 */
	IS_COMPLETED(2, "已完成"),
	/**
	 * 已驳回
	 */
	IS_REJECTED(3, "已驳回"),
	/**
	 * 已撤销
	 */
	IS_CANCELLED(4, "已撤销"),
	;

	final Integer code;
	final String name;

	public static ScrapStatusEnum getByCode(Integer code){
		for (ScrapStatusEnum value : ScrapStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
