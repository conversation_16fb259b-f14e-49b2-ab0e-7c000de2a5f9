package com.snszyk.simas.ai.entity.chatflow;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SQL执行请求VO
 * <AUTHOR>
 * @Date 2025/04/10 17:34
 */
@Data
public class ConversationMessagesRequest {

	@JsonProperty("conversation_id")
	private String conversationId;

	@JsonProperty("first_id")
	private String firstId;

	private int limit;
	private String user;
}
