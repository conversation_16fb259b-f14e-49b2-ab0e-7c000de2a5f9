package com.snszyk.simas.ai.entity.chatflow;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 获取会话列表请求实体
 *
 * <AUTHOR>
 * @Date 2025/04/14 10:33
 */
@Data
public class ConversationRequest {
	private int limit;
	private String user;
	@JsonProperty("last_id")
	private String lastId;
	@JsonProperty("sort_by")
	private String sortBy;
}
