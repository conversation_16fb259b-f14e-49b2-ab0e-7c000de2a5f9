package com.snszyk.simas.ai.vo.chatflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SQL执行请求VO
 * <AUTHOR>
 * @Date 2025/04/10 17:34
 */
@Data
@ApiModel(value = "QaRequestVO", description = "QaRequestVO")
public class QaRequestVO {

	@ApiModelProperty(value = "查询内容", required = true)
	private String query;

	@ApiModelProperty(value = "会话id", required = false)
	private String conversationId;

	@ApiModelProperty(value = "任务id", required = false)
	private String taskId;

}
