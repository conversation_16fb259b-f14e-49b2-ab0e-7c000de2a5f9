package com.snszyk.simas.ai.dto.chatflow;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 获取会话列表响应实体
 * <AUTHOR>
 * @Date 2025/04/14 10:33
 */
@Data
public class ConversationResponseDTO {
	private int limit;
	private boolean hasMore;
	private List<DataItem> data;

	@Data
	public static class DataItem {
		private String id;
		private String name;
		private Map<String, Object> inputs;
		private String status;
		private long createdAt;
		private long updatedAt;
	}
}
