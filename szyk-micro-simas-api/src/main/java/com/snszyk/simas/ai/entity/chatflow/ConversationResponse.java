package com.snszyk.simas.ai.entity.chatflow;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 获取会话列表响应实体
 *
 * data (array[object]) 会话列表
 * id (string) 会话 ID
 * name (string) 会话名称，默认由大语言模型生成。
 * inputs (object) 用户输入参数。
 * status (string) 会话状态
 * introduction (string) 开场白
 * created_at (timestamp) 创建时间
 * updated_at (timestamp) 更新时间
 * has_more (bool)
 * limit (int) 返回条数，若传入超过系统限制，返回系统限制数量
 *
 * <AUTHOR>
 * @Date 2025/04/14 10:33
 */
@Data
public class ConversationResponse {
	private int limit;

	@JsonProperty("has_more")
	private boolean hasMore;
	private List<DataItem> data;

	@Data
	public static class DataItem {
		private String id;
		private String name;
		private Map<String, Object> inputs;
		private String status;

		@JsonProperty("created_at")
		private long createdAt;

		@JsonProperty("updated_at")
		private long updatedAt;

	}
}
