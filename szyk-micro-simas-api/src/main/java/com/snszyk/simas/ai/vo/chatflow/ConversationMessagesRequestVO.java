package com.snszyk.simas.ai.vo.chatflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SQL执行请求VO
 * <AUTHOR>
 * @Date 2025/04/10 17:34
 */
@Data
@ApiModel(value = "ConversationMessagesRequestVO", description = "ConversationMessagesRequestVO")
public class ConversationMessagesRequestVO {

	@ApiModelProperty(value = "会话ID", required = true)
	private String conversationId;

	@ApiModelProperty(value = "当前页第一条聊天记录的 ID，默认 null", required = false)
	private String firstId;

	@ApiModelProperty(value = "一次请求返回多少条聊天记录，默认 20 条。", required = false)
	private int limit;
}
