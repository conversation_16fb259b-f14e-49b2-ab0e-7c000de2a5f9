package com.snszyk.simas.ai.vo.chatflow;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChatMessagesRequestVO {
    private Map<String, Object> inputs;
    private String query;

    @JSONField(name = "response_mode")
    private String responseMode;

    @JSONField(name = "conversation_id")
    private String conversationId;
    private String user;
    private List<FileItem> files;


	@Data
    public static class FileItem {
		private String type;

		@JSONField(name = "transfer_method")
		private String transferMethod;

		private String url;
	}
}
