package com.snszyk.simas.ai.dto.chatflow;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 获取会话列表响应实体
 *
 * data (array[object]) 会话列表
 * id (string) 会话 ID
 * name (string) 会话名称，默认由大语言模型生成。
 * inputs (object) 用户输入参数。
 * status (string) 会话状态
 * introduction (string) 开场白
 * created_at (timestamp) 创建时间
 * updated_at (timestamp) 更新时间
 * has_more (bool)
 * limit (int) 返回条数，若传入超过系统限制，返回系统限制数量
 *
 * <AUTHOR>
 * @Date 2025/04/14 10:33
 */
@Data
public class ConversationMessagesResponseDTO {
	private int limit;

	private boolean hasMore;
	private List<DataItem> data;

	@Data
	public static class DataItem {
		private String id;
		private String conversationId;
		// 用户输入参数。
		private Map<String, Object> inputs;
		// 用户输入 / 提问内容。
		private String query;
		// 回答消息内容
		private String answer;
		// 消息文件列表
		private List<MessageFile> messageFiles;
		// 反馈信息
		private Object feedback;
		// 引用和归属分段列表
		private List<RetrieverResources> retrieverResources;
		private long createdAt;

	}

	/**
	 *  引用和归属分段列表
	 */
	@Data
	public static class RetrieverResources {
		private int position;
		private String datasetId;
		private String datasetName;
		private String documentId;
		private String documentName;
		private String segmentId;
		private double score;
		private String content;
	}

	@Data
	public static class MessageFile {
		private String id;
		// 文件类型，image 图片
		private String type;
		// 预览图片地址
		private String url;
		// 文件归属方，user 或 assistant
		private String belongsTo;
	}
}
