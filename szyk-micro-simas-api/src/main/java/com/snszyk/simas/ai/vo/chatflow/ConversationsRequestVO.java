package com.snszyk.simas.ai.vo.chatflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * SQL执行请求VO
 * <AUTHOR>
 * @Date 2025/04/10 17:34
 */
@Data
@ApiModel(value = "ConversationsRequestVO", description = "ConversationsRequestVO")
public class ConversationsRequestVO {

	@ApiModelProperty(value = "一次请求返回多少条记录，默认20条，最大100条，最小1条。", required = false)
	private int limit;

	@ApiModelProperty(value = "当前页最后面一条记录的 ID，默认 null", required = false)
	private String lastId;

}
