package com.snszyk.simas.ai.vo.chatflow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * SQL执行请求VO
 * <AUTHOR>
 * @Date 2025/04/10 17:34
 */
@Data
@ApiModel(value = "ConversationDeleteRequestVO", description = "ConversationDeleteRequestVO")
public class ConversationDeleteRequestVO {

	@NotBlank(message = "会话ID不能为空")
	@ApiModelProperty(value = "会话 ID", required = true)
	private String conversationId;

}
