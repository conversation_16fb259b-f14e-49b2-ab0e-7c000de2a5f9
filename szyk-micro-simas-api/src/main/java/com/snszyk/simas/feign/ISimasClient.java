/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.simas.feign;

import com.snszyk.common.equipment.feign.ICommonClientFallbackFactory;
import com.snszyk.core.tool.api.R;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Feign接口类
 *
 * <AUTHOR>
 * @Date 2025/3/18
 */
@FeignClient(
	value = "szyk-simas",
	fallbackFactory = ICommonClientFallbackFactory.class
)
public interface ISimasClient {
	String API_PREFIX = "/client/simas";
	String SUBMIT_PRE_FILE = API_PREFIX + "/submit-pre-file";
	String REMOVE_PRE_FILE = API_PREFIX + "/remove-pre-file";
	String LIST_FILE = API_PREFIX + "/list-file";
	String COUNT_BY_EQUIPMENT_ID = API_PREFIX + "/countByEquipmentId";
	String OVERHAUL_METHODS = API_PREFIX + "/overhaul-methods";
	/**
	 * 删除设备关联的信息
	 **/
	String DELETE_DEVICE_RELATION = API_PREFIX + "/delete-device-relation";
	/**
	 * @return 设备归还的资料
	 **/
	String LEASE_BACK_FILE = API_PREFIX + "/lease-back-file";
	/**
	 * @return 设备归还的信息
	 **/
	String LEASE_BACK_INFO = API_PREFIX + "/lease-back-info";
	/**
	 * 根据设备id删除文件
	 **/
	String DELETE_FILE_BY_EQUIPMENT_ID = API_PREFIX + "/delete-file-by-equipment-id";
	/**
	 * 根据部位ids查询是否有关联的标准
	 **/
	String HAS_STANDARDS_BY_MONITOR_IDS = API_PREFIX + "/has-standards-by-monitor-ids";

	/**
	 * 根据供应商id查询维修信息
	 */
	String LIST_REPAIR_BY_SUPPLIER_ID = API_PREFIX + "/list-repair-by-supplier-id";

	/**
	 * hasBusinessByMeasureUnit
	 */
	String HAS_BUSINESS_BY_MEASURE_UNIT = API_PREFIX + "/has-business-by-measure-unit";
	/**
	 * 根据设备文件类型id查询是否存在业务引用
	 */
	String HAS_BUSINESS_BY_FILE_CATEGORY_ID = API_PREFIX + "/has-business-by-file-category-id";
	/**
	 * 根据设备id查询是否有业务引用
	 */
	String HAS_BUSINESS_BY_EQUIPMENT_ID = API_PREFIX + "/has-business-by-equipment-id";

	@PostMapping(SUBMIT_PRE_FILE)
	@ApiOperation(value = "提交（前期资料管理）", notes = "传入preFile")
	R<Boolean> submitPreFile(@Valid @RequestBody EquipmentPreFileVO preFile);

	@PostMapping(REMOVE_PRE_FILE)
	@ApiOperation(value = "逻辑删除文件", notes = "传入ids")
	R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids);

	@PostMapping(LIST_FILE)
	@ApiOperation(value = "资料查询", notes = "")
	R<List<EquipmentFileVO>> listFile(@RequestBody EquipmentFileVO fileVO);

	@GetMapping(COUNT_BY_EQUIPMENT_ID)
	@ApiOperation(value = "文件统计", notes = "")
	R<Integer> countByEquipmentId(@RequestParam("equipmentId") Long equipmentId,
								  @RequestParam("categoryCode") String categoryCode);

	/**
	 * 获取检修方式
	 *
	 * @param id
	 * @return
	 */
	@GetMapping(OVERHAUL_METHODS)
	R<OverhaulMethods> getOverhaulMethods(@RequestParam("id") Long id);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description 设备归还的资料
	 * @Date 下午3:45 2025/3/21
	 * @Param
	 **/
	@PostMapping(LEASE_BACK_FILE)
	@ApiOperation(value = "设备归还的资料", notes = "传入equipmentId")
	R<List<Attach>> leaseBackFile(@ApiParam(value = "设备id", required = true) @RequestParam("equipmentId") Long equipmentId);

	@DeleteMapping(DELETE_DEVICE_RELATION)
	@ApiOperation(value = "删除设备关联的信息", notes = "传入equipmentId")
	R<Boolean> deleteDeviceRelation(@ApiParam(value = "设备ids", required = true) @RequestParam("equipmentIds") String equipmentIds);

	@GetMapping(LEASE_BACK_INFO)
	@ApiOperation(value = "设备归还的信息", notes = "传入equipmentId")
	R<DeviceBackDto> leaseBackInfo(@ApiParam(value = "设备id", required = true) @RequestParam("equipmentId") Long equipmentId);

	@DeleteMapping(DELETE_FILE_BY_EQUIPMENT_ID)
	@ApiOperation(value = "根据设备id删除文件", notes = "传入equipmentId")
	R<Boolean> deleteFileByEquipmentId(@ApiParam(value = "设备id", required = true) @RequestParam("equipmentId") Long equipmentId);

	/**
	 * 根据部位ids查询是否有关联的标准(包括点巡检、润滑、检修、保养的标准)
	 *
	 * @param monitorIds 部位ids，多个id用逗号分隔
	 * @return 关联的标准类型列表
	 */
	@GetMapping(HAS_STANDARDS_BY_MONITOR_IDS)
	@ApiOperation(value = "根据部位ids查询关联的标准类型", notes = "传入monitorIds")
	R<List<String>> hasStandardsByMonitorIds(@ApiParam(value = "部位ids", required = true) @RequestParam("monitorIds") String monitorIds);

	/**
	 * @param supplierIds
	 * @return
	 */
	@GetMapping(LIST_REPAIR_BY_SUPPLIER_ID)
	@ApiOperation(value = "根据供应商id查询维修信息", notes = "传入supplierIds")
	R<List<RepairDTO>> listRepairBySupplierIds(@ApiParam(value = "供应商id", required = true) @RequestParam("supplierIds") String supplierIds);

	/**
	 * 根据计量单位id查询是否存在业务引用
	 */
	@GetMapping(HAS_BUSINESS_BY_MEASURE_UNIT)
	@ApiOperation(value = "根据计量单位id查询是否存在业务引用", notes = "传入measureUnitId")
	R<Boolean> hasBusinessByMeasureUnit(@ApiParam(value = "计量单位id", required = true) @RequestParam("measureUnitId") Long measureUnitId);
	/**
	 * 根据设备文件类型id查询是否存在业务引用
	 */
	@GetMapping(HAS_BUSINESS_BY_FILE_CATEGORY_ID)
	@ApiOperation(value = "根据设备文件类型id查询是否存在业务引用", notes = "fileCategoryId")
	R<Boolean> hasBusinessByFileCategoryId(@ApiParam(value = "设备文件类型id", required = true) @RequestParam("fileCategoryId") Long fileCategoryId);
	/**
	 * 根据设备id查询是否有业务引用
	 */
	@GetMapping(HAS_BUSINESS_BY_EQUIPMENT_ID)
	@ApiOperation(value = "根据设备id查询是否有业务引用", notes = "传入deviceId")
	R<Boolean> hasBusinessByDeviceIds(@ApiParam(value = "设备ids", required = true) @RequestParam("deviceIds") String deviceIds);
}
