package com.snszyk.simas.feign;

import com.snszyk.core.tool.api.R;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.vo.EquipmentFileVO;
import com.snszyk.simas.common.vo.EquipmentPreFileVO;
import com.snszyk.simas.leaseback.dto.DeviceBackDto;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Feign接口类
 *
 * <AUTHOR>
 * @Date 2025/3/18
 */
@Component
public class ISimasClientFallbackFactory implements FallbackFactory<ISimasClient> {

	private static final Logger logger = LoggerFactory.getLogger(ISimasClientFallbackFactory.class);

	@Override
	public ISimasClient create(Throwable cause) {
		logger.error("Feign client error: {}", cause.getMessage(), cause);
		return new ISimasClientFallback(cause);
	}

	static class ISimasClientFallback implements ISimasClient {
		private final Throwable cause;

		ISimasClientFallback(Throwable cause) {
			this.cause = cause;
		}


		@Override
		public R<Boolean> submitPreFile(EquipmentPreFileVO preFile) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}

		@Override
		public R<Boolean> remove(String ids) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);

		}

		@Override
		public R<List<EquipmentFileVO>> listFile(EquipmentFileVO fileVO) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(new ArrayList<>());
		}

		@Override
		public R<Integer> countByEquipmentId(Long equipmentId, String categoryCode) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(null);
		}

		@Override
		public R<OverhaulMethods> getOverhaulMethods(Long id) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(null);
		}

		@Override
		public R<List<Attach>> leaseBackFile(Long equipmentId) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(new ArrayList<>());

		}

		@Override
		public R<Boolean> deleteDeviceRelation(String equipmentIds) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);

		}

		@Override
		public R<DeviceBackDto> leaseBackInfo(Long equipmentId) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(null);
		}

		@Override
		public R<Boolean> deleteFileByEquipmentId(Long equipmentId) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}

		@Override
		public R<List<String>> hasStandardsByMonitorIds(String monitorIds) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.data(new ArrayList<>());
		}

		@Override
		public R<List<RepairDTO>> listRepairBySupplierIds(String supplierIds) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}

		@Override
		public R<Boolean> hasBusinessByMeasureUnit(Long measureUnitId) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}

		@Override
		public R<Boolean> hasBusinessByFileCategoryId(Long fileCategoryId) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}

		@Override
		public R<Boolean> hasBusinessByDeviceIds(String deviceIds) {
			logger.error("Feign client error: {}", cause.getMessage(), cause);
			return R.status(false);
		}
	}
}
