/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修单类型枚举类
 *
 * <AUTHOR>
 * @date 2024/08/27 20:35
 **/
@Getter
@AllArgsConstructor
public enum RepairBizTypeEnum {

	/**
	 * 内部维修
	 */
	INTERNAL("INTERNAL", "内部维修"),

	/**
	 * 外委维修
	 */
	EXTERNAL("EXTERNAL", "外委维修"),

	;

	final String code;
	final String name;

	public static RepairBizTypeEnum getByCode(String code){
		for (RepairBizTypeEnum value : RepairBizTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
