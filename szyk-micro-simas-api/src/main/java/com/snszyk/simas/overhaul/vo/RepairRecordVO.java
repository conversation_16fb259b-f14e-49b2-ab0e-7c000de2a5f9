/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.overhaul.entity.RepairRecord;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备维修记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RepairRecordVO对象", description = "设备维修记录表")
public class RepairRecordVO extends RepairRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operateUserName;

	/**
	 * 验证人
	 */
	@ApiModelProperty(value = "验证人")
	private String verifyUserName;

	/**
	 * 更换部件列表
	 */
	@ApiModelProperty(value = "更换部件列表")
	private List<ComponentMaterialVO> materialList;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;

	/**
	 * 维修结果
	 */
	@ApiModelProperty(value = "维修结果")
	private String resultName;


}
