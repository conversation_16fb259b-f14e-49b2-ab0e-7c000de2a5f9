/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 检修计划状态枚举类
 *
 * <AUTHOR>
 * @date 2024/10/25 13:56
 **/
@Getter
@AllArgsConstructor
public enum OverhaulPlanStatusEnum {

	/**
	 * 待审核
	 */
	UNDER_REVIEW(0, "待审核"),
	/**
	 * 驳回
	 */
	REJECTED(1, "驳回"),
	/**
	 * 未开始
	 */
	NO_START(2, "未开始"),
	/**
	 * 执行中
	 */
	IN_PROGRESS(3, "执行中"),
	/**
	 * 已终止
	 */
	IS_TERMINATED(4, "已终止"),
	/**
	 * 已完成
	 */
	IS_COMPLETED(5, "已完成"),
	;

	final Integer code;
	final String name;

	public static OverhaulPlanStatusEnum getByCode(Integer code){
		for (OverhaulPlanStatusEnum value : OverhaulPlanStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
