/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.fault.enums.FaultSourceEnum;
import com.snszyk.simas.fault.vo.FaultDefectVO;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.enums.RepairSourceEnum;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备维修单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RepairVO对象", description = "设备维修单表")
public class RepairVO extends Repair {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件-关键字（工单单号、设备编号、设备名称）
	 */
	@ApiModelProperty(value = "查询条件-关键字（工单单号、设备编号、设备名称）")
	private String keywords;

	/**
	 * 查询条件-开始日期
	 */
	@ApiModelProperty(value = "查询条件-开始日期")
	private String startDate;

	/**
	 * 查询条件-结束日期
	 */
	@ApiModelProperty(value = "查询条件-结束日期")
	private String endDate;

	/**
	 * 更换部件列表
	 */
	@ApiModelProperty(value = "更换部件列表")
	private List<ComponentMaterialVO> materialList;

	/**
	 * 维修记录
	 */
	@ApiModelProperty(value = "维修记录")
	private RepairRecordVO repairRecord;

	/**
	 * 超时时间间隔
	 */
	@ApiModelProperty(value = "超时时间间隔")
	private BigDecimal timeInterval;

	private List<Integer> statusList = new ArrayList<>();

	@ApiModelProperty(value = "不等于状态")
	private Integer neStatus;

	@ApiModelProperty(value = "维修人名称")
	private String receiveUserName;
	public RepairVO() {
		super();
	}

	public RepairVO(Long id) {
		super();
		this.setId(id);
	}

	/**
	 * 故障缺陷生成维修单
	 *
	 * @param faultDefect
	 * @return
	 */
	public RepairVO toRepairVO(FaultDefectVO faultDefect) {
		RepairVO vo = faultDefect.getRepair();
		vo.setTenantId(AuthUtil.getTenantId());
		vo.setBizType(RepairBizTypeEnum.INTERNAL.getCode());
		vo.setSourceNo(faultDefect.getNo());
		switch (FaultSourceEnum.getByCode(faultDefect.getSource())) {
			case INSPECT:
				vo.setSource(RepairSourceEnum.INSPECT_ORDER.getCode());
				break;
			case MAINTAIN:
				vo.setSource(RepairSourceEnum.MAINTAIN_ORDER.getCode());
				break;
			case LUBRICATE:
				vo.setSource(RepairSourceEnum.LUBRICATE_ORDER.getCode());
				break;
			default:
				vo.setSource(RepairSourceEnum.FAULT_DEFECT.getCode());
		}
		vo.setEquipmentId(faultDefect.getEquipmentId());
		vo.setMonitorId(faultDefect.getMonitorId());
		vo.setReportUser(AuthUtil.getUserId());
		vo.setReportDept(Func.firstLong(AuthUtil.getDeptId()));
		log.info("故障缺陷上报维修==========》上报人：{}，上报部门：{}", vo.getReportUser(), vo.getReportDept());
		vo.setReportTime(DateUtil.now());
		// 待派单
		vo.setStatus(OrderStatusEnum.WAIT.getCode());
		return vo;
	}

}
