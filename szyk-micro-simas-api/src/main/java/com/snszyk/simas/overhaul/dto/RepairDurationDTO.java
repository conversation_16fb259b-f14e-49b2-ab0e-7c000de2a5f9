package com.snszyk.simas.overhaul.dto;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.utils.StringPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * ClassName: RepairDurationDTO
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 9:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RepairDurationDTO extends DeviceAccountVO {
	/**
	 * 总维修时长，单位小时
	 */
	@ApiModelProperty(value = "总维修时长，单位小时")
	private BigDecimal repairDuration;
	/**
	 * 总时长
	 */
	@ApiModelProperty(value = "总时长")
	private BigDecimal totalDuration;
	/**
	 * 维修率
	 */
	@ApiModelProperty(value = "维修率")
	private String repairRate;

	/**
	 * 初始化
	 */
	public RepairDurationDTO init() {
		this.repairDuration = BigDecimal.ZERO;
		this.totalDuration = BigDecimal.ZERO;
		this.repairRate = StringPool.ZERO_PERCENT;
		return this;
	}

	/**
	 * 计算完成率
	 */
	public void computeRepairRate() {
		if (this.repairDuration.compareTo(BigDecimal.ZERO) == 0 || this.totalDuration.compareTo(BigDecimal.ZERO) == 0) {
			this.repairRate = StringPool.ZERO_PERCENT;
			return;
		}
		BigDecimal result = this.repairDuration.divide(this.totalDuration, 4, RoundingMode.HALF_UP)
			.multiply(new BigDecimal(StringPool.HUNDRED))
			.setScale(2, RoundingMode.HALF_UP);
		this.repairRate = result.toPlainString() + StringPool.PERCENT;
	}

}
