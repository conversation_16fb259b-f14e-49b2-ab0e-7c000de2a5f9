/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import com.snszyk.simas.overhaul.entity.OverhaulPlan;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备检修工单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OverhaulOrderVO对象", description = "设备检修工单表")
public class OverhaulOrderVO extends OverhaulOrder {
	private static final long serialVersionUID = 1L;
	/**
	 * 查询特种设备类型
	 */
	@ApiModelProperty(value = "只查询特种设备类型")
	private Boolean onlyQuerySpecialType;
	/**
	 * 设备idList
	 */
	@ApiModelProperty(value = "设备idList")
	private List<Long> equipmentIdList;
	/**
	 * 查询条件-关键字（工单单号、设备编号、设备名称）
	 */
	@ApiModelProperty(value = "查询条件-关键字（工单单号、设备编号、设备名称）")
	private String keywords;

	/**
	 * 查询条件-开始日期
	 */
	@ApiModelProperty(value = "查询条件-开始日期")
	private String startDate;

	/**
	 * 查询条件-结束日期
	 */
	@ApiModelProperty(value = "查询条件-结束日期")
	private String endDate;

	/**
	 * 超时时间间隔
	 */
	@ApiModelProperty(value = "超时时间间隔")
	private BigDecimal timeInterval;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;

	/**
	 * 设备检修结果列表
	 */
	@ApiModelProperty(value = "设备检修结果列表")
	private List<OverhaulRecordVO> overhaulRecordList;

	/**
	 * 工单id列表
	 */
	@ApiModelProperty(value = "工单id列表")
	private List<Long> orderIds;

	@ApiModelProperty(value = "状态列表")
	private List<Integer> statuses;

	@ApiModelProperty(value = "状态的集合")
	private List<Integer> statusList;
	@ApiModelProperty(value = "数据权限角色 0 全部 1 操作人 2 其他")
	private Integer queryAuthRole;
	@ApiModelProperty(value = "不等于状态")
	private Integer neStatus;
	/**
	 * 标准信息
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准信息")
	private String standardInfo;
	@ApiModelProperty(value = "仅查看执行人")
	private Long onlyQueryExecuteUser;
	public OverhaulOrderVO() {
		super();
	}

	public OverhaulOrderVO(OverhaulPlan plan, Long equipmentId, Long executeDept, Long executeUser) {
		super();
		this.setTenantId(plan.getTenantId());
		this.setPlanId(plan.getId());
		this.setPlanInfo(JSONUtil.toJsonStr(plan));
		this.setEquipmentId(equipmentId);
		this.setExecuteDept(executeDept);
		this.setExecuteUser(executeUser);
		this.setNo(BizCodeUtil.generate("OO"));
		this.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		this.setCreateTime(DateUtil.now());
	}

	public OverhaulOrderVO(Long id) {
		super();
		this.setId(id);
	}

}
