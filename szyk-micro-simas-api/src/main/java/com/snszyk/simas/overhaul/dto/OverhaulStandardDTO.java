/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.dto;

import com.snszyk.simas.overhaul.entity.OverhaulStandard;
import com.snszyk.simas.overhaul.vo.OverhaulRecordVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备检修标准表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OverhaulStandardDTO extends OverhaulStandard {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 方法名称
	 */
	@ApiModelProperty(value = "方法名称")
	private String methodsName;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private Integer isAbnormal;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

	@ApiModelProperty(value = "检修记录")
	private OverhaulRecordVO record;
}
