/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.overhaul.entity.OverhaulRecord;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备检修记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OverhaulRecordVO对象", description = "设备检修记录表")
public class OverhaulRecordVO extends OverhaulRecord {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "故障缺陷等级")
	private String faultLevelName;
	/**
	 * 故障缺陷类型
	 */
	@ApiModelProperty(value = "故障缺陷类型")
	private String faultTypeName;

	// 备品备件消耗列表
	private List<ComponentMaterialVO> materialList;

	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;

}
