/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备维修单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@ApiModel(value = "RepairComponentVO对象", description = "设备维修单表")
public class RepairComponentVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;

	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Integer count;

}
