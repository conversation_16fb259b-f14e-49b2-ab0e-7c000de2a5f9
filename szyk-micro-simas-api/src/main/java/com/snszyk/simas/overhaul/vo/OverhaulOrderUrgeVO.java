package com.snszyk.simas.overhaul.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName: OverhaulUrgeVO
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/9 10:19
 */
@Data
@ApiModel(value = "OverhaulUrgeVO", description = "检修工单催办")
public class OverhaulOrderUrgeVO {
	/**
	 * 催办消息内容
	 */
	@ApiModelProperty(value = "催办消息内容", required = true)
	private String content;
}
