/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备检修记录表实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
@TableName("simas_overhaul_record")
@ApiModel(value = "OverhaulRecord对象", description = "设备检修记录表")
public class OverhaulRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 检修单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检修单id")
	private Long orderId;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;
	/**
	 * 标准id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准id")
	private Long standardId;
	/**
	 * 标准信息
	 */
	@ApiModelProperty(value = "标准信息")
	private String standardInfo;
	/**
	 * 维修单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修单id")
	private Long repairId;
	/**
	 * 是否异常
	 */
	@ApiModelProperty(value = "是否异常")
	private Integer isAbnormal;

	/**
	 * 操作人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检修人")
	private Long overhaulUser;
	/**
	 * 检修时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "检修时间")
	private Date overhaulTime;

	//===========================故障缺陷========================================

	/**
	 * 故障缺陷名称
	 */
	@ApiModelProperty(value = "故障缺陷名称")
	private String faultName;

	/**
	 * 故障缺陷等级
	 */
	@ApiModelProperty(value = "故障缺陷等级（字典：defect_level）")
	private Integer faultLevel;
	/**
	 * 故障缺陷类型
	 */
	@ApiModelProperty(value = "故障缺陷类型（字典：repair_type）")
	private Integer faultType;
	/**
	 * 故障缺陷描述
	 */
	@ApiModelProperty(value = "故障缺陷描述")
	private String faultRemark;

	//===================================维修内容===================================================

	/**
	 * 是否上报（0：否，1：是）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "是否上报（0：现场已处理 1：现场未处理，需上报）")
	private Integer isReport;
	/**
	 * 维修结果（1：已修复，2：未完全修复，可运行）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修结果（1：已修复，2：未完全修复，可运行）")
	private Integer result;
	/**
	 * 检查方式
	 */
	@ApiModelProperty(value = "检查方式")
	private String checkMethod;
	/**
	 * 维修时长
	 */
	@ApiModelProperty(value = "维修时长")
	private String duration;
	/**
	 * 故障原因
	 */
	@ApiModelProperty(value = "故障原因")
	private String faultReason;
	/**
	 * 解决方案
	 */
	@ApiModelProperty(value = "解决方案")
	private String solution;
	/**
	 * 更换部件
	 */
	@ApiModelProperty(value = "更换部件")
	private String component;
	/**
	 * 上传图片
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传图片")
	private String attachId;

}
