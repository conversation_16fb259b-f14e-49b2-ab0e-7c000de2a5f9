/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备维修记录表实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
@TableName("simas_repair_record")
@ApiModel(value = "RepairRecord对象", description = "设备维修记录表")
public class RepairRecord implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 维修单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修单id")
	private Long repairId;
	/**
	 * 维修结果（1：已修复，2：未完全修复，可运行）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修结果（1：已修复，2：未完全修复，可运行）")
	private Integer result;
	/**
	 * 检查方式
	 */
	@ApiModelProperty(value = "检查方式")
	private String checkMethod;
	/**
	 * 维修时长
	 */
	@ApiModelProperty(value = "维修时长")
	private String duration;
	/**
	 * 故障原因
	 */
	@ApiModelProperty(value = "故障原因")
	private String faultReason;
	/**
	 * 解决方案
	 */
	@ApiModelProperty(value = "解决方案")
	private String solution;
	/**
	 * 更换部件
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "更换部件")
	private String component;
	/**
	 * 上传图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传图片")
	private String attachId;
	/**
	 * 验证人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "验证人")
	private Long verifyUser;
	/**
	 * 验证时间
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "验证时间")
	private Date verifyTime;
	/**
	 * 验证结果（0：未通过，1：通过）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "验证结果（0：未通过，1：通过）")
	private Integer verifyResult;
	/**
	 * 验证评论
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "验证评论")
	private String verifyComment;
	/**
	 * 操作人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "操作人")
	private Long operateUser;
	/**
	 * 操作时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "操作时间")
	private Date operateTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
