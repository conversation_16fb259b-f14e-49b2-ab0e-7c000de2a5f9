/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修单来源枚举类
 *
 * <AUTHOR>
 * @date 2024/08/27 20:35
 **/
@Getter
@AllArgsConstructor
public enum RepairSourceEnum {

	/**
	 * 故障缺陷
	 */
	FAULT_DEFECT(1, "故障缺陷"),
	/**
	 * 自定义新增
	 */
	MANUAL_ADD(2, "自定义新增"),

	/**
	 * 计划性检修
	 */
	PLANNING_OVERHAUL(3, "计划性检修"),

	/**
	 * 点巡检工单
	 */
	INSPECT_ORDER(4, "点巡检"),

	/**
	 * 保养工单
	 */
	MAINTAIN_ORDER(5, "保养"),

	/**
	 * 润滑工单
	 */
	LUBRICATE_ORDER(6, "润滑"),
	;

	final Integer code;
	final String name;

	public static RepairSourceEnum getByCode(Integer code){
		for (RepairSourceEnum value : RepairSourceEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
