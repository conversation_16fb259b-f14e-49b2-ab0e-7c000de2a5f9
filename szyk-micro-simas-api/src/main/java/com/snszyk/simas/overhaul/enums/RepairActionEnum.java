package com.snszyk.simas.overhaul.enums;

import com.snszyk.simas.common.enums.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维修工单动作
 * ClassName: RepairActionEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/17 11:29
 */
@AllArgsConstructor
@Getter
public enum RepairActionEnum {

	/**
	 * 处理缺陷-上报（内部维修）
	 * 示例：
	 * 2024/12/12 8：00：00李四将故障缺陷单“1321231”转为内部维修单。
	 */
	HANDLING_DEFECTS_REPORT(OrderStatusEnum.WAIT.getCode(), "%s %s将故障缺陷单转为内部维修单"),
	/**
	 * 内部派单
	 * 示例：
	 * 2024/12/12 8：00：00李四将工单指派给了张三。
	 */
	INTERNAL_DISPATCH(OrderStatusEnum.IN_PROCESS.getCode(), "%s %s将工单指派给了%s"),
	/**
	 * 外部派单
	 * 示例：
	 * 2024/12/12 8：00：00李四将内部维单“132321”转为外委维修。
	 */
	EXTERNAL_DISPATCH(OrderStatusEnum.IN_PROCESS.getCode(), "%s %s将内部维单%s转为外委维修"),
	/**
	 * 正常创建（内部、外委）
	 * 示例：
	 * 2024/12/12 8：00：00李四新增了内部维修单。
	 * 2024/12/12 8：00：00李四新增了外委维修单。
	 */
	CREATE(OrderStatusEnum.IN_PROCESS.getCode(), "%s %s新增了%s"),

	/**
	 * 维修提交工单（内部、外委）
	 * 示例：
	 * 2024/12/12 8：00：00张三提交了工单。
	 */
	REPAIRED_SUBMIT(OrderStatusEnum.WAIT_CONFIRM.getCode(), "%s %s提交了工单"),
	/**
	 * 审核通过（内部、外委）
	 * 2024/12/12 8：00：00李四审核通过了了工单。
	 */
	AUDIT_PASS(OrderStatusEnum.IS_COMPLETED.getCode(), "%s %s审核通过了工单"),
	/**
	 * 审核不通过（内部、外委）
	 * 2024/12/12 8：00：00李四驳回了工单，驳回原因“具体原因”。
	 */
	AUDIT_FAIL(OrderStatusEnum.IS_REJECTED.getCode(), "%s %s驳回了工单，驳回原因“%s”"),

	/**
	 * 正常关闭工单（内部、外委）
	 * 2024/12/12 8：00：00工单关闭，关闭原因“设备完成报废处置”。
	 */
	CLOSE(OrderStatusEnum.IS_CLOSED.getCode(), "%s %s关闭了工单"),

	/**
	 * 设备报废关闭（内部、外委）
	 */
	SCRAP_CLOSE(OrderStatusEnum.IS_CLOSED.getCode(), "%s %s工单关闭，关闭原因“设备完成报废处置”");

	private Integer code;
	private String desc;
}
