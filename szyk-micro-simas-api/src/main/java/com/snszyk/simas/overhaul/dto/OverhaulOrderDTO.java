/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.overhaul.entity.OverhaulOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备检修工单表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OverhaulOrderDTO extends OverhaulOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单名称
	 */
	@ApiModelProperty(value = "工单名称")
	private String orderName;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	/**
	 * 设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型")
	private Long equipmentCategory;

	/**
	 * 设备类型名称
	 */
	@ApiModelProperty(value = "设备类型名称")
	private String equipmentCategoryName;

	/**
	 * 地点名称
	 */
	@ApiModelProperty(value = "设备位置")
	private String locationPath;

	/**
	 * 周期类型（字典：plan_cycle）
	 */
	@ApiModelProperty(value = "周期类型（字典：plan_cycle）")
	private String cycleTypeName;

	/**
	 * 执行部门
	 */
	@ApiModelProperty(value = "执行部门")
	private String executeDeptName;

	/**
	 * 执行人
	 */
	@ApiModelProperty(value = "执行人")
	private String executeUserName;

	/**
	 * 检查人
	 */
	@ApiModelProperty(value = "检查人")
	private String checkUserName;

	private String approvalUserName;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTimeStr;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTimeStr;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 检修结果
	 */
	@ApiModelProperty(value = "检修结果")
	private String overhaulResult;

	/**
	 * 设备台账信息
	 */
	@ApiModelProperty(value = "设备台账信息")
	private DeviceAccountVO equipmentAccount;

	/**
	 * 检修计划
	 */
	@ApiModelProperty(value = "检修计划")
	private OverhaulPlanDTO overhaulPlan;

	/**
	 * 检修标准列表
	 */
	@ApiModelProperty(value = "检修标准列表")
	List<OverhaulStandardDTO> standardList;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;
	/**
	 * 特种设备类型
	 */
	@ApiModelProperty(value = "特种设备类型")
	private String specialType;
	/**
	 * 特种设备类型名称
	 */
	@ApiModelProperty(value = "特种设备类型名称")
	private String specialTypeName;


	@ApiModelProperty(value = "是否需要审核")
	private Boolean isNeedApproval = false;
	/**
	 * 催单次数
	 */
	@ApiModelProperty(value = "催单次数")
	private Long urgeCount;

	private String createUserName;

	private String updateUserName;
	/**
	 * 标准信息
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准信息")
	private String standardInfo;

}
