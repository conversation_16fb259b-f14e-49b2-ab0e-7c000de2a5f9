package com.snszyk.simas.overhaul.vo;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.simas.common.enums.TimeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.AssertTrue;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * ClassName: RepairDurationPageVO
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 9:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RepairDurationPageVO extends Page {
	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 设备类型id
	 */
	@ApiModelProperty(value = "设备类型id")
	private Long categoryId;
	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "部门id")
	private Long deptId;
	/**
	 * 统计时间
	 */
	@ApiModelProperty(value = "统计时间,与自定义时间不可同时选择")
	private TimeTypeEnum timeType;
	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "开始时间")
	private LocalDate startDate;
	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "结束时间")
	private LocalDate endDate;

	/**
	 * 开始日期时间
	 *
	 * @return
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime startDateTime;

	/**
	 * 结束日期时间
	 *
	 * @return
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime endDateTime;

	/**
	 * 使用部门列表
	 *
	 * @return
	 */
	@ApiModelProperty(value = "使用部门列表")
	private List<Long> useDeptList;

	/**
	 * 维修状态列表
	 *
	 * @return
	 */
	@ApiModelProperty(value = "维修状态列表")
	private List<Integer> repairStatusList;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private String sortOrder;

	@AssertTrue(message = "统计日期和自定义日期必填其一")
	public Boolean getDateValid() {
		return !(ObjectUtil.isEmpty(timeType) && ObjectUtil.isEmpty(startDate));
	}

	@ApiModelProperty(value = "是否已归还")
	private Integer isLeaseBack;


	/**
	 * 处理开始日期时间和结束日期时间
	 *
	 * @return
	 */
	public RepairDurationPageVO preHandleStartAndEndDateTime() {
		if (ObjectUtil.isNotEmpty(this.getStartDate())) {
			this.setStartDateTime(this.getStartDate().atStartOfDay());
		}
		if (ObjectUtil.isNotEmpty(this.getEndDate())) {
			this.setEndDateTime(this.getEndDate().atTime(LocalTime.MAX));
		}
		return this;
	}

	/**
	 * 计算时间间隔，单位小时
	 */
	public Long getTimeInterval() {
		if (ObjectUtil.isEmpty(this.getStartDate()) || ObjectUtil.isEmpty(this.getEndDate())) {
			return 0L;
		}
		long days = ChronoUnit.DAYS.between(this.getStartDate(), this.getEndDate());
		return (days + 1) * 24L;
	}
}
