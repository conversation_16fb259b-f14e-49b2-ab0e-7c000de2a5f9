/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.common.equipment.vo.DeviceMonitorVO;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.dto.OrderDefectDTO;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.overhaul.entity.Repair;
import com.snszyk.simas.overhaul.vo.RepairRecordVO;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 设备维修单表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RepairDTO extends Repair {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String equipmentModel;

	/**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码")
    private String equipmentCode;

	/**
	 * 设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型")
	private Long equipmentCategory;

	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String equipmentCategoryName;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 设备信息
	 */
	@ApiModelProperty(value = "设备信息")
	private DeviceAccountVO equipmentAccount;

	/**
	 * 部位信息
	 */
	@ApiModelProperty(value = "部位信息")
	private DeviceMonitorVO equipmentMonitor;

	/**
	 * 维修类型
	 */
	@ApiModelProperty(value = "维修类型")
	private String bizTypeName;

	/**
	 * 故障等级
	 */
	@ApiModelProperty(value = "故障等级")
	private String faultLevelName;

	/**
	 * 工单来源
	 */
	@ApiModelProperty(value = "工单来源")
	private String sourceName;

	/**
	 * 来源单号
	 */
	@ApiModelProperty("来源单号")
	private String sourceNo;

	/**
	 * 报修类型
	 */
	@ApiModelProperty(value = "报修类型")
	private String repairTypeName;

	/**
	 * 报修人
	 */
	@ApiModelProperty(value = "报修人")
	private String reportUserName;

	/**
	 * 报修部门
	 */
	@ApiModelProperty(value = "报修部门")
	private String reportDeptName;

	/**
	 * 维修人
	 */
	@ApiModelProperty(value = "维修人")
	private String receiveUserName;

	/**
	 * 跟进人
	 */
	@ApiModelProperty(value = "跟进人")
	private String followUserName;

	/**
	 * 派单人
	 */
	@ApiModelProperty(value = "派单人")
	private String dispatchUserName;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

	/**
	 * 处理时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "处理时间")
	private Date handleTime;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;

	/**
	 * 维修单附件
	 */
	@ApiModelProperty(value = "维修单附件")
	private List<Attach> attachList;

	/**
	 * 工单上报缺陷
	 */
	@ApiModelProperty(value = "工单上报缺陷")
	private OrderDefectDTO orderDefect;

	/**
	 * 维修记录
	 */
	@ApiModelProperty(value = "维修记录")
	private RepairRecordVO repairRecord;

	/**
	 * 业务日志
	 */
	@ApiModelProperty(value = "业务日志")
	private List<BizLogVO> bizLogList;

	/**
	 * 更换部件列表
	 */
	@ApiModelProperty(value = "更换部件列表")
	private List<ComponentMaterialVO> repairComponentList;

	/**
	 * 故障缺陷信息
	 */
	@ApiModelProperty("故障缺陷信息")
	private FaultDefectDTO faultDefect;

	/**
	 * 审批人姓名
	 */
	@ApiModelProperty("审批人姓名")
	private String approvalUserName;

	/**
	 * 供应商名称
	 */
	@ApiModelProperty(value = "供应商名称")
	private String supplierName;


}
