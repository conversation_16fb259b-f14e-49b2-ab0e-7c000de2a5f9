/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.vo;

import com.snszyk.simas.overhaul.entity.OverhaulStandard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备检修标准表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OverhaulStandardVO对象", description = "设备检修标准表")
public class OverhaulStandardVO extends OverhaulStandard {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 方法名称
	 */
	@ApiModelProperty(value = "方法名称")
	private String methodsName;
	@ApiModelProperty(value = "部位类型")
	private Integer monitorType;
	@ApiModelProperty(value = "部位类型名称")
	private String monitorTypeName;

}
