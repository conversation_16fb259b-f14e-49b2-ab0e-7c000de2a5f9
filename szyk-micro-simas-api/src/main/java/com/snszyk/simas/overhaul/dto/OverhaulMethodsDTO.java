package com.snszyk.simas.overhaul.dto;

import com.snszyk.simas.overhaul.entity.OverhaulMethods;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "OverhaulMethods对象", description = "检修方式")
public class OverhaulMethodsDTO extends OverhaulMethods {


	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "更新人名")
	private String updateUserName;


	@ApiModelProperty(value = "创建人名")
	private String createUserName;


}
