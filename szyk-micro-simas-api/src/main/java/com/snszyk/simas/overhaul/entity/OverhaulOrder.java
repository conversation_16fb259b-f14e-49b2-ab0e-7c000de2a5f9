/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.IOrderField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备检修工单表实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@TableName("simas_overhaul_order")
@ApiModel(value = "OverhaulOrder对象", description = "设备检修工单表")
public class OverhaulOrder extends TenantEntity implements Serializable, IOrderField {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;

	/**
	 * 工单名称
	 */
	@ApiModelProperty(value = "工单名称")
	private String orderName;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;
	/**
	 * 执行部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行部门")
	private Long executeDept;
	/**
	 * 执行人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行人")
	private Long executeUser;
	/**
	 * 检查人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检查人")
	private Long checkUser;
	/**
	 * 开始执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "开始执行日期")
	private Date startTime;
	/**
	 * 结束执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "结束执行日期")
	private Date endTime;
	/**
	 * 计划id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计划id")
	private Long planId;
	/**
	 * 计划信息
	 */
	@ApiModelProperty(value = "计划信息")
	private String planInfo;

	/**
	 * 浮动时间（单位：天）
	 */
	@ApiModelProperty(value = "浮动时间（单位：天）")
	private Integer floatDate;
	/**
	 * 是否异常（0否1是）
	 */
	@ApiModelProperty(value = "是否异常（0否1是）")
	private Integer isAbnormal;
	/**
	 * 是否即将超时（0否1是）
	 */
	@ApiModelProperty(value = "是否即将超时（0否1是）")
	private Integer isExpired;
	/**
	 * /**
	 * 提交时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "提交时间")
	private Date submitTime;
	/**
	 * 完成时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "完成时间")
	private Date completeTime;
	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;

	@ApiModelProperty(value = "是否需要审批 v1.2.1")
	private Boolean isNeedApproval;

	/**
	 * 标准信息
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准信息")
	private String standardInfo;

	@Override
	public Long getDeptId() {
		return this.getExecuteDept();
	}
}
