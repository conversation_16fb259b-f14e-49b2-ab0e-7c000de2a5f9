package com.snszyk.simas.overhaul.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("simas_overhaul_methods")
@ApiModel(value = "OverhaulMethods对象", description = "检修方式")
public class OverhaulMethods extends TenantEntity {


	private static final long serialVersionUID = 1L;


	/**
	 * 类型名称
	 */
	@ApiModelProperty(value = "类型名称")
	private String name;

	@ApiModelProperty(value = "备注")
	private String remark;



}
