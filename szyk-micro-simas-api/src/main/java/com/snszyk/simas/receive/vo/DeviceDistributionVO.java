/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备分配
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@ApiModel(value = "DeviceDistributionVo", description = "设备分配")
public class DeviceDistributionVO {


	/**
	 * 领用的id
	 */
	@ApiModelProperty(value = "领用的id")
	private Long receiveId;

	/**
	 * 设备的ids
	 */
	@ApiModelProperty(value = "设备的ids")
	private List<Long> equipmentIds;


}
