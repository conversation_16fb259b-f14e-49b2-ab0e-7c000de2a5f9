/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备分配
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@ApiModel(value = "DeviceDistributionVo", description = "设备分配")
public class DeviceBackVO {


	/**
	 * 领用的id
	 */
	@ApiModelProperty(value = "领用的id")
	private List<Long> receiveIds = new ArrayList<>();

	/*
	设备的ids
	 */
	@ApiModelProperty(value = "设备的ids")
	private List<Long> equipmentIds = new ArrayList<>();


}
