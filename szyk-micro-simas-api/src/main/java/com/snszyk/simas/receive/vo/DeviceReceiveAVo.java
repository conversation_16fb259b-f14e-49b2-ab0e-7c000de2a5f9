/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@ApiModel(value = "DeviceReceiveVo对象", description = "设备领用")
public class DeviceReceiveAVo {

	/**
	 * 领用日期开始
	 */
	@ApiModelProperty(value = "领用日期开始")
	private LocalDate receiveStartDate;
	/**
	 * 领用日期结束
	 */
	@ApiModelProperty(value = "领用日期结束")
	private LocalDate receiveEndDate;
	/**
	 * 申请人id
	 */
	@ApiModelProperty(value = "申请人id")
	private Long applyUserId;
	/**
	 * 需求
	 */
	@ApiModelProperty(value = "需求")
	private String demand;
	/**
	 * 补充说明
	 */
	@ApiModelProperty(value = "补充说明")
	private String additionalRemarks;

	@ApiModelProperty(value = "设备ids")
	private List<Long> deviceIds=new ArrayList<>();
//	/**
//	 * 所属部门
//	 */
//	@ApiModelProperty(value = "所属部门")
//	private Long belongDeptId;
	/**
//	 * 归还日期
//	 */
//	@ApiModelProperty(value = "归还日期")
//	private LocalDate backDate;
//	/**
//	 * 领用单号
//	 */
//	@ApiModelProperty(value = "领用单号")
//	private String orderNo;
//	/**
//	 * 申请时间
//	 */
//	@ApiModelProperty(value = "申请时间")
//	private LocalDateTime applyTime;
//	/**
//	 * 备注
//	 */
//	@ApiModelProperty(value = "备注")
//	private String remark;


}
