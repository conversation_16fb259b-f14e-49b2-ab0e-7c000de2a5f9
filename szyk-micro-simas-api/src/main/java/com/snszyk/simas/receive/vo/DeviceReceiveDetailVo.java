/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveDetailVo对象", description = "设备领用")
public class DeviceReceiveDetailVo extends BaseCrudVo {

	/**
	 * 领用id
	 */
	@ApiModelProperty(value = "领用id")
	private Long receiveId;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
