package com.snszyk.simas.receive.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备领用的 到期的状态
 * <AUTHOR>
 * @date 2023/3/16
 * 0 未到期 1 已到期
 */
@Getter
@AllArgsConstructor
public enum DeviceReceiveExpireEnum {
	UNEXPIRED(0, "未到期"),
	EXPIRED(1, "已到期");


	final Integer code;
	final String name;

	/**
	 * <AUTHOR>
	 * @Description
	 * @Date 上午9:49 2025/3/19
	 * @Param [code]
	 * @return com.snszyk.simas.receive.enums.DeviceReceiveStatusEnum
	 **/
	public static DeviceReceiveExpireEnum getByCode(Integer code) {
		for (DeviceReceiveExpireEnum value : DeviceReceiveExpireEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}

}
