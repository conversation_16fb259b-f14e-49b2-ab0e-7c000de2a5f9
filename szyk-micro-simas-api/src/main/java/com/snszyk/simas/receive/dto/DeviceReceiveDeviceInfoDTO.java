/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.dto;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveDetailDto对象", description = "设备领用")
public class DeviceReceiveDeviceInfoDTO extends DeviceAccountVO {


	/**
	 * 备注
	 */
	@ApiModelProperty(value = "领用设备的状态")
	private Integer receiveStatus;
	@ApiModelProperty(value = "领用设备的状态")
	private String receiveStatusName;

}
