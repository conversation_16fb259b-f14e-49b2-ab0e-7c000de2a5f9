/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveDto对象", description = "设备领用")
public class DeviceReceiveDto extends BaseCrudDto {

	/**
	 * 领用日期开始
	 */
	@ApiModelProperty(value = "领用日期开始")
	private LocalDate receiveStartDate;
	/**
	 * 领用日期结束
	 */
	@ApiModelProperty(value = "领用日期结束")
	private LocalDate receiveEndDate;
	/**
	 * 申请人id
	 */
	@ApiModelProperty(value = "申请人id")
	private Long applyUserId;
	private String applyUserName;
	/**
	 * 使用位置id
	 */
	@ApiModelProperty(value = "使用位置id")
	private Long locationId;
	/**
	 * 使用位置路径
	 */
	private String locationPath;
	/**
	 * 需求
	 */
	@ApiModelProperty(value = "需求")
	private String demand;
	/**
	 * 补充说明
	 */
	@ApiModelProperty(value = "补充说明")
	private String additionalRemarks;
	/**
	 * 所属部门
	 */
	@ApiModelProperty(value = "所属部门")
	private Long belongDeptId;
	@ApiModelProperty(value = "所属部门")
	private String belongDeptName;
	/**
	 * 归还日期
	 */
	@ApiModelProperty(value = "归还日期")
	private LocalDateTime backTime;
	/**
	 * 领用单号
	 */
	@ApiModelProperty(value = "领用单号")
	private String orderNo;
	/**
	 * 申请时间
	 */
	@ApiModelProperty(value = "申请时间")
	private LocalDateTime applyTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "更新人")
	private String updateUserName;

	private String statusName;

	private List<DeviceReceiveDeviceInfoDTO> deviceAccountList = new ArrayList<>();
}
