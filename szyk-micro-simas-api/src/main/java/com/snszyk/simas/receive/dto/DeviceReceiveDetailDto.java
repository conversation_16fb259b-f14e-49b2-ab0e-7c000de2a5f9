/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveDetailDto对象", description = "设备领用")
public class DeviceReceiveDetailDto extends BaseCrudDto {

	/**
	 * 领用id
	 */
	@ApiModelProperty(value = "领用id")
	private Long receiveId;
	/**
	 * 领用单号
	 */
	@ApiModelProperty(value = "领用单号")
	private String orderNo;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	/**
	 * 使用位置id
	 */
	@ApiModelProperty(value = "使用位置id")
	private Long locationId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "更新人")
	private String updateUserName;
}
