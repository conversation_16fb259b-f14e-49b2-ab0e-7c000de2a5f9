package com.snszyk.simas.receive.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备领用的状态
 * <AUTHOR>
 * @date 2023/3/16
 * 待分配 已分配 已归还
 */
@Getter
@AllArgsConstructor
public enum DeviceReceiveStatusEnum {
	WAIT(1, "待分配"),
	DISTRIBUTE(2, "已分配"),
	BACK(3, "已归还"),
	STOP(4, "已终止"),
	;


	final Integer code;
	final String name;

	/**
	 * <AUTHOR>
	 * @Description
	 * @Date 上午9:49 2025/3/19
	 * @Param [code]
	 * @return com.snszyk.simas.receive.enums.DeviceReceiveStatusEnum
	 **/
	public static DeviceReceiveStatusEnum getByCode(Integer code) {
		for (DeviceReceiveStatusEnum value : DeviceReceiveStatusEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}

}
