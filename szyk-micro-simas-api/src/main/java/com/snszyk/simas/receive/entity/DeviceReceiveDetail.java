/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备领用 实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@TableName("simas_device_receive_detail")
@EqualsAndHashCode(callSuper = true)
public class DeviceReceiveDetail extends TenantEntity {

	/**
	 * 领用id
	 */
	private Long receiveId;
	/**
	 * 设备id
	 */
	private Long deviceId;
	/**
	 * 备注
	 */
	private String remark;

	private LocalDateTime backTime;


}
