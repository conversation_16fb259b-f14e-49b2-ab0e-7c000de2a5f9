/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveVo对象", description = "设备领用")
public class DeviceReceiveVo extends BaseCrudVo {

	/**
	 * 领用日期开始
	 */
	@ApiModelProperty(value = "领用日期开始")
	private LocalDate receiveStartDate;
	/**
	 * 领用日期结束
	 */
	@ApiModelProperty(value = "领用日期结束")
	private LocalDate receiveEndDate;
	/**
	 * 申请人id
	 */
	@ApiModelProperty(value = "申请人id")
	private Long applyUserId;
	/**
	 * 需求
	 */
	@ApiModelProperty(value = "需求")
	private String demand;
	/**
	 * 补充说明
	 */
	@ApiModelProperty(value = "补充说明")
	private String additionalRemarks;
	/**
	 * 所属部门
	 */
	@ApiModelProperty(value = "所属部门")
	private Long belongDeptId;
	/**
	 * 归还日期
	 */
	@ApiModelProperty(value = "归还日期")
	private LocalDateTime backTime;
	/**
	 * 领用单号
	 */
	@ApiModelProperty(value = "领用单号")
	private String orderNo;
	/**
	 * 申请时间
	 */
	@ApiModelProperty(value = "申请时间")
	private LocalDateTime applyTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
