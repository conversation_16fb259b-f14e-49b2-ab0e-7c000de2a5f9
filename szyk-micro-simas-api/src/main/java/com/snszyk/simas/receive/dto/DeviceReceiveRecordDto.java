/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 设备领用dto
 * @Date 上午10:17 2025/3/19
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveRecordDto", description = "设备领用")
public class DeviceReceiveRecordDto extends BaseCrudDto {

	/**
	 * 领用日期开始
	 */
	@ApiModelProperty(value = "领用日期开始")
	private LocalDate receiveStartDate;
	/**
	 * 领用日期结束
	 */
	@ApiModelProperty(value = "领用日期结束")
	private LocalDate receiveEndDate;
	/**
	 * 申请人id
	 */
	@ApiModelProperty(value = "申请人id")
	private Long applyUserId;
	private String applyUserName;
	/**
	 * 需求
	 */
	@ApiModelProperty(value = "需求")
	private String demand;
	/**
	 * 补充说明
	 */
	@ApiModelProperty(value = "补充说明")
	private String additionalRemarks;
	/**
	 * 所属部门
	 */
	@ApiModelProperty(value = "所属部门")
	private Long belongDeptId;
	@ApiModelProperty(value = "所属部门")
	private String belongDeptName;
	/**
	 * 归还日期
	 */
	@ApiModelProperty(value = "归还日期")
	private LocalDateTime backTime;
	/**
	 * 领用单号
	 */
	@ApiModelProperty(value = "领用单号")
	private String orderNo;
	/**
	 * 申请时间
	 */
	@ApiModelProperty(value = "申请时间")
	private LocalDateTime applyTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "更新人")
	private String updateUserName;

	private String statusName;

	@ApiModelProperty(value = "设备编码")
	private String deviceCode;
	@ApiModelProperty(value = "规格型号")
	private String deviceModel;

	@ApiModelProperty(value = "设备名称")
	private String deviceName;

	@ApiModelProperty(value = "设备id")
	private Long deviceId;

	/**是否到期
	 **/
	@ApiModelProperty(value = "是否到期")
	private Integer isExpired;

	/**
	 到期名
	 **/
	@ApiModelProperty(value = "到期名")
	private String expiredName;

	@ApiModelProperty(value = "领用单id")
	private Long orderId;
}
