/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.core.crud.vo.BaseCrudVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveDetailVo对象", description = "设备领用")
public class DeviceReceiveDetailPageVo extends Page {

    /**
     * 领用id
     */
        @ApiModelProperty(value = "领用id")
        private Long receiveId;
    /**
     * 设备id
     */
        @ApiModelProperty(value = "设备id")
        private Long deviceId;
    /**
     * 备注
     */
        @ApiModelProperty(value = "备注")
        private String remark;


}
