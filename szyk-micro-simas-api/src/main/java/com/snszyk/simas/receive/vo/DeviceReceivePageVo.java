/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 设备领用实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceReceiveVo对象", description = "设备领用")
public class DeviceReceivePageVo extends Page {

	/**
	 * 申请人
	 */
	@ApiModelProperty(value = "申请人")
	private String applyUserName;

	/**
	 * 领用单号
	 */
	@ApiModelProperty(value = "领用单号")
	private String orderNo;
	/**
	 * 申请时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "申请日期start")
	private LocalDate startApplyDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "归还期start")
	private LocalDate startBackDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "申请日期end")
	private LocalDate endApplyDate;


	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "归还日期end")
	private LocalDate endBackDate;

	/**
	 * status
	 */
	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "是否过期 0否  1是")
	private Integer isExpired;


	@ApiModelProperty(value = "设备名称")
	private String deviceName;
	@ApiModelProperty(value = "设备编号")
	private String deviceCode;

}
