/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.receive.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 设备领用 实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@TableName("simas_device_receive")
@EqualsAndHashCode(callSuper = true)
public class DeviceReceive extends TenantEntity {

	/**
	 * 领用日期开始
	 */
	private LocalDate receiveStartDate;
	/**
	 * 领用日期结束
	 */
	private LocalDate receiveEndDate;
	/**
	 * 申请人id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long applyUserId;
	/**
	 * 使用位置id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用位置id")
	private Long locationId;
	/**
	 * 需求
	 */
	private String demand;
	/**
	 * 补充说明
	 */
	private String additionalRemarks;
	/**
	 * 所属部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long belongDeptId;
	/**
	 * 归还日期
	 */
	private LocalDateTime backTime;
	/**
	 * 领用单号
	 */
	private String orderNo;
	/**
	 * 申请时间
	 */
	private LocalDateTime applyTime;
	/**
	 * 备注
	 */
	private String remark;


}
