/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备故障缺陷表视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "FaultDefectAbnormalVO对象", description = "FaultDefectAbnormalVO对象")
public class FaultDefectAbnormalVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 异常等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;

	/**
	 * 异常描述
	 */
	@ApiModelProperty(value = "异常描述")
	private String abnormalComment;

	/**
	 * 异常图片
	 */
	@ApiModelProperty(value = "异常图片")
	private String abnormalImage;


}
