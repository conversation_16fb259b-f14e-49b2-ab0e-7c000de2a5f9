/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.vo;

import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.overhaul.vo.RepairVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备故障缺陷表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FaultDefectVO对象", description = "设备故障缺陷表")
public class FaultDefectVO extends FaultDefect {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	/**
	 * 上报人
	 */
	@ApiModelProperty(value = "上报人")
	private String reportUserName;
	/**
	 * 处理人
	 */
	@ApiModelProperty(value = "处理人")
	private String operateUserName;
	/**
	 * 维修上报
	 */
	@ApiModelProperty(value = "维修上报")
	private RepairVO repair;
	/**
	 * 自增故障缺陷异常信息
	 */
	@ApiModelProperty(value = "自增故障缺陷异常信息")
	private FaultDefectAbnormalVO faultDefectAbnormal;


}
