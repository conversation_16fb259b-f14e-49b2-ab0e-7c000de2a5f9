package com.snszyk.simas.fault.dto;

import com.snszyk.simas.fault.entity.FaultDefectCase;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "FaultDefectCaseDTO对象", description = "设备故障缺陷案例库")
public class FaultDefectCaseDTO extends FaultDefectCase {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "故障等级")
	private String faultLevelName;

	@ApiModelProperty(value = "故障类型")
	private String faultTypeName;

	@ApiModelProperty(value = "更换零件情况")
	private List<ComponentMaterialVO> materialList;
}
