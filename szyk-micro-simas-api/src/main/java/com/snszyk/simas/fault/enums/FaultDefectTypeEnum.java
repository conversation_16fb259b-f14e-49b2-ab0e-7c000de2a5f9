/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 故障缺陷类型枚举类
 *
 * <AUTHOR>
 * @date 2024/08/27 10:56
 **/
@Getter
@AllArgsConstructor
public enum FaultDefectTypeEnum {

	/**
	 * 故障
	 */
	FAULT(1, "故障"),
	/**
	 * 缺陷
	 */
	DEFECT(2, "缺陷"),
	/**
	 * 隐患
	 */
	RISK(3, "隐患"),

	;

	final Integer code;
	final String name;

	public static FaultDefectTypeEnum getByCode(Integer code){
		for (FaultDefectTypeEnum value : FaultDefectTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
