/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 故障缺陷来源枚举类
 *
 * <AUTHOR>
 * @date 2024/08/27 10:56
 **/
@Getter
@AllArgsConstructor
public enum FaultSourceEnum {

	/**
	 * 点巡检
	 */
	INSPECT("INSPECT", "点巡检"),

	/**
	 * 保养
	 */
	MAINTAIN("MAINTAIN", "保养"),

	/**
	 * 润滑
	 */
	LUBRICATE("LUBRICATE", "润滑"),

	/**
	 * 人工上报
	 */
	MANUAL("MANUAL", "人工上报"),

	;

	final String code;
	final String name;

	public static FaultSourceEnum getByCode(String code){
		for (FaultSourceEnum value : FaultSourceEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
