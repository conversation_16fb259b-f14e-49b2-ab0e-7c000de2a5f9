package com.snszyk.simas.fault.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("simas_fault_defect_case")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FaultDefectCase对象", description = "设备故障缺陷案例库")
public class FaultDefectCase extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "编号")
	private String no;

	@ApiModelProperty(value = "报修单id")
	private Long repairId;

	@ApiModelProperty(value = "报修单编号")
	private String repairNo;

	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	@ApiModelProperty(value = "部位id")
	private Long monitorId;

	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型id")
	private Long equipmentCategoryId;

	@ApiModelProperty(value = "设备类型名称")
	private String equipmentCategoryName;

	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	@ApiModelProperty(value = "设备型号")
	private String equipmentModel;

	@ApiModelProperty(value = "故障名称")
	private String faultName;

	@ApiModelProperty(value = "故障等级")
	private Integer faultLevel;

	@ApiModelProperty(value = "故障类型")
	private Integer faultType;

	@ApiModelProperty(value = "故障描述")
	private String faultDesc;

	@ApiModelProperty(value = "故障原因")
	private String faultReason;

	@ApiModelProperty(value = "解决方案")
	private String solution;
}
