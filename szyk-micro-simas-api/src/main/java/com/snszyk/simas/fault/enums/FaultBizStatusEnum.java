/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 故障缺陷工单状态枚举类
 *
 * <AUTHOR>
 * @date 2024/08/27 10:56
 **/
@Getter
@AllArgsConstructor
public enum FaultBizStatusEnum {

	/**
	 * 待处理
	 */
	WAIT_HANDLED(1, "待处理"),
	/**
	 * 已关闭
	 */
	IS_CLOSED(2, "已关闭"),
	/**
	 * 已报修
	 */
	IS_REPAIR(3, "已报修"),
	/**
	 * 已处理
	 */
	IS_HANDLED(4, "已处理"),

	;

	final Integer code;
	final String name;

	public static FaultBizStatusEnum getByCode(Integer code){
		for (FaultBizStatusEnum value : FaultBizStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
