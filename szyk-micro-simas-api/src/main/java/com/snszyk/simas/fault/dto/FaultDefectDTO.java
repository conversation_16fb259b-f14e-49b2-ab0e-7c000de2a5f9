/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.vo.BizLogVO;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备故障缺陷表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FaultDefectDTO extends FaultDefect {
	private static final long serialVersionUID = 1L;


	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String equipmentModel;
	/**
	 * 地点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "地点id")
	private Long locationId;
	/**
	 * 地点名称
	 */
	@ApiModelProperty(value = "地点名称")
	private String locationName;
	/**
	 * 地点路径
	 */
	@ApiModelProperty(value = "地点路径")
	private String locationPath;
	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;
	/**
	 * 故障等级（缺陷选择故障时选择的）
	 */
	@ApiModelProperty(value = "故障等级（缺陷选择故障时选择的）")
	private String levelName;
	/**
	 * 来源
	 */
	@ApiModelProperty(value = "来源")
	private String sourceName;
	/**
	 * 类型
	 */
	@ApiModelProperty(value = "类型")
	private String typeName;
	/**
	 * 上报人
	 */
	@ApiModelProperty(value = "上报人")
	private String reportUserName;
	/**
	 * 上报部门
	 */
	@ApiModelProperty(value = "上报部门")
	private String reportDeptName;
	/**
	 * 处理结果
	 */
	@ApiModelProperty(value = "处理结果")
	private String resultName;
	/**
	 * 处理人
	 */
	@ApiModelProperty(value = "处理人")
	private String operateUserName;
	/**
	 * 处理部门
	 */
	@ApiModelProperty(value = "处理部门")
	private String operateDeptName;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;
	/**
	 * 异常等级（上报的异常等级）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级（上报的异常等级）")
	private Integer abnormalLevel;
	/**
	 * 异常等级（上报的异常等级）
	 */
	@ApiModelProperty(value = "异常等级（上报的异常等级）")
	private String abnormalLevelName;
	/**
	 * 异常等级（上报的异常描述）
	 */
	@ApiModelProperty(value = "异常描述（上报的异常描述）")
	private String abnormalComment;
	/**
	 * 异常图片列表
	 */
	@ApiModelProperty(value = "异常图片列表")
	private List<Attach> abnormalAttachList;
	/**
	 * 补充图片列表
	 */
	@ApiModelProperty(value = "补充图片列表")
	private List<Attach> attachList;
	/**
	 * 业务日志
	 */
	@ApiModelProperty(value = "业务日志")
	private List<BizLogVO> bizLogList;
	/**
	 * 设备信息
	 */
	@ApiModelProperty(value = "设备信息")
	private DeviceAccountVO equipmentAccount;
	/**
	 * 关联维修单
	 */
	@ApiModelProperty(value = "关联维修单")
	private RepairDTO repair;
	/**
	 * 产生次数
	 */
	@ApiModelProperty(value = "产生次数")
	private Long count;
	/**
	 * 维修工单数量
	 */
	@ApiModelProperty(value = "维修工单数量")
	private Long repairCount;
	/**
	 * 完成次数
	 */
	@ApiModelProperty(value = "完成次数")
	private Long completeCount;
	/**
	 * 平均维修时长（小时）
	 */
	@ApiModelProperty(value = "平均维修时长（小时）")
	private BigDecimal averageTimeTake;
	/**
	 * 完成率
	 */
	@ApiModelProperty(value = "完成率")
	private BigDecimal completeRate;
	/**
	 * 标准名称
	 */
	@ApiModelProperty(value = "标准名称")
	private String standardName;

	@ApiModelProperty(value = "已完成工单ids")
	private String completedIds;

	/**
	 * 初始化统计数值
	 */
	public void init() {
		this.repairCount = 0L;
		this.completeCount = 0L;
		this.averageTimeTake = BigDecimal.ZERO;
		this.completeRate = BigDecimal.ZERO;
	}


}
