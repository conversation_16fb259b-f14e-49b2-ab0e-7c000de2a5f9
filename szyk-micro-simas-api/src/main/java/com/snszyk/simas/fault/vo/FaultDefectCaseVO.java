package com.snszyk.simas.fault.vo;

import com.snszyk.simas.fault.entity.FaultDefectCase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "FaultDefectCaseDTO对象", description = "设备故障缺陷案例库")
public class FaultDefectCaseVO extends FaultDefectCase {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "查询-开始时间")
	private String queryStartTime;

	@ApiModelProperty(value = "查询-结束时间")
	private String queryEndTime;
}
