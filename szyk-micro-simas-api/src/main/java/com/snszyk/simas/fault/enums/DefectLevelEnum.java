/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常等级枚举类
 *
 * <AUTHOR>
 * @date 2024/08/20 15:56
 **/
@Getter
@AllArgsConstructor
public enum DefectLevelEnum {

	/**
	 * 正常
	 */
	NORMAL(0, "正常"),
	/**
	 * 轻度
	 */
	LEVEL_ONE(1, "轻度"),
	/**
	 * 中度
	 */
	LEVEL_TWO(2, "一般"),
	/**
	 * 严重
	 */
	LEVEL_THREE(3, "严重"),
	/**
	 * 重度
	 */
	LEVEL_FOUR(4, "重度"),

	;

	final Integer code;
	final String name;

	public static DefectLevelEnum getByCode(Integer code){
		for (DefectLevelEnum value : DefectLevelEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
