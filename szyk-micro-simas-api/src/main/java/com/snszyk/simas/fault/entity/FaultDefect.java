/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.fault.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备故障缺陷表实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Accessors(chain = true)
@TableName("simas_fault_defect")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FaultDefect对象", description = "设备故障缺陷表")
public class FaultDefect extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;
	/**
	 * 标准id（来源是点巡检、保养时有）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准id（来源是点巡检、保养时有）")
	private Long standardId;
	/**
	 * 业务id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "业务id")
	private Long bizId;
	/**
	 * 异常来源（INSPECT：点巡检，MAINTAIN：保养，LUBRICATE：润滑，MANUAL：人工上报）
	 */
	@ApiModelProperty(value = "异常来源（INSPECT：点巡检，MAINTAIN：保养，LUBRICATE：润滑，MANUAL：人工上报）")
	private String source;
	/**
	 * 来源单号
	 */
	@ApiModelProperty(value = "来源单号")
	private String sourceNo;
	/**
	 * 缺陷名称
	 */
	@ApiModelProperty(value = "缺陷名称")
	private String name;
	/**
	 * （字典：repair_type）报修类型为“故障”时填写故障等级，故障名称
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "缺陷类型（字典：repair_type）报修类型为“故障”时填写故障等级，故障名称")
	private Integer type;
	/**
	 * 故障等级（缺陷选择故障时选择的）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "故障等级（缺陷选择故障时选择的）")
	private Integer level;
	/**
	 * 异常描述
	 */
	@ApiModelProperty(value = "异常描述")
	private String comment;
	/**
	 * 处理结果（1：已处理，2：异常不存在）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "处理结果（1：已处理，2：异常不存在）")
	private Integer result;
	/**
	 * 异常图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "异常图片")
	private String attachId;
	/**
	 * 异常信息
	 */
	@ApiModelProperty(value = "异常信息")
	private String abnormalInfo;
	/**
	 * 维修工单号
	 */
	@ApiModelProperty(value = "维修工单号")
	private String repairNo;
	/**
	 * 上报人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "上报人")
	private Long reportUser;
	/**
	 * 上报部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "上报部门")
	private Long reportDept;
	/**
	 * 上报时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "上报时间")
	private Date reportTime;
	/**
	 * 处理人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "处理人")
	private Long operateUser;
	/**
	 * 处理部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "处理部门")
	private Long operateDept;
	/**
	 * 处理备注
	 */
	@ApiModelProperty(value = "处理备注")
	private String operateRemark;
	/**
	 * 处理时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "处理时间")
	private Date operateTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
