package com.snszyk.simas.inventory.beanmapper;

import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanCountDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryPlan;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanSaveVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryPlanVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface EquipmentInventoryPlanBeanMapper {
	EquipmentInventoryPlanBeanMapper INSTANCE = Mappers.getMapper(EquipmentInventoryPlanBeanMapper.class);

	List<EquipmentInventoryPlanDTO> toDTOList(List<EquipmentInventoryPlan> list);

	EquipmentInventoryPlanVO toVO(EquipmentInventoryPlanSaveVO v);

	EquipmentInventoryPlanVO toVO(EquipmentInventoryPlanDTO dto);

	EquipmentInventoryPlan toEntity(EquipmentInventoryPlanVO vo);

	EquipmentInventoryPlanDTO toDTO(EquipmentInventoryPlan entity);
}
