/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件盘点工单实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@TableName("simas_equipment_inventory_order")
@EqualsAndHashCode(callSuper = true)
public class EquipmentInventoryOrder extends BaseCrudEntity {

	/**
	 * 盘点单号
	 */
	private String no;
	/**
	 * 盘点计划id
	 */
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	private String planNo;
	/**
	 * 盘点名称
	 */
	private String planName;
	/**
	 * 盘点开始日期
	 */
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	private LocalDate planEndDate;
	/**
	 * 计划创建人id
	 */
	private Long planCreateUserId;
	/**
	 * 计划创建人姓名
	 */
	private String planCreateUserName;
	/**
	 * 设备使用部门或归属部门id
	 */
	private Long deviceDeptId;
	/**
	 * 设备使用部门或者归属部门名称
	 */
	private String deviceDeptName;
	/**
	 * 设备使用人或负责人id
	 */
	private Long deviceUserId;
	/**
	 * 设备使用人或负责人姓名
	 */
	private String deviceUserName;
	/**
	 * 生成来源类型，1-使用部门+使用人；2-归属部门+负责人
	 */
	private Integer genSourceType;
	/**
	 * 完成时间
	 */
	private LocalDateTime completeTime;
	/**
	 * 总数
	 */
	private Integer totalQuantity;
	/**
	 * 草稿状态；1-草稿；2-已提交
	 */
	private Boolean draftStatus;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;
	/**
	 * 租户id
	 */
	private String tenantId;


}
