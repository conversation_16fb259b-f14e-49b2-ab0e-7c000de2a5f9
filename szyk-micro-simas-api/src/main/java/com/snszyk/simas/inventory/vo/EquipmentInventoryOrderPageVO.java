/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 备品备件盘点工单实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryOrderVo对象", description = "备品备件盘点工单")
public class EquipmentInventoryOrderPageVO extends BaseCrudSlimVo {
	/**
	 * 盘点名称
	 */
	@ApiModelProperty(value = "盘点名称")
	private String planName;
	/**
	 * 盘点工单状态
	 */
	@ApiModelProperty(value = "盘点工单状态")
	private Integer status;
	/**
	 * 盘点开始日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "盘点开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "盘点结束日期")
	private LocalDate planEndDate;
	/**
	 * 不等于某个状态
	 */
	@ApiModelProperty(hidden = true)
	private Integer neStatus;
}
