package com.snszyk.simas.inventory.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName: EquipmentInventoryPlanDTO
 * Package: com.snszyk.simas.inventory.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/27 19:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentInventoryPlanDTO extends BaseCrudDto {
	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 计划名称
	 */
	@ApiModelProperty(value = "计划名称")
	private String name;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private LocalDate endDate;
	/**
	 * 总数
	 */
	@ApiModelProperty(value = "总数")
	private Integer totalQuantity;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private String inventoryUserId;
	/**
	 * 盘点人员姓名
	 */
	@ApiModelProperty(value = "盘点人员姓名")
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	@ApiModelProperty(value = "完成时间")
	private LocalDateTime completeTime;
	/**
	 * 盘点部门id
	 */
	@ApiModelProperty(value = "盘点部门id")
	private String inventoryDeptId;
	/**
	 * 盘点部门名称
	 */
	@ApiModelProperty(value = "盘点部门名称")
	private String inventoryDeptName;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;
	/**
	 * 盘点计划状态名称
	 */
	@ApiModelProperty(value = "盘点计划状态名称")
	private String statusName;
	/**
	 * 已经盘点数量
	 */
	@ApiModelProperty(value = "已经盘点数量")
	private Integer inventory;
	/**
	 * 租户id
	 */
	private String tenantId;
}
