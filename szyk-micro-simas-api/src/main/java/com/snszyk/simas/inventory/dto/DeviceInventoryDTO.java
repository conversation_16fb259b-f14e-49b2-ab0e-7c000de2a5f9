package com.snszyk.simas.inventory.dto;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.inventory.enums.EquipmentInventoryOrderGenSourceTypeEnum;
import lombok.Data;

/**
 * ClassName: DeviceAccountDTO
 * Package: com.snszyk.simas.inventory.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/27 19:33
 */
@Data
public class DeviceInventoryDTO extends DeviceAccountVO {
	/**
	 * 部门id，可能使用部门id和归属部门id
	 */
	private Long useDeptIdOrBelongDeptId;
	/**
	 * 使用人和责任人id
	 */
	private Long useUserIdOrResponsibleId;
	/**
	 * 生成来源类型
	 */
	private EquipmentInventoryOrderGenSourceTypeEnum genSourceTypeEnum;
}
