// package com.snszyk.simas.inventory.enums;
//
// import lombok.AllArgsConstructor;
// import lombok.Getter;
//
// @Getter
// @AllArgsConstructor
// public enum InventoryStatusEnum {
// 	FINISH(1, "已盘"),
// 	WAIT(2, "未盘");
//
//
// 	final Integer code;
// 	final String name;
//
// 	public static InventoryStatusEnum getByCode(Integer code){
// 		for (InventoryStatusEnum value : InventoryStatusEnum.values()) {
// 			if (code.equals(value.getCode())){
// 				return value;
// 			}
// 		}
// 		return null;
// 	}
//
//
// }
