package com.snszyk.simas.inventory.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName: InventoryPlanTypeDTO
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/26 14:50
 */
@Data
@AllArgsConstructor
@Accessors(chain = true)
public class EquipmentInventoryPlanTypeDTO {
	/**
	 * 设备盘点计划数量
	 */
	@ApiModelProperty(value = "设备盘点计划数量")
	private Long equipmentInventoryPlanCount;
	/**
	 * 备品备件盘点计划数量
	 */
	@ApiModelProperty(value = "备品备件盘点计划数量")
	private Long sparePartsInventoryPlanCount;


	/**
	 * 初始化
	 */
	public static EquipmentInventoryPlanTypeDTO init() {
		return new EquipmentInventoryPlanTypeDTO(0L, 0L);
	}
}
