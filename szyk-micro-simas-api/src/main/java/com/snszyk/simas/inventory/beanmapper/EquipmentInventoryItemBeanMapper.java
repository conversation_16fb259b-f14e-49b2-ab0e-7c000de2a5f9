package com.snszyk.simas.inventory.beanmapper;

import com.snszyk.simas.inventory.dto.DeviceInventoryDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryItemDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryItem;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemDraftOrSubmitVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryItemVO;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface EquipmentInventoryItemBeanMapper {
	EquipmentInventoryItemBeanMapper INSTANCE = Mappers.getMapper(EquipmentInventoryItemBeanMapper.class);

	List<EquipmentInventoryItemDTO> toDTOList(List<EquipmentInventoryItem> list);

	/**
	 * Entity转DTO
	 */
	EquipmentInventoryItemDTO toDTO(EquipmentInventoryItem entity);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "planId", source = "planDTO.id")
	@Mapping(target = "planNo", source = "planDTO.no")
	@Mapping(target = "planName", source = "planDTO.name")
	@Mapping(target = "planStartDate", source = "planDTO.startDate")
	@Mapping(target = "planEndDate", source = "planDTO.endDate")
	@Mapping(target = "inventoryOrderId", source = "orderVO.id")
	@Mapping(target = "inventoryOrderNo", source = "orderVO.no")
	@Mapping(target = "deviceUserId", source = "orderVO.deviceUserId")
	@Mapping(target = "deviceId", source = "device.id")
	@Mapping(target = "belongDeptId", source = "device.belongDept")
	@Mapping(target = "responsibleUserId", source = "device.responsiblePerson")
	@Mapping(target = "useDeptId", source = "device.useDept")
	@Mapping(target = "useUserId", source = "device.userId")
	EquipmentInventoryItemVO toVO(EquipmentInventoryPlanDTO planDTO, EquipmentInventoryOrderVO orderVO, DeviceInventoryDTO device);

	Collection<EquipmentInventoryItem> toEntityList(List<EquipmentInventoryItemVO> itemVOList);


	EquipmentInventoryItemVO toVO(EquipmentInventoryItemDraftOrSubmitVO vo);

	EquipmentInventoryItemVO toVO(EquipmentInventoryItemDTO dto);
}
