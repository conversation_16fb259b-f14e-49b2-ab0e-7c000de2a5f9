/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 盘点计划表实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryPlanVo对象", description = "盘点计划表")
public class EquipmentInventoryPlanPageVO extends BaseCrudSlimVo {

	/**
	 * 计划名称
	 */
	@ApiModelProperty(value = "计划名称")
	private String name;
	/**
	 * 开始日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "开始日期")
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "结束日期")
	private LocalDate endDate;
	/**
	 * 盘点状态
	 */
	@ApiModelProperty(value = "盘点状态")
	private Integer status;

}
