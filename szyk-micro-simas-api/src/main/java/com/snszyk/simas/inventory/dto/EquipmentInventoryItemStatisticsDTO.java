/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 备品备件盘点明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
public class EquipmentInventoryItemStatisticsDTO {
	/**
	 * 全部
	 *
	 * @return
	 */
	@ApiModelProperty(value = "全部")
	private Long total;
	/**
	 * 未盘点
	 */
	@ApiModelProperty(value = "未盘点")
	private Long notStart;
	/**
	 * 正常
	 *
	 * @return
	 */
	@ApiModelProperty(value = "正常")
	private Long normal;
	/**
	 * 缺失
	 *
	 * @return
	 */
	@ApiModelProperty(value = "缺失")
	private Long loss;

	/**
	 * 初始化
	 */
	public EquipmentInventoryItemStatisticsDTO init() {
		this.total = 0L;
		this.notStart = 0L;
		this.normal = 0L;
		this.loss = 0L;
		return this;
	}


}
