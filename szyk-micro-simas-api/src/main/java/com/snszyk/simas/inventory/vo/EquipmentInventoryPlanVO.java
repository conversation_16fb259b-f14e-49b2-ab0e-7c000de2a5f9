/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 盘点计划表实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryPlanVo对象", description = "盘点计划表")
public class EquipmentInventoryPlanVO extends BaseCrudVo {

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 计划名称
	 */
	@ApiModelProperty(value = "计划名称")
	private String name;
	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private LocalDate endDate;
	/**
	 * 总数
	 */
	@ApiModelProperty(value = "总数")
	private Integer totalQuantity;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private String inventoryUserId;
	/**
	 * 盘点人员姓名
	 */
	@ApiModelProperty(value = "盘点人员姓名")
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	@ApiModelProperty(value = "完成时间")
	private LocalDateTime completeTime;
	/**
	 * 盘点部门id
	 */
	@ApiModelProperty(value = "盘点部门id")
	private String inventoryDeptId;
	/**
	 * 盘点部门名称
	 */
	@ApiModelProperty(value = "盘点部门名称")
	private String inventoryDeptName;
	/**
	 * 租户id
	 */
	private String tenantId;


}
