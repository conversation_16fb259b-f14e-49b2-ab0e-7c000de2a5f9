package com.snszyk.simas.inventory.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EquipmentInventoryOrderStatusEnum {

	NOT_START(0, "未启动"),
	PROCESS(1, "盘点中"),
	COMPLETE(2, "已完成"),
	;

	final Integer code;
	final String name;

	public static EquipmentInventoryOrderStatusEnum getByCode(Integer code) {
		for (EquipmentInventoryOrderStatusEnum value : EquipmentInventoryOrderStatusEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}
}
