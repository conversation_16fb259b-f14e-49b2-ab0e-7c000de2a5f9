package com.snszyk.simas.inventory.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EquipmentInventoryItemResultEnum {
	/**
	 * 未盘点
	 */
	NOT_START(0, "未盘点"),
	/**
	 * 正常
	 */
	NORMAL(1, "正常"),
	/**
	 * 缺失
	 */
	LOSS(2, "缺失"),
	;

	final Integer code;
	final String name;

	public static EquipmentInventoryItemResultEnum getByCode(Integer code) {
		for (EquipmentInventoryItemResultEnum value : EquipmentInventoryItemResultEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
