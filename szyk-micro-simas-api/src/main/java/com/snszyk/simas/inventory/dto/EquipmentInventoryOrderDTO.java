/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 备品备件盘点工单实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryOrderDto对象", description = "备品备件盘点工单")
public class EquipmentInventoryOrderDTO extends BaseCrudDto {

	/**
	 * 盘点单号
	 */
	@ApiModelProperty(value = "盘点单号")
	private String no;
	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	@ApiModelProperty(value = "盘点计划单号")
	private String planNo;
	/**
	 * 盘点名称
	 */
	@ApiModelProperty(value = "盘点名称")
	private String planName;
	/**
	 * 盘点开始日期
	 */
	@ApiModelProperty(value = "盘点开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点结束日期
	 */
	@ApiModelProperty(value = "盘点结束日期")
	private LocalDate planEndDate;
	/**
	 * 计划创建人id
	 */
	@ApiModelProperty(value = "计划创建人id")
	private Long planCreateUserId;
	/**
	 * 计划创建人姓名
	 */
	@ApiModelProperty(value = "计划创建人姓名")
	private String planCreateUserName;
	/**
	 * 设备使用部门或归属部门id
	 */
	@ApiModelProperty(value = "设备使用部门或归属部门id")
	private Long deviceDeptId;
	/**
	 * 设备使用部门或者归属部门名称
	 */
	@ApiModelProperty(value = "设备使用部门或者归属部门名称")
	private String deviceDeptName;
	/**
	 * 设备使用人或负责人id
	 */
	@ApiModelProperty(value = "设备使用人或负责人id")
	private Long deviceUserId;
	/**
	 * 设备使用人或负责人姓名
	 */
	@ApiModelProperty(value = "设备使用人或负责人姓名")
	private String deviceUserName;
	/**
	 * 生成来源类型，1-使用部门+使用人；2-归属部门+负责人
	 */
	@ApiModelProperty(value = "生成来源类型，1-使用部门+使用人；2-归属部门+负责人")
	private Integer genSourceType;
	/**
	 * 完成时间
	 */
	@ApiModelProperty(value = "完成时间")
	private LocalDateTime completeTime;
	/**
	 * 总数
	 */
	@ApiModelProperty(value = "总数")
	private Integer totalQuantity;
	/**
	 * 草稿状态；1-草稿；2-已提交
	 */
	@ApiModelProperty(value = "草稿状态；1-草稿；2-已提交")
	private Boolean draftStatus;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;
	/**
	 * 盘点工单状态名称
	 */
	@ApiModelProperty(value = "盘点工单状态名称")
	private String statusName;
	/**
	 * 已盘点数量
	 */
	@ApiModelProperty(value = "已盘点数量")
	private Integer hasInventoryQuantity;
	/**
	 * 创建人姓名
	 */
	@ApiModelProperty(value = "创建人姓名")
	private String createUserName;
	/**
	 * 修改人姓名
	 */
	@ApiModelProperty(value = "修改人姓名")
	private String updateUserName;



}
