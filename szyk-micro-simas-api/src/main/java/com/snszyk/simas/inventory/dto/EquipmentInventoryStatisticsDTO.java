package com.snszyk.simas.inventory.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentInventoryStatisticsDTO {

	@ApiModelProperty(value = "全部数量")
	private Integer all;
	@ApiModelProperty(value = "未盘数量")
	private Integer unInventory;
	@ApiModelProperty(value = "已盘数量")
	private Integer inventory;

}
