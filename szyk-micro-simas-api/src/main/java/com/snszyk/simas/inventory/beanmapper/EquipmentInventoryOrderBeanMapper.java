package com.snszyk.simas.inventory.beanmapper;

import com.snszyk.simas.inventory.dto.EquipmentInventoryOrderDTO;
import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanDTO;
import com.snszyk.simas.inventory.entity.EquipmentInventoryOrder;
import com.snszyk.simas.inventory.vo.EquipmentInventoryOrderVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Package: com.snszyk.simas.beanmapper
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 14:11
 */
@Mapper
public interface EquipmentInventoryOrderBeanMapper {
	EquipmentInventoryOrderBeanMapper INSTANCE = Mappers.getMapper(EquipmentInventoryOrderBeanMapper.class);

	@BeanMapping(ignoreByDefault = true)
	@Mapping(target = "deviceUserId", source = "userId")
	@Mapping(target = "planId", source = "dto.id")
	@Mapping(target = "planNo", source = "dto.no")
	@Mapping(target = "planName", source = "dto.name")
	@Mapping(target = "planStartDate", source = "dto.startDate")
	@Mapping(target = "planEndDate", source = "dto.endDate")
	@Mapping(target = "planCreateUserId", source = "dto.createUser")
	EquipmentInventoryOrderVO toVO(EquipmentInventoryPlanDTO dto, Long userId);

	List<EquipmentInventoryOrder> toEntityList(List<EquipmentInventoryOrderVO> orderVOList);

	List<EquipmentInventoryOrderDTO> toDTOList(List<EquipmentInventoryOrder> entityList);

	EquipmentInventoryOrderVO toVO(EquipmentInventoryOrderDTO dto);
}
