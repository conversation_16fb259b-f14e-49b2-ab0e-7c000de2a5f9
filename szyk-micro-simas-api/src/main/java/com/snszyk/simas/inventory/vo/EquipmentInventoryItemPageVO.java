/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import com.snszyk.core.crud.vo.BaseCrudSlimVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备盘点明细分页查询对象
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryItemPageVO对象", description = "设备盘点明细分页查询")
public class EquipmentInventoryItemPageVO extends BaseCrudSlimVo {
	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点工单id
	 */
	@ApiModelProperty(value = "盘点工单id")
	private Long inventoryOrderId;
	/**
	 * 盘点结果
	 */
	@ApiModelProperty(value = "盘点结果（0：未盘点，1：正常，2：缺失）")
	private Integer result;
	/**
	 * 设备名称（模糊搜索）
	 */
	@ApiModelProperty(value = "设备名称，支持模糊搜索")
	private String deviceName;


}
