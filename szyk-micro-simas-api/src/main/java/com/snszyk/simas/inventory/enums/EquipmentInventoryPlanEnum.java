package com.snszyk.simas.inventory.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: EquipmentInventoryPlanEnum
 * Package: com.snszyk.simas.inventory.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/26 18:51
 */
@Getter
@AllArgsConstructor
public enum EquipmentInventoryPlanEnum {
	NOT_START(0, "未盘点"),

	PROCESS(1, "盘点中"),

	COMPLETE(2, "已完成"),
	;

	final Integer code;
	final String name;

	public static EquipmentInventoryPlanEnum getByCode(Integer code) {
		for (EquipmentInventoryPlanEnum value : EquipmentInventoryPlanEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}
}
