/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 备品备件盘点明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@TableName("simas_equipment_inventory_item")
@EqualsAndHashCode(callSuper = true)
public class EquipmentInventoryItem extends BaseCrudEntity {

	/**
	 * 盘点计划id
	 */
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	private String planNo;
	/**
	 * 盘点计划名称
	 */
	private String planName;
	/**
	 * 盘点计划开始日期
	 */
	private LocalDate planStartDate;
	/**
	 * 盘点计划结束日期
	 */
	private LocalDate planEndDate;
	/**
	 * 盘点工单id
	 */
	private Long inventoryOrderId;
	/**
	 * 盘点工单号
	 */
	private String inventoryOrderNo;
	/**
	 * 设备id
	 */
	private Long deviceId;
	/**
	 * 所属部门id
	 */
	private Long belongDeptId;
	/**
	 * 责任人id
	 */
	private Long responsibleUserId;
	/**
	 * 使用部门id
	 */
	private Long useDeptId;
	/**
	 * 使用人id
	 */
	private Long useUserId;
	/**
	 * 盘点结果（0：未盘点，1:正常，2：缺失）
	 */
	private Integer result;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 盘点人员id
	 */
	private Long deviceUserId;
	/**
	 * 备注
	 */
	private String remark;


}
