/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 设备临时盘点或提交VO
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@ApiModel(value = "EquipmentInventoryItemDraftOrSubmitVO", description = "设备临时盘点或提交VO")
public class EquipmentInventoryItemDraftOrSubmitVO {
	/**
	 * id
	 */
	@NotNull
	@ApiModelProperty(value = "明细id", required = true)
	private Long id;
	/**
	 * 盘点工单id
	 */
	@NotNull
	@ApiModelProperty(value = "盘点工单id", required = true)
	private Long inventoryOrderId;
	/**
	 * 设备台账id
	 */
	@NotNull
	@ApiModelProperty(value = "设备id", required = true)
	private Long deviceId;
	/**
	 * 所属部门id
	 */
	@ApiModelProperty(value = "所属部门id")
	private Long belongDeptId;
	/**
	 * 责任人id
	 */
	@ApiModelProperty(value = "责任人id")
	private Long responsibleUserId;
	/**
	 * 使用部门id
	 */
	@ApiModelProperty(value = "使用部门id")
	private Long useDeptId;
	/**
	 * 使用人id
	 */
	@ApiModelProperty(value = "使用人id")
	private Long useUserId;
	/**
	 * 盘点结果（0：未盘点，1:正常，2：缺失）
	 */
	@NotNull
	@ApiModelProperty(value = "盘点结果（0：未盘点，1:正常，2：缺失）", required = true)
	private Integer result;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
