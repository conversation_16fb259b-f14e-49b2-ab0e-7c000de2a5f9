/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 盘点计划表实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@TableName("simas_equipment_inventory_plan")
@EqualsAndHashCode(callSuper = true)
public class EquipmentInventoryPlan extends BaseCrudEntity {

	/**
	 * 编号
	 */
	private String no;
	/**
	 * 计划名称
	 */
	private String name;
	/**
	 * 开始日期
	 */
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	private LocalDate endDate;
	/**
	 * 总数
	 */
	private Integer totalQuantity;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 盘点人员id
	 */
	private String inventoryUserId;
	/**
	 * 盘点人员姓名
	 */
	private String inventoryUserName;
	/**
	 * 完成时间
	 */
	private LocalDateTime completeTime;
	/**
	 * 盘点部门id
	 */
	private String inventoryDeptId;
	/**
	 * 盘点部门名称
	 */
	private String inventoryDeptName;
	/**
	 * 删除标志
	 */
	@TableField(exist = false)
	private Integer isDeleted;
	/**
	 * 删除时间
	 */
	@TableLogic(delval = "id")
	private Long deleteTime;
	/**
	 * 租户id
	 */
	private String tenantId;


}
