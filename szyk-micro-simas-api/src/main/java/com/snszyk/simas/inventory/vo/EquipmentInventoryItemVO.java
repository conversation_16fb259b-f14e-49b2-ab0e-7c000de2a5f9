/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 备品备件盘点明细实体类
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryItemVo对象", description = "备品备件盘点明细")
public class EquipmentInventoryItemVO extends BaseCrudVo {

	/**
	 * 盘点计划id
	 */
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;
	/**
	 * 盘点计划单号
	 */
	@ApiModelProperty(value = "盘点计划单号")
	private String planNo;
	/**
	 * 盘点计划名称
	 */
	@ApiModelProperty(value = "盘点计划名称")
	private String planName;
	/**
	 * 盘点计划开始日期
	 */
	@ApiModelProperty(value = "盘点计划开始日期")
	private LocalDate planStartDate;
	/**
	 * 盘点计划结束日期
	 */
	@ApiModelProperty(value = "盘点计划结束日期")
	private LocalDate planEndDate;
	/**
	 * 盘点工单id
	 */
	@ApiModelProperty(value = "盘点工单id")
	private Long inventoryOrderId;
	/**
	 * 盘点工单号
	 */
	@ApiModelProperty(value = "盘点工单号")
	private String inventoryOrderNo;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	/**
	 * 所属部门id
	 */
	@ApiModelProperty(value = "所属部门id")
	private Long belongDeptId;
	/**
	 * 责任人id
	 */
	@ApiModelProperty(value = "责任人id")
	private Long responsibleUserId;
	/**
	 * 使用部门id
	 */
	@ApiModelProperty(value = "使用部门id")
	private Long useDeptId;
	/**
	 * 使用人id
	 */
	@ApiModelProperty(value = "使用人id")
	private Long useUserId;
	/**
	 * 盘点结果（0：未盘点，1:正常，2：缺失）
	 */
	@ApiModelProperty(value = "盘点结果（0：未盘点，1:正常，2：缺失）")
	private Integer result;
	/**
	 * 是否已删除
	 */
	@ApiModelProperty(value = "是否已删除")
	private Long deleteTime;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;
	/**
	 * 盘点人员id
	 */
	@ApiModelProperty(value = "盘点人员id")
	private Long deviceUserId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
