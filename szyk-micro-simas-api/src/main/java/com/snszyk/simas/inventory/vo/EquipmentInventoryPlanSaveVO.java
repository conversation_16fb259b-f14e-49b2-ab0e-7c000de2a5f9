/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 盘点计划表实体类
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Data
@ApiModel(value = "EquipmentInventoryPlanVo对象", description = "盘点计划表")
public class EquipmentInventoryPlanSaveVO {

	/**
	 * 计划名称
	 */
	@NotBlank
	@ApiModelProperty(value = "计划名称", required = true)
	private String name;
	/**
	 * 开始日期
	 */
	@NotNull
	@ApiModelProperty(value = "开始日期", required = true)
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@NotNull
	@ApiModelProperty(value = "结束日期", required = true)
	private LocalDate endDate;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 盘点部门id
	 */
	@NotBlank
	@ApiModelProperty(value = "盘点部门id", required = true)
	private String inventoryDeptId;
	/**
	 * 盘点部门名称
	 */
	@NotBlank
	@ApiModelProperty(value = "盘点部门名称", required = true)
	private String inventoryDeptName;

}
