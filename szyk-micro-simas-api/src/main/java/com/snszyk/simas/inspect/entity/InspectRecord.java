/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备点巡检记录表实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@Accessors(chain = true)
@TableName("simas_inspect_record")
@ApiModel(value = "InspectRecord对象", description = "设备点巡检记录表")
public class InspectRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 工单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "工单id")
	private Long orderId;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;
	/**
	 * 标准id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准id")
	private Long standardId;
	/**
	 * 标准信息
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准信息")
	private String standardInfo;
	/**
	 * 是否异常
	 */
	@ApiModelProperty(value = "是否异常")
	private Integer isAbnormal;
	/**
	 * 异常等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;
	/**
	 * 异常描述
	 */
	@ApiModelProperty(value = "异常描述")
	private String abnormalComment;
	/**
	 * 异常图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "异常图片")
	private String abnormalImage;
	/**
	 * 是否现场处理
	 */
	@ApiModelProperty(value = "是否现场处理")
	private Integer isHandled;
	/**
	 * 检查人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检查人")
	private Long inspectUser;
	/**
	 * 检查时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "检查时间")
	private Date inspectTime;


}
