/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备点巡检标准表实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@TableName("simas_inspect_standard")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectStandard对象", description = "设备点巡检标准表")
public class InspectStandard extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 标准
	 */
	@ApiModelProperty(value = "标准")
	private String standard;
	/**
	 * 方法
	 */
	@ApiModelProperty(value = "方法")
	private String method;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;


}
