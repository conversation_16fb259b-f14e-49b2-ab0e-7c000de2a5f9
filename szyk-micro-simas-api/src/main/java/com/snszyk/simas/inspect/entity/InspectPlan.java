/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备点巡检计划表实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@TableName("simas_inspect_plan")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectPlan对象", description = "设备点巡检计划表")
public class InspectPlan extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 周期类型（字典：plan_cycle）
	 */
	@ApiModelProperty(value = "周期类型（字典：plan_cycle）")
	private String cycleType;
	/**
	 * 周期间隔
	 */
	@ApiModelProperty(value = "周期间隔")
	private Integer cycleInterval;
	/**
	 * 执行部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行部门")
	private Long executeDept;
	/**
	 * 执行人
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行人")
	private Long executeUser;
	/**
	 * 开始执行日期（可选大于当天）
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "开始执行日期（可选大于当天）")
	private Date startDate;
	/**
	 * 结束执行日期（可选大于等于开始）
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "结束执行日期（可选大于等于开始）")
	private Date endDate;
	/**
	 * 执行时间
	 */
	@ApiModelProperty(value = "执行时间")
	private String executeTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
