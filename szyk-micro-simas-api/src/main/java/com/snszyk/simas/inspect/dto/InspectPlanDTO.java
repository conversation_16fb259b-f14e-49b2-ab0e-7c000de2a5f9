/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.dto;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.common.vo.ByDaySetVO;
import com.snszyk.simas.inspect.entity.InspectPlan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备点巡检计划表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectPlanDTO extends InspectPlan {
	private static final long serialVersionUID = 1L;

	/**
	 * 周期类型（字典：plan_cycle）
	 */
	@ApiModelProperty(value = "周期类型（字典：plan_cycle）")
	private String cycleTypeName;

	/**
	 * 执行部门
	 */
	@ApiModelProperty(value = "执行部门")
	private String executeDeptName;

	/**
	 * 执行人
	 */
	@ApiModelProperty(value = "执行人")
	private String executeUserName;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 设备数量
	 */
	@ApiModelProperty(value = "设备数量")
	private Integer equipmentCount;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

	/**
	 * 按日-时间设置
	 */
	@ApiModelProperty(value = "按日-时间设置")
	private List<ByDaySetVO> byDaySet;

	/**
	 * 按周-时间设置
	 */
	@ApiModelProperty(value = "按周-时间设置")
	private List<String> byWeekSet;

	/**
	 * 按月-时间设置
	 */
	@ApiModelProperty(value = "按月-时间设置")
	private List<String> byMonthSet;

	/**
	 * 时间设置（查看用）
	 */
	@ApiModelProperty(value = "时间设置（查看用）")
	private String executeTimeStr;

	/**
	 * 所选设备id列表
	 */
	@ApiModelProperty(value = "所选设备id列表")
	private List<Long> equipmentIds;

	/**
	 * 所选设备列表
	 */
	@ApiModelProperty(value = "所选设备列表")
	private List<DeviceAccountVO> equipmentList;

	/**
	 * 所选设备列表
	 */
	@ApiModelProperty(value = "正常设备数量")
	private Integer normalCount;

	/**
	 * 所选设备列表
	 */
	@ApiModelProperty(value = "异常设备数量")
	private Integer abnormalCount;

	/**
	 * 漏检设备数量
	 */
	@ApiModelProperty(value = "漏检设备数量")
	private Integer omissionCount;


}
