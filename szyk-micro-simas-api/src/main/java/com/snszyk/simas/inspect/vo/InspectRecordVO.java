/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.vo;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.inspect.entity.InspectRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备点巡检记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectRecordVO对象", description = "设备点巡检记录表")
public class InspectRecordVO extends InspectRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 点巡检工单号
	 */
	@ApiModelProperty(value = "点巡检工单号")
	private String orderNo;

	/**
	 * 点巡检人
	 */
	@ApiModelProperty(value = "点巡检人")
	private String inspectUserName;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

	/**
	 * 异常等级
	 */
	@ApiModelProperty(value = "异常等级")
	private String abnormalLevelName;

	/**
	 * 是否现场处理
	 */
	@ApiModelProperty(value = "是否现场处理")
	private String isHandledName;

	/**
	 * 异常图片列表
	 */
	@ApiModelProperty(value = "异常图片列表")
	private List<Attach> abnormalImageList;


}
