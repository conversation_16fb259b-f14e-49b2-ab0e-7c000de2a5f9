/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.dto;

import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.inspect.entity.InspectOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备点巡检工单表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectOrderDTO extends InspectOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单名称
	 */
	@ApiModelProperty(value = "工单名称")
	private String orderName;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	/**
	 * 设备类型名称
	 */
	@ApiModelProperty(value = "设备类型名称")
	private String equipmentCategoryName;


	/**
	 * 周期类型（字典：plan_cycle）
	 */
	@ApiModelProperty(value = "周期类型（字典：plan_cycle）")
	private String cycleTypeName;

	/**
	 * 执行部门
	 */
	@ApiModelProperty(value = "执行部门")
	private String executeDeptName;

	/**
	 * 执行人
	 */
	@ApiModelProperty(value = "执行人")
	private String executeUserName;

	/**
	 * 审批人员
	 */
	@ApiModelProperty(value = "审批人员")
	private String approvalUserName;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTimeStr;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTimeStr;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 点巡检结果
	 */
	@ApiModelProperty(value = "点巡检结果")
	private String inspectResult;

	/**
	 * 设备台账信息
	 */
	@ApiModelProperty(value = "设备台账信息")
	private DeviceAccountVO equipmentAccount;

	/**
	 * 点巡检计划
	 */
	@ApiModelProperty(value = "点巡检计划")
	private InspectPlanDTO inspectPlan;

	/**
	 * 点巡检标准列表
	 */
	@ApiModelProperty(value = "点巡检标准列表")
	List<InspectStandardDTO> standardList;

	@ApiModelProperty(value = "统计的部门id")
	private Long analysisDeptId;


}
