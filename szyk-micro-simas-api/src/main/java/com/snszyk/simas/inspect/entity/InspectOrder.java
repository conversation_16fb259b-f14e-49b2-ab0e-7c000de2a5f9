/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.IOrderField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备点巡检工单表实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@Accessors(chain = true)
@TableName("simas_inspect_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectOrder对象", description = "设备点巡检工单表")
public class InspectOrder extends TenantEntity implements Serializable, IOrderField {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;
	/**
	 * 执行部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行部门")
	private Long executeDept;
	/**
	 * 执行人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行人")
	private Long executeUser;
	/**
	 * 开始执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "开始执行日期")
	private Date startTime;
	/**
	 * 结束执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "结束执行日期")
	private Date endTime;
	/**
	 * 计划id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计划id")
	private Long planId;
	/**
	 * 计划信息
	 */
	@ApiModelProperty(value = "计划信息")
	private String planInfo;
	/**
	 * 执行标准列表
	 */
	@ApiModelProperty(value = "执行标准列表")
	private String standardInfo;
	/**
	 * 是否异常（0否1是）
	 */
	@ApiModelProperty(value = "是否异常（0否1是）")
	private Integer isAbnormal;
	/**
	 * 是否即将超期（0否1是）
	 */
	@ApiModelProperty(value = "是否即将超期（0否1是）")
	private Integer isExpired;
	/**
	 * 超时说明
	 */
	@ApiModelProperty(value = "超时说明")
	private String remark;
	/**
	 * 提交时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "提交时间")
	private Date submitTime;
	/**
	 * 审批人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "审批人")
	private Long approvalUser;
	/**
	 * 拒绝原因
	 */
	@ApiModelProperty(value = "拒绝原因 v1.2.1")
	private String rejectReason;
	/**
	 * 是否需要审批
	 */
	@ApiModelProperty(value = "是否需要审批 v1.2.1")
	private Boolean isNeedApproval;

	/**
	 * 设备名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备sn
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备sn")
	private String equipmentSn;

	/**
	 * 设备分类
	 */
	@TableField(exist = false)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型")
	private Long equipmentCategory;

	@Override
	public Long getDeptId() {
		return this.getExecuteDept();
	}
}
