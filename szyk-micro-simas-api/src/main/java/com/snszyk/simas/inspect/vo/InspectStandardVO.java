/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.vo;

import com.snszyk.simas.inspect.entity.InspectStandard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备点巡检标准表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectStandardVO对象", description = "设备点巡检标准表")
public class InspectStandardVO extends InspectStandard {
    private static final long serialVersionUID = 1L;

    /**
     * 部位名称
     */
    @ApiModelProperty(value = "部位名称")
    private String monitorName;

	/**
	 * 部位类型
	 */
    @ApiModelProperty(value = "部位类型")
    private Integer monitorType;

	/**
	 * 部位类型名称
	 */
    @ApiModelProperty(value = "部位类型名称")
    private String monitorTypeName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUserName;


}
