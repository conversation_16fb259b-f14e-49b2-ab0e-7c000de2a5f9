/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.vo;

import com.snszyk.simas.inspect.entity.InspectPlanEquipment;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 设备点巡检计划关联表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectPlanEquipmentVO对象", description = "设备点巡检计划关联表")
public class InspectPlanEquipmentVO extends InspectPlanEquipment {
	private static final long serialVersionUID = 1L;

}
