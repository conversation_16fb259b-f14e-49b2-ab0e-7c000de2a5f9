/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.dto;

import com.snszyk.simas.inspect.entity.InspectStandard;
import com.snszyk.simas.inspect.vo.InspectRecordVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备点巡检标准表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectStandardDTO extends InspectStandard {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private Integer isAbnormal;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

	/**
	 * 点巡检结果
	 */
	@ApiModelProperty(value = "点巡检结果")
	private InspectRecordVO inspectRecord;

}
