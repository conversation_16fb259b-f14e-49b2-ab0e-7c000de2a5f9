/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.inspect.vo;

import cn.hutool.json.JSONUtil;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.inspect.entity.InspectOrder;
import com.snszyk.simas.inspect.entity.InspectPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备点巡检工单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectOrderVO对象", description = "设备点巡检工单表")
public class InspectOrderVO extends InspectOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件-关键字（工单单号、设备编号、设备名称）
	 */
	@ApiModelProperty(value = "查询条件-关键字（工单单号、设备编号、设备名称）")
	private String keywords;

	/**
	 * 查询条件-工单名称
	 */
	@ApiModelProperty(value = "查询条件-工单名称")
	private String orderName;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 查询条件-开始日期
	 */
	@ApiModelProperty(value = "查询条件-开始日期")
	private String startDate;

	/**
	 * 查询条件-结束日期
	 */
	@ApiModelProperty(value = "查询条件-结束日期")
	private String endDate;

	/**
	 * 超时时间间隔
	 */
	@ApiModelProperty(value = "超时时间间隔")
	private BigDecimal timeInterval;

	/**
	 * 设备点巡检结果列表
	 */
	@ApiModelProperty(value = "设备点巡检结果列表")
	private List<InspectRecordVO> inspectRecordList;

	/**
	 * 部门id列表
	 */
	@ApiModelProperty(value = "部门id列表")
	private List<Long> deptIdList;

	/**
	 * 工单id列表
	 */
	@ApiModelProperty(value = "工单id列表")
	private List<Long> orderIds;

	/**
	 * 终端类型（PC端：PC，APP端：APP）
	 */
	@ApiModelProperty(value = "终端类型（PC端：PC，APP端：APP）")
	private String clientType;

	@ApiModelProperty(value = "数据权限角色 0 全部 1 操作人 2 其他")
	private Integer queryAuthRole;

	@ApiModelProperty(value = "状态列表")
	private List<Integer> statusList;

	@ApiModelProperty(value = "仅查看执行人")
	private Long onlyQueryExecuteUser;
	@ApiModelProperty(value = "不等于状态")
	private Integer neStatus;

	public InspectOrderVO() {
		super();
	}

	public InspectOrderVO(InspectPlan plan, Long equipmentId, Long executeDept, Long executeUser) {
		super();
		this.setTenantId(plan.getTenantId());
		this.setPlanId(plan.getId());
		this.setPlanInfo(JSONUtil.toJsonStr(plan));
		this.setEquipmentId(equipmentId);
		// this.setStartTime(DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME));
		// this.setEndTime(DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME));
		this.setExecuteDept(executeDept);
		this.setExecuteUser(executeUser);
		this.setNo(BizCodeUtil.generate("IO"));
		this.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		this.setCreateTime(DateUtil.now());
	}

}
