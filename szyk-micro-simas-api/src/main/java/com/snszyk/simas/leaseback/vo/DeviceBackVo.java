/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 设备租赁归还实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceBackVo对象", description = "设备租赁归还")
public class DeviceBackVo extends BaseCrudVo {

	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	/**
	 * 实际租赁日期
	 */
	@ApiModelProperty(value = "实际租赁日期start")
	private LocalDate actualStartLeaseDate;

	@ApiModelProperty(value = "实际租赁日期end")
	private LocalDate actualEndLeaseDate;
	/**
	 * 归还人
	 */
	@ApiModelProperty(value = "归还人")
	private Long backPersonId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;


}
