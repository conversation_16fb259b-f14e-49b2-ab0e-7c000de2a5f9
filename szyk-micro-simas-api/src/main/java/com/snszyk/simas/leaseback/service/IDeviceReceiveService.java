/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.receive.dto.DeviceReceiveDto;
import com.snszyk.simas.receive.dto.DeviceReceiveRecordDto;
import com.snszyk.simas.receive.entity.DeviceReceive;
import com.snszyk.simas.receive.vo.DeviceReceivePageVo;

/**
 * 设备领用 服务类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface IDeviceReceiveService extends BaseService<DeviceReceive> {

	/**
	 * 名称校验
	 */
	void checkName(Long id, String name);

	/**
	 * 分页查询
	 */
	IPage<DeviceReceiveDto> pageList(DeviceReceivePageVo v);

	/**
	 * 详情
	 */
	DeviceReceiveDto detail(Long id);

	IPage<DeviceReceiveRecordDto> recordPage(DeviceReceivePageVo v);
}
