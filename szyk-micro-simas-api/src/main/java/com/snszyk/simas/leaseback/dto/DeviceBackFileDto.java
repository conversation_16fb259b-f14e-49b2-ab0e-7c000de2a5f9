/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备归还资料实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceBackFileDto对象", description = "设备归还资料")
public class DeviceBackFileDto extends BaseCrudDto {

	/**
	 * 归还id
	 */
	@ApiModelProperty(value = "归还id")
	private Long backId;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;


	@ApiModelProperty(value = "创建人")
	private String createUserName;

	@ApiModelProperty(value = "更新人")
	private String updateUserName;
}
