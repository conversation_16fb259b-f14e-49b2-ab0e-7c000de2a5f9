/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备租赁归还实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@ApiModel(value = "DeviceBackVo对象", description = "设备租赁归还")
public class DeviceBackAVo {


	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	@ApiModelProperty(value = "实际租赁日期start")
	private LocalDate actualStartLeaseDate;

	@ApiModelProperty(value = "实际租赁日期end")
	private LocalDate actualEndLeaseDate;
	/**
	 * 归还人
	 */
	@ApiModelProperty(value = "归还人")
	private Long backPersonId;

	/**
	 * 归还资料
	 */
	@ApiModelProperty(value = "归还资料")
	private List<Long> fileIds=new ArrayList<>();


}
