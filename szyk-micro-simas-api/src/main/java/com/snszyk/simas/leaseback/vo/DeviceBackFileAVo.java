/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备归还资料实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@ApiModel(value = "DeviceBackFileVo对象", description = "设备归还资料")
public class DeviceBackFileAVo {

	private Long id;

	/**
	 * 归还id
	 */
	@ApiModelProperty(value = "归还id")
	private Long backId;
	/**
	 * 附件id
	 */
	@ApiModelProperty(value = "附件id")
	private Long attachId;


}
