/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备归还资料 实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@TableName("simas_device_back_file")
@EqualsAndHashCode(callSuper = true)
public class DeviceBackFile extends TenantEntity {

	/**
	 * 归还id
	 */
	private Long backId;
	/**
	 * 附件id
	 */
	private Long attachId;


}
