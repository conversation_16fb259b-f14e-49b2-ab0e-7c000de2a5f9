/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 设备租赁归还 实体类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
@TableName("simas_device_back")
@EqualsAndHashCode(callSuper = true)
public class DeviceBack extends TenantEntity {

	/**
	 * 设备id
	 */
	private Long deviceId;
	@ApiModelProperty(value = "实际租赁日期start")
	private LocalDate actualStartLeaseDate;

	@ApiModelProperty(value = "实际租赁日期end")
	private LocalDate actualEndLeaseDate;
	/**
	 * 归还人
	 */
	private Long backPersonId;
	/**
	 * 备注
	 */
	private String remark;


}
