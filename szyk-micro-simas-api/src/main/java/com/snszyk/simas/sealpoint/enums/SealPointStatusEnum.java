package com.snszyk.simas.sealpoint.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: SealPointStatusEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/15 18:21
 */
@AllArgsConstructor
@Getter
public enum SealPointStatusEnum {
	/**
	 * 正常
	 */
	NORMAL(1, "正常"),
	/**
	 * 泄漏
	 */
	LEAK(0, "泄漏");

	private final Integer code;
	private final String message;

	public static String getMessage(Integer code) {
		for (SealPointStatusEnum item : SealPointStatusEnum.values()) {
			if (item.getCode().equals(code)) {
				return item.getMessage();
			}
		}
		return null;
	}
}
