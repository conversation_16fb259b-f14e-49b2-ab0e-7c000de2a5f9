package com.snszyk.simas.sealpoint.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: SealPointStatusEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/15 18:21
 */
@AllArgsConstructor
@Getter
public enum SealPointTypeEnum {
	/**
	 * 动密封点
	 */
	DYNAMIC(1, "动密封点"),
	/**
	 * 静密封点
	 */
	STATIC(2, "静密封点"),
	/**
	 * 其他
	 */
	OTHER(3, "其他");
	private final Integer code;
	private final String message;

	public static String getMessage(Integer code) {
		for (SealPointTypeEnum item : SealPointTypeEnum.values()) {
			if (item.getCode().equals(code)) {
				return item.getMessage();
			}
		}
		return null;
	}
}
