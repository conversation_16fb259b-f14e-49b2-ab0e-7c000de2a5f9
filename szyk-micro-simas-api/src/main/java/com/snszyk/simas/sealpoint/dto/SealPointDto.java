/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateDateConverter;
import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 密封点实体类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SealPointDto对象", description = "密封点")
public class SealPointDto extends BaseCrudDto {

	/**
	 * 密封点类型
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "密封点类型")
	private Integer type;
	/**
	 * 密封点名称
	 */
	@ExcelProperty(value = "密封点名称")
	@ApiModelProperty(value = "密封点名称")
	private String name;
	/**
	 * 密封点位置
	 */
	@ExcelProperty(value = "密封点位置")
	@ApiModelProperty(value = "密封点位置")
	private String location;
	/**
	 * 密封点类型名称
	 */
	@ExcelProperty(value = "密封点类型")
	@ApiModelProperty(value = "密封点类型名称")
	private String typeName;
	/**
	 * 密封点状态名称
	 */
	@ExcelProperty(value = "密封点状态")
	@ApiModelProperty(value = "密封点状态名称")
	private String statusName;
	/**
	 * 更新人姓名
	 */
	@ExcelProperty(value = "更新人姓名")
	@ApiModelProperty(value = "更新人姓名")
	private String updateUserName;
	/**
	 * 更新时间
	 */
	@ExcelProperty(value = "更新时间", converter = DateDateConverter.class)
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

}
