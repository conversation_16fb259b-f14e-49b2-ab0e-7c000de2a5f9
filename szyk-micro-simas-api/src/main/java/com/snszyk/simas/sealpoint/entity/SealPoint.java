/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 密封点 实体类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@TableName("simas_seal_point")
@EqualsAndHashCode(callSuper = true)
public class SealPoint extends TenantEntity {

	/**
	 * 密封点类型
	 */
	private Integer type;
	/**
	 * 密封点名称
	 */
	private String name;
	/**
	 * 密封点位置
	 */
	private String location;

	public SealPointStatusRecord convertToSealPointStatusRecord(LocalDate recordDate) {
		SealPointStatusRecord statusRecord = new SealPointStatusRecord();
		statusRecord.setTenantId(this.getTenantId());
		statusRecord.setPointId(this.getId());
		statusRecord.setStatus(this.getStatus());
		statusRecord.setRecordDate(recordDate);
		return statusRecord;
	}
}
