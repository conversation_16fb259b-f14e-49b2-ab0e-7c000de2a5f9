/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 密封点实体类
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Data
@TableName("simas_seal_point_status_record")
@EqualsAndHashCode(callSuper = true)
public class SealPointStatusRecord extends TenantEntity {

	/**
	 * 密封点id
	 */
	private Long pointId;
	/**
	 * 记录日期
	 */
	private LocalDate recordDate;
	/**
	 * 周期
	 */
	@TableField(exist = false)
	private String cycle;


}
