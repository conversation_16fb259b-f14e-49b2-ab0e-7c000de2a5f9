/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 密封点实体类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@ApiModel(value = "SealPointVo对象", description = "密封点")
public class SealPointVo {
	/**
	 * id
	 */
	@ApiModelProperty(value = "修改时必传")
	private Long id;
	/**
	 * 密封点类型
	 */
	@NotNull
	@ApiModelProperty(value = "密封点类型,1-动密封点；2-静密封点；3-其他", required = true)
	private Integer type;
	/**
	 * 密封点名称
	 */
	@NotBlank
	@ApiModelProperty(value = "密封点名称", required = true)
	private String name;
	/**
	 * 密封点位置
	 */
	@NotBlank
	@ApiModelProperty(value = "密封点位置", required = true)
	private String location;


}
