/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 密封点实体类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@ApiModel(value = "SealPointBatchUpdateVo", description = "SealPointBatchUpdateVo")
public class SealPointBatchUpdateVo {
	/**
	 * id
	 */
	@NotNull
	@ApiModelProperty(value = "id", required = true)
	private Long id;

	@NotNull
	@ApiModelProperty(value = "是否正常：1-正常，0-泄漏", required = true)
	private Integer status;
}
