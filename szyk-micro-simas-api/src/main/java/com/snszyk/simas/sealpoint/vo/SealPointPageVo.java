/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.sealpoint.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 密封点实体类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SealPointPageVo对象", description = "密封点")
public class SealPointPageVo extends Page {
	/**
	 * 密封点类型
	 */

	@ApiModelProperty(value = "密封点类型")
	private Integer type;
	/**
	 * 密封点名称
	 */
	@ApiModelProperty(value = "密封点名称")
	private String name;


}
