/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备总数统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@ApiModel(value = "设备总数统计DTO", description = "设备总数统计数据传输对象")
public class EquipmentTotalStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备总数
     */
    @ApiModelProperty(value = "设备总数")
    private Integer totalCount;

    /**
     * 在用数量
     */
    @ApiModelProperty(value = "在用数量")
    private Integer inUseCount;

    /**
     * 备用数量
     */
    @ApiModelProperty(value = "备用数量")
    private Integer idleCount;

    /**
     * 维修数量
     */
    @ApiModelProperty(value = "维修数量")
    private Integer inRepairCount;

    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private Integer scrappedCount;

}
