/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 超期工单统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@ApiModel(value = "超期工单统计DTO", description = "已超期工单展示数据传输对象")
public class OverdueOrderStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String orderNo;

    /**
     * 工单类型
     */
    @ApiModelProperty(value = "工单类型")
    private String orderType;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "设备ID")
    private Long equipmentId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /**
     * 截止时间
     */
    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
    @ApiModelProperty(value = "截止时间")
    private Date endTime;

    /**
     * 负责部门ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "负责部门ID")
    private Long responsibleDeptId;

    /**
     * 负责部门名称
     */
    @ApiModelProperty(value = "负责部门名称")
    private String responsibleDeptName;

    /**
     * 负责人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "负责人ID")
    private Long responsibleUserId;

    /**
     * 负责人姓名
     */
    @ApiModelProperty(value = "负责人姓名")
    private String responsibleUserName;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态")
    private Integer status;

    /**
     * 工单状态名称
     */
    @ApiModelProperty(value = "工单状态名称")
    private String statusName;

    /**
     * 超期天数
     */
    @ApiModelProperty(value = "超期天数")
    private Integer overdueDays;

}
