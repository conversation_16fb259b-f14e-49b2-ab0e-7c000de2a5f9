/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.dashboard.dto.FaultDefectStatisticsDto;

import java.util.List;

/**
 * 故障大屏服务接口
 * 负责设备故障统计和维修时长分析
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IFaultDashboardService {

    /**
     * 查询近30天故障缺陷列表（分页）
     * ServiceImpl层只负责数据查询，不包含业务逻辑判断
     * 使用数据库层面的查询，查询近30天内的故障缺陷记录
     * MyBatis Plus分页插件会自动处理分页和总数统计
     *
     * @param page 分页参数
     * @return 故障缺陷分页列表
     */
    IPage<FaultDefectStatisticsDto> getLast30DaysFaultDefectStatistics(IPage<FaultDefectStatisticsDto> page);

    /**
     * 统计去年同期故障缺陷数量
     * ServiceImpl层只负责数据查询，不包含业务逻辑判断
     * 使用数据库层面的聚合查询，统计去年同一30天时间段的故障缺陷总数
     *
     * @return 去年同期故障缺陷总数
     */
    Integer getLastYearSamePeriodFaultDefectCount();

}
