/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 维修耗时统计DTO（按部位分组）
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@ApiModel(value = "维修耗时统计DTO", description = "近一年维修耗时统计数据传输对象（按部位分组）")
public class RepairDurationStatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 部位ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位ID")
	private Long monitorId;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 该部位总耗时（小时）
	 */
	@ApiModelProperty(value = "该部位总耗时（小时）")
	private BigDecimal totalTimeTake;

	/**
	 * 该部位维修工单数量
	 */
	@ApiModelProperty(value = "该部位维修工单数量")
	private Integer repairCount;

}
