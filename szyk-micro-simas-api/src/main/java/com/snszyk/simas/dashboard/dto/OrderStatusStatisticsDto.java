/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.snszyk.simas.common.enums.OrderStatusEnum;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 工单状态统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@ApiModel(value = "工单状态统计DTO", description = "工单状态统计数据传输对象")
public class OrderStatusStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单状态列表
     */
    @ApiModelProperty(value = "工单状态列表")
    private List<OrderStatusItem> statusList;

    /**
     * 工单总数量
     */
    @ApiModelProperty(value = "工单总数量")
    private Integer totalCount;

    /**
     * 创建空的工单状态统计对象
     * 包含所有工单状态，数量均为0
     *
     * @return 包含所有状态的空统计对象
     */
    public static OrderStatusStatisticsDto createEmpty() {
        OrderStatusStatisticsDto result = new OrderStatusStatisticsDto();

        // 创建包含所有工单状态的列表，数量均为0
        List<OrderStatusItem> statusList = new ArrayList<>();
        for (OrderStatusEnum statusEnum : OrderStatusEnum.values()) {
            OrderStatusItem item = new OrderStatusItem();
            item.setStatusCode(statusEnum.getCode());
            item.setStatusName(statusEnum.getName());
            item.setCount(0);
            statusList.add(item);
        }

        result.setStatusList(statusList);
        result.setTotalCount(0);
        return result;
    }

    /**
     * 工单状态项
     */
    @Data
    @ApiModel(value = "工单状态项", description = "工单状态统计项")
    public static class OrderStatusItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 状态编码
         */
        @ApiModelProperty(value = "状态编码")
        private Integer statusCode;

        /**
         * 状态名称
         */
        @ApiModelProperty(value = "状态名称")
        private String statusName;

        /**
         * 状态数量
         */
        @ApiModelProperty(value = "状态数量")
        private Integer count;
    }
}
