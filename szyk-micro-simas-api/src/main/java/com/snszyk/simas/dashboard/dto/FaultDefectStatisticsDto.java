/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 故障缺陷统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "故障缺陷统计DTO", description = "近30天故障缺陷统计数据传输对象")
public class FaultDefectStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 故障缺陷ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "故障缺陷ID")
    private Long faultDefectId;

    /**
     * 故障缺陷编号
     */
    @ApiModelProperty(value = "故障缺陷编号")
    private String faultDefectNo;

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "设备ID")
    private Long equipmentId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /**
     * 故障部位
     */
    @ApiModelProperty(value = "故障部位")
    private String monitorName;

    /**
     * 故障状态
     */
    @ApiModelProperty(value = "故障状态")
    private Integer status;

    /**
     * 故障状态名称
     */
    @ApiModelProperty(value = "故障状态名称")
    private String statusName;

    /**
     * 处理时间
     */
    @DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
    @JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
    @ApiModelProperty(value = "处理时间")
    private Date operateTime;

}
