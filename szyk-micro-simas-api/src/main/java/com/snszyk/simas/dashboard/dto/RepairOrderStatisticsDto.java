/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 维修工单统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@ApiModel(value = "维修工单统计DTO", description = "近30天维修工单统计数据传输对象")
public class RepairOrderStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 维修工单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "维修工单ID")
    private Long repairId;

    /**
     * 维修工单编号
     */
    @ApiModelProperty(value = "维修工单编号")
    private String repairNo;

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "设备ID")
    private Long equipmentId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String equipmentName;

    /**
     * 故障部位
     */
    @ApiModelProperty(value = "故障部位")
    private String monitorName;

    /**
     * 故障状态
     */
    @ApiModelProperty(value = "故障状态")
    private Integer faultStatus;

    /**
     * 故障状态名称
     */
    @ApiModelProperty(value = "故障状态名称")
    private String faultStatusName;

    /**
     * 维修方式
     */
    @ApiModelProperty(value = "维修方式（INTERNAL：内部维修，EXTERNAL：外委维修）")
    private String bizType;

    /**
     * 维修方式名称
     */
    @ApiModelProperty(value = "维修方式名称")
    private String bizTypeName;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态")
    private Integer status;

    /**
     * 工单状态名称
     */
    @ApiModelProperty(value = "工单状态名称")
    private String statusName;


}
