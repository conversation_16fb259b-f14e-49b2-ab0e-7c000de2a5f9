/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 备品备件出库简单查询DTO
 * 用于简化查询，在业务层进行统计计算
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "备品备件出库简单查询DTO", description = "备品备件出库基础数据")
public class SparePartsOutboundSimpleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "出库单ID")
    private Long outboundOrderId;

    /**
     * 出库类型
     */
    @ApiModelProperty(value = "出库类型")
    private String outboundType;

    /**
     * 请领单ID（仅请领出库时有值）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "请领单ID")
    private Long issuanceOrderId;

    /**
     * 库存ID（仅其他出库时有值）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "库存ID")
    private Long stockId;

    /**
     * 备品备件字典ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "备品备件字典ID")
    private Long dictId;

    /**
     * 出库数量
     */
    @ApiModelProperty(value = "出库数量")
    private BigDecimal outboundQuantity;

}
