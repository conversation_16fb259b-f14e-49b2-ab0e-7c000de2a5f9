/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 故障缺陷统计结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "故障缺陷统计结果DTO", description = "故障缺陷统计结果包含分页数据和同比统计")
public class FaultDefectStatisticsResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分页数据
     */
    @ApiModelProperty(value = "分页数据")
    private IPage<FaultDefectStatisticsDto> pageData;

    /**
     * 同比增长率（小数形式）
     */
    @ApiModelProperty(value = "同比增长率（小数形式），如0.1525表示15.25%，正数表示增长，负数表示下降")
    private BigDecimal yearOverYearGrowthRate;

}
