/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单设备统计DTO
 * 通用的工单设备统计数据传输对象，适用于各类工单（点巡检、保养等）
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "工单设备统计DTO", description = "按部门统计工单设备完成情况")
public class OrderDeviceStatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "部门id")
	private Long deptId;

	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptName;

	/**
	 * 设备总量
	 */
	@ApiModelProperty(value = "设备总量")
	private Integer totalDeviceCount;

	/**
	 * 完成量
	 */
	@ApiModelProperty(value = "完成量")
	private Integer completedDeviceCount;

	/**
	 * 完成率（小数形式，0.0-1.0范围）
	 */
	@ApiModelProperty(value = "完成率（小数形式，0.0-1.0范围）")
	private BigDecimal completionRate;

}
