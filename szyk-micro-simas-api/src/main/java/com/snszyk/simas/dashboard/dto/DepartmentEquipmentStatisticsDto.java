/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 部门设备统计DTO
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
@ApiModel(value = "部门设备统计DTO", description = "按部门统计设备分布情况")
public class DepartmentEquipmentStatisticsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 部门路径
     */
    @ApiModelProperty(value = "部门路径")
    private String deptPath;

    /**
     * 设备总数
     */
    @ApiModelProperty(value = "设备总数")
    private Integer totalCount;

    /**
     * 在用数量
     */
    @ApiModelProperty(value = "在用数量")
    private Integer inUseCount;

    /**
     * 备用数量
     */
    @ApiModelProperty(value = "备用数量")
    private Integer idleCount;

    /**
     * 维修数量
     */
    @ApiModelProperty(value = "维修数量")
    private Integer inRepairCount;

    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private Integer scrappedCount;

}
