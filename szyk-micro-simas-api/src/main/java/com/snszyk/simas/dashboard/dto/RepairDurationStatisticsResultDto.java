/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 维修耗时统计结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Data
@ApiModel(value = "维修耗时统计结果DTO", description = "维修耗时统计结果包含分页数据和平均耗时")
public class RepairDurationStatisticsResultDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分页数据
     */
    @ApiModelProperty(value = "分页数据")
    private IPage<RepairDurationStatisticsDto> pageData;

    /**
     * 平均耗时（小时）
     */
    @ApiModelProperty(value = "平均耗时（小时）")
    private BigDecimal averageDuration;

}
