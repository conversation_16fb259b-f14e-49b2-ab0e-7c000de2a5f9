/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工单简单查询DTO
 * 用于简化查询，在业务层进行统计计算
 * 适用于各类工单（点巡检、保养等）
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "工单简单查询DTO", description = "工单基础数据")
public class InspectOrderSimpleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "工单ID")
    private Long id;

    /**
     * 设备ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "设备ID")
    private Long equipmentId;

    /**
     * 工单状态
     */
    @ApiModelProperty(value = "工单状态")
    private Integer status;

    /**
     * 部门ID（工单执行部门）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

}
