/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service;

import com.snszyk.simas.dashboard.dto.DepartmentEquipmentStatisticsDto;
import com.snszyk.simas.dashboard.dto.EquipmentTotalStatisticsDto;

import java.util.List;

/**
 * 设备大屏展示服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IEquipmentDashboardService {

    /**
     * 统计设备总数概览
     * ServiceImpl层只负责数据查询，不包含业务逻辑判断
     * 使用数据库层面的聚合查询，提高查询性能
     *
     * @return 设备总数统计信息
     */
    EquipmentTotalStatisticsDto getEquipmentTotalStatistics();

    /**
     * 按部门统计设备分布情况
     * ServiceImpl层只负责数据查询，不包含业务逻辑判断
     * 使用数据库层面的聚合查询，提高查询性能
     *
     * @return 部门设备统计列表
     */
    List<DepartmentEquipmentStatisticsDto> getDepartmentEquipmentStatistics();

}
