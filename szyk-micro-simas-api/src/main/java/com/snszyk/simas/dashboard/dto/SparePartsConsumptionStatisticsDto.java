/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.common.annotation.DynamicPrecision;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 备品备件消耗统计DTO
 * 用于统计备品备件的出库消耗情况
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Data
@ApiModel(value = "备品备件消耗统计DTO", description = "备品备件消耗统计数据传输对象")
public class SparePartsConsumptionStatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 备品备件字典ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "备品备件字典ID")
	private Long sparePartsId;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String model;

	/**
	 * 计量单位ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计量单位ID")
	private Long measureUnitId;

	/**
	 * 计量单位名称
	 */
	@ApiModelProperty(value = "计量单位名称")
	private String measureUnitName;

	/**
	 * 默认库房ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "默认库房ID")
	private Long defaultWarehouseId;

	/**
	 * 默认库房名称
	 */
	@ApiModelProperty(value = "默认库房名称")
	private String defaultWarehouseName;

	/**
	 * 启用状态
	 */
	@ApiModelProperty(value = "启用状态")
	private Integer status;

	/**
	 * 启用状态名称
	 */
	@ApiModelProperty(value = "启用状态名称")
	private String statusName;

	/**
	 * 安全库存数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "安全库存数量")
	private BigDecimal safeStockAmount;

	/**
	 * 消耗数量
	 */
	@DynamicPrecision(precisionField = "measureUnitPrecision")
	@ApiModelProperty(value = "消耗数量")
	private BigDecimal consumptionAmount;

	/**
	 * 计量单位精度
	 */
	@ApiModelProperty(value = "计量单位精度")
	private Integer measureUnitPrecision;

}
