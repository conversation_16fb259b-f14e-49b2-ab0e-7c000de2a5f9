/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service;

import com.snszyk.simas.dashboard.dto.SparePartsOutboundSimpleDto;

import java.util.List;

/**
 * 备品备件大屏服务接口
 * 负责备品备件消耗分析和排名统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface ISparePartsDashboardService {

    /**
     * 查询备品备件出库基础数据
     * ServiceImpl层只负责数据查询，不包含业务逻辑判断
     * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
     *
     * @return 备品备件出库基础数据列表
     */
    List<SparePartsOutboundSimpleDto> getSparePartsOutboundSimpleData();

}
