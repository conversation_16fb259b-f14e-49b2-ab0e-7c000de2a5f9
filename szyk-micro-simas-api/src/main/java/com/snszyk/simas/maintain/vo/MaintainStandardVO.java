/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.maintain.entity.MaintainStandard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备保养标准表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainStandardVO对象", description = "设备保养标准表")
public class MaintainStandardVO extends MaintainStandard {
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部门id")
	private Long useDept;

	/**
	 * 设备id列表
	 */
	@ApiModelProperty(value = "设备id列表")
	private List<Long> equipmentIdList;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 是否需要确认
	 */
	@ApiModelProperty(value = "是否需要确认")
	private String needConfirmName;

	/**
	 * 部位类型
	 */
	@ApiModelProperty(value = "部位类型")
	private Integer monitorType;

	/**
	 * 部位类型名称
	 */
	@ApiModelProperty(value = "部位类型名称")
	private String monitorTypeName;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;


}
