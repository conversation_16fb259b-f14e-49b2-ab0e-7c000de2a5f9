/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备保养工单表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MaintainOrderDTO extends MaintainOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单名称
	 */
	@ApiModelProperty(value = "工单名称")
	private String orderName;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentSn;

	/**
	 * 设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型")
	private Long equipmentCategory;

	/**
	 * 设备类型名称
	 */
	@ApiModelProperty(value = "设备类型名称")
	private String equipmentCategoryName;

	/**
	 * 周期类型（字典：plan_cycle）
	 */
	@ApiModelProperty(value = "周期类型（字典：plan_cycle）")
	private String cycleTypeName;

	/**
	 * 执行部门
	 */
	@ApiModelProperty(value = "执行部门")
	private String executeDeptName;

	/**
	 * 执行人
	 */
	@ApiModelProperty(value = "执行人")
	private String executeUserName;

	/**
	 *
	 */
	@ApiModelProperty(value = "审批人")
	private String approvalUserName;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;


	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startTimeStr;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endTimeStr;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 保养结果
	 */
	@ApiModelProperty(value = "保养结果")
	private String maintainResult;

	/**
	 * 设备台账信息
	 */
	@ApiModelProperty(value = "设备台账信息")
	private DeviceAccountVO equipmentAccount;

	/**
	 * 保养计划
	 */
	@ApiModelProperty(value = "保养计划")
	private MaintainPlanDTO maintainPlan;

	/**
	 * 保养标准列表
	 */
	@ApiModelProperty(value = "保养标准列表")
	List<MaintainStandardDTO> standardList;

	/**
	 * 保养耗材列表
	 */
	@ApiModelProperty(value = "保养耗材列表")
	private List<ComponentMaterialVO> materialList;

	@ApiModelProperty(value = "统计的部门id")
	private Long analysisDeptId;


}
