/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.vo;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.maintain.entity.MaintainRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备保养记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainRecordVO对象", description = "设备保养记录表")
public class MaintainRecordVO extends MaintainRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 保养工单号
	 */
	@ApiModelProperty(value = "保养工单号")
	private String orderNo;

	/**
	 * 保养人
	 */
	@ApiModelProperty(value = "保养人")
	private String maintainUserName;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

	/**
	 * 异常等级
	 */
	@ApiModelProperty(value = "异常等级")
	private String abnormalLevelName;

	/**
	 * 是否现场处理
	 */
	@ApiModelProperty(value = "是否现场处理")
	private String isHandledName;

	/**
	 * 异常图片列表
	 */
	@ApiModelProperty(value = "异常图片列表")
	private List<Attach> abnormalImageList;

}
