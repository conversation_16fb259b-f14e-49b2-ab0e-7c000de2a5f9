/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.vo;

import cn.hutool.json.JSONUtil;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.enums.OrderStatusEnum;
import com.snszyk.simas.maintain.entity.MaintainOrder;
import com.snszyk.simas.maintain.entity.MaintainPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 设备保养工单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainOrderVO对象", description = "设备保养工单表")
public class MaintainOrderVO extends MaintainOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件-关键字（工单单号、设备编号、设备名称）
	 */
	@ApiModelProperty(value = "查询条件-关键字（工单单号、设备编号、设备名称）")
	private String keywords;

	/**
	 * 查询条件-工单名称
	 */
	@ApiModelProperty(value = "查询条件-工单名称")
	private String orderName;

	/**
	 * 查询条件-开始日期
	 */
	@ApiModelProperty(value = "查询条件-开始日期")
	private String startDate;

	/**
	 * 查询条件-结束日期
	 */
	@ApiModelProperty(value = "查询条件-结束日期")
	private String endDate;

	/**
	 * 超时时间间隔
	 */
	@ApiModelProperty(value = "超时时间间隔")
	private BigDecimal timeInterval;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;

	/**
	 * 设备保养结果列表
	 */
	@ApiModelProperty(value = "设备保养结果列表")
	private List<MaintainRecordVO> maintainRecordList;

	/**
	 * 工单id列表
	 */
	@ApiModelProperty(value = "工单id列表")
	private List<Long> orderIds;

	@ApiModelProperty(value = "数据权限角色 0 全部 1 操作人 2 其他")
	private Integer queryAuthRole;

	@ApiModelProperty(value = "工单状态列表")
	private List<Integer> statusList;

	@ApiModelProperty(value = "仅查看执行人")
	private Long onlyQueryExecuteUser;

	@ApiModelProperty(value = "不等于状态")
	private Integer neStatus;

	public MaintainOrderVO() {
		super();
	}

	public MaintainOrderVO(MaintainPlan plan, Long equipmentId, Long executeDept, Long executeUser) {
		super();
		this.setTenantId(plan.getTenantId());
		this.setPlanId(plan.getId());
		this.setPlanInfo(JSONUtil.toJsonStr(plan));
		this.setEquipmentId(equipmentId);
		this.setExecuteDept(executeDept);
		this.setExecuteUser(executeUser);
		this.setNo(BizCodeUtil.generate("MO"));
		this.setStatus(OrderStatusEnum.IN_PROCESS.getCode());
		this.setCreateTime(DateUtil.now());
	}

	public MaintainOrderVO(Long id) {
		super();
		this.setId(id);
	}

}
