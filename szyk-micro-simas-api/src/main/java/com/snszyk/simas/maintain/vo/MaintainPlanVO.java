/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.vo;

import com.snszyk.simas.maintain.entity.MaintainPlan;
import com.snszyk.simas.common.vo.ByDaySetVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备保养计划表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainPlanVO对象", description = "设备保养计划表")
public class MaintainPlanVO extends MaintainPlan {
	private static final long serialVersionUID = 1L;

	/**
	 * 查询条件：计划名称或编号
	 */
	@ApiModelProperty(value = "查询条件：计划名称或编号")
	private String keywords;

	/**
	 * 查询-开始日期
	 */
	@ApiModelProperty(value = "查询-开始日期")
	private String queryStartDate;

	/**
	 * 查询-结束日期
	 */
	@ApiModelProperty(value = "查询-结束日期")
	private String queryEndDate;

	/**
	 * 按日-时间设置
	 */
	@ApiModelProperty(value = "按日-时间设置")
	private List<ByDaySetVO> byDaySet;

	/**
	 * 按周-时间设置
	 */
	@ApiModelProperty(value = "按周-时间设置")
	private List<String> byWeekSet;

	/**
	 * 按月-时间设置
	 */
	@ApiModelProperty(value = "按月-时间设置")
	private List<String> byMonthSet;

	/**
	 * 所选设备id列表
	 */
	@ApiModelProperty(value = "所选设备id列表")
	private String equipmentIds;

	/**
	 * 所选标准id列表
	 */
	@ApiModelProperty(value = "所选标准id列表")
	private String standardIds;


}
