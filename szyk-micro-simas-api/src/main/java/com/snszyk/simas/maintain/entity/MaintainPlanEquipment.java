/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

/**
 * 设备保养计划关联表实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@TableName("simas_maintain_plan_equipment")
@ApiModel(value = "MaintainPlanEquipment对象", description = "设备保养计划关联表")
public class MaintainPlanEquipment implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 计划id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计划id")
	private Long planId;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 标准id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "标准id")
	private Long standardId;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 状态（0；未检，1：已检）
	 */
	@ApiModelProperty(value = "状态（0；未检，1：已检）")
	private Integer status;


}
