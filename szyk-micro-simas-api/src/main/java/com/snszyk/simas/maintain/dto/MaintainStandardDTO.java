/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.dto;

import com.snszyk.simas.maintain.entity.MaintainStandard;
import com.snszyk.simas.maintain.vo.MaintainRecordVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备保养标准表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MaintainStandardDTO extends MaintainStandard {
	private static final long serialVersionUID = 1L;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 是否需要确认
	 */
	@ApiModelProperty(value = "是否需要确认")
	private String needConfirmName;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private Integer isAbnormal;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

	/**
	 * 保养结果
	 */
	@ApiModelProperty(value = "保养结果")
	private MaintainRecordVO maintainRecord;

}
