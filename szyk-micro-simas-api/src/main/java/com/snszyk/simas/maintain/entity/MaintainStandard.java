/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备保养标准表实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@Accessors(chain = true)
@TableName("simas_maintain_standard")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainStandard对象", description = "设备保养标准表")
public class MaintainStandard extends TenantEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 标准
	 */
	@ApiModelProperty(value = "标准")
	private String standard;
	/**
	 * 方法
	 */
	@ApiModelProperty(value = "方法")
	private String method;
	/**
	 * 是否需要确认（0否1是）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "是否需要确认（0否1是）")
	private Integer needConfirm;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 设备名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备sn
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "设备sn")
	private String equipmentSn;


}
