/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.maintain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.maintain.entity.MaintainPlanEquipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备保养计划关联表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MaintainPlanEquipmentVO对象", description = "设备保养计划关联表")
public class MaintainPlanEquipmentVO extends MaintainPlanEquipment {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 标准
	 */
	@ApiModelProperty(value = "标准")
	private String standard;

	/**
	 * 方法
	 */
	@ApiModelProperty(value = "方法")
	private String method;

	/**
	 * 是否需要确认（0否1是）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "是否需要确认（0否1是）")
	private Integer needConfirm;

	/**
	 * 是否需要确认
	 */
	@ApiModelProperty(value = "是否需要确认")
	private String needConfirmName;

	public MaintainPlanEquipmentVO() {
		super();
	}

	/**
	 * 构造方法
	 *
	 * @param planId
	 * @param equipmentId
	 * @param monitorId
	 * @param standardId
	 */
	public MaintainPlanEquipmentVO(Long planId, Long equipmentId, Long monitorId, Long standardId) {
		super();
		this.setPlanId(planId);
		this.setEquipmentId(equipmentId);
		this.setMonitorId(monitorId);
		this.setStandardId(standardId);
	}


}
