/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.cache;

import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.simas.feign.ISimasClient;
import com.snszyk.simas.overhaul.entity.OverhaulMethods;

import static com.snszyk.core.cache.constant.CacheConstant.BIZ_CACHE;

/**
 * 设备全生命周期管理缓存工具类
 *
 * <AUTHOR>
 */
public class SimasCache {

	private static final String CATEGORY_ID = "category:id:";
	private static final String LOCATION_ID = "location:id:";
	private static final String MONITOR_ID = "monitor:id:";
	private static final String OVERHAUL_METHODS_ID = "overhaul:methods:id:";

	private static ISimasClient simasClient;

	private static ISimasClient getSimasClient() {
		if (simasClient == null) {
			simasClient = SpringUtil.getBean(ISimasClient.class);
		}
		return simasClient;
	}


	public static OverhaulMethods getOverhaulMethods(Long id) {
		return CacheUtil.get(BIZ_CACHE, OVERHAUL_METHODS_ID, id, () -> {
			R<OverhaulMethods> result = getSimasClient().getOverhaulMethods(id);
			return result.getData();
		});
	}
}
