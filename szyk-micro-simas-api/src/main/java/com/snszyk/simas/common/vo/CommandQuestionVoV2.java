/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 问答
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@ApiModel(value = "问答", description = "问答")
public class CommandQuestionVoV2 {


	/**
	 * 用户问题
	 */
	@NotBlank(message = "用户问题不能为空")
	@ApiModelProperty(value = "用户问题")
	private String question;


}
