package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.vo.BaseCrudVo;
import com.snszyk.simas.common.vo.EquipmentCategoryAttrVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "RsEquipmentClassifyExpandVo", description = "设备台账")
public class EquipmentClassifyExpandVo extends BaseCrudVo {

	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类id")
	private Long parentId;
	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;
	/**
	 * 拓展属性
	 */
	@ApiModelProperty(value = "拓展属性")
	private List<EquipmentCategoryAttrVo> list;

}
