/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备按日点巡检时间设置视图实体类
 *
 * <AUTHOR>
 * @since 2024-06-16
 */
@Data
@ApiModel(value = "ByDaySetVO对象", description = "ByDaySetVO对象")
public class ByDaySetVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间：时")
	private String startTime;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间：时")
	private String endTime;


}
