/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指令库实体类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CommandLibraryDto对象", description = "指令库")
public class CommandLibraryDto extends BaseCrudDto {

	/**
	 * 指令路由
	 */
	@ApiModelProperty(value = "指令路由")
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	@ApiModelProperty(value = "动作（查看、审核等）")
	private String action;
	/**
	 * 页面路由
	 */
	@ApiModelProperty(value = "页面路由")
	private String pageRoute;
	/**
	 * 页面检索元素
	 */
	@ApiModelProperty(value = "页面检索元素")
	private String pageElement;
//	/**
//	 * 大模型提示词的配置项(暂未使用)
//	 */
//	private String promptConfig;
	/**
	 * 页面菜单编码
	 */
	private String pageMenuCode;

}
