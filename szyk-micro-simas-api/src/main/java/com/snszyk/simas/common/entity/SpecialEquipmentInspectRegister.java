package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 特种设备检验登记表实体类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@TableName("simas_special_equipment_inspect_register")
@EqualsAndHashCode(callSuper = true)
public class SpecialEquipmentInspectRegister extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 单号
	 */
	@ApiModelProperty(value = "单号")
	private String no;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 检验类型（字典：inspect_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检验类型（字典：inspect_type）")
	private Integer inspectType;
	/**
	 * 检验机构
	 */
	@ApiModelProperty(value = "检验机构")
	private String inspectOrg;
	/**
	 * 检验结论（字典：inspect_result）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检验结论（字典：inspect_result）")
	private Integer inspectResult;
	/**
	 * 安全管理员
	 */
	@ApiModelProperty(value = "安全管理员")
	private String securityAdmin;
	/**
	 * 检验人员
	 */
	@ApiModelProperty(value = "检验人员")
	private String inspectUser;
	/**
	 * 检验日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "检验日期")
	private Date inspectDate;
	/**
	 * 下次检验日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "下次检验日期")
	private Date nextInspectDate;
	/**
	 * 主要问题
	 */
	@ApiModelProperty(value = "主要问题")
	private String importantIssues;
	/**
	 * 处理措施
	 */
	@ApiModelProperty(value = "处理措施")
	private String measures;
	/**
	 * 上传附件
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传附件")
	private String attachId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

}
