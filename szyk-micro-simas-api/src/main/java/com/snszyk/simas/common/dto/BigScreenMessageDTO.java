/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 大屏的通知
 * 是否特种设备
 * 设备名
 * 工单类型 OrderTypeEnum
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@ApiModel(value = "大屏的通知", description = "大屏的通知")
public class BigScreenMessageDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "是否特种设备")
	private Boolean isSpecialEquipment;

	@ApiModelProperty(value = "设备名")
	private String equipmentName;

	@ApiModelProperty(value = "工单类型 OrderTypeEnum")
	private String orderType;

	@ApiModelProperty(value = "工单号")
	private String orderNo;

	private String  bizType;





}
