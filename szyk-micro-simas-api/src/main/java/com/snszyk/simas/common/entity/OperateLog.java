/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 操作日志表实体类
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@TableName("simas_operate_log")
@ApiModel(value = "OperateLog对象", description = "操作日志表")
public class OperateLog extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 业务id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "业务id")
	private Long bizId;
	/**
	 * 系统模块（字典：system_module）
	 */
	@ApiModelProperty(value = "系统模块（字典：system_module）")
	private String module;
	/**
	 * 操作类型（字典：operate_type）
	 */
	@ApiModelProperty(value = "操作类型（字典：operate_type）")
	private String type;
	/**
	 * 请求方式
	 */
	@ApiModelProperty(value = "请求方式")
	private String request;
	/**
	 * 主机
	 */
	@ApiModelProperty(value = "主机")
	private String host;
	/**
	 * 操作地点（1：内网，2：外网）
	 */
	@ApiModelProperty(value = "操作地点（1：内网，2：外网）")
	private Integer net;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private Integer status;
	/**
	 * 操作人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "操作人")
	private Long operateUser;
	/**
	 * 操作时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "操作时间")
	private Date operateTime;


}
