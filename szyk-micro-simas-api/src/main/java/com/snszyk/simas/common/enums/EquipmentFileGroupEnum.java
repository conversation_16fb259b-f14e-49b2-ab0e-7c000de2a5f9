///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.common.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
//import java.util.Objects;
//
///**
// * 设备资料归类枚举类
// *
// * <AUTHOR>
// * @date 2024/08/13 15:16
// **/
//@Getter
//@AllArgsConstructor
//public enum EquipmentFileGroupEnum {
//
//	/**
//	 * 设备前期资料
//	 */
//	PRE("1", "设备前期资料"),
//	/**
//	 * 注册登记资料
//	 */
//	REGISTER("2", "注册登记资料"),
//	/**
//	 * 设备运维相关资料
//	 */
//	OPERATION("3", "设备运维相关资料"),
//	;
//
//	final String code;
//	final String name;
//
//	public static EquipmentFileGroupEnum getByCode(String code){
//		for (EquipmentFileGroupEnum value : EquipmentFileGroupEnum.values()) {
//			if (Objects.equals(code, value.getCode())){
//				return value;
//			}
//		}
//		return null;
//	}
//
//}
