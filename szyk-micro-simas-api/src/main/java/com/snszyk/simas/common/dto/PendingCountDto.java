package com.snszyk.simas.common.dto;

import com.snszyk.simas.common.enums.ApprovalModuleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName: PendingCountDto
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/10 10:30
 */
@Data
@Accessors(chain = true)
public class PendingCountDto {
	/**
	 * 模块key
	 */
	@ApiModelProperty(value = "模块key")
	private ApprovalModuleEnum module;
	/**
	 * 模块名称
	 */
	@ApiModelProperty(value = "模块名称")
	private String moduleName;
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "数量")
	private Long count;

	public PendingCountDto setModule(ApprovalModuleEnum module) {
		this.module = module;
		setMuleName();
		return this;
	}

	public void setMuleName() {
		if (module != null) {
			this.moduleName = module.getName();
		}
	}

}
