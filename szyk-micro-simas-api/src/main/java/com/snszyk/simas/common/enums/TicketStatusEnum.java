package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单据状态枚举
 * ClassName: ImportantLevelEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/12 20:20
 */
@Getter
@AllArgsConstructor
public enum TicketStatusEnum {
	/**
	 * 生成
	 */
	GENERATED("生成", "产生新的"),
	/**
	 * 关闭
	 */
	CLOSED("关闭", "关闭"),
	/**
	 * 完成
	 */
	COMPLETED("完成", "完成");
	final String name;
	final String messageName;

	public static TicketStatusEnum getByCode(String code) {
		for (TicketStatusEnum ticketStatusEnum : TicketStatusEnum.values()) {
			if (ticketStatusEnum.name.equals(code)) {
				return ticketStatusEnum;
			}
		}
		return null;
	}

}
