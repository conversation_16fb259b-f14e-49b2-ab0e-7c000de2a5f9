/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务日志表实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
@TableName("simas_biz_log")
@ApiModel(value = "BizLog对象", description = "业务日志表")
public class BizLog implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 业务id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "业务id")
	private Long bizId;
	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String bizNo;
	/**
	 * 业务状态
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "业务状态")
	private Integer bizStatus;
	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String module;
	/**
	 * 操作内容
	 */
	@ApiModelProperty(value = "操作内容")
	private String content;
	/**
	 * 业务信息
	 */
	@ApiModelProperty(value = "业务信息")
	private String bizInfo;
	/**
	 * 操作人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "操作人")
	private Long operateUser;
	/**
	 * 操作时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "操作时间")
	private Date operateTime;


}
