package com.snszyk.simas.common.vo;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.entity.SpecialEquipmentInspectRegister;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 特种设备检验登记表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SpecialEquipmentInspectRegisterVO对象", description = "特种设备检验登记表")
public class SpecialEquipmentInspectRegisterVO extends SpecialEquipmentInspectRegister {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 检验类型
	 */
	@ApiModelProperty(value = "检验类型")
	private String inspectTypeName;

	/**
	 * 检验结论
	 */
	@ApiModelProperty(value = "检验结论")
	private String inspectResultName;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;


}
