package com.snszyk.simas.common.enums;

import com.snszyk.common.utils.DateUtils;
import lombok.Getter;

import java.time.LocalDate;

/**
 * ClassName: TimeTypeEnumV2
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/2/12 11:34
 */
@Getter
public enum TimeTypeEnumV2 {
	/**
	 * 近30天
	 */
	THIRTY_DAYS("近30天"),
	/**
	 * 月度
	 */
	MONTH("月度"),
	/**
	 * 季度
	 */
	QUARTER("季度"),
	/**
	 * 年度
	 */
	YEAR("年度");

	private String desc;

	TimeTypeEnumV2(String desc) {
		this.desc = desc;
	}


	/**
	 * 计算枚举开始日期
	 */
	public LocalDate computeStartDate() {
		LocalDate now = LocalDate.now();
		switch (this) {
			case THIRTY_DAYS:
				return DateUtils.calculateDate29DaysAgo(now);
			case MONTH:
				return DateUtils.calculateFirstDayOf11MonthsAgo(now);
			case QUARTER:
				return DateUtils.calculatePreviousThreeQuartersStartDate(now);
			case YEAR:
				return DateUtils.calculateFirstDayOfTwoYearsAgo(now);
			default:
				throw new IllegalArgumentException("不支持的统计类型: " + this);
		}
	}
}
