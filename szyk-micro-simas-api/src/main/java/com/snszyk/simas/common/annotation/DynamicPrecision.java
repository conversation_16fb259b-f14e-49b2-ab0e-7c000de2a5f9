package com.snszyk.simas.common.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.snszyk.simas.common.serializer.DynamicPrecisionSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ClassName: DynamicPrecision
 * Package: com.snszyk.simas.common.annotation
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/29 15:41
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = DynamicPrecisionSerializer.class) // 绑定序列化器
public @interface DynamicPrecision {
	/**
	 * 关联的精度字段名（如 "measureUnitPrecision"）
	 */
	String precisionField();
}
