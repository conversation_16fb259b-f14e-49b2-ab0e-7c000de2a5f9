package com.snszyk.simas.common.vo;

import com.snszyk.simas.common.entity.EquipmentInventory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventoryVO对象", description = "设备盘点记录表")
public class EquipmentInventoryVO extends EquipmentInventory {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "app标签（0：全部 1：已盘 2：未盘）")
	private Integer tab;
}
