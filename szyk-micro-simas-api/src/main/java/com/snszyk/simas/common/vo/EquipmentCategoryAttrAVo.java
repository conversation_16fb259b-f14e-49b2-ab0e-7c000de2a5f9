package com.snszyk.simas.common.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "EquipmentCategoryAttrVo", description = "设备台账")
public class EquipmentCategoryAttrAVo  {

	@ApiModelProperty(value = "id")
	private Long id;

	/**
	 * 分类id
	 */
	@ApiModelProperty(value = "分类id")
	private Long categoryId;
	/**
	 * 字段名称
	 */
	@ApiModelProperty(value = "字段名称")
	private String attrName;
	/**
	 * 字段类型
	 */
	@ApiModelProperty(value = "字段类型")
	private String attrType;
	/**
	 * 是否必填，0否1是
	 */
	@ApiModelProperty(value = "是否必填，0否1是")
	private Integer isRequire;
	/**
	 * 待选值数据源
	 */
	@ApiModelProperty(value = "待选值数据源")
	private String selectData;
	/**
	 * 精度
	 */
	@ApiModelProperty(value = "精度")
	private Integer precision;
	/**
	 * 日期类型
	 */
	@ApiModelProperty(value = "日期类型")
	private String dataType;
	/**
	 * 字段长度
	 */
	@ApiModelProperty(value = "字段长度")
	private Integer fieldLength;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
