package com.snszyk.simas.common.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * ClassName: SpecialEquipmentUsageLogVo
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 17:05
 */
@Data
public class SpecialEquipmentUsageLogVo{
	/**
	 * 设备id
	 */
	@NotNull
	@ApiModelProperty(value = "设备id", required = true)
	private Long equipmentId;
	/**
	 * 使用人id
	 */
	@NotNull
	@ApiModelProperty(value = "使用人id", required = true)
	private Long operatorId;
	/**
	 * 使用人姓名
	 */
	@NotBlank
	@ApiModelProperty(value = "使用人姓名", required = true)
	private String operatorName;
	/**
	 * 开始日期
	 */
	@NotNull
	@ApiModelProperty(value = "开始日期,yyyy-MM-dd", required = true)
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@NotNull
	@ApiModelProperty(value = "结束日期,yyyy-MM-dd", required = true)
	private LocalDate endDate;
	/**
	 * 使用前状态
	 */
	@NotNull
	@ApiModelProperty(value = "使用前状态", required = true)
	private String preUseStatus;
	/**
	 * 使用后状态
	 */
	@NotNull
	@ApiModelProperty(value = "使用后状态", required = true)
	private String postUseStatus;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
}
