/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举类
 *
 * <AUTHOR>
 * @date 2024/08/14 10:18
 **/
@Getter
@AllArgsConstructor
public enum BizTypeEnum {

	/**
	 * 点巡检
	 */
	INSPECT("INSPECT", "点巡检"),

	/**
	 * 保养
	 */
	MAINTAIN("MAINTAIN", "保养"),

	/**
	 * 人工上报
	 */
	MANUAL("MANUAL", "人工上报"),

	/**
	 * 智能诊断
	 */
	INTELLIGENT("INTELLIGENT", "智能诊断"),

	/**
	 * 维修
	 */
	REPAIR("REPAIR", "维修"),

	/**
	 * 润滑
	 */
	LUBRICATE("LUBRICATE", "润滑"),

	/**
	 * 检修
	 */
	OVERHAUL("OVERHAUL", "检修"),

	;

	final String code;
	final String name;

	public static BizTypeEnum getByCode(String code){
		for (BizTypeEnum value : BizTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
