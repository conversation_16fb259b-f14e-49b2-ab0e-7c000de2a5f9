/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备前期资料表实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@TableName("simas_equipment_pre_file")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentPreFile对象", description = "设备前期资料表")
public class EquipmentPreFile extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备台账id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备台账id")
	private Long equipmentId;

	/**
	 * 设备归类
	 */
	@ApiModelProperty(value = "设备归类")
	private Integer groupType;

	/**
	 * 资料类型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "资料类型id")
	private Long fileCategoryId;
	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "资料名称")
	private String name;

	/**
	 * 文件后缀
	 */
	@ApiModelProperty(value = "文件后缀")
	private String extension;
	/**
	 * 附件id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "附件id")
	private Long attachId;
	/**
	 * 前期资料类型路径
	 */
	@ApiModelProperty(value = "前期资料类型路径")
	private String fileCategoryPath;

}
