package com.snszyk.simas.common.entity;

import com.snszyk.common.utils.StringPool;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: YearQuarter
 * Package: com.snszyk.simas.common
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/2/11 16:21
 */
@Data
@AllArgsConstructor
public class YearQuarter implements Comparable<YearQuarter> {
	private Integer year;
	private Integer quarter;

	private YearQuarter(int year, int quarter) {
		this.year = year;
		this.quarter = quarter;
	}

	public static YearQuarter from(LocalDate localDate) {
		YearMonth yearMonth = YearMonth.from(localDate);
		int year = yearMonth.getYear();
		int quarter = (yearMonth.getMonthValue() - 1) / 3 + 1;
		return new YearQuarter(year, quarter);
	}

	public YearQuarter plusQuarters(int quarters) {
		int newQuarter = this.quarter + quarters;
		int additionalYears = (newQuarter - 1) / 4;
		int newYear = this.year + additionalYears;
		newQuarter = ((newQuarter - 1) % 4) + 1;
		return new YearQuarter(newYear, newQuarter);
	}

	@Override
	public String toString() {
		return year + StringPool.DASH + quarter + "季度";
	}

	@Override
	public int compareTo(YearQuarter other) {
		if (this.year != other.year) {
			return Integer.compare(this.year, other.year);
		}
		return Integer.compare(this.quarter, other.quarter);
	}

	public boolean isAfter(YearQuarter other) {
		return this.compareTo(other) > 0;
	}

	public static List<YearQuarter> getQuartersBetween(YearQuarter start, YearQuarter end) {
		List<YearQuarter> quarters = new ArrayList<>();
		YearQuarter current = start;
		while (!current.equals(end)) {
			quarters.add(current);
			current = current.plusQuarters(1);
		}
		quarters.add(end); // 添加结束季度
		return quarters;
	}

	public static List<YearQuarter> getQuartersBetween(LocalDate startDate, LocalDate endDate) {
		YearQuarter startYearQuarter = YearQuarter.from(startDate);
		YearQuarter endYearQuarter = YearQuarter.from(endDate);
		return YearQuarter.getQuartersBetween(startYearQuarter, endYearQuarter);
	}

	public static void main(String[] args) {

		LocalDate startDate = LocalDate.of(2022, 9, 1);
		LocalDate endDate = LocalDate.of(2023, 4, 30);

		getQuartersBetween(startDate, endDate);
	}
}
