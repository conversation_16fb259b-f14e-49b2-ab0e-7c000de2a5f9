/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 设备文件更新实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@ApiModel(value = "EquipmentFileUpdateVo对象", description = "设备文件更新")
public class EquipmentFileUpdateAVo  {

	@ApiModelProperty(value = "资料变更记录的id")
	private Long id;
	/**
	 * 设备资料id
	 */
	@NotNull
	@ApiModelProperty(value = "设备资料id",required = true)
	private Long equipmentFileId;

	@ApiModelProperty(value = "附件id",required = true)
	private String attachId;


	@ApiModelProperty(value = "变更状态 0  1 已更新",hidden = true)
	private  String changeStatus;


	@ApiModelProperty(value = "资料名称",required = true)
	private String name;
	@ApiModelProperty(value = "资料归类",required = true)
	private Long type;

	@ApiModelProperty(value = "资料类型id",required = true)
	private Long fileCategoryId;

	@ApiModelProperty(value = "文件后缀",required = true)
	private String extension;

}
