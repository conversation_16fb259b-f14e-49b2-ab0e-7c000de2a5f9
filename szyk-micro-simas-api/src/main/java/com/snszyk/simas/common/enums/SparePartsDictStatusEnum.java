package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: SparePartsDictStatusEnum
 * Package: com.snszyk.simas.common.enums
 * Description:
 *
 * <AUTHOR>
 * @Auth zhangzhenpu
 * @Create 2025/3/18 14:50
 */
@AllArgsConstructor
@Getter
public enum SparePartsDictStatusEnum {
	/**
	 * 在用
	 */
	ENABLED(0, "在用"),
	/**
	 * 停用
	 */
	DISABLED(1, "停用");

	private Integer code;
	private String value;

	public static SparePartsDictStatusEnum getByCode(Integer status) {
		for (SparePartsDictStatusEnum statusEnum : values()) {
			if (statusEnum.getCode().equals(status)) {
				return statusEnum;
			}
		}
		return null;
	}
}
