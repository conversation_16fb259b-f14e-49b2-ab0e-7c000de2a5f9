package com.snszyk.simas.common.vo;

import com.snszyk.simas.spare.enums.SparePartConsumptionTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 统计用查询条件
 *
 * <AUTHOR>
 * @since 2023-09-09
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "StatisticSearchVO对象", description = "StatisticSearchVO对象")
public class StatisticSearchVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;

	/**
	 * 按时间查询（0：近1年；1：近30天；2：近7天；3：当天）
	 */
	@ApiModelProperty(value = "按时间查询（0：近1年；1：近30天；2：近7天；3：当天）")
	private Integer queryDate;

	/**
	 * 部门id（，分隔）
	 */
	@ApiModelProperty(value = "部门id（，分隔）")
	private String deptId;

	/**
	 * 部门id列表
	 */
	@ApiModelProperty(value = "部门id列表")
	private List<Long> deptIdList;

	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	/**
	 * 业务模块（INSPECT_ORDER：点巡检，MAINTAIN_ORDER：保养，REPAIR：维修，LUBRICATE：润滑）
	 */
	@ApiModelProperty(value = "业务模块（INSPECT_ORDER：点巡检，MAINTAIN_ORDER：保养，REPAIR：维修，LUBRICATE_ORDER：润滑）")
	private String bizModule;

	// /**
	//  * 备件损耗统统计方式（0：全部，1：保养，2：维修）
	//  */
	// @ApiModelProperty(value = "备件损耗统计方式（0：全部，1：保养，2：维修）")
	// private Integer queryType;

	@ApiModelProperty(value = "备件消耗类型")
	private SparePartConsumptionTypeEnum sparePartConsumptionType;

	/**
	 * 设备id列表
	 */
	@ApiModelProperty(value = "设备id列表")
	private List<Long> equipmentIds;
	/**
	 * 工单状态
	 */
	@ApiModelProperty(value = "工单状态")
	private Integer status;


	@ApiModelProperty(value = "业务模块列表")
	private List<String> bizModuleList;


	@ApiModelProperty(value = "是否升序")
	private Boolean asc;

	public StatisticSearchVO(){
		super();
	}

	public StatisticSearchVO(Integer queryDate, String startDate, String endDate, List<Long> equipmentIds){
		super();
		this.setQueryDate(queryDate);
		this.setStartDate(startDate);
		this.setEndDate(endDate);
		this.setEquipmentIds(equipmentIds);
	}

}
