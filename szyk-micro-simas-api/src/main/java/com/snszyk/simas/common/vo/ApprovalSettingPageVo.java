/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单审核配置实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ApprovalSettingVo对象", description = "工单审核配置")
public class ApprovalSettingPageVo extends Page {

	@ApiModelProperty(value = "租户id",required = false,hidden = true)
	private String tenantId;




}
