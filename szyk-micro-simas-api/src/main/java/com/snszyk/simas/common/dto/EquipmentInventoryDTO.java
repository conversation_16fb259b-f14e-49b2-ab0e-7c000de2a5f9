package com.snszyk.simas.common.dto;

import com.snszyk.simas.common.entity.EquipmentInventory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentInventoryDTO对象", description = "设备盘点记录")
public class EquipmentInventoryDTO extends EquipmentInventory {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "盘点人员名称")
	private String inventoryUserName;

	@ApiModelProperty(value = "盘点结果")
	private String resultName;

	@ApiModelProperty(value = "盘点状态（缺失、正常）")
	private String statusName;

	@ApiModelProperty(value = "盘点方式")
	private String methodsName;

	@ApiModelProperty(value = "使用部门")
	private String useDeptName;

	@ApiModelProperty(value = "使用人员姓名")
	private String userName;

	@ApiModelProperty(value = "盘点记录")
	private List<EquipmentInventoryDTO> inventoryRecord;
}
