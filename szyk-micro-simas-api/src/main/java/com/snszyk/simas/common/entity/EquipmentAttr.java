/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备自定义属性数据
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("simas_equipment_attr")
@EqualsAndHashCode(callSuper = false)
public class EquipmentAttr extends TenantEntity {

	/**
	 * 设备id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long equipmentId;
	/**
	 * 属性id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long attrId;
	/**
	 * 属性值
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String attrValue;
	private String tenantId;
}
