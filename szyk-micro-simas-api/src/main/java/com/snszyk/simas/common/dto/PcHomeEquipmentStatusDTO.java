/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 本年度设备运行情况
 */
@Data
@ApiModel(value = "本年度设备运行情况", description = "本年度设备运行情况")
public class PcHomeEquipmentStatusDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 维修工单数量
	 */
	@ApiModelProperty(value = "维修工单数量")
	private Integer repairNum=0;
	/**
	 * 故障缺陷数量
	 */
	@ApiModelProperty(value = "故障缺陷数量")
	private Integer defaultNum=0;

	@ApiModelProperty(value = "月份")
	private Integer month;


}
