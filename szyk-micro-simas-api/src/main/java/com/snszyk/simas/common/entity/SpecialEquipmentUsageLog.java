package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * ClassName: SpecialEquipmentUsageLog
 * Package: com.snszyk.simas.entity
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:18
 */
@Data
@TableName("simas_special_equipment_usage_log")
@EqualsAndHashCode(callSuper = true)
public class SpecialEquipmentUsageLog extends TenantEntity {
	/**
	 * 设备id
	 */
	private Long equipmentId;
	/**
	 * 使用人id
	 */
	private Long operatorId;
	/**
	 * 使用人姓名
	 */
	private String operatorName;
	/**
	 * 开始日期
	 */
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	private LocalDate endDate;
	/**
	 * 使用前状态
	 */
	private String preUseStatus;
	/**
	 * 使用后状态
	 */
	private String postUseStatus;
	/**
	 * 备注
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String remark;
}
