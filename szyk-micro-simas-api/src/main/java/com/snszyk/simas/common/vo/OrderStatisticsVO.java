package com.snszyk.simas.common.vo;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName: OrderStatisticsVO
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 14:34
 */
@Data
@AllArgsConstructor
public class OrderStatisticsVO {
	/**
	 * 工单类型
	 */
	@ApiModelProperty(value = "工单类型")
	private OrderTypeEnum orderType;
	/**
	 * 开始日期
	 */
	@NotNull
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "开始日期,yyyy-MM-dd", required = true)
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@NotNull
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "结束日期,yyyy-MM-dd", required = true)
	private LocalDate endDate;
	/**
	 * 开始日期时间
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime startDateTime;
	/**
	 * 结束日期时间
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime endDateTime;

	public void setStartDate(LocalDate startDate) {
		this.startDate = startDate;
		if (ObjectUtil.isNotEmpty(this.getStartDate())) {
			this.startDateTime = this.startDate.atStartOfDay();
		}
	}

	public void setEndDate(LocalDate endDate) {
		this.endDate = endDate;
		if (ObjectUtil.isNotEmpty(this.getEndDate())) {
			this.endDateTime = this.endDate.plusDays(1).atStartOfDay();
		}
	}
}
