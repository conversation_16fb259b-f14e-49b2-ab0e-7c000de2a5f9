/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 回答
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Accessors(chain = true)
@Data
@ApiModel(value = "回答", description = "回答")
public class CommandAnswerDto {
	/**
	 * 页面路由
	 */
	@ApiModelProperty(value = "1页面路由")
	private String pageRoute;

	/**
	 * 页面的tab
	 */
	@ApiModelProperty(value = "2页面的tab")
	private String tab;


	/**
	 * 页面的按钮
	 */
	@ApiModelProperty(value = "3页面的按钮")
	private String button;

	/**
	 */
	@ApiModelProperty(value = "4页面过滤条件")
	private Map<String,Object> condition;

	private String returnMsg;



}
