/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单审核配置实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ApprovalSettingDto对象", description = "工单审核配置")
public class ApprovalSettingDto extends BaseCrudDto {

	/**
	 * 工单类型
	 */
	@ApiModelProperty(value = "工单类型")
	private String orderType;
	/**
	 * 工单类型名称
	 */
	@ApiModelProperty(value = "工单类型名称")
	private String orderTypeName;
	/**
	 * 是否审核 1是  0否
	 */
	@ApiModelProperty(value = "是否审核 1是  0否")
	private Integer isApproval;

	@ApiModelProperty(value = "修改人员")
	private String updateUserName;


}
