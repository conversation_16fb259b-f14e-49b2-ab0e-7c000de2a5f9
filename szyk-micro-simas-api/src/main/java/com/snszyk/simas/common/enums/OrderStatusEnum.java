/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 工单状态枚举类
 *
 * <AUTHOR>
 * @date 2024/08/16 15:56
 **/
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
	/**
	 * 待派单
	 */
	WAIT(0, "待派单"),

	/*
	 ********************   目前只有维修工单含有待派单状态，下面的状态通用 ******************
	 */
	/**
	 * 执行中
	 */
	IN_PROCESS(1, "执行中"),
	/**
	 * 已完成
	 */
	IS_COMPLETED(2, "已完成"),
	/**
	 * 已超期
	 */
	IS_OVERDUE(3, "已超期"),
	/**
	 * 超期完成
	 */
	OVERDUE_COMPLETED(4, "超期完成"),
	/**
	 * 待验证
	 */
	WAIT_CONFIRM(5, "待验证"),
	/**
	 * 已驳回
	 */
	IS_REJECTED(6, "已驳回"),
	/**
	 * 已关闭
	 */
	IS_CLOSED(7, "已关闭"),
	;

	final Integer code;
	final String name;

	public static OrderStatusEnum getByCode(Integer code) {
		for (OrderStatusEnum value : OrderStatusEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}

	public static OrderStatusEnum getByName(String name) {
		for (OrderStatusEnum value : OrderStatusEnum.values()) {
			if (value.getName().equals(name)) {
				return value;
			}
		}
		return null;
	}

	/**
	 * 已完成的状态码集合
	 *
	 * @return
	 */
	public static List<Integer> completedStatus() {
		return Lists.newArrayList(IS_COMPLETED.getCode(), OVERDUE_COMPLETED.getCode());
	}

}
