/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.entity.BizLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 业务日志表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
public class BizLogDTO extends BizLog {
	private static final long serialVersionUID = 1L;

	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务信息")
	private String bizInfo;

	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String bizNo;

	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String module;

	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String moduleName;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "结束时间")
	private Date endTime;

	/**
	 * 执行人员
	 */
	@ApiModelProperty(value = "执行人员")
	private String operateUserName;

	@ApiModelProperty(value = "操作结果")
	private String operateResultName="成功";

	@ApiModelProperty(value = "操作结果")
	private Integer operateResult=1;
	/**
	 * 业务状态
	 */
	@ApiModelProperty(value = "业务状态")
	private String bizStatusName;

	private Long deptId;

	private String deptName;

	@ApiModelProperty(value = "日志类型")
	private String logType;

	@ApiModelProperty(value = "日志类型")
	private String logTypeName;

}
