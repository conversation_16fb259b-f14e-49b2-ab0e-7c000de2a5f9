/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.entity.EquipmentFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备资料表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentFileDTO对象", description = "设备资料表")
public class EquipmentFileDTO extends EquipmentFile {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "归类名称")
	private String typeName;

	@ApiModelProperty(value = "归类类型字典key")
	private  String typeKey;
	@ApiModelProperty(value = "资料类型名称")
	private String fileCategoryName;

	@ApiModelProperty(value = "附件id")
	private String attachId;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private Attach attach;
}
