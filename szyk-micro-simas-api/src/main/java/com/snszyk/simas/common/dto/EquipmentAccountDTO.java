/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备台账表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@Accessors(chain = true)
public class EquipmentAccountDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long id;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String name;
	/**
	 * 设备编码
	 */
	@ApiModelProperty(value = "设备编码")
	private String code;
	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String model;
	/**
	 * 设备单位
	 */
	@ApiModelProperty(value = "设备单位")
	private String measureUnitName;
	/**
	 * 使用单位
	 */
	@ApiModelProperty(value = "使用单位")
	private String useDeptName;
	private Long useDept;
	/**
	 * 使用人
	 */
	@ApiModelProperty(value = "使用人")
	private String userName;
	/**
	 * 地点
	 */
	@ApiModelProperty(value = "地点")
	private String locationName;
	/**
	 * 地点路径
	 */
	@ApiModelProperty(value = "地点路径")
	private String locationPath;
	/**
	 * 工单-总数
	 */
	@ApiModelProperty(value = "设备-总数")
	private Integer equipmentTotal;

	/**
	 * 工单-在用数量
	 */
	@ApiModelProperty(value = "设备-在用数量")
	private Integer inUseCount;

	/**
	 * 工单-闲置数量
	 */
	@ApiModelProperty(value = "设备-闲置数量")
	private Integer idleCount;

	/**
	 * 工单-维修数量
	 */
	@ApiModelProperty(value = "设备-维修数量")
	private Integer inRepairCount;

	/**
	 * 工单-报废数量
	 */
	@ApiModelProperty(value = "设备-报废数量")
	private Integer scrappedCount;
	@ApiModelProperty(value = "特种设备类型")
	private String  specialType;
	@ApiModelProperty(value = "特种设备类型")
	private String  specialTypeName;

	@ApiModelProperty(value = "特种设备检查周期")
	private Integer specialInspectPeriod;

	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "二级部门的id")
	private Long secDeptId;

	@ApiModelProperty(value = "二级部门的名称")
	private String secDeptName;

	private String deptAncestors;


}
