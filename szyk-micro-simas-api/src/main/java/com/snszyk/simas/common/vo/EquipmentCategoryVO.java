///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.common.vo;
//
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.snszyk.core.tool.node.INode;
//import com.snszyk.simas.common.entity.EquipmentCategory;
//import io.swagger.annotations.ApiModel;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 设备分类表视图实体类
// *
// * <AUTHOR>
// * @since 2024-08-24
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ApiModel(value = "EquipmentCategoryVO对象", description = "设备分类表")
//public class EquipmentCategoryVO extends EquipmentCategory implements INode<EquipmentCategoryVO> {
//	private static final long serialVersionUID = 1L;
//
//	/**
//	 * 子孙节点
//	 */
//	@JsonInclude(JsonInclude.Include.NON_EMPTY)
//	private List<EquipmentCategoryVO> children;
//
//	/**
//	 * 是否有子孙节点
//	 */
//	@JsonInclude(JsonInclude.Include.NON_EMPTY)
//	private Boolean hasChildren;
//
//	@Override
//	public List<EquipmentCategoryVO> getChildren() {
//		if (this.children == null) {
//			this.children = new ArrayList<>();
//		}
//		return this.children;
//	}
//
//	/**
//	 * 上级机构
//	 */
//	private String parentName;
//
//	/**
//	 * 查询关键字
//	 */
//	private String keywords;
//
//}
