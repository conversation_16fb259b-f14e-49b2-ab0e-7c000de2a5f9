/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 点巡检计划状态枚举类
 *
 * <AUTHOR>
 * @date 2024/08/16 13:56
 **/
@Getter
@AllArgsConstructor
public enum PlanStatusEnum {

	/**
	 * 未开始
	 */
	NO_START(0, "未开始"),
	/**
	 * 执行中
	 */
	IN_PROGRESS(1, "执行中"),
	/**
	 * 已完成
	 */
	IS_COMPLETED(2, "已完成"),
	/**
	 * 已终止
	 */
	IS_TERMINATED(3, "已终止"),
	;

	final Integer code;
	final String name;

	public static PlanStatusEnum getByCode(Integer code){
		for (PlanStatusEnum value : PlanStatusEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
