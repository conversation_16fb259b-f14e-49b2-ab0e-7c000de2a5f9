/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.inspect.vo.InspectRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 设备点巡检记录表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentInspectVO对象", description = "设备点巡检记录表")
public class EquipmentInspectVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "工单id")
	private Long orderId;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 设备点巡检结果列表
	 */
	@ApiModelProperty(value = "设备点巡检结果列表")
	private List<InspectRecordVO> inspectRecordList;

	/**
	 * 备注信息
	 */
	@ApiModelProperty(value = "备注信息")
	private String remark;

}
