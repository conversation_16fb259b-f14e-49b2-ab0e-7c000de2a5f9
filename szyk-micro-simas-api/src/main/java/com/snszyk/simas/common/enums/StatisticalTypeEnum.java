/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统计报表统计方式枚举类
 *
 * <AUTHOR>
 * @date 2024/09/09 10:18
 **/
@Getter
@AllArgsConstructor
public enum StatisticalTypeEnum {

	/**
	 * 按工单
	 */
	ORDER("ORDER", "按工单"),

	/**
	 * 按设备
	 */
	EQUIPMENT("EQUIPMENT", "按设备"),

	/**
	 * 保养
	 */
	MAINTAIN("MAINTAIN", "保养"),

	/**
	 * 维修
	 */
	REPAIR("REPAIR", "维修"),

	;

	final String code;
	final String name;

	public static StatisticalTypeEnum getByCode(String code){
		for (StatisticalTypeEnum value : StatisticalTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
