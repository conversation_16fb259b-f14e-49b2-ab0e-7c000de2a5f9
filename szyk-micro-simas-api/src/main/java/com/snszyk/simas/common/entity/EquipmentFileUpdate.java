/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备文件更新 实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("simas_equipment_file_update")
@EqualsAndHashCode(callSuper = true)
public class EquipmentFileUpdate extends TenantEntity {

	/**
	 * 设备资料id
	 */
	private Long equipmentFileId;
	/**
	 * 变更id
	 */
	private Long changeId;
	/**
	 * 文件更新状态(0 待更新 1 已更新)
	 */
	private String updateStatus;


	@ApiModelProperty(value = "资料名称")
	private String name;
	@ApiModelProperty(value = "资料归类")
	private Long type;

	@ApiModelProperty(value = "资料类型id")
	private Long fileCategoryId;

	private String attachId;

	private String extension;


}
