/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.system.cache.SysCache;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单覆盖率数据
 *
 * <AUTHOR>
 * @date 2024/09/07 10:56
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "OrderCoverDataDTO对象", description = "OrderCoverDataDTO对象")
public class OrderCoverDataDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 部门id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部门id")
	private Long deptId;

	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	private String deptName;

	/**
	 * 设备总量
	 */
	@ApiModelProperty(value = "设备总量")
	private Integer totalCount;

	/**
	 * 完成量
	 */
	@ApiModelProperty(value = "完成量")
	private Integer completeCount;

	/**
	 * 覆盖率
	 */
	@ApiModelProperty(value = "覆盖率")
	private BigDecimal coverRate;

	public OrderCoverDataDTO(){
		super();
	}

	public OrderCoverDataDTO(Long deptId){
		super();
		this.setDeptId(deptId);
		this.setDeptName(SysCache.getDept(deptId).getDeptName());
	}


}
