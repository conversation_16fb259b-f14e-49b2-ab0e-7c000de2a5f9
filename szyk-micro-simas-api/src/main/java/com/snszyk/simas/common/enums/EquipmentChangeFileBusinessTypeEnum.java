/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备变更的附件类型
1 风险评估资料2 设备验收图片
 **/
@Getter
@AllArgsConstructor
public enum EquipmentChangeFileBusinessTypeEnum {
	RISK_ASSESSMENT("1", "风险评估资料"),
	DEVICE_ACCEPTANCE("2", "设备验收图片")


	;

	final String code;
	final String name;

	public static EquipmentChangeFileBusinessTypeEnum getByCode(String code){
		for (EquipmentChangeFileBusinessTypeEnum value : EquipmentChangeFileBusinessTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
