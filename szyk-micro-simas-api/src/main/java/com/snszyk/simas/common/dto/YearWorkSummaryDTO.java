/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * pc首页未完成工单数量统计
 */
@Data
@ApiModel(value = "pc首页未完成工单数量统计", description = "pc首页未完成工单数量统计")
public class YearWorkSummaryDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "总工单数量")
	private Integer allNum;

	@ApiModelProperty(value = "待完成工单数量")
	private Integer todoNum;

	@ApiModelProperty(value = "超期工单数量")
	private Integer overdueNum;

	@ApiModelProperty(value = "超期完成工单量")
	private Integer overdueCompletedNum;

	@ApiModelProperty(value = "正常完成工单量")
	private Integer normalCompletedNum;


}
