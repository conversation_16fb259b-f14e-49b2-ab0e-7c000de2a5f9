package com.snszyk.simas.common.enums;

import com.snszyk.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 工单类型
 * ClassName: OrderTypeEnum
 * Package: com.snszyk.simas.entity
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 14:26
 */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {
	/**
	 * 点巡检工单
	 */
	INSPECT_ORDER("点巡检工单"),
	/**
	 * 保养工单
	 */
	MAINTAIN_ORDER("保养工单"),
	/**
	 * 外委维修工单
	 */
	EXTERNAL_REPAIR("外委维修工单"),
	/**
	 * 内部维修工单
	 */
	INTERNAL_REPAIR("内部维修工单"),
	/**
	 * 检修工单
	 */
	OVERHAUL_ORDER("检修工单"),
	/**
	 * 润滑工单
	 */
	LUBRICATE_ORDER("润滑工单");;
	private final String desc;

	public static OrderTypeEnum getByName(String name) {
		if (ObjectUtil.isEmpty(name)) {
			return null;
		}
		for (OrderTypeEnum orderTypeEnum : values()) {
			if (orderTypeEnum.name().equals(name)) {
				return orderTypeEnum;
			}
		}
		return null;
	}
}
