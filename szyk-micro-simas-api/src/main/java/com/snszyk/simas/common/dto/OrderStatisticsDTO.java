package com.snszyk.simas.common.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.simas.common.enums.OrderTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * ClassName: OrderStatisticsDTO
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 14:31
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class OrderStatisticsDTO {
	/**
	 * 工单类型
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "工单类型")
	private OrderTypeEnum orderType;
	/**
	 * 工单类型名称
	 */
	@ExcelProperty(value = "工单类型")
	@ApiModelProperty(value = "工单类型名称")
	private String orderTypeName;
	/**
	 * 工单数量
	 */
	@ExcelProperty(value = "全部工单数量")
	@ApiModelProperty(value = "工单数量")
	private Long orderNum;
	/**
	 * 完成数量
	 */
	@ExcelProperty(value = "完成数量")
	@ApiModelProperty(value = "完成数量")
	private Long completeNum;
	/**
	 * 未完成数量
	 */
	@ExcelProperty(value = "未完成数量")
	@ApiModelProperty(value = "未完成数量")
	private Long unCompleteNum;
	/**
	 * 工单完成率
	 */
	@ExcelProperty(value = "完成比例")
	@ApiModelProperty(value = "工单完成率")
	@NumberFormat(value = "0.00%")
	private BigDecimal completeRate;
	/**
	 * id,可能是用户id，可能是部门id，也可能是设备id
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "用户id/部门id,设备id")
	private Long id;
	/**
	 * name可能是部门名称，可能是人员名称，也可能是设备名称
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "部门名称/人员名称/设备名称")
	private String name;
	/**
	 * 设备sn码
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "设备sn码")
	private String equipmentSn;

	/**
	 * 初始化
	 */
	public OrderStatisticsDTO init() {
		return this.init(null);
	}

	public OrderStatisticsDTO init(OrderTypeEnum orderType) {
		this.orderNum = 0L;
		this.completeNum = 0L;
		this.unCompleteNum = 0L;
		this.completeRate = BigDecimal.ZERO;
		this.orderType = orderType;
		this.orderTypeName = OrderTypeEnum.getByName(orderType.name()).getDesc();
		return this;
	}

	/**
	 * 计算未完成数量
	 */
	public OrderStatisticsDTO computeUnCompleteNum() {
		this.unCompleteNum = this.orderNum - this.completeNum;
		return this;
	}

	/**
	 * 计算完成率
	 */
	public void computeCompleteRate() {
		if (this.orderNum == 0L || this.completeNum == 0L) {
			this.completeRate = BigDecimal.ZERO;
		}
		this.completeRate = new BigDecimal(this.getCompleteNum())
			.divide(new BigDecimal(this.getOrderNum()), 4, RoundingMode.HALF_UP);
	}
}
