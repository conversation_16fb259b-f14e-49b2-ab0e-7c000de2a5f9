package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 锁定状态
 * ClassName: LockStockEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/15 10:08
 */
@AllArgsConstructor
@Getter
public enum LockStockEnum {
	/**
	 * 未锁定
	 */
	UNLOCK(0, "未锁定"),
	/**
	 * 锁定
	 */
	LOCK(1, "锁定");

	private final Integer code;
	private final String message;
}
