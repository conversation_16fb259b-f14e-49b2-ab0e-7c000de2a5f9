package com.snszyk.simas.common.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "EquipmentAttrVo", description = "设备台账")
public class EquipmentAttrVo extends BaseCrudVo {

	/**
	 * 设备id
	 */
	@NotNull
	@ApiModelProperty(value = "设备id", required = true)
	private Long equipmentId;
	/**
	 * 属性id
	 */
	@NotNull
	@ApiModelProperty(value = "属性id", required = true)
	private Long attrId;
	/**
	 * 属性值
	 */
	@NotBlank
	@ApiModelProperty(value = "属性值",required = true)
	private String attrValue;

	@ApiModelProperty(value = "属性名称")
	private String attrName;

	@ApiModelProperty(value = "排序")
	private Integer sort;

}
