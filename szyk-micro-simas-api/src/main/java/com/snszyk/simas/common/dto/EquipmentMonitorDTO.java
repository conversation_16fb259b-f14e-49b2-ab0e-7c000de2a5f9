/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.simas.common.entity.EquipmentMonitor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备部位表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EquipmentMonitorDTO extends EquipmentMonitor {
	private static final long serialVersionUID = 1L;

}
