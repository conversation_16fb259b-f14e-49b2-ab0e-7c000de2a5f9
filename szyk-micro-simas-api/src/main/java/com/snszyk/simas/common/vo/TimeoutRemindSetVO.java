/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.simas.common.entity.TimeoutRemindSet;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备工单超时提醒设置表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TimeoutRemindSetVO对象", description = "设备工单超时提醒设置表")
public class TimeoutRemindSetVO extends TimeoutRemindSet {
	private static final long serialVersionUID = 1L;

}
