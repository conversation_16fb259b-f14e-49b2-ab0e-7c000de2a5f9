/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备等级信息实体类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@ApiModel(value = "EquipmentFaultMaintenanceNotificationRuleDto对象", description = "设备等级信息")
public class EquipmentFaultRepairMessageRuleDto {
	/**
	 * id
	 */
	@ApiModelProperty(value = "id，修改时必传")
	private Long id;
	/**
	 * 设备等级
	 */

	@ApiModelProperty(value = "设备等级字典key")
	private List<Integer> levelList;
	/**
	 * 推送内容类型
	 */

	@ApiModelProperty(value = "推送内容类型字典key")
	private List<Integer> pushTypeList;
	/**
	 * 推送角色
	 */
	@ApiModelProperty(value = "推送角色ids")
	private List<Long> pushRoleIdList;


}
