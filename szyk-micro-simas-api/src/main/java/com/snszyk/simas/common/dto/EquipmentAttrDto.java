package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
public class EquipmentAttrDto extends BaseCrudDto {

	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String equipmentNo;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "sn编号")
	private String snNo;
	/**
	 * 属性id
	 */
	@ApiModelProperty(value = "属性id")
	private Long attrId;
	/**
	 * 属性值
	 */
	@ApiModelProperty(value = "属性值")
	private String attrValue;

	/**
	 * 分类id
	 */
	@ApiModelProperty(value = "分类id")
	private Long categoryId;

	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String categoryNo;

	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String categoryName;
	/**
	 * 字段名称
	 */
	@ApiModelProperty(value = "字段名称")
	private String attrName;
	/**
	 * 字段类型
	 */
	@ApiModelProperty(value = "字段类型")
	private String attrType;
	/**
	 * 是否必填，0否1是
	 */
	@ApiModelProperty(value = "是否必填，0否1是")
	private Integer isRequire;
	/**
	 * 待选值数据源
	 */
	@ApiModelProperty(value = "待选值数据源")
	private String selectData;
	/**
	 * 精度
	 */
	@ApiModelProperty(value = "精度")
	private Integer precision;
	/**
	 * 日期类型
	 */
	@ApiModelProperty(value = "日期类型")
	private String dataType;
	/**
	 * 字段长度
	 */
	@ApiModelProperty(value = "字段长度")
	private Integer fieldLength;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;


}
