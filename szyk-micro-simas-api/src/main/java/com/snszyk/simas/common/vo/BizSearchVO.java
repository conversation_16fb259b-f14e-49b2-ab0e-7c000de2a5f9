/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 业务日志表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BizLogVO对象", description = "业务日志表")
public class BizSearchVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private String equipmentId;

	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String bizNo;

	/**
	 * 业务模块
	 */
	@ApiModelProperty(value = "业务模块")
	private String module;

	private List<String> moduleList=new java.util.ArrayList<>();


}
