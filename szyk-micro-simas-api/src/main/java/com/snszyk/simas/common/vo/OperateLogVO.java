/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.simas.common.entity.OperateLog;
import com.snszyk.simas.common.enums.OperateTypeEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 操作日志表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OperateLogVO对象", description = "操作日志表")
public class OperateLogVO extends OperateLog {
	private static final long serialVersionUID = 1L;

	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private String startDate;

	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private String endDate;

	/**
	 * 系统模块
	 */
	@ApiModelProperty(value = "系统模块")
	private String moduleName;

	/**
	 * 操作类型
	 */
	@ApiModelProperty(value = "操作类型")
	private String typeName;

	/**
	 * 操作地点
	 */
	@ApiModelProperty(value = "操作地点")
	private String netName;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operateUserName;

	public OperateLogVO(){
		super();
	}

	public OperateLogVO(Long bizId, SystemModuleEnum systemModule, OperateTypeEnum operateType){
		super();
		this.setBizId(bizId);
		this.setModule(systemModule.getCode());
		this.setType(operateType.getCode());
	}


}
