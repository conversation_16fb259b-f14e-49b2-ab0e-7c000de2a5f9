/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 大屏数据 二级部门的设备使用情况
 *
 * @date 2024/09/07 08:56
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "二级部门的设备使用情况", description = "二级部门的设备使用情况")
public class DashboardSecEquipmentDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单-总数
	 */
	@ApiModelProperty(value = "设备-总数")
	private Integer equipmentTotal;

	/**
	 * 工单-在用数量
	 */
	@ApiModelProperty(value = "设备-在用数量")
	private Long inUseCount;

	/**
	 * 工单-闲置数量
	 */
	@ApiModelProperty(value = "设备-闲置数量")
	private Long idleCount;

	/**
	 * 工单-维修数量
	 */
	@ApiModelProperty(value = "设备-维修数量")
	private Long inRepairCount;

	/**
	 * 工单-报废数量
	 */
	@ApiModelProperty(value = "设备-报废数量")
	private Long scrappedCount;

	@ApiModelProperty(value = "二级的部门")
	private Long secDeptId;
	@ApiModelProperty(value = "二级的部门名称")
	private String secDeptName;




}
