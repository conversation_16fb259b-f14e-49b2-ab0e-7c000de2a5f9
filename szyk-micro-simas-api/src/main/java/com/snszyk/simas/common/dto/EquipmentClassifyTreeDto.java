package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.tool.node.INode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class EquipmentClassifyTreeDto extends BaseCrudDto implements INode<EquipmentClassifyTreeDto> {

	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类id")
	private Long parentId;
	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类名称")
	private String parentName;
	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<EquipmentClassifyTreeDto> children;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	@Override
	public List<EquipmentClassifyTreeDto> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}
}
