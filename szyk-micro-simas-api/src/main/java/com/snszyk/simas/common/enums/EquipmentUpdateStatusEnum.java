/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资料更新枚举类
0 待更新
 1 已更新
 **/
@Getter
@AllArgsConstructor
public enum EquipmentUpdateStatusEnum {
	WAIT_UPDATE("0", "待更新"),
	ALREADY_UPDATE("1", "已更新"),
	NO_CHOOSE("2", "未选择")


	;

	final String code;
	final String name;

	public static EquipmentUpdateStatusEnum getByCode(String code){
		for (EquipmentUpdateStatusEnum value : EquipmentUpdateStatusEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
