package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备变更动作枚举
 * ClassName: EquipmentChangeActionEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/14 9:58
 */
@AllArgsConstructor
@Getter
public enum EquipmentChangeActionEnum {
	/**
	 * 初始化
	 */
	INIT(EquipmentChangeStatusEnum.WAIT_CHECK.getCode(), "提交了"),
	/**
	 * 再次提交
	 */
	RE_SUBMIT(EquipmentChangeStatusEnum.WAIT_CHECK.getCode(), "再次提交了"),
	/**
	 * 审核通过
	 */
	AUDIT_PASS(EquipmentChangeStatusEnum.IS_COMPLETED.getCode(), "审核通过了"),
	/**
	 * 审核不通过
	 */
	AUDIT_FAIL(EquipmentChangeStatusEnum.REJECT.getCode(), "驳回了"),
	/**
	 * 撤销
	 */
	CANCEL(EquipmentChangeStatusEnum.CANCEL.getCode(), "撤销了"),
	/**
	 * 验收
	 */
	ACCEPT(EquipmentChangeStatusEnum.CHANGE_COMPLETED.getCode(), "验收了"),
	;

	private String code;
	private String desc;
}
