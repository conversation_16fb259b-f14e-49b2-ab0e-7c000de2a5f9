package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuditStatusEnum {
	PASS(1, "通过"),
	REJECT(2, "驳回");


	final Integer code;
	final String name;

	public static AuditStatusEnum getByCode(Integer code){
		for (AuditStatusEnum value : AuditStatusEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}


}
