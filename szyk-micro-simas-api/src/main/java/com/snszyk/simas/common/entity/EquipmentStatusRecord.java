/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 设备状态记录实体类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Data
@TableName("simas_equipment_status_record")
@EqualsAndHashCode(callSuper = true)
public class EquipmentStatusRecord extends TenantEntity {

	/**
	 * 设备id
	 */
	private Long equipmentId;
	/**
	 * 记录日期
	 */
	private LocalDate recordDate;
	/**
	 * 周期
	 */
	@TableField(exist = false)
	private String cycle;

}
