/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.inspect.entity.InspectRecord;
import com.snszyk.simas.inspect.entity.InspectStandard;
import com.snszyk.simas.maintain.entity.MaintainRecord;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 工单上报缺陷数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@Accessors(chain = true)
public class OrderDefectDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "工单id")
	private Long orderId;
	/**
	 * 异常来源
	 */
	@ApiModelProperty(value = "异常来源")
	private String sourceName;
	/**
	 * 上报人
	 */
	@ApiModelProperty(value = "上报人")
	private String reportUserName;
	/**
	 * 上报时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "上报时间")
	private Date reportTime;
	/**
	 * 检查部位
	 */
	@ApiModelProperty(value = "检查部位")
	private Long monitorId;
	/**
	 * 检查部位
	 */
	@ApiModelProperty(value = "检查部位")
	private String monitorName;
	/**
	 * 检查标准
	 */
	@ApiModelProperty(value = "检查标准")
	private String standard;
	/**
	 * 是否异常
	 */
	@ApiModelProperty(value = "是否异常")
	private String abnormalStatus;
	/**
	 * 异常描述
	 */
	@ApiModelProperty(value = "异常描述")
	private String abnormalComment;
	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;

	/**
	 * 点巡检生成
	 *
	 * @param inspectRecord
	 * @return
	 */
	public OrderDefectDTO toOrderDefectDTO(InspectRecord inspectRecord){
		OrderDefectDTO dto = Objects.requireNonNull(BeanUtil.copy(inspectRecord, OrderDefectDTO.class));
		if(Func.isNotEmpty(inspectRecord.getIsAbnormal())){
			dto.setAbnormalStatus(inspectRecord.getIsAbnormal() == 1 ? "异常" : "正常");
		}
		if(Func.isNotEmpty(inspectRecord.getStandardInfo())){
			dto.setStandard(JSONUtil.toBean(inspectRecord.getStandardInfo(), InspectStandard.class).getStandard());
		}
		if(Func.isNotEmpty(inspectRecord.getInspectUser())){
			User reportUser = UserCache.getUser(inspectRecord.getInspectUser());
			if(Func.isNotEmpty(reportUser)){
				dto.setReportUserName(reportUser.getRealName());
			}
		}
		dto.setReportTime(inspectRecord.getInspectTime());
		return dto;

	}

	/**
	 * 保养生成
	 *
	 * @param maintainRecord
	 * @return
	 */
	public OrderDefectDTO toOrderDefectDTO(MaintainRecord maintainRecord){
		OrderDefectDTO dto = Objects.requireNonNull(BeanUtil.copy(maintainRecord, OrderDefectDTO.class));
		if(Func.isNotEmpty(maintainRecord.getIsAbnormal())){
			dto.setAbnormalStatus(maintainRecord.getIsAbnormal() == 1 ? "异常" : "正常");
		}
		if(Func.isNotEmpty(maintainRecord.getStandardInfo())){
			dto.setStandard(JSONUtil.toBean(maintainRecord.getStandardInfo(), InspectStandard.class).getStandard());
		}
		if(Func.isNotEmpty(maintainRecord.getMaintainUser())){
			User reportUser = UserCache.getUser(maintainRecord.getMaintainUser());
			if(Func.isNotEmpty(reportUser)){
				dto.setReportUserName(reportUser.getRealName());
			}
		}
		dto.setReportTime(maintainRecord.getMaintainTime());
		return dto;
	}

	/**
	 * 保养生成
	 *
	 * @param faultDefect
	 * @return
	 */
	public OrderDefectDTO toOrderDefectDTO(FaultDefect faultDefect){
		OrderDefectDTO dto = Objects.requireNonNull(BeanUtil.copy(faultDefect, OrderDefectDTO.class));
		if(Func.isNotEmpty(faultDefect.getReportUser())){
			User reportUser = UserCache.getUser(faultDefect.getReportUser());
			if(Func.isNotEmpty(reportUser)){
				dto.setReportUserName(reportUser.getRealName());
			}
		}
		return dto;
	}


}
