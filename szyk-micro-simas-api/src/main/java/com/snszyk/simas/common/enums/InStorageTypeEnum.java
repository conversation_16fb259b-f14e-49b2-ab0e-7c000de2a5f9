// package com.snszyk.simas.enums;
//
// import lombok.AllArgsConstructor;
// import lombok.Getter;
//
// @Getter
// @AllArgsConstructor
// public enum InStorageTypeEnum {
//
// 	RETURN(1, "归还"),
// 	ALLOT(2, "调拨"),
// 	USE_OLD(3, "利旧"),
// 	ADD(4, "新增"),
// 	;
//
// 	final Integer code;
// 	final String name;
//
// 	public static InStorageTypeEnum getByCode(Integer code){
// 		for (InStorageTypeEnum value : InStorageTypeEnum.values()) {
// 			if (code.equals(value.getCode())){
// 				return value;
// 			}
// 		}
// 		return null;
// 	}
// }
