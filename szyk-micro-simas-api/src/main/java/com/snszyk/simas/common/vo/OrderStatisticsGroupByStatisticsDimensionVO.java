package com.snszyk.simas.common.vo;

import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.enums.OrderTypeEnum;
import com.snszyk.simas.common.enums.SortOrderEnum;
import com.snszyk.simas.common.enums.StatisticsDimensionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName: OrderStatisticsVO
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/18 14:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderStatisticsGroupByStatisticsDimensionVO extends Query {
	/**
	 * 工单类型
	 */
	@NotNull
	@ApiModelProperty(value = "工单类型", required = true)
	private OrderTypeEnum orderType;
	/**
	 * 统计维度
	 */
	@NotNull
	@ApiModelProperty(value = "统计维度", required = true)
	private StatisticsDimensionEnum statisticsDimension;
	/**
	 * 开始日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "开始日期,yyyy-MM-dd")
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "结束日期,yyyy-MM-dd")
	private LocalDate endDate;
	/**
	 * 部门id
	 *
	 * @param startDate
	 */
	@ApiModelProperty(value = "部门id")
	private Long deptId;
	/**
	 * 用户名称
	 */
	@ApiModelProperty(value = "用户名称")
	private String userName;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 开始日期时间
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime startDateTime;
	/**
	 * 结束日期时间
	 */
	@ApiModelProperty(hidden = true)
	private LocalDateTime endDateTime;
	/**
	 * 排序，正序倒序
	 */
	@ApiModelProperty(value = "排序，正序倒序")
	private SortOrderEnum sortOrder;


	public void setStartDate(LocalDate startDate) {
		this.startDate = startDate;
		if (ObjectUtil.isNotEmpty(this.getStartDate())) {
			this.startDateTime = this.startDate.atStartOfDay();
		}
	}

	public void setEndDate(LocalDate endDate) {
		this.endDate = endDate;
		if (ObjectUtil.isNotEmpty(this.getEndDate())) {
			this.endDateTime = this.endDate.plusDays(1).atStartOfDay();
		}
	}

}
