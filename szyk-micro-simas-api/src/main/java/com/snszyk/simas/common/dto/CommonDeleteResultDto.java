package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 1.0.0
 * @since 2024/3/13 08:54
 * 删除的结果
 **/
@ApiModel("删除的结果")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CommonDeleteResultDto {

	@ApiModelProperty("结果")
	private Boolean result=true;

	@ApiModelProperty("名称")
	private String name;

	@ApiModelProperty("id")
	private Long id;

	@ApiModelProperty("msg")
	private String msg="";
}
