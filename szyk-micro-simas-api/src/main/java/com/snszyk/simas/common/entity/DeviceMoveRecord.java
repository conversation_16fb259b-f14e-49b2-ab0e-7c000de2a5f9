/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备移动记录表实体类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Accessors(chain = true)
@TableName("simas_device_move_record")
@ApiModel(value = "DeviceMoveRecord对象", description = "设备移动记录表")
public class DeviceMoveRecord implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;
	/**
	 * 单号
	 */
	@ApiModelProperty(value = "单号")
	private String no;
	/**
	 * 来源（字典：move_source）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "来源（字典：move_source）")
	private Integer source;
	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String bizNo;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long deviceId;
	/**
	 * 使用部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用部门")
	private Long useDept;
	/**
	 * 使用人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用人")
	private Long userId;
	/**
	 * 移动前位置id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "移动前位置id")
	private Long originalLocation;
	/**
	 * 移动后位置id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "移动后位置id")
	private Long newLocation;
	/**
	 * 移动时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "移动时间")
	private Date moveTime;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 操作人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "操作人")
	private Long operateUser;
	/**
	 * 操作时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "操作时间")
	private Date operateTime;


}
