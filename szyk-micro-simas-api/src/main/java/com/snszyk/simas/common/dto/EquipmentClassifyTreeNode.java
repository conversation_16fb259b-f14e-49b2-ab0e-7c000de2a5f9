package com.snszyk.simas.common.dto;

/**
 * 设备台帐分类
 * @atuthor
 * @date 2023-05-12
 * @apiNote
 */

import com.snszyk.core.tool.node.BaseNode;
import lombok.Data;

@Data
public class EquipmentClassifyTreeNode extends BaseNode<EquipmentClassifyTreeNode> {
	private static final long serialVersionUID = 1L;
	private String title;

	private String key;

	private String value;

	private Boolean isLeaf;
	private String parentName;

	public Boolean getIsLeaf() {
		return !this.getHasChildren();
	}

	public void setIsLeaf(final Boolean isLeaf) {
		this.isLeaf = isLeaf;
	}

	public EquipmentClassifyTreeNode() {
	}


	public String toString() {
		return "TreeNodeNew(title=" + this.getTitle() + ", key=" + this.getKey() + ", value=" + this.getValue() + ")";
	}

	public boolean equals(final Object o) {
		if (o == this) {
			return true;
		} else if (!(o instanceof EquipmentClassifyTreeNode)) {
			return false;
		} else {
			EquipmentClassifyTreeNode other = (EquipmentClassifyTreeNode) o;
			if (!other.canEqual(this)) {
				return false;
			} else {
				label47:
				{
					Object this$key = this.getKey();
					Object other$key = other.getKey();
					if (this$key == null) {
						if (other$key == null) {
							break label47;
						}
					} else if (this$key.equals(other$key)) {
						break label47;
					}

					return false;
				}

				Object this$value = this.getValue();
				Object other$value = other.getValue();
				if (this$value == null) {
					if (other$value != null) {
						return false;
					}
				} else if (!this$value.equals(other$value)) {
					return false;
				}

				Object this$title = this.getTitle();
				Object other$title = other.getTitle();
				if (this$title == null) {
					if (other$title != null) {
						return false;
					}
				} else if (!this$title.equals(other$title)) {
					return false;
				}

				return true;
			}
		}
	}

	protected boolean canEqual(final Object other) {
		return other instanceof EquipmentClassifyTreeNode;
	}


}
