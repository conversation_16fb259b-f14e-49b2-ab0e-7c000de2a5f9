/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 盘点统计数据
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryStatisticsDTO对象", description = "盘点统计数据")
public class InventoryStatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 盘点中数量
	 */
	@ApiModelProperty(value = "盘点中数量")
	private Integer processCount;

	/**
	 * 已完成数量
	 */
	@ApiModelProperty(value = "已完成数量")
	private Integer completeCount;

	public InventoryStatisticsDTO() {
		super();
	}

	public InventoryStatisticsDTO(Integer processCount, Integer completeCount) {
		super();
		this.processCount = processCount;
		this.completeCount = completeCount;
	}
}
