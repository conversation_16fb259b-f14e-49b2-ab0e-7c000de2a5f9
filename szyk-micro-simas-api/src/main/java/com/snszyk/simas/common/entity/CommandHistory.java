/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 指令历史 实体类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@TableName("simas_command_history")
@EqualsAndHashCode(callSuper = true)
public class CommandHistory extends TenantEntity {

	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 指令路由
	 */
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	private String action;
	/**
	 * 用户输入内容
	 */
	private String inputContent;
	/**
	 * 生效时间
	 */
	private LocalDateTime effectiveTime;


}
