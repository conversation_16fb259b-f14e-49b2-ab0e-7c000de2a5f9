package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class EquipmentAttrShowDto  {


	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;


	/**
	 * 属性id
	 */
	@ApiModelProperty(value = "属性id")
	private Long attrId;
	/**
	 * 属性值
	 */
	@ApiModelProperty(value = "属性值")
	private String attrValue;

	/**
	 * 属性名称
	 */
	@ApiModelProperty(value = "属性名称")
	private String attrName;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String categoryNo;


}
