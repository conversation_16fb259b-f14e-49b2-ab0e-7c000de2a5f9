/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备台账信息视图实体类
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeviceInfoVO对象", description = "设备台账信息")
public class DeviceInfoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;

	@ApiModelProperty("租户ID")
	private String tenantId;

	/**
	 * 唯一识别码（系统生成）
	 */
	@ApiModelProperty(value = "唯一识别码（系统生成）")
	private String code;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String sn;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 设备类型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型id")
	private Long categoryId;
	/**
	 * 设备类型路径
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型路径")
	private String categoryPath;
	/**
	 * 重要级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "重要级（字典：important_level）")
	private Integer importantLevel;
	/**
	 * 工艺类别
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "工艺类别（字典：process_category）")
	private Integer processCategory;
	/**
	 * 计量单位
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计量单位（字典：measure_unit）")
	private Long measureUnit;
	/**
	 * 使用部门
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用部门")
	private Long useDept;
	/**
	 * 使用人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用人")
	private Long userId;
	/**
	 * 购买日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "购买日期")
	private Date purchaseDate;
	/**
	 * 投产日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "投产日期")
	private Date productDate;
	/**
	 * 存放地点
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "存放地点")
	private Long locationId;
	/**
	 * 供应商
	 */
	@ApiModelProperty(value = "供应商")
	private String supplier;

	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String categoryName;

	/**
	 * 重要级
	 */
	@ApiModelProperty(value = "重要级")
	private String importantLevelName;

	/**
	 * 工艺类别
	 */
	@ApiModelProperty(value = "工艺类别")
	private String processCategoryName;

	/**
	 * 计量单位
	 */
	@ApiModelProperty(value = "计量单位")
	private String measureUnitName;

	/**
	 * 使用部门
	 */
	@ApiModelProperty(value = "使用部门")
	private String useDeptName;

	/**
	 * 使用人
	 */
	@ApiModelProperty(value = "使用人")
	private String userName;

	/**
	 * 地点名称
	 */
	@ApiModelProperty(value = "地点名称")
	private String locationName;

	/**
	 * 地点路径
	 */
	@ApiModelProperty(value = "地点路径")
	private String locationPath;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("创建人")
	private Long createUser;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("创建部门")
	private Long createDept;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("更新人")
	private Long updateUser;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("更新时间")
	private Date updateTime;

	@ApiModelProperty("业务状态")
	private Integer status;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;

	/**
	 * 计划id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计划id")
	private Long planId;


}
