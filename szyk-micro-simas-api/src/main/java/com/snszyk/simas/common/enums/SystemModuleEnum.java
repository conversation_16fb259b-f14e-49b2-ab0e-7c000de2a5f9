/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统模块枚举类
 *
 * <AUTHOR>
 * @date 2024/08/14 10:16
 **/
@Getter
@AllArgsConstructor
public enum SystemModuleEnum {

	/**
	 * 设备管理
	 */
	EQUIPMENT_ACCOUNT("EQUIPMENT_ACCOUNT", "设备台账"),

	/**
	 * 点巡检标准
	 */
	INSPECT_STANDARD("INSPECT_STANDARD", "点巡检标准"),

	/**
	 * 点巡检计划
	 */
	INSPECT_PLAN("INSPECT_PLAN", "点巡检计划"),

	/**
	 * 点巡检工单
	 */
	INSPECT_ORDER("INSPECT_ORDER", "点巡检工单"),

	/**
	 * 保养标准
	 */
	MAINTAIN_STANDARD("MAINTAIN_STANDARD", "保养标准"),

	/**
	 * 保养计划
	 */
	MAINTAIN_PLAN("MAINTAIN_PLAN", "保养计划"),

	/**
	 * 保养工单
	 */
	MAINTAIN_ORDER("MAINTAIN_ORDER", "保养工单"),

	/**
	 * 故障缺陷
	 */
	FAULT_DEFECT("FAULT_DEFECT", "故障缺陷"),

	/**
	 * 内部维修
	 */
	INTERNAL_REPAIR("INTERNAL_REPAIR", "内部维修"),

	/**
	 * 外委维修
	 */
	EXTERNAL_REPAIR("EXTERNAL_REPAIR", "外委维修"),

	/**
	 * 设备报废
	 */
	EQUIPMENT_SCRAP("EQUIPMENT_SCRAP", "设备报废"),


	/**
	 * 润滑标准
	 */
	LUBRICATE_STANDARD("LUBRICATE_STANDARD", "润滑标准"),

	/**
	 * 润滑计划
	 */
	LUBRICATE_PLAN("LUBRICATE_PLAN", "润滑计划"),

	/**
	 * 润滑工单
	 */
	LUBRICATE_ORDER("LUBRICATE_ORDER", "润滑工单"),

	/**
	 * 检修标准
	 */
	OVERHAUL_STANDARD("OVERHAUL_STANDARD", "检修标准"),

	/**
	 * 检修计划
	 */
	OVERHAUL_PLAN("OVERHAUL_PLAN", "检修计划"),

	/**
	 * 检修工单
	 */
	OVERHAUL_ORDER("OVERHAUL_ORDER", "计划性检修工单"),

	/**
	 * 设备盘点
	 */
	EQUIPMENT_INVENTORY("EQUIPMENT_INVENTORY", "设备盘点"),

	/**
	 * 备品备件盘点
	 */
	SPARE_INVENTORY("SPARE_INVENTORY", "备品备件盘点"),

	/**
	 * 设备变更
	 */
	EQUIPMENT_CHANGE("EQUIPMENT_CHANGE", "设备变更"),

	/**
	 * 备品备件库存变化
	 */
	SPARE_STOCK_CHANGE("SPARE_STOCK_CHANGE", "备品备件库存变化"),

	/**
	 * 备品备件领用
	 */
	SPARE_PART_RECEIVE("SPARE_PART_RECEIVE", "备品备件领用"),

	/**
	 * 维修工单
	 */
	REPAIR("REPAIR","维修工单"),

	/**
	 * 特种设备检验登记
	 */
	SPECIAL_EQUIPMENT_INSPECT_REGISTER("SPECIAL_EQUIPMENT_INSPECT_REGISTER", "特种设备检验登记")
	;

	final String code;
	final String name;

	public static SystemModuleEnum getByCode(String code) {
		for (SystemModuleEnum value : SystemModuleEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}

}
