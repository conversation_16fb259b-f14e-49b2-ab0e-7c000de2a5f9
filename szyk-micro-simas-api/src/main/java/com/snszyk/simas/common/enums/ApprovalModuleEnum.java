package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: ApprovalOrderModuleEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/1/10 11:06
 */
@AllArgsConstructor
@Getter
public enum ApprovalModuleEnum {
	/**
	 * 点巡检
	 */
	INSPECT("点巡检"),
	/**
	 * 保养
	 */
	MAINTAIN("保养"),
	/**
	 * 润滑
	 */
	LUBRICATE("润滑"),
	/**
	 * 内部维修
	 */
	EXTERNAL_REPAIR("内部维修"),
	/**
	 * 外委维修
	 */
	EQUIPMENT_SCRAP("外委维修"),
	/**
	 * 检修
	 */
	OVERHAUL_ORDER("检修"),
	/**
	 * 备品备件
	 */
	SPARE_PART("备件申请"),

	;
	final String name;

}
