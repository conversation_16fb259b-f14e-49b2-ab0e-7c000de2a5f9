/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文件变更 附件的业务类型

 **/
@Getter
@AllArgsConstructor
public enum EquipmentFileTypeEnum {
	RISK_FILE("1", ""),
	ACCEPT_IMAGE("2", "已更新")


	;

	final String code;
	final String name;

	public static EquipmentFileTypeEnum getByCode(String code){
		for (EquipmentFileTypeEnum value : EquipmentFileTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
