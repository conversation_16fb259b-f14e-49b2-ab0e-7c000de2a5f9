/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备资料表实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@TableName("simas_equipment_file")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentFile对象", description = "设备资料表")
public class EquipmentFile extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "归类类型id")
	private Long type;
	/**
	 * 设备类型id
	 */
	@ApiModelProperty(value = "设备类型id")
	private String categoryId;
	/**
	 * 附件id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "附件id")
	private String attachId;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 资料类型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "资料类型id")
	private Long fileCategoryId;

	@ApiModelProperty(value = "文件后缀")
	private String extension;
}
