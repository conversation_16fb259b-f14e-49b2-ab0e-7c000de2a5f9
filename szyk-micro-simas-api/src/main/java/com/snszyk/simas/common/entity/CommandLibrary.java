/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指令库 实体类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@TableName("simas_command_library")
@EqualsAndHashCode(callSuper = true)
public class CommandLibrary extends TenantEntity {

	/**
	 * 指令路由
	 */
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	private String action;
	/**
	 * 页面路由
	 */
	private String pageRoute;
	/**
	 * 页面检索元素
	 */
	private String pageElement;
//	/**
//	 * 大模型提示词的配置项 (暂未使用)
//	 */
//	private String promptConfig;

	/**
	 * 页面菜单编码
	 */
	private String pageMenuCode;


}
