/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 指令历史实体类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CommandHistoryVo对象", description = "指令历史")
public class CommandHistoryVo extends BaseCrudVo {

	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private Long userId;
	/**
	 * 指令路由
	 */
	@ApiModelProperty(value = "指令路由")
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	@ApiModelProperty(value = "动作（查看、审核等）")
	private String action;
	/**
	 * 用户输入内容
	 */
	@ApiModelProperty(value = "用户输入内容")
	private String inputContent;
	/**
	 * 生效时间
	 */
	@ApiModelProperty(value = "生效时间")
	private LocalDateTime effectiveTime;


}
