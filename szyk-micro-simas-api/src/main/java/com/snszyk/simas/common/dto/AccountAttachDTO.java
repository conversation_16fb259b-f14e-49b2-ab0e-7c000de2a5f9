/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备台账表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@ApiModel(value = "AccountAttachDTO对象", description = "设备台账表")
public class AccountAttachDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 附件类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "附件类型（字典：attach_type）")
	private Integer attachType;

	/**
	 * 附件类型
	 */
	@ApiModelProperty(value = "附件类型")
	private String attachTypeName;

	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private Attach attach;

	public AccountAttachDTO(){
		super();
	}

	public AccountAttachDTO(Integer attachType, String attachTypeName){
		super();
		this.attachType = attachType;
		this.attachTypeName = attachTypeName;
	}

}
