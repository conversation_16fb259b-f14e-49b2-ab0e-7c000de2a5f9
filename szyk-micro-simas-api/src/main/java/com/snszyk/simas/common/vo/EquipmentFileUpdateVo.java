/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备文件更新实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentFileUpdateVo对象", description = "设备文件更新")
public class EquipmentFileUpdateVo extends BaseCrudVo {

	/**
	 * 设备资料id
	 */
	@ApiModelProperty(value = "设备资料id")
	private Long equipmentFileId;
	/**
	 * 变更id
	 */
	@ApiModelProperty(value = "变更id")
	private Long changeId;
	/**
	 * 文件更新状态(0 待更新 1 已更新)
	 */
	@ApiModelProperty(value = "文件更新状态(0 待更新 1 已更新)")
	private String updateStatus;

	@ApiModelProperty(value = "资料名称")
	private String name;
	@ApiModelProperty(value = "资料归类")
	private Long type;

	@ApiModelProperty(value = "资料类型id")
	private Long fileCategoryId;
	private String attachId;
	@ApiModelProperty(value = "文件后缀",required = true)
	private String extension;
}
