/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备等级信息 实体类
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Data
@TableName("simas_equipment_fault_repair_message_rule")
@EqualsAndHashCode(callSuper = true)
public class EquipmentFaultRepairMessageRule extends TenantEntity {

	/**
	 * 设备等级
	 */
	private String importantLevel;
	/**
	 * 推送内容类型
	 */
	private String pushType;
	/**
	 * 推送角色
	 */
	private String pushRoleId;


}
