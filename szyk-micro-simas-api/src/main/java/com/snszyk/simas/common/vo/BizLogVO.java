/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import cn.hutool.json.JSONUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.entity.BizLog;
import com.snszyk.simas.common.entity.EquipmentInventory;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.simas.fault.entity.FaultDefect;
import com.snszyk.simas.inspect.vo.InspectOrderVO;
import com.snszyk.simas.lubricate.vo.LubricateOrderVO;
import com.snszyk.simas.maintain.vo.MaintainOrderVO;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.overhaul.vo.OverhaulOrderVO;
import com.snszyk.simas.overhaul.vo.RepairVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 业务日志表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BizLogVO对象", description = "业务日志表")
public class BizLogVO extends BizLog {
	private static final long serialVersionUID = 1L;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operateUserName;

	public BizLogVO() {
		super();
	}

	public BizLogVO(InspectOrderVO inspectOrder) {
		super();
		this.setBizId(inspectOrder.getId());
		this.setBizNo(inspectOrder.getNo());
		this.setBizStatus(inspectOrder.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(inspectOrder));
		this.setModule(SystemModuleEnum.INSPECT_ORDER.getCode());
	}

	public BizLogVO(MaintainOrderVO maintainOrder) {
		super();
		this.setBizId(maintainOrder.getId());
		this.setBizNo(maintainOrder.getNo());
		this.setBizStatus(maintainOrder.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(maintainOrder));
		this.setModule(SystemModuleEnum.MAINTAIN_ORDER.getCode());
	}

	public BizLogVO(LubricateOrderVO lubricateOrder) {
		super();
		this.setBizId(lubricateOrder.getId());
		this.setBizNo(lubricateOrder.getNo());
		this.setBizStatus(lubricateOrder.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(lubricateOrder));
		this.setModule(SystemModuleEnum.LUBRICATE_ORDER.getCode());
	}

	public BizLogVO(OverhaulOrderVO overhaulOrder) {
		super();
		this.setBizId(overhaulOrder.getId());
		this.setBizNo(overhaulOrder.getNo());
		this.setBizStatus(overhaulOrder.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(overhaulOrder));
		this.setModule(SystemModuleEnum.OVERHAUL_ORDER.getCode());
	}

	public BizLogVO(FaultDefect faultDefect) {
		super();
		this.setBizId(faultDefect.getId());
		this.setBizStatus(faultDefect.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(faultDefect));
		this.setModule(SystemModuleEnum.FAULT_DEFECT.getCode());
		this.setOperateTime(DateUtil.now());
	}

	public BizLogVO(RepairVO repair) {
		super();
		this.setBizId(repair.getId());
		this.setBizNo(repair.getNo());
		this.setBizStatus(repair.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(repair));
		if (RepairBizTypeEnum.INTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
			this.setModule(SystemModuleEnum.INTERNAL_REPAIR.getCode());
		}
		if (RepairBizTypeEnum.EXTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())) {
			this.setModule(SystemModuleEnum.EXTERNAL_REPAIR.getCode());
		}
	}

	public BizLogVO(EquipmentInventory inventoryRecord) {
		super();
		this.setBizId(inventoryRecord.getId());
		this.setBizStatus(inventoryRecord.getStatus());
		this.setBizInfo(JSONUtil.toJsonStr(inventoryRecord));
		this.setModule(SystemModuleEnum.EQUIPMENT_INVENTORY.getCode());
	}


}
