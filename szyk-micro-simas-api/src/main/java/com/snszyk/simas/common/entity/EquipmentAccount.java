/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备台账表实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@TableName("simas_equipment_account")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentAccount对象", description = "设备台账表")
public class EquipmentAccount extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 唯一识别码（系统生成）
	 */
	@ApiModelProperty(value = "唯一识别码（系统生成）")
	private String code;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String sn;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	 * 设备类型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型id")
	private Long categoryId;
	/**
	 * 设备类型路径
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型路径")
	private String categoryPath;
	/**
	 * 重要级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "重要级（字典：important_level）")
	private Integer importantLevel;
	/**
	 * 工艺类别
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "工艺类别（字典：process_category）")
	private Integer processCategory;
	/**
	 * 计量单位
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "计量单位（字典：measure_unit）")
	private Integer measureUnit;
	/**
	 * 使用部门
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用部门")
	private Long useDept;
	/**
	 * 使用人
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用人")
	private Long userId;
	/**
	 * 购买日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "购买日期")
	private Date purchaseDate;
	/**
	 * 投产日期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "投产日期")
	private Date productDate;
	/**
	 * 存放地点
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "存放地点")
	private Long locationId;
	/**
	 * 供应商
	 */
	@ApiModelProperty(value = "供应商")
	private String supplier;
	/**
	 * 联系人
	 */
	@ApiModelProperty(value = "联系人")
	private String contact;
	/**
	 * 联系方式
	 */
	@ApiModelProperty(value = "联系方式")
	private String tel;
	/**
	 * 报废单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报废单id")
	private Long scrapId;
	/**
	 * 报废日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "报废日期")
	private Date scrapDate;
	/**
	 * 绑定RFID
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "绑定RFID")
	private String rfid;
	/**
	 * 绑定NFC
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "绑定NFC")
	private String nfc;
	/**
	 * 二维码
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "二维码")
	private Long qrCode;
	/**
	 * 上传图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传图片")
	private String imageId;
	/**
	 * 上传附件
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传附件")
	private String attachInfo;
	/**
	 * APP贴码图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "APP贴码图片")
	private String pasteImage;
	/**
	 * 是否贴码（0：否，1：是）
	 */
	@ApiModelProperty(value = "是否贴码（0：否，1：是）")
	private Integer isPasted;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "特种设备类型")
	private String  specialType;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "特种设备检查周期")
	private Integer specialInspectPeriod;



}
