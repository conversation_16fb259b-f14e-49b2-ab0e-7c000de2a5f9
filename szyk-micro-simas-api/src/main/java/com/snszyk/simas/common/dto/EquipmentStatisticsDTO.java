/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.IOrderField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 设备台账表统计数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-09-09
 */
@Data
@Accessors(chain = true)
public class EquipmentStatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long id;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String name;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String sn;
	/**
	 * 设备类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型")
	private Long categoryId;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String categoryName;
	/**
	 * 归属部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "归属部门")
	private Long useDept;
	/**
	 * 归属部门
	 */
	@ApiModelProperty(value = "归属部门")
	private String useDeptName;
	/**
	 * 次数
	 */
	@ApiModelProperty(value = "次数")
	private Integer count;
	/**
	 *
	 */
	@ApiModelProperty(value = "完成次数")
	private Integer completeCount;
	/**
	 *
	 */
	@ApiModelProperty(value = "未完成次数")
	private Integer unfinishedCount;
	/**
	 *
	 */
	@ApiModelProperty(value = "完成率")
	private BigDecimal completeRate;
	/**
	 *
	 */
	@ApiModelProperty(value = "异常数量")
	private Integer abnormalCount;
	/**
	 *
	 */
	@ApiModelProperty(value = "异常率")
	private BigDecimal abnormalRate;


	@ApiModelProperty(value = "状态", hidden = true)
	private Integer status;

	@ApiModelProperty(value = "是否异常", hidden = true)
	private Integer isAbnormal;

	/**
	 * 计算完成率
	 */
	public void calculateCompleteRate() {
		if (ObjectUtil.isEmpty(this.count) || this.count == 0) {
			return;
		}
		if (this.completeCount == null || this.completeCount == 0) {
			this.completeRate = BigDecimal.ZERO;
		} else {
			this.completeRate = new BigDecimal(this.completeCount)
				.divide(new BigDecimal(this.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
		}
	}

	/**
	 * 计算异常率
	 */
	public void calculateAbnormalRate() {
		if (ObjectUtil.isEmpty(this.count) || this.count == 0) {
			return;
		}
		if (this.abnormalCount == null || this.abnormalCount == 0) {
			this.abnormalRate = BigDecimal.ZERO;
		} else {
			this.abnormalRate = new BigDecimal(this.abnormalCount)
				.divide(new BigDecimal(this.getCount()), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
		}
	}

}
