/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.crud.base.BaseCrudEntity;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备分类自定义属性表
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@TableName("simas_equipment_category_attr")
@EqualsAndHashCode(callSuper = false)
public class EquipmentCategoryAttr extends TenantEntity {

	/**
	 * 分类id
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Long categoryId;
	/**
	 * 字段名称
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String attrName;
	/**
	 * 字段类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String attrType;
	/**
	 * 是否必填，0否1是
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer isRequire;
	/**
	 * 待选值数据源
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String selectData;
	/**
	 * 精度
	 */
	@TableField(value = "`precision`", updateStrategy = FieldStrategy.IGNORED)
	private Integer precision;
	/**
	 * 日期类型
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private String dataType;
	/**
	 * 字段长度
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer fieldLength;
	/**
	 * 排序
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer sort;
	private String tenantId;
}
