/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 工单审核配置 实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@TableName("simas_approval_setting")
@EqualsAndHashCode(callSuper = true)
public class ApprovalSetting extends TenantEntity {

	/**
	 * 工单类型
	 */
	private String orderType;
	/**
	 * 是否审核 1是  0否
	 */
	private Integer isApproval;


}
