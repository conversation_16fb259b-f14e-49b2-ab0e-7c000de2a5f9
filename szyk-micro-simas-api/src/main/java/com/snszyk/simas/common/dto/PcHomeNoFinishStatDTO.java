/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * pc首页未完成工单数量统计
 */
@Data
@ApiModel(value = "pc首页未完成工单数量统计", description = "pc首页未完成工单数量统计")
public class PcHomeNoFinishStatDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 点巡检数据
	 */
	@ApiModelProperty(value = "点巡检数据")
	private Integer inspectNum;
	/**
	 * 润滑数据
	 */
	@ApiModelProperty(value = "润滑数据")
	private Integer lubricateNum;
	/**
	 * 保养数据
	 */
	@ApiModelProperty(value = "保养数据")
	private Integer maintainNum;

	/**
	 * 维修单数据
	 */
	@ApiModelProperty(value = "维修单数据")
	private Integer repairNum;

	/**
	 * 内委维修单数据
	 */
	@ApiModelProperty(value = "内部维修")
	private Integer repairInnerNum;

	/**
	 * 检修数据
	 */
	@ApiModelProperty(value = "检修数据")
	private Integer overhaulNum;


	/**
	 * 外委维修单数据
	 */
	@ApiModelProperty(value = "外委维修单数据")
	private Integer repairExternalNum;

	@ApiModelProperty(value = "总数")
	private Integer allNum;





}
