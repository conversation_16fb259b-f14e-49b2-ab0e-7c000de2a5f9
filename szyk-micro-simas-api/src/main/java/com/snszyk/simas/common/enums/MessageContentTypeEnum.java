package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息内容类型
 * ClassName: MessageContentTypeEnum
 * Package: com.snszyk.message.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 9:48
 */
@AllArgsConstructor
@Getter
public enum MessageContentTypeEnum {
	/**
	 * 故障缺陷信息
	 */
	SIMAS_FAULT_DEFECT("故障缺陷"),
	/**
	 * 维修信息
	 */
	SIMAS_REPAIR("维修工单"),

	;
	private String desc;

}
