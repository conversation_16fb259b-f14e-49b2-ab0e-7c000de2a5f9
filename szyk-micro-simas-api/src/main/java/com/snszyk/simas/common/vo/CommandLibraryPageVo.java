/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指令库实体类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CommandLibraryVo对象", description = "指令库")
public class CommandLibraryPageVo extends Page {

	/**
	 * 指令路由
	 */
	@ApiModelProperty(value = "指令路由")
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	@ApiModelProperty(value = "动作（查看、审核等）")
	private String action;
	/**
	 * 页面路由
	 */
	@ApiModelProperty(value = "页面路由")
	private String pageRoute;
	/**
	 * 页面检索元素
	 */
	@ApiModelProperty(value = "页面检索元素")
	private String pageElement;


}
