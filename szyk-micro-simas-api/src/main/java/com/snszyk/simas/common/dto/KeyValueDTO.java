package com.snszyk.simas.common.dto;

import com.snszyk.common.utils.DateUtils;
import com.snszyk.simas.common.entity.YearQuarter;
import com.snszyk.simas.common.enums.TimeTypeEnumV2;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName: KeyValueDTO
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/2/11 11:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyValueDTO {
	/**
	 * 年度格式
	 */
	public static final String YEAR_FORMAT = "yyyy";
	/**
	 * 月度格式
	 */
	public static final String MONTH_FORMAT = "yyyy-MM月";
	/**
	 * 季度格式
	 */
	public static final String QUARTER_FORMAT = "%d-%d季度";
	/**
	 * 天格式
	 */
	public static final String DAY_FORMAT = "yyyy-MM-dd";

	/**
	 * key
	 */
	@ApiModelProperty(value = "key值")
	private String key;
	/**
	 * value
	 */
	@ApiModelProperty(value = "value值")
	private BigDecimal value;


	public static List<KeyValueDTO> initKeyValueDTOList(TimeTypeEnumV2 timeType) {
		final LocalDate startDate = timeType.computeStartDate();
		final LocalDate endDate = LocalDate.now();
		switch (timeType) {
			case THIRTY_DAYS:
				return DateUtils.listLocalDateBetween(startDate, endDate)
					.stream()
					.map(LocalDate -> {
						KeyValueDTO dto = new KeyValueDTO();
						dto.setKey(LocalDate.format(DateTimeFormatter.ofPattern(KeyValueDTO.DAY_FORMAT)));
						dto.setValue(BigDecimal.ZERO);
						return dto;
					}).collect(Collectors.toList());
			case MONTH:
				return DateUtils.listMonthsBetween(startDate, endDate)
					.stream()
					.map(yearMonth -> {
						KeyValueDTO dto = new KeyValueDTO();
						dto.setKey(yearMonth.format(DateTimeFormatter.ofPattern(KeyValueDTO.MONTH_FORMAT)));
						dto.setValue(BigDecimal.ZERO);
						return dto;
					}).collect(Collectors.toList());
			case QUARTER:
				return YearQuarter.getQuartersBetween(startDate, endDate)
					.stream()
					.map(yearQuarter -> {
						KeyValueDTO dto = new KeyValueDTO();
						dto.setKey(String.format(KeyValueDTO.QUARTER_FORMAT, yearQuarter.getYear(), yearQuarter.getQuarter()));
						dto.setValue(BigDecimal.ZERO);
						return dto;
					}).collect(Collectors.toList());
			case YEAR:
				return DateUtils.listYearsBetween(startDate, endDate)
					.stream()
					.map(year -> {
						KeyValueDTO dto = new KeyValueDTO();
						dto.setKey(year.format(DateTimeFormatter.ofPattern(KeyValueDTO.YEAR_FORMAT)));
						dto.setValue(BigDecimal.ZERO);
						return dto;
					}).collect(Collectors.toList());
			default:
				throw new IllegalArgumentException("时间类型错误");

		}
	}

}
