package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * ClassName: EquipmentLevelSaveVo
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/12 18:51
 */
@Data
@ApiModel(value = "EquipmentLevelVo对象", description = "设备等级信息")
public class EquipmentFaultRepairMessageRuleSaveVo {
	/**
	 * id
	 */
	@ApiModelProperty(value = "id，修改时必传")
	private Long id;
	/**
	 * 设备等级
	 */
	@NotEmpty
	@ApiModelProperty(value = "重要程度字典key", required = true)
	private List<Integer> importantLevelList;
	/**
	 * 推送内容类型
	 */
	@NotEmpty
	@ApiModelProperty(value = "推送内容类型字典key", required = true)
	private List<Integer> pushTypeList;
	/**
	 * 推送角色
	 */
	@NotEmpty
	@ApiModelProperty(value = "推送角色ids", required = true)
	private List<Long> pushRoleIdList;
}
