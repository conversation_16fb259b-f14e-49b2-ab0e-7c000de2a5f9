package com.snszyk.simas.common.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.simas.common.enums.TimeTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * ClassName: SpecialEquipmentUsageLogVo
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 17:05
 */
@Data
public class SpecialEquipmentUsagePageVo extends Page {
	/**
	 * 查询时间类型：1-近30天，2-近180天，3-近一年
	 */
	@NotNull
	@ApiModelProperty(value = "查询时间类型：1-近30天，2-近180天，3-近一年", required = true)
	private TimeTypeEnum timeType;
	/**
	 * 开始时间
	 */

	@ApiModelProperty(value = "开始时间", hidden = true)
	private LocalDate startDate;
	/**
	 * 结束时间
	 */

	@ApiModelProperty(value = "结束时间", hidden = true)
	private LocalDate endDate;
	/**
	 * 设备id
	 */
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
}
