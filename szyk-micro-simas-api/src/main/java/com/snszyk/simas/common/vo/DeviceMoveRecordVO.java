/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.common.entity.DeviceMoveRecord;
import com.snszyk.simas.common.enums.MoveSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备移动记录表视图实体类
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceMoveRecordVO对象", description = "设备移动记录表")
public class DeviceMoveRecordVO extends DeviceMoveRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户id")
	private String tenantId;

	/**
	 * 来源
	 */
	@ApiModelProperty(value = "来源")
	private String sourceName;

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String deviceCode;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String deviceName;

	/**
	 * 规格型号
	 */
	@ApiModelProperty(value = "规格型号")
	private String deviceModel;

	/**
	 * 使用部门
	 */
	@ApiModelProperty(value = "使用部门")
	private String useDeptName;

	/**
	 * 使用人
	 */
	@ApiModelProperty(value = "使用人")
	private String userName;

	/**
	 * 移动前位置
	 */
	@ApiModelProperty(value = "移动前位置")
	private String originalLocationName;

	/**
	 * 移动后位置
	 */
	@ApiModelProperty(value = "移动后位置")
	private String newLocationName;

	/**
	 * 操作人
	 */
	@ApiModelProperty(value = "操作人")
	private String operateUserName;

	/**
	 * 设备id列表
	 */
	@ApiModelProperty(value = "设备id列表")
	private List<Long> deviceIds;

	public DeviceMoveRecordVO() {
		super();
	}

	/**
	 * 构造方法
	 *
	 * @param deviceIds        设备id列表
	 * @param originalLocation 移动前位置
	 * @param newLocation      移动后位置
	 * @param useDept          使用部门
	 * @param userId           使用人
	 * @param moveSource       来源
	 */
	public DeviceMoveRecordVO(List<Long> deviceIds, Long originalLocation, Long newLocation,
							  Long useDept, Long userId, MoveSourceEnum moveSource) {
		super();
		this.deviceIds = deviceIds;
		this.setSource(moveSource.getCode());
		if(Func.isNotEmpty(originalLocation)){
			this.setNewLocation(originalLocation);
		}
		if(Func.isNotEmpty(newLocation)){
			this.setNewLocation(newLocation);
		}
		this.setUseDept(useDept);
		this.setUserId(userId);
	}

}
