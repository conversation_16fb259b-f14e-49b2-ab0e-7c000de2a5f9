package com.snszyk.simas.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.common.annotation.DynamicPrecision;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * ClassName: DynamicPrecisionSerializer
 * Package: com.snszyk.simas.common.serializer
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/29 15:40
 */
public class DynamicPrecisionSerializer extends JsonSerializer<BigDecimal> {
	@Override
	public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		// 1. 获取当前字段的 @DynamicPrecision 注解
		DynamicPrecision annotation = findAnnotation(gen);

		if (ObjectUtil.isNotEmpty(annotation)) {
			// 2. 从当前对象中获取精度值
			Integer precision = getPrecisionValue(gen, annotation.precisionField());

			// 3. 格式化并写入
			if (precision != null && value != null) {
				BigDecimal formatted = value.setScale(precision, RoundingMode.HALF_UP);
				gen.writeString(formatted.toPlainString());
			} else {
				if (value != null) {
					gen.writeNumber(value); // 默认不处理
				} else {
					gen.writeNull(); // 如果 value 为 null，写入 null
				}
			}
		} else {
			if (value != null) {
				gen.writeNumber(value); // 默认不处理
			} else {
				gen.writeNull(); // 如果 value 为 null，写入 null
			}
		}

	}

	private DynamicPrecision findAnnotation(JsonGenerator gen) {
		String fieldName = gen.getOutputContext().getCurrentName();
		if (fieldName == null) {
			return null;
		}
		Object currentObj = gen.getOutputContext().getCurrentValue();
		if (currentObj == null) {
			return null;
		}
		try {
			final Class<?> currentObjClass = currentObj.getClass();
			Field field = currentObjClass.getDeclaredField(fieldName);
			field.setAccessible(true);
			return field.getAnnotation(DynamicPrecision.class);
		} catch (NoSuchFieldException e) {
			// 字段不存在时的日志记录
			System.err.println("Field " + fieldName + " not found in " + currentObj.getClass().getName());
		} catch (Exception e) {
			// 其他异常处理
			System.err.println("An error occurred while trying to access field " + fieldName + ": " + e.getMessage());
		}
		return null;
	}

	private Integer getPrecisionValue(JsonGenerator gen, String precisionField) {
		try {
			// 通过反射获取精度字段值
			Object currentObj = gen.getOutputContext().getCurrentValue();
			Field field = currentObj.getClass().getDeclaredField(precisionField);
			field.setAccessible(true);
			return (Integer) field.get(currentObj);
		} catch (NoSuchFieldException e) {
			// 字段不存在时的日志记录
		} catch (IllegalAccessException e) {
			// 字段不可访问时的日志记录
		} catch (Exception e) {
			// 其他异常处理
			System.err.println("An error occurred while trying to access field " + precisionField + ": " + e.getMessage());
		}
		// 返回默认值或null
		return null;
	}
}
