/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.common.equipment.enums.EquipmentFileGroupEnum;
import com.snszyk.common.equipment.vo.DeviceCategoryVO;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.common.entity.EquipmentFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备资料表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentFileVO对象", description = "设备资料表")
public class EquipmentFileVO extends EquipmentFile {
	private static final long serialVersionUID = 1L;

	/**
	 * 资料数量
	 */
	@ApiModelProperty(value = "资料数量")
	private Integer fileCount;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;
	/**
	 * 资料类型
	 */
	@ApiModelProperty(value = "归类类型")
	private String typeName;

	@ApiModelProperty(value = "资料类型名称")
	private String fileCategoryName;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String statusName;
	/**
	 * 设备类型id列表
	 */
	@ApiModelProperty(value = "设备类型id列表")
	private List<Long> categoryIds;
	/**
	 * 设备类型列表
	 */
	@ApiModelProperty(value = "设备类型列表")
	private List<DeviceCategoryVO> categoryList;
	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> attachList;
	/**
	 * 附件
	 */
	@ApiModelProperty(value = "附件")
	private Attach attach;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String equipmentCategory;

	@ApiModelProperty(value = "PRE: 前期资料，REGISTER：注册登记资料，OPERATION：设备运维相关资料")
	private EquipmentFileGroupEnum module;

	private String typeKey;

	private String categoryIdLike;

}
