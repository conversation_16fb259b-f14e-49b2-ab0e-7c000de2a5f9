/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.common.equipment.enums.EquipmentFileGroupEnum;
import com.snszyk.simas.common.entity.EquipmentFile;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备资料表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentPreFileVO对象", description = "设备前期资料表")
public class EquipmentPreFileVO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	@ApiModelProperty(value = "PRE: 前期资料，REGISTER：注册登记资料，OPERATION：设备运维相关资料")
	private EquipmentFileGroupEnum module;
	@ApiModelProperty(value = "资料列表")
	private List<EquipmentFile> fileList;
}
