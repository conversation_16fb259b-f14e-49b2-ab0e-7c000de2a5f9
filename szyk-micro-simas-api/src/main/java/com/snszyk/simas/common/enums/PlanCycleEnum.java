/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计划周期枚举类
 *
 * <AUTHOR>
 * @date 2024/08/16 13:56
 **/
@Getter
@AllArgsConstructor
public enum PlanCycleEnum {

	/**
	 * 按日
	 */
	DAY("DAY", "按日"),
	/**
	 * 按日
	 */
	WEEK("WEEK", "按周"),
	/**
	 * 按日
	 */
	MONTH("MONTH", "按月"),

	;

	final String code;
	final String name;

	public static PlanCycleEnum getByCode(String code){
		for (PlanCycleEnum value : PlanCycleEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
