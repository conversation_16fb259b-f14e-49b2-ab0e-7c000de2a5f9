/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.simas.inspect.dto.InspectOrderDTO;
import com.snszyk.simas.maintain.dto.MaintainOrderDTO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.inspect.entity.InspectPlan;
import com.snszyk.simas.common.enums.PlanCycleEnum;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import com.snszyk.simas.common.enums.SystemModuleEnum;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.user.cache.UserCache;
import com.snszyk.user.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 告警工单视图实体类
 *
 * <AUTHOR>
 * @since 2024-09-07
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "WarningOrderVO对象", description = "WarningOrderVO对象")
public class WarningOrderVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单号
	 */
	@ApiModelProperty(value = "工单号")
	private String orderNo;

	/**
	 * 工单类型
	 */
	@ApiModelProperty(value = "工单类型")
	private String orderType;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 截止时间
	 */
	@ApiModelProperty(value = "截止时间")
	private String endDate;

	/**
	 * 负责部门
	 */
	@ApiModelProperty(value = "负责部门")
	private String executeDept;

	/**
	 * 负责人
	 */
	@ApiModelProperty(value = "负责人")
	private String executeUser;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	public WarningOrderVO toOrderVO(InspectOrderDTO inspectOrder){
		WarningOrderVO vo = Objects.requireNonNull(BeanUtil.copy(inspectOrder, WarningOrderVO.class));
		vo.setOrderNo(inspectOrder.getNo()).setOrderType(SystemModuleEnum.INSPECT_ORDER.getName())
			.setCreateTime(inspectOrder.getCreateTime());
		Dept dept = SysCache.getDept(inspectOrder.getExecuteDept());
		if(Func.isNotEmpty(dept)){
			vo.setExecuteDept(dept.getDeptName());
		}
		if(Func.isNotEmpty(inspectOrder.getExecuteUser())){
			User user = UserCache.getUser(inspectOrder.getExecuteUser());
			if(Func.isNotEmpty(user)){
				vo.setExecuteUser(user.getRealName());
			}
		}
		if(Func.isNotEmpty(inspectOrder.getPlanInfo())) {
			InspectPlan plan = JSONUtil.toBean(inspectOrder.getPlanInfo(), InspectPlan.class);
			switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
				case DAY:
					vo.setEndDate(DateUtil.format(inspectOrder.getEndTime(), "yyyy-MM-dd HH:mm"));
					break;
				case WEEK:
				case MONTH:
					vo.setEndDate(DateUtil.format(inspectOrder.getEndTime(), "yyyy-MM-dd"));
					break;
				default:
			}
		}
		return vo;
	}

	public WarningOrderVO toOrderVO(MaintainOrderDTO maintainOrder){
		WarningOrderVO vo = Objects.requireNonNull(BeanUtil.copy(maintainOrder, WarningOrderVO.class));
		vo.setOrderNo(maintainOrder.getNo()).setOrderType(SystemModuleEnum.MAINTAIN_ORDER.getName())
			.setCreateTime(maintainOrder.getCreateTime());
		Dept dept = SysCache.getDept(maintainOrder.getExecuteDept());
		if(Func.isNotEmpty(dept)){
			vo.setExecuteDept(dept.getDeptName());
		}
		if(Func.isNotEmpty(maintainOrder.getExecuteUser())){
			User user = UserCache.getUser(maintainOrder.getExecuteUser());
			if(Func.isNotEmpty(user)){
				vo.setExecuteUser(user.getRealName());
			}
		}
		if(Func.isNotEmpty(maintainOrder.getPlanInfo())) {
			InspectPlan plan = JSONUtil.toBean(maintainOrder.getPlanInfo(), InspectPlan.class);
			switch (PlanCycleEnum.getByCode(plan.getCycleType())) {
				case DAY:
					vo.setEndDate(DateUtil.format(maintainOrder.getEndTime(), "yyyy-MM-dd HH:mm"));
					break;
				case WEEK:
				case MONTH:
					vo.setEndDate(DateUtil.format(maintainOrder.getEndTime(), "yyyy-MM-dd"));
					break;
				default:
			}
		}
		return vo;
	}

	public WarningOrderVO toOrderVO(RepairDTO repair){
		WarningOrderVO vo = Objects.requireNonNull(BeanUtil.copy(repair, WarningOrderVO.class));
		vo.setOrderNo(repair.getNo()).setOrderType(SystemModuleEnum.INTERNAL_REPAIR.getName())
			.setCreateTime(repair.getCreateTime());
		User user = UserCache.getUser(repair.getReceiveUser());
		if(Func.isNotEmpty(user)){
			vo.setExecuteUser(user.getRealName());
		}
		if(RepairBizTypeEnum.EXTERNAL == RepairBizTypeEnum.getByCode(repair.getBizType())){
			vo.setOrderType(SystemModuleEnum.EXTERNAL_REPAIR.getName());
			user = UserCache.getUser(repair.getFollowUser());
			if(Func.isNotEmpty(user)){
				vo.setExecuteUser(user.getRealName());
			}
		}
		vo.setEndDate(DateUtil.format(repair.getCompleteTime(), "yyyy-MM-dd HH:mm"));
		return vo;
	}


}
