/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 问答
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Data
@ApiModel(value = "问答", description = "问答")
public class CommandQuestionVo {

	/**
	 * 指令路由
	 */
	@NotBlank(message = "指令路由不能为空")
	@ApiModelProperty(value = "指令路由格式:  工单处理|点巡检|工单审核", required = true)
	private String commandRoute;
	/**
	 * 动作（查看、审核等）
	 */
	@ApiModelProperty(value = "动作（查看、审核等）", required = false)
	private String action;

	/**
	 * 用户问题
	 */
//	@NotBlank(message = "用户问题不能为空")
	@ApiModelProperty(value = "用户问题")
	private String question;


}
