/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.common.utils.DateUtils;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.enums.TimeTypeEnumV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 设备状态记录实体类
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Data
@ApiModel(value = "EquipmentStatusRecordVo对象", description = "设备状态记录")
public class EquipmentStatusRecordVo {
	/**
	 * 使用部门id
	 */
	@ApiModelProperty(value = "使用部门id")
	private Long useDeptId;
	/**
	 * 设备等级
	 */
	@ApiModelProperty(value = "设备等级")
	private Integer importantLevel;
	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型id")
	private Long categoryId;

	/**
	 * 统计时间类型
	 */
	@NotNull
	@ApiModelProperty(value = "统计时间类型", required = true)
	private TimeTypeEnumV2 timeType;

	/**
	 * 开始日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "开始日期", hidden = true)
	private LocalDate startDate;

	/**
	 * 结束日期
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "结束日期", hidden = true)
	private LocalDate endDate;


	public void setTimeType(TimeTypeEnumV2 timeType) {
		this.timeType = timeType;

		// 计算开始日期
		this.setStartDate(timeType.computeStartDate());
		// 计算结束日期
		this.setEndDate(DateUtils.calculateYesterday(LocalDate.now()));
	}
}
