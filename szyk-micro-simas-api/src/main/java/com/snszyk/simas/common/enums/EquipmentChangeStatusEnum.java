/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备变更状态字典
 * 待审核 1
 * 审核中 2
 * 驳回 3
 * 已撤销 4
 * 审核完成 5
 * 变更完成 6
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum EquipmentChangeStatusEnum {

	WAIT_CHECK("1", "待审核"),
	CHECKING("2", "审核中"),
	REJECT("3", "驳回"),
	CANCEL("4", "已撤销"),
	IS_COMPLETED("5", "审核完成"),
	CHANGE_COMPLETED("6", "变更完成")
	;

	final String code;
	final String name;

	public static EquipmentChangeStatusEnum getByCode(String code){
		for (EquipmentChangeStatusEnum value : EquipmentChangeStatusEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
