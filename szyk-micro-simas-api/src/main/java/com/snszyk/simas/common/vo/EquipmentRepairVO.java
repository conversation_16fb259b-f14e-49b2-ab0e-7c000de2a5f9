/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.simas.overhaul.vo.RepairRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备维修单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentRepairVO对象", description = "设备维修单表")
public class EquipmentRepairVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 维修单id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修单id")
	private Long repairId;

	/**
	 * 设备维修内容
	 */
	@ApiModelProperty(value = "设备维修内容")
	private RepairRecordVO repairRecord;


}
