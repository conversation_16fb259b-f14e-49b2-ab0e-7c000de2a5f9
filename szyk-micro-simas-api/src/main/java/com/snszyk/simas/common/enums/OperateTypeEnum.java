/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举类
 *
 * <AUTHOR>
 * @date 2024/08/14 10:18
 **/
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

	/**
	 * 新增
	 */
	CREATE("CREATE", "新增"),

	/**
	 * 查看
	 */
	RETRIEVE("RETRIEVE", "查看"),

	/**
	 * 修改
	 */
	UPDATE("UPDATE", "修改"),

	/**
	 * 删除
	 */
	DELETE("DELETE", "删除"),

	;

	final String code;
	final String name;

	public static OperateTypeEnum getByCode(String code){
		for (OperateTypeEnum value : OperateTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
