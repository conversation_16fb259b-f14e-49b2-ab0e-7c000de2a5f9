package com.snszyk.simas.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("simas_equipment_inventory")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentInventory对象", description = "设备盘点记录表")
public class EquipmentInventory extends TenantEntity {

	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "盘点计划id")
	private Long planId;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备台账id")
	private Long equipmentId;


	@ApiModelProperty(value = "sn编码")
	private String sn;

	@ApiModelProperty(value = "台账编码")
	private String code;

	@ApiModelProperty(value = "台账名称")
	private String name;

	@ApiModelProperty(value = "规格型号")
	private String model;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备使用部门id")
	private Long useDeptId;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "使用人id")
	private Long userId;

	@ApiModelProperty(value = "盘点结果（1：正常，2：未盘点，3：盘亏（缺失））")
	private Integer result;

	@ApiModelProperty(value = "盘点次数")
	private Integer times;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "盘点人员")
	private Long inventoryUser;

	@ApiModelProperty(value = "盘点方式（0：扫码，1：NFC，2：RFID，3：其它）")
	private Integer methods;

	@ApiModelProperty(value = "是否更换部门（1：是）")
	private Integer changeUseDept;
}
