/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备资料类型枚举类
 *
 * <AUTHOR>
 * @date 2024/08/26 15:16
 **/
@Getter
@AllArgsConstructor
public enum FileTypeEnum {

	/**
	 * 设备基本资料
	 */
	BASIC(1, "设备基本资料"),
	/**
	 * 设备安装资料
	 */
	INSTALL(2, "设备安装资料"),
	/**
	 * 设备维护资料
	 */
	UPKEEP(3, "设备维护资料"),
	/**
	 * 设备维修资料
	 */
	REPAIR(4, "设备维修资料"),
	/**
	 * 设备基保养资料
	 */
	MAINTAIN(5, "设备保养资料"),
	/**
	 * 设备使用说明书
	 */
	INSTRUCTION(6, "设备使用说明书"),
	;

	final Integer code;
	final String name;

	public static FileTypeEnum getByCode(Integer code){
		for (FileTypeEnum value : FileTypeEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
