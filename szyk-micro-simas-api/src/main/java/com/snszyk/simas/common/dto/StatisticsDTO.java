/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 统计数据
 *
 * <AUTHOR>
 * @date 2024/08/20 20:56
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "StatisticsDTO对象", description = "StatisticsDTO对象")
public class StatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单-执行中数量
	 */
	@ApiModelProperty(value = "工单-执行中数量")
	private Integer inProcessOrderCount;

	/**
	 * 工单-超期数量
	 */
	@ApiModelProperty(value = "工单-超期数量")
	private Integer isOverdueOrderCount;

	/**
	 * 工单-即将超期数量
	 */
	@ApiModelProperty(value = "工单-即将超期数量")
	private Integer expireSoonOrderCount;

	/**
	 * 点巡检工单完成情况
	 */
	@ApiModelProperty(value = "点巡检工单完成情况")
	private List<Map<String, Object>> inspectOrders;

	/**
	 * 保养工单完成情况
	 */
	@ApiModelProperty(value = "保养工单完成情况")
	private List<Map<String, Object>> maintainOrders;

	/**
	 * 日期列表
	 */
	@ApiModelProperty(value = "日期列表")
	private List<String> dateList;

	/**
	 * 正常设备数量列表
	 */
	@ApiModelProperty(value = "正常设备数量列表")
	private List<Integer> normalCountList;

	/**
	 * 异常设备数量列表
	 */
	@ApiModelProperty(value = "异常设备数量列表")
	private List<Integer> abnormalCountList;

	/**
	 * 点巡检设备数量
	 */
	@ApiModelProperty(value = "点巡检设备数量")
	private Integer inspectCount;

	/**
	 * 保养设备数量
	 */
	@ApiModelProperty(value = "保养设备数量")
	private Integer maintainCount;

	/**
	 * 点巡检正常设备数量
	 */
	@ApiModelProperty(value = "点巡检正常设备数量")
	private Integer normalCount;

	/**
	 * 点巡检异常设备数量
	 */
	@ApiModelProperty(value = "点巡检异常设备数量")
	private Integer abnormalCount;

	/**
	 * 漏检设备数量
	 */
	@ApiModelProperty(value = "漏检设备数量")
	private Integer omissionCount;

	public StatisticsDTO(){
		super();
	}

	public StatisticsDTO(Integer inProcessOrderCount, Integer isOverdueOrderCount, Integer expireSoonOrderCount){
		super();
		this.setInProcessOrderCount(inProcessOrderCount);
		this.setIsOverdueOrderCount(isOverdueOrderCount);
		this.setExpireSoonOrderCount(expireSoonOrderCount);
	}


}
