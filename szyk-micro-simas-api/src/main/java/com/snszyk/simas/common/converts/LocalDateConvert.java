package com.snszyk.simas.common.converts;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.WorkBookUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName: LocalDateConvert
 * Package: com.snszyk.simas.excel.converts
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/14 13:49
 */
public class LocalDateConvert implements Converter<LocalDate> {
	public LocalDateConvert() {
	}
	public Class<LocalDate> supportJavaTypeKey() {
		return LocalDate.class;
	}

	public WriteCellData<?> convertToExcelData(LocalDate value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		LocalDateTime localDateTime = value == null ? null : value.atTime(0, 0);
		WriteCellData<?> cellData = new WriteCellData(localDateTime);
		String format = null;
		if (contentProperty != null && contentProperty.getDateTimeFormatProperty() != null) {
			format = contentProperty.getDateTimeFormatProperty().getFormat();
		}

		WorkBookUtil.fillDataFormat(cellData, format, "yyyy-MM-dd");
		return cellData;
	}
}
