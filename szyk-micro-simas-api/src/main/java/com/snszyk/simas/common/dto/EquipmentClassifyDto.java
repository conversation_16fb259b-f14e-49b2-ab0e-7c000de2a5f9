package com.snszyk.simas.common.dto;

import com.snszyk.core.crud.dto.BaseCrudDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class EquipmentClassifyDto extends BaseCrudDto {

	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类id")
	private Long parentId;
	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类名称")
	private String parentName;
	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;
}
