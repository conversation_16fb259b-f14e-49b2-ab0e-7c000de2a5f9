/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单审核配置实体类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel(value = "ApprovalSettingVo对象", description = "工单审核配置")
public class ApprovalSettingAVo {

	private Long id;

	/**
	 * 工单类型
	 */
	@ApiModelProperty(value = "工单类型")
	private String orderType;
	/**
	 * 是否审核 1是  0否
	 */
	@ApiModelProperty(value = "是否审核 1是  0否")
	private Integer isApproval;


}
