package com.snszyk.simas.common.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.snszyk.simas.common.converts.LocalDateConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * ClassName: SpecialEquipmentUsageLogDto
 * Package: com.snszyk.simas.dto
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 16:21
 */
@Data
@ApiModel(value = "SpecialEquipmentUsageLogDto对象", description = "特种设备使用记录")
@ColumnWidth(16)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class SpecialEquipmentUsageLogDto {
	/**
	 * 设备id
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 使用人id
	 */
	@ExcelIgnore
	@ApiModelProperty(value = "使用人id")
	private Long operatorId;
	/**
	 * 使用人姓名
	 */
	@ExcelProperty("操作人员")
	@ApiModelProperty(value = "使用人姓名")
	private String operatorName;
	/**
	 * 开始日期
	 */
	@ExcelProperty(value = "开始时间", converter = LocalDateConvert.class)
	@ApiModelProperty(value = "开始日期")
	private LocalDate startDate;
	/**
	 * 结束日期
	 */
	@ExcelProperty(value = "截止时间", converter = LocalDateConvert.class)
	@ApiModelProperty(value = "结束日期")
	private LocalDate endDate;
	/**
	 * 使用前状态
	 */
	@ExcelProperty(value = "使用前状态")
	@ApiModelProperty(value = "使用前状态")
	private String preUseStatus;
	/**
	 * 使用后状态
	 */
	@ExcelProperty(value = "使用后状态")
	@ApiModelProperty(value = "使用后状态")
	private String postUseStatus;
	/**
	 * 备注
	 */
	@ExcelProperty("备注")
	@ApiModelProperty(value = "备注")
	private String remark;
}
