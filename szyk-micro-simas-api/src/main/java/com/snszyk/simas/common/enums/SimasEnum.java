package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: SimasEnum
 * Package: com.snszyk.simas.common.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2025/3/20 10:28
 */
@AllArgsConstructor
@Getter
public enum SimasEnum {
	/**
	 * 入库类型
	 */
	INBOUND_TYPE("INBOUND_TYPE"),
	/**
	 * 出库类型
	 */
	OUTBOUND_TYPE("OUTBOUND_TYPE"),
	/**
	 * 用途
	 */
	OUTBOUND_USE("OUTBOUND_USE"),

	;
	final String name;
}
