/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备移动来源枚举类
 *
 * <AUTHOR>
 * @date 2025/07/08 13:51
 **/
@Getter
@AllArgsConstructor
public enum MoveSourceEnum {

	/**
	 * 手动新增
	 */
	MANUAL_ADD(1, "手动新增"),

	/**
	 * 设备领用
	 */
	DEVICE_RECEIVE(2, "设备领用"),

	/**
	 * 设备归还
	 */
	DEVICE_BACK(3, "设备归还"),

	;

	final Integer code;
	final String name;

	public static MoveSourceEnum getByCode(Integer code){
		for (MoveSourceEnum value : MoveSourceEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}


}
