/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.snszyk.common.equipment.entity.DeviceMonitor;
import com.snszyk.simas.maintain.entity.MaintainStandard;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备部位表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentMonitorVO对象", description = "设备部位表")
public class EquipmentMonitorVO extends DeviceMonitor {
	private static final long serialVersionUID = 1L;

	/**
	 * 保养标准列表
	 */
	@ApiModelProperty(value = "保养标准列表")
	private List<MaintainStandard> standardList;


}
