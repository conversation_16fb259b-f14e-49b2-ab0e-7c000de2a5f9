package com.snszyk.simas.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ClassName: EquipmentLevelSaveVo
 * Package: com.snszyk.simas.vo
 * Description:
 *
 * <AUTHOR>
 * @Auth zhangzhenpu
 * @Create 2024/11/12 18:51
 */
@Data
@ApiModel(value = "MessageRuleVo对象", description = "规则消息")
@Accessors(chain = true)
public class MessageRuleVo {
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 设备位置名称
	 */
	@ApiModelProperty(value = "设备位置名称")
	private String equipmentLocationName;
	/**
	 * 位置路径
	 */
	@ApiModelProperty(value = "设备位置路径")
	private String equipmentLocationPath;
	/**
	 * 设备重要等级
	 */
	@ApiModelProperty(value = "设备重要等级")
	private Integer importantLevel;
	/**
	 * 设备重要等级名称
	 */
	@ApiModelProperty(value = "设备重要等级名称")
	private String importantLevelName;
	/**
	 * 推送内容
	 */
	@ApiModelProperty(value = "推送内容")
	private String pushContent;
	// /**
	//  * 工单号
	//  */
	// @ApiModelProperty(value = "工单号")
	// private String no;
	// /**
	//  * 来源动作
	//  */
	// @ApiModelProperty(value = "来源动作")
	// private String sourceActionName;
	// /**
	//  * 来源单据类型
	//  */
	// @ApiModelProperty(value = "来源类型")
	// private String contentTypeName;

}
