/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 日志的类型
 * 1.系统操作日志
 * 2.系统运行日志
 **/
@Getter
@AllArgsConstructor
public enum LogTypeEnum {
	SYSTEM_OPERATION("1", "系统操作日志"),
	SYSTEM_RUN("2", "系统运行日志"),
	;
	final String code;
	final String name;

	public static LogTypeEnum getByCode(String code) {
		for (LogTypeEnum value : LogTypeEnum.values()) {
			if (value.getCode().equals(code)) {
				return value;
			}
		}
		return null;
	}
}
