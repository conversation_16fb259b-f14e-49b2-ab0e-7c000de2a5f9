///*
// *      Copyright (c) 2018-2028
// */
//package com.snszyk.simas.common.vo;
//
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.snszyk.core.tool.node.INode;
//import com.snszyk.simas.common.entity.EquipmentFileCategory;
//import io.swagger.annotations.ApiModel;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 设备资料类型表
// *
// * <AUTHOR>
// * @since 2024-08-24
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ApiModel(value = "EquipmentFileCategoryVO对象", description = "设备资料类型表")
//public class EquipmentFileCategoryVO extends EquipmentFileCategory implements INode<EquipmentFileCategoryVO> {
//	private static final long serialVersionUID = 1L;
//
//	/**
//	 * 子孙节点
//	 */
//	@JsonInclude(JsonInclude.Include.NON_EMPTY)
//	private List<EquipmentFileCategoryVO> children;
//
//	/**
//	 * 是否有子孙节点
//	 */
//	@JsonInclude(JsonInclude.Include.NON_EMPTY)
//	private Boolean hasChildren;
//
//	@Override
//	public List<EquipmentFileCategoryVO> getChildren() {
//		if (this.children == null) {
//			this.children = new ArrayList<>();
//		}
//		return this.children;
//	}
//
//	/**
//	 * 上级机构
//	 */
//	private String parentName;
//
//	/**
//	 * 查询关键字
//	 */
//	private String keywords;
//
//}
