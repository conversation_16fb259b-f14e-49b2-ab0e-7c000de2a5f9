/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 图表展示
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "图表展示", description = "图表展示")
public class CommonChartDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "展示名")
	private String name;

	@ApiModelProperty(value = "展示值")
	private String value;


}
