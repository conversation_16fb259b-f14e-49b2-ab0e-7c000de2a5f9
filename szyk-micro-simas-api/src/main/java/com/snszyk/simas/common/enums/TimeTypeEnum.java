package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ClassName: TimeTypeEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 17:22
 */
@AllArgsConstructor
@Getter
public enum TimeTypeEnum {
	/**
	 * 今天
	 */
	TODAY("今天"),
	/**
	 * 近7天
	 */
	SEVEN_DAYS("近7天"),
	/**
	 * 近30天
	 */
	THIRTY_DAYS("近30天"),
	/**
	 * 近180天
	 */
	ONE_HUNDRED_EIGHTY_DAYS("近180天"),
	/**
	 * 近一年
	 */
	ONE_YEAR("近一年");
	String desc;

}
