/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.common.equipment.vo.DeviceAccountVO;
import com.snszyk.simas.inspect.vo.InspectStandardVO;
import com.snszyk.simas.lubricate.vo.LubricateStandardsVO;
import com.snszyk.simas.maintain.vo.MaintainStandardVO;
import com.snszyk.simas.overhaul.vo.OverhaulStandardVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 设备点巡检标准表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentStandardVO对象", description = "设备点巡检标准表")
public class EquipmentStandardVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "设备id")
    private Long equipmentId;

    /**
     * 设备详情
     */
    @ApiModelProperty(value = "设备详情")
    private DeviceAccountVO equipmentAccount;

    /**
     * 部位点巡检标准列表
     */
    @ApiModelProperty(value = "部位点巡检标准列表")
    private List<InspectStandardVO> monitorStandardList;

    /**
     * 部位保养标准列表
     */
    @ApiModelProperty(value = "部位保养标准列表")
    private List<MaintainStandardVO> maintainStandardList;

    /**
     * 部位润滑标准
     */
    @ApiModelProperty(value = "部位润滑标准")
    private List<LubricateStandardsVO> lubricateStandardsList;

    /**
     * 部位检修标准列表
     */
    @ApiModelProperty(value = "部位检修标准列表")
    private List<OverhaulStandardVO> overhaulStandardList;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

	@ApiModelProperty(value = "部位类型")
	private String monitorType;
	@ApiModelProperty(value = "部位类型名称")
	private String monitorTypeName;
    public EquipmentStandardVO() {
        super();
    }

    public EquipmentStandardVO(Long equipmentId) {
        super();
        this.equipmentId = equipmentId;
    }

}
