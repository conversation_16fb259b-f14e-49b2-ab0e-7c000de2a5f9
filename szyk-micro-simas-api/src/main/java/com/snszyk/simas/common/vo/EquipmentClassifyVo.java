package com.snszyk.simas.common.vo;

import com.snszyk.core.crud.dto.BaseCrudDto;
import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "EquipmentClassifyVo", description = "设备台账")
public class EquipmentClassifyVo extends BaseCrudVo {

	/**
	 * 父分类id
	 */
	@ApiModelProperty(value = "父分类id")
	private Long parentId;
	/**
	 * 分类编码
	 */
	@ApiModelProperty(value = "分类编码")
	private String classifyNo;
	/**
	 * 分类名称
	 */
	@ApiModelProperty(value = "分类名称")
	private String classifyName;

}
