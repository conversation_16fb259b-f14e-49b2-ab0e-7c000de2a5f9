package com.snszyk.simas.common.enums;

import com.snszyk.simas.fault.enums.FaultBizStatusEnum;
import com.snszyk.simas.spare.enums.IssuanceOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *工单日志动作
 */
@AllArgsConstructor
@Getter
public enum OrderActionEnum {
	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 工单生成。
	 */
	GEN(OrderStatusEnum.IN_PROCESS.getCode(), "%s 工单生成"),

	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 张三提交了申请单。
	 */
	INIT(OrderStatusEnum.WAIT.getCode(), "%s %s提交了工单"),
	/**
	 * 再次提交
	 * 2024/12/12 8：00：00 张三再次提交了申请单。
	 */
	RE_SUBMIT(OrderStatusEnum.WAIT.getCode(), "%s %s再次提交了工单"),
	/**
	 * 审核通过
	 * 2024/12/12 8：00：00 李四审核通过了申请单。
	 */
	AUDIT_PASS(OrderStatusEnum.WAIT_CONFIRM.getCode(), "%s %s审核通过了工单"),
	/**
	 * 审核不通过
	 * 2024/12/12 8：00：00 李四驳回了申请单，驳回原因“具体原因”。
	 */
	AUDIT_FAIL(OrderStatusEnum.IS_REJECTED.getCode(), "%s %s驳回了工单，驳回原因“%s”"),
	/**
	 * 工单关闭
	 * 2024/12/12 8：00：00 工单关闭，关闭原因“设备完成报废处置”。
	 */
	CANCEL(IssuanceOrderStatusEnum.CANCEL.getCode(), "%s 工单关闭，关闭原因“设备完成报废处置”"),
	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 工单生成。
	 */
	OVERDUE(OrderStatusEnum.IS_OVERDUE.getCode(), "%s 工单超时"),

	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 上报。
	 */
	ABNORMAL_GEN(FaultBizStatusEnum.WAIT_HANDLED.getCode(), "%s %s上报"),

	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 处理。
	 */
	ABNORMAL_HANDLE(FaultBizStatusEnum.IS_HANDLED.getCode(), "%s %s处理"),

	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 报修。
	 */
	ABNORMAL_REPAIR(FaultBizStatusEnum.WAIT_HANDLED.getCode(), "%s %s报修"),

	/**
	 * 初始化
	 * 示例：2024/12/12 8：00：00 报修。
	 */
	ABNORMAL_CLOSE(FaultBizStatusEnum.IS_CLOSED.getCode(), "%s %s关闭"),


	;

	private Integer code;
	private String desc;


}
