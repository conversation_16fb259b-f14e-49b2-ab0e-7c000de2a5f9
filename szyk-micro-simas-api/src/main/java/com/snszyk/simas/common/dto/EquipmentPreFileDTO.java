/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 设备资料表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentPreFileVO对象", description = "设备前期资料表")
public class EquipmentPreFileDTO {
	private static final long serialVersionUID = 1L;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	/**
	 * 资料类型
	 */
	@ApiModelProperty(value = "资料类型")
	private String fileCategoryName;
	/**
	 * 归类
	 */
	@ApiModelProperty(value = "归类")
	private String mainCategoryName;
}
