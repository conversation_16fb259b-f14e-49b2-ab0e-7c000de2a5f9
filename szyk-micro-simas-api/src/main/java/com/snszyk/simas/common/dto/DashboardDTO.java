/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.simas.fault.dto.FaultDefectDTO;
import com.snszyk.simas.lubricate.dto.LubricateOrderDTO;
import com.snszyk.simas.overhaul.dto.RepairDTO;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import com.snszyk.simas.common.vo.WarningOrderVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 大屏数据
 *
 * <AUTHOR>
 * @date 2024/09/07 08:56
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "DashboardDTO对象", description = "DashboardDTO对象")
public class DashboardDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 工单-总数
	 */
	@ApiModelProperty(value = "设备-总数")
	private Integer equipmentTotal;
	@ApiModelProperty(value = "部位-总数")
	private Integer monitorTotal;
	@ApiModelProperty(value = "工单-总数")
	private Integer orderTotal;

	/**
	 * 工单-在用数量
	 */
	@ApiModelProperty(value = "设备-在用数量")
	private Integer inUseCount;

	/**
	 * 工单-闲置数量
	 */
	@ApiModelProperty(value = "设备-闲置数量")
	private Integer idleCount;

	/**
	 * 工单-维修数量
	 */
	@ApiModelProperty(value = "设备-维修数量")
	private Integer inRepairCount;

	/**
	 * 工单-报废数量
	 */
	@ApiModelProperty(value = "设备-报废数量")
	private Integer scrappedCount;

	/**
	 * 维修耗时统计-平均耗时
	 */
	@ApiModelProperty(value = "维修耗时统计-平均耗时")
	private BigDecimal repairAverageTimeTake;

	/**
	 * 维修耗时统计-耗时排名列表
	 */
	@ApiModelProperty(value = "维修耗时统计-耗时排名列表")
	private List<RepairDTO> repairTimeTakeRankList;

	/**
	 * 点巡检覆盖率列表
	 */
	@ApiModelProperty(value = "点巡检覆盖率列表")
	private List<OrderCoverDataDTO> inspectOrderCoverDataList;

	/**
	 * 保养覆盖率列表
	 */
	@ApiModelProperty(value = "保养覆盖率列表")
	private List<OrderCoverDataDTO> maintainOrderCoverDataList;

	/**
	 * 维修情况-维修单列表
	 */
	@ApiModelProperty(value = "维修情况-维修单列表")
	private List<RepairDTO> repairList;

	/**
	 * 工单状态列表
	 */
	@ApiModelProperty(value = "工单状态列表")
	private List<String> orderStatusList;

	/**
	 * 工单状态数量列表
	 */
	@ApiModelProperty(value = "工单状态数量列表")
	private List<Integer> orderStatusCountList;

	/**
	 * 故障缺陷列表
	 */
	@ApiModelProperty(value = "故障缺陷列表")
	private List<FaultDefectDTO> faultDefectList;

	/**
	 * 故障缺陷同比
	 */
	@ApiModelProperty(value = "故障缺陷同比")
	private BigDecimal faultDefectMom;

	/**
	 * 配件与耗材排名列表
	 */
	@ApiModelProperty(value = "配件与耗材排名列表")
	private List<ComponentMaterialVO> componentMaterialRankList;

	/**
	 * 告警工单列表
	 */
	@ApiModelProperty(value = "告警工单列表")
	private List<WarningOrderVO> warningOrderList;

	/**
	 * 中间大图按部门统计设备情况
	 */
	@ApiModelProperty(value = "中间大图按部门统计设备情况")
	private List<EquipmentAccountDTO> equipmentList;

	@ApiModelProperty(value = "润滑情况")
	private List<LubricateOrderDTO> lubricateList;

	@ApiModelProperty(value = "二级单位的设备使用情况")
	private List<DashboardSecEquipmentDTO> secEquipmentList=new ArrayList<>();


}
