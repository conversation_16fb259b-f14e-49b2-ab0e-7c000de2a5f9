/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.dto;

import com.snszyk.simas.inventory.dto.EquipmentInventoryPlanTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 工作台数据视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Data
@ApiModel(value = "WorkbenchDataVO对象", description = "WorkbenchDataVO对象")
public class WorkbenchDataDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 点巡检数据
	 */
	@ApiModelProperty(value = "点巡检数据")
	private StatisticsDTO inspectData;

	/**
	 * 保养数据
	 */
	@ApiModelProperty(value = "保养数据")
	private StatisticsDTO maintainData;

	/**
	 * 内部维修单数据
	 */
	@ApiModelProperty(value = "内部维修单数据")
	private StatisticsDTO repairInternalData;

	/**
	 * 外委维修单数据
	 */
	@ApiModelProperty(value = "外委维修单数据")
	private StatisticsDTO repairExternalData;

	/**
	 * 润滑数据
	 */
	@ApiModelProperty(value = "润滑数据")
	private StatisticsDTO lubricateData;
	/**
	 * 检修数据
	 */
	@ApiModelProperty(value = "检修数据")
	private StatisticsDTO overhaulData;

	/**
	 * 设备盘点工单统计
	 */
	@ApiModelProperty(value = "设备盘点工单统计")
	private InventoryStatisticsDTO equipmentInventoryData;

	/**
	 * 备品备件盘点工单统计
	 */
	@ApiModelProperty(value = "备品备件盘点工单统计")
	private InventoryStatisticsDTO sparePartsInventoryData;

}
