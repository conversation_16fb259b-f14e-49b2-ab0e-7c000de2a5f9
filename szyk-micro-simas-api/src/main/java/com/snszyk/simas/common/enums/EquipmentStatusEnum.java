/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备状态枚举类
 *
 * <AUTHOR>
 * @date 2024/08/13 15:16
 **/
@Getter
@AllArgsConstructor
public enum EquipmentStatusEnum {

	/**
	 * 备用
	 */
	IDLE(1, "备用"),
	/**
	 * 在用
	 */
	IN_USE(2, "在用"),
	/**
	 * 维修
	 */
	IN_REPAIR(3, "维修"),
	/**
	 * 报废
	 */
	SCRAPPED(4, "报废"),
	;

	final Integer code;
	final String name;

	public static EquipmentStatusEnum getByCode(Integer code){
		for (EquipmentStatusEnum value : EquipmentStatusEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}

}
