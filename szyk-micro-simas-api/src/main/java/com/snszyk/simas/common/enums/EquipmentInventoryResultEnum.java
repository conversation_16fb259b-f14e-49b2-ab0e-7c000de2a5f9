package com.snszyk.simas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EquipmentInventoryResultEnum {

	NORMAL(1, "正常"),
	NOT_START(2, "未盘点"),
	LOSS(3, "缺失"),
	;

	final Integer code;
	final String name;

	public static EquipmentInventoryResultEnum getByCode(Integer code){
		for (EquipmentInventoryResultEnum value : EquipmentInventoryResultEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return null;
	}
}
