package com.snszyk.simas.common.entity;

import com.snszyk.simas.common.enums.BizTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *工单的数据权限
 * todo 后期可以改为动态数据权限
 */
@AllArgsConstructor
@Getter
public enum OrderQueryDataAuthEnum {

	/**
	 * 全部
	 */
	ADMIN(0, "全部"),

	/**
	 * 操作人
	 */
	OPERATE_USER(1, "查看本人的及分配给部门的工单"),

	/**
	 * 其他人 查看本部门的工单
	 */
	OTHER_USER(2, "查看分配给本部门的工单"),
	;

	final Integer code;
	final String name;

	public static BizTypeEnum getByCode(String code){
		for (BizTypeEnum value : BizTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}
}
