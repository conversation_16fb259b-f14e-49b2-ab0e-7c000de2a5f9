package com.snszyk.simas.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class EquipmentInventoryCountDTO {

	@ApiModelProperty(value = "全部")
	private int total;
	@ApiModelProperty(value = "正常")
	private int normal;
	@ApiModelProperty(value = "未盘点")
	private int notStart;
	@ApiModelProperty(value = "盘亏")
	private int loss;
}
