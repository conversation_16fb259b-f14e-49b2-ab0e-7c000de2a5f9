/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备变更附件 实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("simas_equipment_change_file")
@EqualsAndHashCode(callSuper = true)
public class EquipmentChangeFile extends TenantEntity {

	/**
	 * 业务类型(1.风险评估资料 2验收图片)
	 */
	private String businessType;
	/**
	 * 附件id
	 */
	private Long attachId;
	/**
	 * 风险评估资料类型 字典
	 */
	private String riskType;
	/**
	 * 变更id
	 */
	private Long changeId;


}
