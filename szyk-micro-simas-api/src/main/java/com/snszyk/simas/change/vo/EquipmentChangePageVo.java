/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备变更表实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChangeVo对象", description = "设备变更表")
public class EquipmentChangePageVo extends Page {


	/**
	 * 变更单号
	 */
	@ApiModelProperty(value = "变更单号")
	private String changeNumber;
	/**
	 * 变更名称
	 */
	@ApiModelProperty(value = "变更名称")
	private String changeName;

	/**
	 * 变更状态
	 */
	@ApiModelProperty(value = "变更状态")
	private String changeStatusList;
	private List<String> changeStatusListParam=new ArrayList<>();


	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "开始时间")
	private LocalDate startApplyDate;

	@ApiModelProperty(value = "开始时间",hidden=true)
	private LocalDateTime startApplyTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "结束时间")
	private LocalDate endApplyDate;

	@ApiModelProperty(value = "结束时间",hidden=true)
	private LocalDateTime endApplyTime;

	@ApiModelProperty(value = "租户id",hidden=true)
	private String tenantId;
	@ApiModelProperty(value = "申请人名")
	private String applyUserName;

}
