/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 设备变更附件实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChangeFileVo对象", description = "设备变更附件")
public class EquipmentChangeFilePageVo extends Page {

    /**
     * 业务类型(1.风险评估资料 2验收图片)
     */
        @ApiModelProperty(value = "业务类型(1.风险评估资料 2验收图片)")
        private String businessType;
    /**
     * 附件id
     */
        @ApiModelProperty(value = "附件id")
        private Long attachId;
    /**
     * 风险评估资料类型 字典
     */
        @ApiModelProperty(value = "风险评估资料类型 字典")
        private String riskType;
    /**
     * 变更id
     */
        @ApiModelProperty(value = "变更id")
        private Long changeId;


}
