package com.snszyk.simas.change.vo;

import com.snszyk.simas.change.entity.EquipmentChangeFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class EquipmentChangeAcceptVO {

	@NotNull
	@ApiModelProperty(value = "业务单id",required = true)
	private Long id;
	/**
	 * 改造评价
	 */
	private String renovationEvaluation;

	@ApiModelProperty(value = "评价图片")
	@Valid
	private List<EquipmentChangeFile> imageList=new ArrayList<>();

}
