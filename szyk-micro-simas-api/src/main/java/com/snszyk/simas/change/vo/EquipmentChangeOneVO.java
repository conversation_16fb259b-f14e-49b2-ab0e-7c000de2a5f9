package com.snszyk.simas.change.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class EquipmentChangeOneVO {

	@NotNull
	@ApiModelProperty(value = "资料变更的id",required = true)
	private Long updateId;

	@NotNull
	@ApiModelProperty(value = "资料id",required = true)
	private Long equipmentFileId;

	@NotNull
	@ApiModelProperty(value = "附件id,资料的字段",required = true)
	private String attachId;

	@ApiModelProperty(value = "资料名称,资料的字段")
	private String name;

	@ApiModelProperty(value = "资料类型id,资料的字段")
	private  Long fileCategoryId;

	@ApiModelProperty(value = "归类类型id,资料的字段")
	private Long type;

	@ApiModelProperty(value = "备注,资料的字段")
	private String remark;

}
