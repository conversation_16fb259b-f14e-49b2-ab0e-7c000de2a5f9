/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 设备变更表 实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("simas_equipment_change")
@EqualsAndHashCode(callSuper = true)
public class EquipmentChange extends TenantEntity {

	/**
	 * 设备ID
	 */
	private Long equipmentId;
	/**
	 * 变更单号
	 */
	private String changeNumber;
	/**
	 * 变更名称
	 */
	private String changeName;
	/**
	 * 变更原因
	 */
	private String changeReason;
	/**
	 * 变更目的
	 */
	private String changePurpose;
	/**
	 * 变更类型
	 */
	private String changeCategory;
	/**
	 * 预计实施时间
	 */
	private LocalDate expectedImplementationDate;
	/**
	 * 变更内容
	 */
	private String changeContent;
	/**
	 * 实施方案
	 */
	private String implementationPlan;
	/**
	 * 预期效果
	 */
	private String expectedEffect;
	/**
	 * 备注
	 */
	private String remarks;
	/**
	 * 审批流ID
	 */
	private Long approvalFlowId;
	/**
	 * 变更状态
	 */
	private String changeStatus;
	/**
	 * 改造评价
	 */
	private String renovationEvaluation;
	/**
	 * 驳回人
	 */
	private Long rejectionPerson;
	/**
	 * 驳回时间
	 */
	private LocalDateTime rejectionTime;
	/**
	 * 驳回原因
	 */
	private String rejectionReason;
	/**
	 * 审核人
	 */
	private Long auditPerson;
	/**
	 * 审核时间
	 */
	private LocalDateTime auditTime;


}
