/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.vo;

import com.snszyk.simas.common.vo.EquipmentFileUpdateAVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备变更表实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@ApiModel(value = "EquipmentChangeVo对象", description = "设备变更表")
public class EquipmentChangeAVo {

	private Long id;

	/**
	 * 设备ID
	 */
	@NotNull
	@ApiModelProperty(value = "设备ID",required = true)
	private Long equipmentId;
	/**
	 * 变更单号
	 */
	@ApiModelProperty(value = "变更单号")
	private String changeNumber;
	/**
	 * 变更名称
	 */
	@ApiModelProperty(value = "变更名称")
	private String changeName;
	/**
	 * 变更原因
	 */
	@ApiModelProperty(value = "变更原因")
	private String changeReason;
	/**
	 * 变更目的
	 */
	@ApiModelProperty(value = "变更目的")
	private String changePurpose;
	/**
	 * 变更类型
	 */
	@ApiModelProperty(value = "变更类型")
	private String changeCategory;
	/**
	 * 预计实施时间
	 */
	@ApiModelProperty(value = "预计实施时间")
	private LocalDate expectedImplementationDate;
	/**
	 * 变更内容
	 */
	@ApiModelProperty(value = "变更内容")
	private String changeContent;
	/**
	 * 实施方案
	 */
	@ApiModelProperty(value = "实施方案")
	private String implementationPlan;
	/**
	 * 预期效果
	 */
	@ApiModelProperty(value = "预期效果")
	private String expectedEffect;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remarks;

	@Valid
	@ApiModelProperty(value = "风险评估资料")
	private List<EquipmentChangeFileAVo> riskFileList=new ArrayList<>();

	@Valid
	@ApiModelProperty(value = "变更文件")
	private List<EquipmentFileUpdateAVo> changeFileList=new ArrayList<>();
}
