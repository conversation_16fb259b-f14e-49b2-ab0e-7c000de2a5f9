/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备变更附件实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@ApiModel(value = "EquipmentChangeFileVo对象", description = "设备变更附件")
public class EquipmentChangeFileAVo {

	@ApiModelProperty(value = "评估资料的id,更新的时候传")
	private Long id;

	/**
	 * 业务类型(1.风险评估资料 2验收图片)
	 */
	@ApiModelProperty(value = "业务类型(1.风险评估资料 2验收图片)",hidden = true)
	private String businessType;
	/**
	 * 附件id
	 */
	@NotNull
	@ApiModelProperty(value = "附件id",required = true)
	private Long attachId;
	/**
	 * 风险评估资料类型 字典
	 */
	@NotBlank
	@ApiModelProperty(value = "风险评估资料类型 字典",required = true)
	private String riskType;
	/**
	 * 变更id
	 */
	@ApiModelProperty(value = "变更id",hidden = true)
	private Long changeId;


}
