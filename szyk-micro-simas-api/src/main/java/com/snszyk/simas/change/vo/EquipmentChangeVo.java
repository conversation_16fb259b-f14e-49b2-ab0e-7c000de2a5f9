/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.change.vo;

import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 设备变更表实体类
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentChangeVo对象", description = "设备变更表")
public class EquipmentChangeVo extends BaseCrudVo {

	/**
	 * 设备ID
	 */
	@ApiModelProperty(value = "设备ID")
	private Long equipmentId;
	/**
	 * 变更单号
	 */
	@ApiModelProperty(value = "变更单号")
	private String changeNumber;
	/**
	 * 变更名称
	 */
	@ApiModelProperty(value = "变更名称")
	private String changeName;
	/**
	 * 变更原因
	 */
	@ApiModelProperty(value = "变更原因")
	private String changeReason;
	/**
	 * 变更目的
	 */
	@ApiModelProperty(value = "变更目的")
	private String changePurpose;
	/**
	 * 变更类型
	 */
	@ApiModelProperty(value = "变更类型")
	private String changeCategory;
	/**
	 * 预计实施时间
	 */
	@ApiModelProperty(value = "预计实施时间")
	private LocalDate expectedImplementationDate;
	/**
	 * 变更内容
	 */
	@ApiModelProperty(value = "变更内容")
	private String changeContent;
	/**
	 * 实施方案
	 */
	@ApiModelProperty(value = "实施方案")
	private String implementationPlan;
	/**
	 * 预期效果
	 */
	@ApiModelProperty(value = "预期效果")
	private String expectedEffect;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remarks;
	/**
	 * 审批流ID
	 */
	@ApiModelProperty(value = "审批流ID")
	private Long approvalFlowId;
	/**
	 * 变更状态
	 */
	@ApiModelProperty(value = "变更状态")
	private String changeStatus;
	/**
	 * 改造评价
	 */
	@ApiModelProperty(value = "改造评价")
	private String renovationEvaluation;
	/**
	 * 驳回人
	 */
	@ApiModelProperty(value = "驳回人")
	private Long rejectionPerson;
	/**
	 * 驳回时间
	 */
	@ApiModelProperty(value = "驳回时间")
	private LocalDateTime rejectionTime;
	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectionReason;
	/**
	 * 审核人
	 */
	@ApiModelProperty(value = "审核人")
	private Long auditPerson;
	/**
	 * 审核时间
	 */
	@ApiModelProperty(value = "审核时间")
	private LocalDateTime auditTime;


}
