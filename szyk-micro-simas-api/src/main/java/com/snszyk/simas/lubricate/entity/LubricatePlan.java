/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


@Data
@Accessors(chain = true)
@TableName("simas_lubricate_plan")
@ApiModel(value = "LubricatePlan对象", description = "润滑计划")
public class LubricatePlan extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 计划名称
	 */
	@ApiModelProperty(value = "计划名称")
	private String name;

	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String no;

	/**
	 * 计划开始时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "计划开始时间")
	private Date startTime;

	/**
	 * 计划截止时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "计划截止时间")
	private Date endTime;

	/**
	 * 负责人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "负责人")
	private Long chargeUser;

	/**
	 * 负责部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "负责部门")
	private Long chargeDept;

}
