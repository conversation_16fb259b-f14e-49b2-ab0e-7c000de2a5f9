/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.vo;

import com.snszyk.simas.lubricate.entity.LubricateStandards;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateStandardsVO对象", description = "润滑标准")
public class LubricateStandardsVO extends LubricateStandards {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	@ApiModelProperty(value = "设备部位")
	private String equipmentMonitorName;
	@ApiModelProperty(value = "油品类型")
	private String oilTypeName;
	@ApiModelProperty(value = "润滑手段")
	private String methodsName;

	@ApiModelProperty(value = "部位类型")
	private Integer monitorType;
	@ApiModelProperty(value = "部位类型名称")
	private String monitorTypeName;


}
