package com.snszyk.simas.lubricate.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class LubricatePlanEquipmentOrderDTO {

	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String code;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String name;

	/**
	 * 设备部位名称
	 */
	@ApiModelProperty(value = "设备部位名称")
	private String equipmentMonitorName;

	/**
	 * 油品类型名称
	 */
	@ApiModelProperty(value = "油品类型名称")
	private String oilTypeName;

	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String orderNo;

	/**
	 * 业务单状态
	 */
	@ApiModelProperty(value = "业务单状态")
	private String bizStatus;

}
