package com.snszyk.simas.lubricate.vo;

import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateOrderDTO对象", description = "润滑工单")
public class LubricateOrderVO extends LubricateOrder {
	private static final long serialVersionUID = 1L;

	/**
	 * 关键字
	 */
	@ApiModelProperty(value = "关键字")
	private String keywords;

	/**
	 * 查询-开始日期
	 */
	@ApiModelProperty(value = "查询-开始日期")
	private String queryStartDate;

	/**
	 * 查询-结束日期
	 */
	@ApiModelProperty(value = "查询-结束日期")
	private String queryEndDate;

	/**
	 * 超时时间间隔
	 */
	@ApiModelProperty(value = "超时时间间隔")
	private BigDecimal timeInterval;

	@ApiModelProperty(value = "浮动时间")
	private Integer floatTime;

	@ApiModelProperty(value = "备件消耗列表")
	private List<ComponentMaterialVO> materialList;

	@ApiModelProperty("责任部门ids")
	private List<Long> chargeDeptIdList;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty("创建开始日期")
	private LocalDate startCreateDate;

	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty("创建结束日期")
	private LocalDate endCreateDate;
	/**
	 * 创建时间的开始日期
	 */
	@ApiModelProperty(value = "创建时间的开始日期")
	private LocalDateTime startCreateTime;
	/**
	 * 创建时间的结束日期
	 */
	@ApiModelProperty(value = "创建时间的结束日期")
	private LocalDateTime endCreateTime;
	/**
	 * 设备id列表
	 */
	@ApiModelProperty(value = "设备Ids")
	private List<Long> equipmentIds;
	/**
	 * 工单id列表
	 */
	@ApiModelProperty("工单id列表")
	private List<Long> orderIds;

	@ApiModelProperty("工单状态列表")
	private List<Integer> statusList;

	@ApiModelProperty(value = "数据权限角色 0 全部 1 操作人 2 其他")
	private Integer queryAuthRole;

	@ApiModelProperty(value = "仅查看执行人")
	private Long onlyQueryExecuteUser;
	@ApiModelProperty(value = "不等于状态")
	private Integer neStatus;

	public void setStartCreateDate(LocalDate startCreateDate) {
		this.startCreateDate = startCreateDate;
		if (ObjectUtil.isNotEmpty(this.getStartCreateDate())) {
			this.startCreateTime = this.startCreateDate.atStartOfDay();
		}
	}

	public void setEndCreateDate(LocalDate endCreateDate) {
		this.endCreateDate = endCreateDate;
		if (ObjectUtil.isNotEmpty(this.getEndCreateDate())) {
			this.endCreateTime = this.endCreateDate.plusDays(1).atStartOfDay();
		}
	}
}
