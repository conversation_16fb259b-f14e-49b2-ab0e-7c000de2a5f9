/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.dto;

import com.snszyk.simas.lubricate.entity.LubricateOilType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateOilTypeDTO对象", description = "油品类型")
public class LubricateOilTypeDTO extends LubricateOilType {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "创建人")
	private String createUserName;
	
	@ApiModelProperty(value = "更新人")
	private String updateUserName;


}
