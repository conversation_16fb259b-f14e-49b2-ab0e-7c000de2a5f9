package com.snszyk.simas.lubricate.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.snszyk.resource.entity.Attach;
import com.snszyk.simas.lubricate.entity.LubricateOrder;
import com.snszyk.simas.spare.vo.ComponentMaterialVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateOrderDTO对象", description = "润滑工单")
public class LubricateOrderDTO extends LubricateOrder {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	@ApiModelProperty(value = "设备编码")
	private String equipmentCode;

	@ApiModelProperty(value = "设备部位")
	private String equipmentMonitorName;
	private Long equipmentMonitorId;

	/**
	 * 地点名称
	 */
	@ApiModelProperty(value = "地点名称")
	private String locationName;

	/**
	 * 地点名称
	 */
	@ApiModelProperty(value = "地点路径名称")
	private String locationPath;

	@ApiModelProperty(value = "润滑周期")
	private Integer lubricateCycle;

	@ApiModelProperty(value = "油品类型")
	private String oilTypeName;

	@ApiModelProperty(value = "润滑方法")
	private String methodsName;

	@ApiModelProperty(value = "负责部门")
	private String chargeDeptName;

	@ApiModelProperty(value = "负责人员")
	private String chargeUserName;

	@ApiModelProperty(value = "执行人员")
	private String executeUserName;

	@ApiModelProperty(value = "检查人员")
	private String checkUserName;
	private String approvalUserName;

	@ApiModelProperty(value = "状态名称")
	private String statusName;


	/**
	 * 执行浮动时间
	 */
	@ApiModelProperty(value = "执行浮动时间")
	private Integer floatTime;


	@ApiModelProperty(value = "图片")
	private List<Attach> attachList;

	@ApiModelProperty(value = "备品备件消耗")
	private List<ComponentMaterialVO> materialList;

	@ExcelProperty(value = "设备类型")
	private String equipmentCategoryName;

	@ExcelProperty(value = "是否需要审核 ")
	private Boolean isNeedApproval = false;


	/**
	 * 异常等级
	 */
	@ApiModelProperty(value = "异常等级")
	private String abnormalLevelName;

	/**
	 * 是否现场处理
	 */
	@ApiModelProperty(value = "是否现场处理")
	private String isHandledName;

	@ApiModelProperty(value = "异常状态")
	private String abnormalStatus;

}
