/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@Accessors(chain = true)
@TableName("simas_lubricate_plan_equipment")
@ApiModel(value = "LubricatePlanEquipment对象", description = "润滑计划设备关联表")
public class LubricatePlanEquipment implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "计划id")
	private Long planId;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 标准信息（已结束计划存储）
	 */
	@ApiModelProperty(value = "标准信息")
	private String standardsInfo;

	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 是否删除
	 */
	@TableLogic
	@ApiModelProperty(value = "是否删除")
	private Integer isDeleted;

}
