/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.dto;

import com.snszyk.simas.lubricate.entity.LubricateMethods;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateMethodsDTO对象", description = "润滑手段")
public class LubricateMethodsDTO extends LubricateMethods {

	@ApiModelProperty(value = "创建人名称")
	private String createUserName;

	@ApiModelProperty(value = "更新人名称")
	private String updateUserName;

}
