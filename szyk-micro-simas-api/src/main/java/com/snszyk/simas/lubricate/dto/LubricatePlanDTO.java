package com.snszyk.simas.lubricate.dto;

import com.snszyk.simas.lubricate.entity.LubricatePlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricatePlanDTO对象", description = "计划详情")
public class LubricatePlanDTO extends LubricatePlan {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "负责部门名称")
	private String chargeDeptName;

	@ApiModelProperty(value = "负责人名称")
	private String chargeUserName;

	@ApiModelProperty(value = "制定计划人员")
	private String createUserName;

	@ApiModelProperty(value = "设备数量")
	private Integer equipmentCount;

	@ApiModelProperty(value = "状态名称")
	private String statusName;

	@ApiModelProperty(value = "编码")
	private List<LubricatePlanEquipmentMonitorDTO> equipmentMonitorList;

	@ApiModelProperty(value = "设备工单执行情况")
	private List<LubricatePlanEquipmentOrderDTO> equipmentOrderList;

	@ApiModelProperty(value = "修改人名称")
	private String updateUserName;
}
