/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.vo;


import com.snszyk.simas.lubricate.entity.LubricateMethods;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateMethods对象", description = "润滑手段")
public class LubricateMethodsVO extends LubricateMethods {

}
