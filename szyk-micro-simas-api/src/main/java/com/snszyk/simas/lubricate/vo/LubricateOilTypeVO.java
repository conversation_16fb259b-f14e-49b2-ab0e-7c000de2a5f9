/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.vo;

import com.snszyk.simas.lubricate.entity.LubricateOilType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateOilTypeVO对象", description = "油品类型")
public class LubricateOilTypeVO extends LubricateOilType {

	private static final long serialVersionUID = 1L;


}
