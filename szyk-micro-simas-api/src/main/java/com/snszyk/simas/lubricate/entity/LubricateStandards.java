/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
@Accessors(chain = true)
@TableName("simas_lubricate_standards")
@ApiModel(value = "LubricateStandards对象", description = "润滑标准")
public class LubricateStandards implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty("主键id")
	@TableId(
		value = "id",
		type = IdType.ASSIGN_ID
	)
	private Long id;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 设备部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备部位id")
	private Long equipmentMonitorId;

	/**
	 * 油品类型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "油品类型id")
	private Long oilTypeId;

	/**
	 * 润滑手段id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "润滑手段id")
	private Long lubricateMethodsId;

	/**
	 * 润滑周期
	 */
	@ApiModelProperty(value = "润滑周期")
	private Integer lubricateCycle;

	/**
	 * 执行浮动时间
	 */
	@ApiModelProperty(value = "执行浮动时间")
	private Integer floatTime;

	/**
	 * 开始执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "开始执行时间")
	private Date startTime;

	/**
	 * 是否删除
	 */
	@TableLogic
	@ApiModelProperty(value = "是否删除")
	private Integer isDeleted;

	@ApiModelProperty(value = "排序")
	private Integer sort;
}
