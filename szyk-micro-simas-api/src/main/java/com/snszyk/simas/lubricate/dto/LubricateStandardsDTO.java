/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.dto;

import com.snszyk.simas.lubricate.entity.LubricateStandards;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateStandardsDTO对象", description = "润滑标准")
public class LubricateStandardsDTO extends LubricateStandards {

	private static final long serialVersionUID = 1L;


}
