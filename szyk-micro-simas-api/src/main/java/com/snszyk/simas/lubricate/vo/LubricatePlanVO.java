/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.vo;

import com.snszyk.simas.lubricate.entity.LubricatePlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricatePlanVO对象", description = "润滑计划")
public class LubricatePlanVO extends LubricatePlan {

	private static final long serialVersionUID = 1L;

	/**
	 * 查询-开始日期
	 */
	@ApiModelProperty(value = "查询-开始日期")
	private String queryStartDate;

	/**
	 * 查询-结束日期
	 */
	@ApiModelProperty(value = "查询-结束日期")
	private String queryEndDate;

	/**
	 * 所选设备部位列表
	 */
	@ApiModelProperty(value = "所选设备部位列表")
	private List<LubricatePlanEquipmentMonitorVO> equipmentMonitors;


}
