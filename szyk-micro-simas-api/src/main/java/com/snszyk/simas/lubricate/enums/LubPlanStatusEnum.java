/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 润滑计划状态枚举类
 *
 * <AUTHOR>
 * @date 2024/10/12 11:24
 **/
@Getter
@AllArgsConstructor
public enum LubPlanStatusEnum {

	/**
	 * 未开始
	 */
	NO_START(0, "未开始"),
	/**
	 * 执行中
	 */
	IN_PROGRESS(1, "执行中"),
	/**
	 * 已完成
	 */
	IS_COMPLETED(2, "已完成"),
	/**
	 * 已终止
	 */
	IS_TERMINATED(3, "已终止"),
	;

	final Integer code;
	final String name;

	public static LubPlanStatusEnum getByCode(Integer code){
		for (LubPlanStatusEnum value : LubPlanStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
