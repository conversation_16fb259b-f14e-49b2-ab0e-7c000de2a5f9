package com.snszyk.simas.lubricate.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("simas_lubricate_methods")
@ApiModel(value = "LubricateMethods对象", description = "润滑手段")
public class LubricateMethods extends TenantEntity {

	private static final long serialVersionUID = 1L;


	/**
	 * 类型名称
	 */
	@ApiModelProperty(value = "类型名称")
	private String name;

	@ApiModelProperty(value = "备注")
	private String remark;


}
