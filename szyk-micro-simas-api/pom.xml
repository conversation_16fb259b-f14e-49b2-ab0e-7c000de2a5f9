<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.snszyk</groupId>
        <artifactId>szyk-micro</artifactId>
        <version>device-simas-1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>szyk-micro-simas-api</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-system-api</artifactId>
            <version>${szyk.device.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-message-api</artifactId>
            <version>${szyk.device.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-dict-api</artifactId>
            <version>${szyk.device.basic.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel-core</artifactId>
            <version>3.1.1</version>
            <scope>compile</scope>
        </dependency>
        <!-- mapStruct-lombok -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.4.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.4.1.Final</version>
        </dependency>
        <!-- mapStruct-lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>0.2.0</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snszyk</groupId>
            <artifactId>szyk-micro-common-api</artifactId>
            <version>${szyk.device.common.version}</version>
        </dependency>
    </dependencies>

</project>
