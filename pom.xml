<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.snszyk</groupId>
    <artifactId>szyk-micro</artifactId>
    <version>device-simas-1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <szyk.project.version>2.0.0.RELEASE</szyk.project.version>
        <szyk.device.basic.version>device-basic-1.0-SNAPSHOT</szyk.device.basic.version>
        <szyk.device.simas.version>device-simas-1.0-SNAPSHOT</szyk.device.simas.version>
        <szyk.device.common.version>device-common-1.0-SNAPSHOT</szyk.device.common.version>
        <java.version>1.8</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <flowable.version>6.4.2</flowable.version>

        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
        <spring.cloud.version>Hoxton.SR12</spring.cloud.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>

    </properties>

    <modules>
        <module>szyk-micro-simas</module>
        <module>szyk-micro-simas-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.snszyk.platform</groupId>
                <artifactId>szyk-bom</artifactId>
                <version>${szyk.project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.snszyk</groupId>
                <artifactId>szyk-micro-common</artifactId>
                <version>${szyk.device.basic.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 打jar包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
            </plugin>
        </plugins>
    </build>

    <!-- 阿里云效制品库 -->
    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2127288-release-fXWqLV/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2127288-snapshot-VbfF5u/</url>
        </snapshotRepository>
    </distributionManagement>

    <!--    <repositories>-->
    <!--        <repository>-->
    <!--            <id>aliyun-repos</id>-->
    <!--            <url>https://maven.aliyun.com/repository/public/</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>false</enabled>-->
    <!--            </snapshots>-->
    <!--        </repository>-->
    <!--        <repository>-->
    <!--            <id>szyk-release</id>-->
    <!--            <name>Release Repository</name>-->
    <!--            <url>http://nexus.szyk.vip/repository/maven-releases/</url>-->
    <!--        </repository>-->
    <!--    </repositories>-->

    <!--    <pluginRepositories>-->
    <!--        <pluginRepository>-->
    <!--            <id>aliyun-plugin</id>-->
    <!--            <url>https://maven.aliyun.com/repository/public/</url>-->
    <!--            <snapshots>-->
    <!--                <enabled>false</enabled>-->
    <!--            </snapshots>-->
    <!--        </pluginRepository>-->
    <!--    </pluginRepositories>-->

</project>
